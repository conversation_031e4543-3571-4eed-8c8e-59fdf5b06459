<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multiple Listeners Fix Test - React Error #185</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        .error-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .fix-summary {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>Multiple Listeners Fix Test - React Error #185</h1>
    <p>This page tests the comprehensive fix for React Error #185 that addresses multiple language change listeners.</p>

    <div class="fix-summary">
        <h3>🔧 Fixes Applied:</h3>
        <ul>
            <li><strong>App.tsx</strong>: setTimeout instead of requestAnimationFrame</li>
            <li><strong>StatusBar.tsx</strong>: Removed forceUpdate, use Zustand store</li>
            <li><strong>LanguageSelector.tsx</strong>: setTimeout for safe state updates</li>
            <li><strong>Architecture</strong>: Single source of truth for language state</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>Test Status</h2>
        <div id="test-status">
            <span class="status-indicator status-warning"></span>
            <span>Ready to test - Load the Recall extension and switch languages</span>
        </div>
    </div>

    <div class="test-container">
        <h2>Error Monitoring</h2>
        <p>Any React Error #185 occurrences will be logged below:</p>
        <div id="error-log" class="error-log">No errors detected yet...</div>
        <button class="test-button" onclick="clearErrorLog()">Clear Log</button>
    </div>

    <div class="test-container">
        <h2>Test Instructions</h2>
        <ol>
            <li>Load the Recall extension in Chrome (dist folder as unpacked extension)</li>
            <li>Open the extension popup</li>
            <li>Switch languages multiple times rapidly</li>
            <li>Check this page for any React Error #185 occurrences</li>
            <li>Test different scenarios: first load, rapid switching, etc.</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>Expected Results</h2>
        <div style="color: #28a745;">
            ✅ <strong>Success Criteria:</strong>
            <ul>
                <li>No "Maximum update depth exceeded" errors</li>
                <li>Smooth language switching without console errors</li>
                <li>All components update correctly with new language</li>
                <li>No performance issues during rapid switching</li>
            </ul>
        </div>
    </div>

    <script>
        // Error monitoring setup
        let errorCount = 0;
        const errorLog = document.getElementById('error-log');
        const testStatus = document.getElementById('test-status');

        // Capture console errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            
            const errorMessage = args.join(' ');
            if (errorMessage.includes('Maximum update depth exceeded') || 
                errorMessage.includes('Error #185')) {
                logError('React Error #185 Detected: ' + errorMessage);
            }
        };

        // Capture unhandled errors
        window.addEventListener('error', function(event) {
            if (event.message.includes('Maximum update depth exceeded') || 
                event.message.includes('Error #185')) {
                logError('Unhandled React Error #185: ' + event.message);
            }
        });

        // Capture unhandled promise rejections
        window.addEventListener('unhandledrejection', function(event) {
            if (event.reason && event.reason.toString().includes('Maximum update depth exceeded')) {
                logError('Promise Rejection - React Error #185: ' + event.reason);
            }
        });

        function logError(message) {
            errorCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ERROR #${errorCount}: ${message}\n`;
            
            if (errorLog.textContent === 'No errors detected yet...') {
                errorLog.textContent = '';
            }
            errorLog.textContent += logEntry;
            errorLog.scrollTop = errorLog.scrollHeight;
            
            // Update status
            updateTestStatus('error', `${errorCount} React Error(s) detected!`);
        }

        function clearErrorLog() {
            errorLog.textContent = 'No errors detected yet...';
            errorCount = 0;
            updateTestStatus('warning', 'Ready to test - Load the Recall extension and switch languages');
        }

        function updateTestStatus(type, message) {
            const indicator = testStatus.querySelector('.status-indicator');
            const text = testStatus.querySelector('span:last-child');
            
            indicator.className = `status-indicator status-${type}`;
            text.textContent = message;
        }

        // Auto-update status after 30 seconds if no errors
        setTimeout(() => {
            if (errorCount === 0) {
                updateTestStatus('success', 'No React Error #185 detected - Fix appears successful!');
            }
        }, 30000);

        console.log('Multiple Listeners Fix Test initialized - monitoring for React Error #185');
    </script>
</body>
</html>
