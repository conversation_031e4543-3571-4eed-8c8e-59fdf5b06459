{"manifest_version": 3, "name": "Recall", "version": "3.0.0", "description": "Intelligent browser history search and knowledge management extension", "permissions": ["storage", "scripting", "history", "activeTab", "tabs", "webNavigation"], "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval'; object-src 'self';"}, "host_permissions": ["<all_urls>"], "background": {"service_worker": "src/background/index.ts", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["src/content/index.ts"], "run_at": "document_end"}], "action": {"default_popup": "index.html", "default_title": "Recall - 智能历史搜索", "default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}}, "options_page": "options.html", "icons": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}, "web_accessible_resources": [{"resources": ["assets/*"], "matches": ["<all_urls>"]}]}