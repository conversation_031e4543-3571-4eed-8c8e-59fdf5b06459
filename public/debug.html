<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recall 调试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
        }
        
        .section h2::before {
            content: '';
            width: 4px;
            height: 20px;
            background: #4facfe;
            margin-right: 10px;
            border-radius: 2px;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .test-button:active {
            transform: translateY(0);
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        }
        
        .result-container {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .result {
            padding: 12px 15px;
            border-bottom: 1px solid #f1f3f4;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .result:last-child {
            border-bottom: none;
        }
        
        .result.success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        .result.warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        
        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
            text-align: center;
        }
        
        .status-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .status-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-value.success { color: #28a745; }
        .status-value.error { color: #dc3545; }
        .status-value.warning { color: #ffc107; }
        .status-value.info { color: #17a2b8; }
        
        .clear-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            float: right;
            margin-bottom: 10px;
        }
        
        .clear-button:hover {
            background: #5a6268;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            border-top: 1px solid #e1e5e9;
        }
        
        .emoji {
            font-size: 1.2em;
            margin-right: 5px;
        }
        
        @media (max-width: 768px) {
            .button-grid {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Recall 调试工具</h1>
            <p>诊断和测试扩展功能的专业工具</p>
        </div>
        
        <div class="content">
            <!-- 状态概览 -->
            <div class="section">
                <h2>📊 状态概览</h2>
                <div class="status-grid" id="statusGrid">
                    <div class="status-card">
                        <h3>扩展状态</h3>
                        <div class="status-value info" id="extensionStatus">检查中...</div>
                        <small>Extension Status</small>
                    </div>
                    <div class="status-card">
                        <h3>页面计数</h3>
                        <div class="status-value info" id="pageCount">-</div>
                        <small>Indexed Pages</small>
                    </div>
                    <div class="status-card">
                        <h3>数据库状态</h3>
                        <div class="status-value info" id="dbStatus">检查中...</div>
                        <small>Database Status</small>
                    </div>
                    <div class="status-card">
                        <h3>当前页面</h3>
                        <div class="status-value info" id="currentPage">-</div>
                        <small>Current URL</small>
                    </div>
                </div>
            </div>
            
            <!-- 快速测试 -->
            <div class="section">
                <h2>🚀 快速测试</h2>
                <div class="button-grid">
                    <button class="test-button" onclick="runFullDiagnosis()">
                        <span class="emoji">🔍</span>完整诊断
                    </button>
                    <button class="test-button" onclick="testContentExtraction()">
                        <span class="emoji">📄</span>测试内容提取
                    </button>
                    <button class="test-button" onclick="testDatabase()">
                        <span class="emoji">💾</span>测试数据库
                    </button>
                    <button class="test-button" onclick="forceExtraction()">
                        <span class="emoji">⚡</span>强制提取页面
                    </button>
                </div>
            </div>
            
            <!-- 高级工具 -->
            <div class="section">
                <h2>🛠️ 高级工具</h2>
                <div class="button-grid">
                    <button class="test-button" onclick="checkExtensionConnection()">
                        <span class="emoji">🔗</span>检查扩展连接
                    </button>
                    <button class="test-button" onclick="validateCurrentPage()">
                        <span class="emoji">✅</span>验证当前页面
                    </button>
                    <button class="test-button" onclick="exportDebugInfo()">
                        <span class="emoji">📋</span>导出调试信息
                    </button>
                    <button class="test-button danger" onclick="clearAllData()">
                        <span class="emoji">🗑️</span>清空所有数据
                    </button>
                </div>
            </div>
            
            <!-- 测试结果 -->
            <div class="section">
                <h2>📝 测试结果</h2>
                <button class="clear-button" onclick="clearResults()">清空结果</button>
                <div class="result-container" id="resultContainer">
                    <div class="result info">
                        <strong>[系统]</strong> 调试工具已就绪，点击上方按钮开始测试
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>Recall Debug Tool v1.0 | 用于诊断和测试扩展功能</p>
        </div>
    </div>

    <script src="debug.js"></script>
</body>
</html>
