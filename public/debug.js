/**
 * Recall 调试工具 JavaScript
 */

let debugState = {
    extensionConnected: false,
    dbInitialized: false,
    pageCount: 0,
    currentTab: null
};

/**
 * 页面加载完成后初始化
 */
document.addEventListener('DOMContentLoaded', async () => {
    log('🚀 调试工具初始化中...', 'info');
    await initializeDebugTool();
    await updateStatusOverview();
});

/**
 * 初始化调试工具
 */
async function initializeDebugTool() {
    try {
        // 检查 Chrome 扩展 API
        if (typeof chrome === 'undefined' || !chrome.runtime) {
            throw new Error('Chrome 扩展 API 不可用');
        }

        // 获取当前标签页（排除调试页面本身）
        try {
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (tabs && tabs.length > 0) {
                const currentTab = tabs[0];
                // 如果当前标签页是调试页面，尝试获取其他标签页
                if (currentTab.url && currentTab.url.includes('debug.html')) {
                    const allTabs = await chrome.tabs.query({ currentWindow: true });
                    const nonDebugTabs = allTabs.filter(tab =>
                        tab.url && !tab.url.includes('debug.html') &&
                        !tab.url.startsWith('chrome://') &&
                        !tab.url.startsWith('chrome-extension://')
                    );
                    debugState.currentTab = nonDebugTabs.length > 0 ? nonDebugTabs[0] : currentTab;
                } else {
                    debugState.currentTab = currentTab;
                }
            }
        } catch (tabError) {
            log(`⚠️ 无法获取标签页信息: ${tabError.message}`, 'warning');
            debugState.currentTab = null;
        }

        log('✅ 调试工具初始化成功', 'success');
        debugState.extensionConnected = true;

    } catch (error) {
        log(`❌ 调试工具初始化失败: ${error.message}`, 'error');
    }
}

/**
 * 更新状态概览
 */
async function updateStatusOverview() {
    // 更新扩展状态
    const extensionStatusEl = document.getElementById('extensionStatus');
    if (debugState.extensionConnected) {
        extensionStatusEl.textContent = '正常';
        extensionStatusEl.className = 'status-value success';
    } else {
        extensionStatusEl.textContent = '异常';
        extensionStatusEl.className = 'status-value error';
    }
    
    // 更新当前页面
    const currentPageEl = document.getElementById('currentPage');
    if (debugState.currentTab && debugState.currentTab.url) {
        try {
            const url = new URL(debugState.currentTab.url);
            currentPageEl.textContent = url.hostname;
            currentPageEl.className = 'status-value info';
        } catch (error) {
            // 处理无效 URL（如扩展页面、chrome:// 页面等）
            if (debugState.currentTab.url.startsWith('chrome-extension://')) {
                currentPageEl.textContent = '扩展页面';
            } else if (debugState.currentTab.url.startsWith('chrome://')) {
                currentPageEl.textContent = '浏览器页面';
            } else if (debugState.currentTab.url.startsWith('file://')) {
                currentPageEl.textContent = '本地文件';
            } else {
                currentPageEl.textContent = '未知页面';
            }
            currentPageEl.className = 'status-value warning';
        }
    } else {
        currentPageEl.textContent = '无标签页';
        currentPageEl.className = 'status-value error';
    }
    
    // 尝试获取页面计数和数据库状态
    try {
        const response = await sendMessageToBackground({ type: 'GET_DEBUG_INFO' });
        if (response && response.success) {
            // 更新页面计数
            const pageCountEl = document.getElementById('pageCount');
            pageCountEl.textContent = response.data.pageCount || 0;
            pageCountEl.className = 'status-value success';
            
            // 更新数据库状态
            const dbStatusEl = document.getElementById('dbStatus');
            dbStatusEl.textContent = response.data.dbHealthy ? '正常' : '异常';
            dbStatusEl.className = response.data.dbHealthy ? 'status-value success' : 'status-value error';
            
            debugState.pageCount = response.data.pageCount || 0;
            debugState.dbInitialized = response.data.dbHealthy || false;
        }
    } catch (error) {
        log(`⚠️ 无法获取扩展状态: ${error.message}`, 'warning');
    }
}

/**
 * 运行完整诊断
 */
async function runFullDiagnosis() {
    log('🔍 开始完整诊断...', 'info');
    
    const tests = [
        { name: '扩展连接', func: checkExtensionConnection },
        { name: '数据库状态', func: testDatabase },
        { name: '内容提取', func: testContentExtraction },
        { name: '页面验证', func: validateCurrentPage }
    ];
    
    let passedTests = 0;
    
    for (const test of tests) {
        try {
            log(`🧪 测试: ${test.name}`, 'info');
            const result = await test.func();
            if (result && result.success !== false) {
                passedTests++;
                log(`✅ ${test.name} - 通过`, 'success');
            } else {
                log(`❌ ${test.name} - 失败: ${result?.error || '未知错误'}`, 'error');
            }
        } catch (error) {
            log(`❌ ${test.name} - 异常: ${error.message}`, 'error');
        }
    }
    
    const score = Math.round((passedTests / tests.length) * 100);
    if (score >= 75) {
        log(`🎉 诊断完成! 总体健康度: ${score}% (${passedTests}/${tests.length})`, 'success');
    } else {
        log(`⚠️ 诊断完成! 总体健康度: ${score}% (${passedTests}/${tests.length}) - 需要注意`, 'warning');
    }
    
    await updateStatusOverview();
}

/**
 * 测试内容提取
 */
async function testContentExtraction() {
    log('📄 测试内容提取...', 'info');
    
    try {
        if (!debugState.currentTab) {
            throw new Error('无法获取当前标签页');
        }
        
        // 向当前标签页发送测试消息
        const response = await chrome.tabs.sendMessage(debugState.currentTab.id, {
            type: 'TEST_CONTENT_EXTRACTION'
        });
        
        if (response && response.success) {
            log(`✅ 内容提取成功`, 'success');
            log(`📊 标题: ${response.title}`, 'info');
            log(`📊 内容长度: ${response.contentLength} 字符`, 'info');
            log(`📊 URL: ${response.url}`, 'info');
            return { success: true };
        } else {
            log(`❌ 内容提取失败: ${response?.error || '未知错误'}`, 'error');
            return { success: false, error: response?.error };
        }
        
    } catch (error) {
        log(`❌ 内容提取测试失败: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

/**
 * 测试数据库
 */
async function testDatabase() {
    log('💾 测试数据库...', 'info');
    
    try {
        const response = await sendMessageToBackground({ type: 'TEST_DATABASE' });
        
        if (response && response.success) {
            log(`✅ 数据库测试成功`, 'success');
            log(`📊 页面总数: ${response.data.pageCount}`, 'info');
            log(`📊 存储大小: ${formatBytes(response.data.storageSize)}`, 'info');
            return { success: true };
        } else {
            log(`❌ 数据库测试失败: ${response?.error || '未知错误'}`, 'error');
            return { success: false, error: response?.error };
        }
        
    } catch (error) {
        log(`❌ 数据库测试失败: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

/**
 * 强制提取页面
 */
async function forceExtraction() {
    log('⚡ 强制提取当前页面...', 'info');
    
    try {
        if (!debugState.currentTab) {
            throw new Error('无法获取当前标签页');
        }
        
        const response = await chrome.tabs.sendMessage(debugState.currentTab.id, {
            type: 'FORCE_EXTRACTION'
        });
        
        if (response && response.success) {
            log(`✅ 页面提取成功`, 'success');
            log(`📄 已保存页面: ${response.title}`, 'info');
            await updateStatusOverview();
        } else {
            log(`❌ 页面提取失败: ${response?.error || '未知错误'}`, 'error');
        }
        
    } catch (error) {
        log(`❌ 强制提取失败: ${error.message}`, 'error');
    }
}

/**
 * 检查扩展连接
 */
async function checkExtensionConnection() {
    log('🔗 检查扩展连接...', 'info');
    
    try {
        const response = await sendMessageToBackground({ type: 'HEALTH_CHECK' });
        
        if (response && response.success) {
            log(`✅ 扩展连接正常`, 'success');
            log(`📊 Background Script 版本: ${response.version || 'Unknown'}`, 'info');
            return { success: true };
        } else {
            log(`❌ 扩展连接异常`, 'error');
            return { success: false };
        }
        
    } catch (error) {
        log(`❌ 扩展连接测试失败: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

/**
 * 验证当前页面
 */
async function validateCurrentPage() {
    log('✅ 验证当前页面...', 'info');
    
    try {
        if (!debugState.currentTab) {
            throw new Error('无法获取当前标签页');
        }
        
        const url = debugState.currentTab.url;
        const title = debugState.currentTab.title;

        log(`📊 页面 URL: ${url}`, 'info');
        log(`📊 页面标题: ${title}`, 'info');

        // 检查 URL 协议
        if (url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
            log(`⚠️ 当前页面是浏览器内部页面，无法提取内容`, 'warning');
            return { success: false, error: '浏览器内部页面' };
        }

        if (url.startsWith('file://')) {
            log(`📁 当前页面是本地文件，调试模式下应该可以提取`, 'info');
        }

        // 验证 URL 格式
        try {
            new URL(url);
        } catch (error) {
            log(`❌ 无效的 URL 格式: ${url}`, 'error');
            return { success: false, error: '无效的 URL 格式' };
        }
        
        log(`✅ 页面验证通过`, 'success');
        return { success: true };
        
    } catch (error) {
        log(`❌ 页面验证失败: ${error.message}`, 'error');
        return { success: false, error: error.message };
    }
}

/**
 * 导出调试信息
 */
async function exportDebugInfo() {
    log('📋 导出调试信息...', 'info');
    
    try {
        const debugInfo = {
            timestamp: new Date().toISOString(),
            extension: {
                connected: debugState.extensionConnected,
                version: chrome.runtime.getManifest().version
            },
            database: {
                initialized: debugState.dbInitialized,
                pageCount: debugState.pageCount
            },
            currentTab: debugState.currentTab ? {
                url: debugState.currentTab.url,
                title: debugState.currentTab.title
            } : null,
            browser: {
                userAgent: navigator.userAgent,
                language: navigator.language
            }
        };
        
        const blob = new Blob([JSON.stringify(debugInfo, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `recall-debug-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        
        log(`✅ 调试信息已导出`, 'success');
        
    } catch (error) {
        log(`❌ 导出调试信息失败: ${error.message}`, 'error');
    }
}

/**
 * 清空所有数据
 */
async function clearAllData() {
    if (!confirm('⚠️ 确定要清空所有数据吗？此操作不可撤销！')) {
        return;
    }
    
    log('🗑️ 清空所有数据...', 'warning');
    
    try {
        const response = await sendMessageToBackground({ type: 'CLEAR_ALL_DATA' });
        
        if (response && response.success) {
            log(`✅ 所有数据已清空`, 'success');
            await updateStatusOverview();
        } else {
            log(`❌ 清空数据失败: ${response?.error || '未知错误'}`, 'error');
        }
        
    } catch (error) {
        log(`❌ 清空数据失败: ${error.message}`, 'error');
    }
}

/**
 * 清空测试结果
 */
function clearResults() {
    const container = document.getElementById('resultContainer');
    container.innerHTML = '<div class="result info"><strong>[系统]</strong> 结果已清空</div>';
}

/**
 * 记录日志
 */
function log(message, type = 'info') {
    const container = document.getElementById('resultContainer');
    const timestamp = new Date().toLocaleTimeString();
    
    const resultDiv = document.createElement('div');
    resultDiv.className = `result ${type}`;
    resultDiv.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
    
    container.appendChild(resultDiv);
    container.scrollTop = container.scrollHeight;
    
    console.log(`[Debug Tool] ${message}`);
}

/**
 * 发送消息到 Background Script
 */
async function sendMessageToBackground(message) {
    return new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
            } else {
                resolve(response);
            }
        });
    });
}

/**
 * 格式化字节大小
 */
function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
