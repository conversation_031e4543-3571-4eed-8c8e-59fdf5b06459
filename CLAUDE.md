# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Recall is a Chrome extension that provides intelligent search and knowledge management for browsing history. The extension extracts page content using Mozilla Readability, stores everything locally in IndexedDB, and provides advanced search capabilities using hybrid traditional search engines (Fuse.js and Lunr.js) with support for fuzzy matching, fulltext search, and Chinese text segmentation.

## Documentation and Version History

- PRD documents: `@docs/materials/history_search_prd_v1.0.md`, `@docs/materials/history_search_prd_v2.0.md`
- Development plans: `@docs/materials/history_search_dev_v1.0.md`, `@docs/materials/history_search_dev_v2.0.md`
- Fix plan: `@docs/materials/history_search_fix_v2.0.md`
- All version-numbered documents have been completed

## Development Commands

### Essential Commands
- `npm run dev` - Start development server with hot reload (Vite)
- `npm run build` - Build production version using Vite + CRX plugin
- `npm run build:webpack` - Alternative webpack build for specific needs
- `npm run dev:mock` - Start mock server for testing (Express.js)
- `npm run lint` - Run ESLint for code quality checks
- `npm run test` - Run Jest unit tests
- `npm run test:unit` - Run unit tests with coverage
- `npm run test:watch` - Run tests in watch mode
- `npm run test:e2e` - Run Playwright end-to-end tests
- `npm run test:e2e:ui` - Run E2E tests with Playwright UI
- `npm run test:performance` - Run custom performance tests
- `npm run test:all` - Run complete test suite via shell script
- `npm run test:clean` - Clean test artifacts
- `./scripts/test.sh [unit|e2e|build|all|clean]` - Comprehensive test runner

### Testing Single Components
- `npm test src/services/db.service.test.ts` - Test specific service
- `npm test -- --testNamePattern="specific test name"` - Run specific test
- `npx playwright test e2e/basic.e2e.test.ts` - Run specific E2E test

## Architecture Overview

### Chrome Extension Structure (Manifest V3)
The extension follows Chrome Manifest V3 architecture with these main components:

1. **Background Service Worker** (`src/background/index.ts`)
   - Handles extension lifecycle and message routing
   - Manages task scheduling with IndexScheduler and TaskQueue
   - Resource monitoring and performance optimization
   - Coordinates database operations and AI processing

2. **Content Scripts** (`src/content/`)
   - Injected into web pages to extract content
   - Uses Mozilla Readability and custom content extractors
   - AI-powered reading detection and summary integration
   - Toast notifications and summary panels
   - Detects SPA route changes and content updates

3. **Popup UI** (`src/popup/` + `index.html`)
   - React-based search interface with semantic and fuzzy search
   - Real-time search with multiple search modes
   - Status bar and advanced filtering
   - Results highlighting and management

4. **Options Page** (`src/options/` + `options.html`)
   - Comprehensive extension configuration
   - API key management for BYOK (Bring Your Own Key)
   - Reading assistant settings and customization
   - Data backup/restore functionality
   - History and search settings management

### Key Services Architecture

#### Storage Layer (`src/storage/`)
- **IndexedDBService**: Primary storage with IndexedDB and singleton pattern
- **LRUCacheManager**: LRU-based storage management with 500MB limit
- **Data Models**: Pages, Settings, Search Indexes with comprehensive TypeScript interfaces
- **Operations**: CRUD, batch operations, data export/import with migrations
- **Error Handling**: Custom DBError class with specific error codes

#### External Services Integration (`src/services/`)
- **API Integration**: BYOK support for OpenAI, Anthropic, Google, DeepSeek, Azure, LiteLLM
- **Request Management**: Secure API key storage and request handling
- **Error Handling**: Comprehensive error handling for external service failures

#### Search Engine (`src/search/`)
- **Hybrid Search**: Coordinated search using multiple traditional search engines
- **Fuse.js Engine**: Fuzzy text matching with weighted field scoring
- **Lunr.js Engine**: Fulltext search with Chinese text segmentation support
- **Query Processor**: Advanced query parsing with syntax support (site filters, exclusions, exact phrases)
- **Result Merger**: Intelligent merging and ranking of results from multiple search engines
- **Search Coordinator**: Orchestrates different search strategies based on query type

#### Content Processing (`src/content/`)
- **ContentExtractor**: Enhanced Mozilla Readability with custom handlers
- **SpecialContentHandler**: Specialized extraction for PDFs, videos, SPAs
- **ReadingDetector**: User reading behavior analysis and time tracking
- **ActivityMonitor**: User engagement and attention tracking

### Data Flow
```
Web Page → Content Script → Background Worker → Content Processing → IndexedDB
    ↓           ↓                   ↓               ↓                   ↓
  Content    Reading           Task Queue      Text Extraction    Local Storage
 Extraction  Detection        Scheduling      & Indexing         & Caching
                                ↓               ↓                   ↓
Popup UI ← Search Service ← Query Processing ← Hybrid Search ← Search Indexes
    ↓           ↓               ↓               ↓                   ↓
  Results   Ranking &       Intent          Multi-Engine       Fuse.js +
 Display    Filtering      Detection        Coordination       Lunr.js
```

## File Organization Patterns

### Modular Architecture (`src/`)
- **background/**: Background service worker components and task scheduling
- **content/**: Content scripts and extraction logic
- **search/**: Hybrid search engine implementations (Fuse.js + Lunr.js)
- **storage/**: Storage layer and caching mechanisms
- **security/**: Encryption and key management
- **services/**: Core business logic and external API integration

### Services (`src/services/`)
- Each service has `.service.ts`, `.service.test.ts`, and integration tests
- Services export singleton instances (e.g., `dbService`, `searchService`)
- Index file (`index.ts`) provides unified exports
- Legacy services maintained for backward compatibility

### Components (`src/popup/components/`, `src/options/components/`)
- React functional components with TypeScript
- Component-specific CSS files (e.g., `Component.css`)
- Test files follow `Component.test.tsx` pattern
- Index files for clean exports

### Models (`src/models/`)
- TypeScript interfaces and types
- Database utilities and validation functions
- Shared constants and default configurations

## Testing Strategy

### Unit Tests (Jest)
- **Setup**: `src/tests/setup.ts` with Chrome and IndexedDB mocks
- **Mocks**: Chrome API (`setup/chrome-mock.ts`) and IndexedDB (`setup/indexeddb-mock.ts`)
- **Coverage**: 70% threshold for branches, functions, lines, statements
- **Config**: `jest.config.cjs` with custom TypeScript setup

### E2E Tests (Playwright)
- **Environment**: Chrome with extension loaded (`--load-extension=./dist`)
- **Config**: Single worker, retry on CI, HTML/JSON reporting
- **Test Data**: Located in `e2e/` directory

### Integration Tests
- Database pagination tests (`db.pagination.integration.test.ts`)
- Search service integration (`search.service.integration.test.ts`)
- Cross-service communication tests

## Development Workflow

### Chrome Extension Development
1. Run `npm run dev` to start development build
2. Load `dist/` directory as unpacked extension in Chrome
3. Code changes trigger automatic rebuild
4. Refresh extension in Chrome to see changes

### Database Development
- Use `src/services/db.demo.ts` for testing database operations
- Health checks available via `dbService.healthCheck()`
- Storage info accessible via `dbService.getStorageInfo()`

### Content Script Development
- Test content extraction with `src/content/scraper.test.ts`
- Debug extraction using browser DevTools in content script context
- SPA detection logic handles route changes automatically

## Key Configuration Files

- **`vite.config.ts`**: Primary build configuration with CRX plugin, WASM support, and path aliases
- **`webpack.*.cjs`**: Alternative webpack configurations for specialized builds
- **`jest.config.cjs`**: Test configuration with custom transforms and mocks
- **`playwright.config.ts`**: E2E test configuration with Chrome extension support
- **`public/manifest.json`**: Chrome extension Manifest V3 with all required permissions
- **`tsconfig.*.json`**: Multiple TypeScript configurations for different environments
- **`scripts/test.sh`**: Comprehensive test runner with colored output and reporting

## Common Development Patterns

### Error Handling
- Services use custom error classes (e.g., `DBError`)
- Async operations wrapped in try-catch blocks
- Error codes for specific failure types

### Data Validation
- TypeScript interfaces enforce type safety
- Runtime validation in database utilities
- Input sanitization for search queries

### Performance Optimization
- Debounced content extraction to prevent duplicate work
- Batch database operations for efficiency
- Virtual scrolling for large result sets (React Window)

### Chrome Extension Best Practices
- Minimal permissions requested in manifest
- Content Security Policy compliance
- Message passing between contexts
- Local storage for privacy protection
- WASM integration for high-performance AI processing
- Service worker lifecycle management
- Resource monitoring and performance optimization

### External API Integration Patterns
- BYOK (Bring Your Own Key) for external LLM services
- Secure API key storage with AES-256 encryption
- Direct client-to-provider communication for privacy
- Graceful fallbacks when external services are unavailable
- Rate limiting and error handling for API requests

## V3.0 Search Architecture Highlights

### Hybrid Search Pipeline
1. **Content Extraction**: Mozilla Readability + custom handlers
2. **Text Processing**: Content cleaning and preparation for indexing
3. **Local Storage**: IndexedDB with efficient content management
4. **Hybrid Search**: Fuse.js fuzzy matching + Lunr.js fulltext search
5. **Chinese Language Support**: jieba-wasm for Chinese text segmentation

### BYOK (Bring Your Own Key) Support
- **Supported Providers**: OpenAI, Anthropic, Google, DeepSeek, Azure, LiteLLM
- **Security**: AES-256 encryption for API key storage
- **Privacy**: Direct client-to-provider communication, no proxy servers
- **Features**: AI summaries, conversational search, knowledge insights

### Performance Targets
- Hybrid search response: < 200ms for traditional search
- Content extraction and indexing: < 100ms per page
- 10K+ page support with optimized indexing
- 500MB storage limit with LRU management

### Development Workflow Notes
- Use both Vite (primary) and Webpack (legacy) build systems
- Search components require proper Chinese text segmentation setup
- Test with `test-pages/` for various content types (SPA, static, media)
- Monitor performance with `npm run test:performance`
- Use `npm run test:search:quality` for search relevance testing

## IMPORTANT
- 每次有新的开发计划，都保存到docs/plannings/下
- 每次读取产品需求文档，优先从docs/prd/目录下搜索
- 每次完成任务或者有进度需要更新，保存到docs/plannings/下
- @docs/prd/dev_v3.0.md @docs/prd/prd_v3.0.md 是这次改造的PRD和开发计划，但注意：语义搜索功能已被移除，当前使用传统混合搜索(Fuse.js + Lunr.js)
- 在处理搜索相关任务时，focus on improving traditional search quality rather than implementing AI/semantic features