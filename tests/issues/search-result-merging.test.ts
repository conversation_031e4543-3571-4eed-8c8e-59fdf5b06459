/**
 * Test case to reproduce the search result merging issue
 * 
 * Issue: When searching for "Claude Code", only one result is returned,
 * but searching for "claude c" returns multiple results.
 * 
 * Root cause analysis:
 * 1. Lunr splits the query into multiple terms and uses OR logic
 * 2. HybridSearchEngine merges results by URL, causing duplicates to be lost
 */

import { LunrSearchEngine } from '../../src/search/fulltext/LunrSearchEngine';
import { HybridSearchEngine } from '../../src/search/hybrid/HybridSearchEngine';
import { dbService } from '../../src/services/db.service';
import { searchService } from '../../src/services/search.service';
import type { Page } from '../../src/models';

describe('Search Result Merging Issue', () => {
  let lunrEngine: LunrSearchEngine;
  let hybridEngine: HybridSearchEngine;

  beforeEach(async () => {
    // Clear database
    await dbService.clearAllData();
    
    lunrEngine = new LunrSearchEngine();
    hybridEngine = new HybridSearchEngine();
  });

  afterEach(async () => {
    await dbService.clearAllData();
  });

  it('should demonstrate the search term splitting behavior', async () => {
    // Create test data with multiple pages containing "Claude Code"
    const testPages: Page[] = [
      {
        id: '1',
        url: 'https://claude.ai/code/docs/page1',
        title: 'Getting Started with Claude Code',
        content: 'Claude Code is an AI-powered coding assistant that helps developers write better code faster.',
        domain: 'claude.ai',
        visitTime: Date.now() - 1000 * 60 * 60,
        accessCount: 1,
        lastUpdated: Date.now() - 1000 * 60 * 60
      },
      {
        id: '2',
        url: 'https://claude.ai/code/docs/page2',
        title: 'Claude Code Advanced Features',
        content: 'Learn about the advanced features of Claude Code including AI suggestions and code refactoring.',
        domain: 'claude.ai',
        visitTime: Date.now() - 1000 * 60 * 30,
        accessCount: 1,
        lastUpdated: Date.now() - 1000 * 60 * 30
      },
      {
        id: '3',
        url: 'https://docs.anthropic.com/claude-code',
        title: 'Claude Code Documentation',
        content: 'Complete documentation for Claude Code. This guide covers everything about using Claude Code effectively.',
        domain: 'docs.anthropic.com',
        visitTime: Date.now() - 1000 * 60 * 15,
        accessCount: 1,
        lastUpdated: Date.now() - 1000 * 60 * 15
      },
      {
        id: '4',
        url: 'https://blog.example.com/claude-review',
        title: 'My Experience with Claude',
        content: 'I recently started using Claude for coding and it has been amazing. Claude Code helps me write clean code.',
        domain: 'blog.example.com',
        visitTime: Date.now() - 1000 * 60 * 5,
        accessCount: 1,
        lastUpdated: Date.now() - 1000 * 60 * 5
      }
    ];

    // Add pages to database
    for (const page of testPages) {
      await dbService.addPage(page);
    }

    // Build Lunr index
    await lunrEngine.createIndex(testPages);

    // Test 1: Search for "Claude Code" (two terms)
    console.log('\n=== Test 1: Searching for "Claude Code" ===');
    const claudeCodeResults = await lunrEngine.search('Claude Code');
    console.log(`Found ${claudeCodeResults.length} results for "Claude Code"`);
    claudeCodeResults.forEach((result, idx) => {
      console.log(`  ${idx + 1}. ${result.page.title} (score: ${result.score.toFixed(3)})`);
      console.log(`     Matched terms: ${result.metadata?.matchedTerms?.join(', ')}`);
    });

    // Test 2: Search for "claude c" (partial match)
    console.log('\n=== Test 2: Searching for "claude c" ===');
    const claudeCResults = await lunrEngine.search('claude c');
    console.log(`Found ${claudeCResults.length} results for "claude c"`);
    claudeCResults.forEach((result, idx) => {
      console.log(`  ${idx + 1}. ${result.page.title} (score: ${result.score.toFixed(3)})`);
      console.log(`     Matched terms: ${result.metadata?.matchedTerms?.join(', ')}`);
    });

    // Test 3: Search for just "claude"
    console.log('\n=== Test 3: Searching for "claude" ===');
    const claudeResults = await lunrEngine.search('claude');
    console.log(`Found ${claudeResults.length} results for "claude"`);

    // Test 4: Search for just "code"
    console.log('\n=== Test 4: Searching for "code" ===');
    const codeResults = await lunrEngine.search('code');
    console.log(`Found ${codeResults.length} results for "code"`);

    // Assertions
    expect(claudeCodeResults.length).toBeGreaterThan(0);
    expect(claudeCResults.length).toBeGreaterThan(0);
    
    // The issue: "Claude Code" might return fewer results than "claude c"
    // because of how Lunr handles multi-word queries
    console.log('\n=== Analysis ===');
    console.log(`"Claude Code" returns: ${claudeCodeResults.length} results`);
    console.log(`"claude c" returns: ${claudeCResults.length} results`);
    
    // Check if all pages containing both "claude" and "code" are returned
    const pagesWithBothTerms = testPages.filter(page => 
      page.content.toLowerCase().includes('claude') && 
      page.content.toLowerCase().includes('code')
    );
    console.log(`Pages containing both "claude" AND "code": ${pagesWithBothTerms.length}`);
    
    // The search should return all pages that contain either term
    const pagesWithEitherTerm = testPages.filter(page => 
      page.content.toLowerCase().includes('claude') || 
      page.content.toLowerCase().includes('code')
    );
    console.log(`Pages containing "claude" OR "code": ${pagesWithEitherTerm.length}`);
  });

  it('should test HybridSearchEngine merging behavior', async () => {
    // Create test data with duplicate URLs (simulating multiple visits to same page)
    const testPages: Page[] = [
      {
        id: '1',
        url: 'https://claude.ai/code/docs',
        title: 'Claude Code Documentation v1',
        content: 'First visit: Claude Code is an AI-powered coding assistant.',
        domain: 'claude.ai',
        visitTime: Date.now() - 1000 * 60 * 60,
        accessCount: 1,
        lastUpdated: Date.now() - 1000 * 60 * 60
      },
      {
        id: '2',
        url: 'https://claude.ai/code/docs', // Same URL as id '1'
        title: 'Claude Code Documentation v2',
        content: 'Second visit: Updated Claude Code documentation with new features.',
        domain: 'claude.ai',
        visitTime: Date.now() - 1000 * 60 * 30,
        accessCount: 2,
        lastUpdated: Date.now() - 1000 * 60 * 30
      },
      {
        id: '3',
        url: 'https://docs.anthropic.com/claude-code',
        title: 'Official Claude Code Guide',
        content: 'The official guide for Claude Code from Anthropic.',
        domain: 'docs.anthropic.com',
        visitTime: Date.now() - 1000 * 60 * 15,
        accessCount: 1,
        lastUpdated: Date.now() - 1000 * 60 * 15
      }
    ];

    // Add pages to database
    for (const page of testPages) {
      await dbService.addPage(page);
    }

    // Initialize search service
    await searchService.init();

    // Test hybrid search
    console.log('\n=== Test HybridSearchEngine ===');
    const hybridResults = await hybridEngine.search('Claude Code');
    
    console.log(`Total unique URLs in test data: ${new Set(testPages.map(p => p.url)).size}`);
    console.log(`Total pages in test data: ${testPages.length}`);
    console.log(`Hybrid search returned: ${hybridResults.mergedResults.length} results`);
    
    // The issue: If pages with duplicate URLs are merged, we lose results
    hybridResults.mergedResults.forEach((result, idx) => {
      console.log(`  ${idx + 1}. ${result.title} (${result.url})`);
    });

    // Check string search results
    console.log(`\nString search results: ${hybridResults.stringResults.length}`);
    // Check fulltext search results
    console.log(`Fulltext search results: ${hybridResults.fulltextResults.length}`);
    
    // Assertion: We should see the merging issue
    expect(hybridResults.mergedResults.length).toBeLessThanOrEqual(
      new Set(testPages.map(p => p.url)).size
    );
  });

  it('should analyze the real backup data structure', async () => {
    // This test would analyze the actual backup file to understand the data structure
    // Since we can't directly read the large backup file in the test, we'll document the issue
    
    console.log('\n=== Real Data Analysis ===');
    console.log('The backup file contains multiple pages that may have:');
    console.log('1. Same URL visited multiple times (different IDs, same URL)');
    console.log('2. Pages with "Claude Code" in different contexts');
    console.log('3. Potential issues with Chinese text segmentation if jieba-wasm is not loaded');
    
    // Document the root causes
    console.log('\n=== Root Cause Analysis ===');
    console.log('1. Lunr Query Splitting:');
    console.log('   - "Claude Code" → ["claude", "code"] with OR logic');
    console.log('   - Each term is searched independently');
    console.log('   - Wildcard matching on "c" matches "claude", "code", etc.');
    
    console.log('\n2. Result Merging Issue:');
    console.log('   - HybridSearchEngine uses URL as unique key');
    console.log('   - Multiple pages with same URL are collapsed into one');
    console.log('   - This loses history entries for the same page');
    
    console.log('\n3. Potential Solutions:');
    console.log('   - Use page ID instead of URL for merging');
    console.log('   - Implement phrase search in Lunr for exact matches');
    console.log('   - Group results by URL but show all entries');
  });
});