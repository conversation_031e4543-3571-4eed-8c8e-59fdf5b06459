/**
 * Integration test for advanced search syntax
 * Tests exact phrases, exclude terms, and site filters
 */

import { HybridSearchEngine } from '../../src/search/hybrid/HybridSearchEngine';
import { Page } from '../../src/models';

describe('Advanced Search Syntax Integration Test', () => {
  let searchEngine: HybridSearchEngine;
  let testPages: Page[];

  beforeAll(async () => {
    // Initialize search engine
    searchEngine = new HybridSearchEngine();

    // Create test data
    testPages = [
      {
        id: '1',
        url: 'https://example.com/react-hooks',
        title: 'React Hooks Best Practices',
        content: 'React Hooks are a powerful feature in React. They allow you to use state and other React features without writing a class. This guide covers best practices for using hooks effectively.',
        domain: 'example.com',
        visitTime: Date.now() - 86400000, // 1 day ago
        lastUpdated: Date.now() - 86400000,
        accessCount: 5,
        language: 'en'
      },
      {
        id: '2',
        url: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
        title: 'JavaScript Documentation',
        content: 'JavaScript is a programming language that enables interactive web pages. Learn about JavaScript features, syntax, and best practices.',
        domain: 'developer.mozilla.org',
        visitTime: Date.now() - *********, // 2 days ago
        lastUpdated: Date.now() - *********,
        accessCount: 10,
        language: 'en'
      },
      {
        id: '3',
        url: 'https://stackoverflow.com/questions/react-hooks-vs-class',
        title: 'React Hooks vs Class Components',
        content: 'A comparison between React Hooks and class components. Hooks provide a more direct API to the React concepts you already know.',
        domain: 'stackoverflow.com',
        visitTime: Date.now() - *********, // 3 days ago
        lastUpdated: Date.now() - *********,
        accessCount: 3,
        language: 'en'
      },
      {
        id: '4',
        url: 'https://example.com/vue-composition-api',
        title: 'Vue Composition API Guide',
        content: 'The Vue Composition API is a set of APIs that allows us to author Vue components using imported functions instead of declaring options.',
        domain: 'example.com',
        visitTime: Date.now() - 345600000, // 4 days ago
        lastUpdated: Date.now() - 345600000,
        accessCount: 2,
        language: 'en'
      },
      {
        id: '5',
        url: 'https://github.com/facebook/react',
        title: 'React GitHub Repository',
        content: 'React is a JavaScript library for building user interfaces. This is the official React repository with source code and documentation.',
        domain: 'github.com',
        visitTime: Date.now() - 432000000, // 5 days ago
        lastUpdated: Date.now() - 432000000,
        accessCount: 7,
        language: 'en'
      }
    ];

    // Initialize the search engine with test data
    await searchEngine.initialize(testPages);
  });

  describe('Exact Phrase Search (quotes)', () => {
    it('should find pages with exact phrase "React Hooks"', async () => {
      const results = await searchEngine.search('"React Hooks"');
      
      expect(results.mergedResults.length).toBeGreaterThan(0);
      
      // Should find pages that contain the exact phrase "React Hooks"
      const matchingPages = results.mergedResults.filter(result => 
        result.content.includes('React Hooks') || result.title.includes('React Hooks')
      );
      
      expect(matchingPages.length).toBeGreaterThan(0);
      
      // Verify the exact phrase appears in results
      const titles = matchingPages.map(r => r.title);
      expect(titles).toContain('React Hooks Best Practices');
      expect(titles).toContain('React Hooks vs Class Components');
    });

    it('should not find pages without the exact phrase', async () => {
      const results = await searchEngine.search('"Vue Hooks"');
      
      // Should not find any results as "Vue Hooks" doesn't exist as exact phrase
      expect(results.mergedResults.length).toBe(0);
    });

    it('should handle multiple exact phrases', async () => {
      const results = await searchEngine.search('"best practices" JavaScript');
      
      expect(results.mergedResults.length).toBeGreaterThan(0);
      
      // Should prioritize pages with "best practices" phrase
      const topResult = results.mergedResults[0];
      expect(topResult.content.toLowerCase()).toContain('best practices');
    });
  });

  describe('Exclude Terms (minus sign)', () => {
    it('should exclude pages with specified terms', async () => {
      const results = await searchEngine.search('React -Vue');
      
      expect(results.mergedResults.length).toBeGreaterThan(0);
      
      // Should only find React pages, not Vue pages
      const hasVue = results.mergedResults.some(result => 
        result.content.toLowerCase().includes('vue') || 
        result.title.toLowerCase().includes('vue')
      );
      
      expect(hasVue).toBe(false);
      
      // Should include React pages
      const hasReact = results.mergedResults.some(result => 
        result.content.toLowerCase().includes('react') || 
        result.title.toLowerCase().includes('react')
      );
      
      expect(hasReact).toBe(true);
    });

    it('should handle multiple exclude terms', async () => {
      const results = await searchEngine.search('JavaScript -React -Vue');
      
      expect(results.mergedResults.length).toBeGreaterThan(0);
      
      // Should only find JavaScript pages without React or Vue
      results.mergedResults.forEach(result => {
        const content = result.content.toLowerCase();
        const title = result.title.toLowerCase();
        
        expect(content.includes('react') || title.includes('react')).toBe(false);
        expect(content.includes('vue') || title.includes('vue')).toBe(false);
      });
    });
  });

  describe('Site Filters', () => {
    it('should filter results by site', async () => {
      const results = await searchEngine.search('site:example.com');
      
      expect(results.mergedResults.length).toBeGreaterThan(0);
      
      // All results should be from example.com
      results.mergedResults.forEach(result => {
        expect(result.domain).toBe('example.com');
      });
    });

    it('should combine site filter with search terms', async () => {
      const results = await searchEngine.search('React site:example.com');
      
      expect(results.mergedResults.length).toBeGreaterThan(0);
      
      // Results should be from example.com and contain React
      results.mergedResults.forEach(result => {
        expect(result.domain).toBe('example.com');
        const hasReact = result.content.toLowerCase().includes('react') || 
                        result.title.toLowerCase().includes('react');
        expect(hasReact).toBe(true);
      });
    });

    it('should return no results for non-existent sites', async () => {
      const results = await searchEngine.search('site:nonexistent.com');
      
      expect(results.mergedResults.length).toBe(0);
    });
  });

  describe('Combined Syntax', () => {
    it('should handle exact phrase with exclude terms', async () => {
      const results = await searchEngine.search('"best practices" -Vue');
      
      expect(results.mergedResults.length).toBeGreaterThan(0);
      
      // Should find pages with "best practices" but not Vue
      results.mergedResults.forEach(result => {
        const content = result.content.toLowerCase();
        const title = result.title.toLowerCase();
        
        expect(content.includes('best practices') || title.includes('best practices')).toBe(true);
        expect(content.includes('vue') || title.includes('vue')).toBe(false);
      });
    });

    it('should handle site filter with exact phrase', async () => {
      const results = await searchEngine.search('"React Hooks" site:example.com');
      
      if (results.mergedResults.length > 0) {
        results.mergedResults.forEach(result => {
          expect(result.domain).toBe('example.com');
          const hasExactPhrase = result.content.includes('React Hooks') || 
                               result.title.includes('React Hooks');
          expect(hasExactPhrase).toBe(true);
        });
      }
    });

    it('should handle all three: exact phrase, exclude, and site filter', async () => {
      const results = await searchEngine.search('"JavaScript" -React site:developer.mozilla.org');
      
      if (results.mergedResults.length > 0) {
        results.mergedResults.forEach(result => {
          expect(result.domain).toBe('developer.mozilla.org');
          
          const content = result.content.toLowerCase();
          const title = result.title.toLowerCase();
          
          // Should contain JavaScript
          expect(content.includes('javascript') || title.includes('javascript')).toBe(true);
          
          // Should not contain React
          expect(content.includes('react') || title.includes('react')).toBe(false);
        });
      }
    });
  });

  describe('Query Processing Verification', () => {
    it('should correctly process queries with advanced syntax', async () => {
      // Test that the query is properly analyzed
      const results = await searchEngine.search('"React Hooks" -class site:example.com');
      
      // Check that filters were extracted
      expect(results.cleanedQuery).toBeDefined();
      expect(results.strategy).toBeDefined();
      
      // The query should be processed correctly
      expect(results.query).toBe('"React Hooks" -class site:example.com');
    });
  });
});