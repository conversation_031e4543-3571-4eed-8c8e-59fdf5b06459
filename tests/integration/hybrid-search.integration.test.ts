/**
 * Hybrid Search Integration Test
 * 
 * Tests the complete hybrid search functionality with real data
 */

import { HybridSearchEngine } from '../../src/search/hybrid/HybridSearchEngine';
import { LunrSearchEngine } from '../../src/search/fulltext/LunrSearchEngine';
import { searchService } from '../../src/services/search.service';
import { hybridSearchService } from '../../src/services/hybrid-search.service';
import type { Page } from '../../src/models';
import * as fs from 'fs';
import * as path from 'path';

// Mock Chrome API
(global as any).chrome = {
  storage: {
    local: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue(undefined),
    },
  },
  runtime: {
    sendMessage: jest.fn().mockResolvedValue({}),
    onMessage: {
      addListener: jest.fn(),
    },
  },
};

// Mock IndexedDB
class MockIDBObjectStore {
  constructor(public name: string) {}
  createIndex = jest.fn();
  index = jest.fn().mockReturnThis();
  get = jest.fn();
  add = jest.fn();
  put = jest.fn();
  delete = jest.fn();
  clear = jest.fn();
  getAll = jest.fn();
  getAllKeys = jest.fn();
  count = jest.fn();
  openCursor = jest.fn();
}

class MockIDBTransaction {
  constructor(public storeNames: string[]) {}
  objectStore = jest.fn((name) => new MockIDBObjectStore(name));
  oncomplete: any = null;
  onerror: any = null;
  onabort: any = null;
}

class MockIDBDatabase {
  constructor() {
    this.objectStoreNames = ['pages', 'settings', 'searchIndexes'] as any;
  }
  objectStoreNames: any;
  transaction = jest.fn((storeNames, mode) => new MockIDBTransaction(storeNames));
  createObjectStore = jest.fn((name) => new MockIDBObjectStore(name));
  close = jest.fn();
}

const mockIndexedDB = {
  open: jest.fn().mockImplementation(() => {
    const request: any = {
      result: new MockIDBDatabase(),
      onsuccess: null,
      onerror: null,
      onupgradeneeded: null,
    };
    setTimeout(() => request.onsuccess?.({ target: request }), 0);
    return request;
  }),
  deleteDatabase: jest.fn(),
};

(global as any).indexedDB = mockIndexedDB;
(global as any).IDBKeyRange = {
  bound: jest.fn(),
  only: jest.fn(),
  lowerBound: jest.fn(),
  upperBound: jest.fn(),
};

describe('Hybrid Search Integration', () => {
  let hybridEngine: HybridSearchEngine;
  let lunrEngine: LunrSearchEngine;
  let testPages: Page[];

  beforeAll(() => {
    // Load test data from backup file
    const backupPath = path.join(__dirname, '../materials/recall-backup-2025-06-26.json');
    const backupContent = fs.readFileSync(backupPath, 'utf-8');
    const backup = JSON.parse(backupContent);
    testPages = backup.pages;
  });

  beforeEach(async () => {
    // Reset services
    jest.clearAllMocks();
    
    // Clear search service index
    try {
      searchService.clearIndex();
    } catch (error) {
      // Ignore if method doesn't exist in current implementation
    }
    
    // Create new instances
    hybridEngine = new HybridSearchEngine();
    lunrEngine = new LunrSearchEngine();
  });

  describe('Search Functionality', () => {
    it('should return consistent results for "Claude Code" search', async () => {
      // Initialize engines with test data
      await hybridEngine.initialize(testPages);
      
      // Search for "Claude Code"
      const results = await hybridEngine.search('Claude Code');
      
      console.log(`\\nHybrid search returned ${results.mergedResults.length} results`);
      console.log(`String results: ${results.stringResults.length}`);
      console.log(`Fulltext results: ${results.fulltextResults.length}`);
      
      // Verify we get results
      expect(results.mergedResults.length).toBeGreaterThan(0);
      
      // Verify no duplicate URLs in merged results
      const urls = results.mergedResults.map(r => r.url);
      const uniqueUrls = new Set(urls);
      expect(urls.length).toBe(uniqueUrls.size);
      
      // Check that we find the zhihu page
      const zhihuResult = results.mergedResults.find(r => r.url.includes('zhihu.com'));
      expect(zhihuResult).toBeDefined();
    });
    
    it('should support advanced search syntax', async () => {
      // Initialize engines with test data
      await hybridEngine.initialize(testPages);
      
      console.log('\\n=== Advanced Search Syntax Tests ===');
      
      // Test 1: Regular search (OR logic) - should return all pages with either "Claude" OR "Code"
      const regularResults = await hybridEngine.search('Claude Code');
      console.log(`\\n1. Regular Search "Claude Code": ${regularResults.mergedResults.length} results`);
      
      // Test 2: Exact phrase search - should return only pages with exact phrase "Claude Code"
      const exactPhraseResults = await hybridEngine.search('"Claude Code"');
      console.log(`2. Exact Phrase Search "Claude Code": ${exactPhraseResults.mergedResults.length} results`);
      
      // Test 3: Exclude search - should return pages with "Claude" but NOT "Code"
      const excludeResults = await hybridEngine.search('Claude -Code');
      console.log(`3. Exclude Search "Claude -Code": ${excludeResults.mergedResults.length} results`);
      
      // Test 4: Site filter - should return only pages from claude.ai domain
      const siteFilterResults = await hybridEngine.search('site:claude.ai');
      console.log(`4. Site Filter "site:claude.ai": ${siteFilterResults.mergedResults.length} results`);
      
      // Test 5: Combined search - exact phrase with site filter
      const combinedResults = await hybridEngine.search('"Claude Code" site:claude.ai');
      console.log(`5. Combined Search '"Claude Code" site:claude.ai': ${combinedResults.mergedResults.length} results`);
      
      // Analyze exact phrase results
      console.log('\\nExact Phrase Results:');
      exactPhraseResults.mergedResults.forEach((result, index) => {
        console.log(`  ${index + 1}. ${result.title} (ID: ${result.id})`);
        console.log(`     URL: ${result.url}`);
        console.log(`     Score: ${result.combinedScore.toFixed(4)}`);
        // Verify the exact phrase exists in content
        const content = `${result.title} ${result.content}`.toLowerCase();
        const hasExactPhrase = content.includes('claude code');
        console.log(`     Contains "Claude Code": ${hasExactPhrase}`);
      });
      
      // Verify search behavior
      expect(regularResults.mergedResults.length).toBeGreaterThan(0);
      expect(exactPhraseResults.mergedResults.length).toBeLessThanOrEqual(regularResults.mergedResults.length);
      
      // Verify exact phrase results actually contain the phrase
      exactPhraseResults.mergedResults.forEach(result => {
        const content = `${result.title} ${result.content}`.toLowerCase();
        expect(content).toContain('claude code');
      });
      
      // Verify exclude results don't contain excluded term
      excludeResults.mergedResults.forEach(result => {
        const content = `${result.title} ${result.content}`.toLowerCase();
        expect(content).toContain('claude');
        expect(content).not.toContain('code');
      });
      
      // Verify site filter results are from the specified domain
      siteFilterResults.mergedResults.forEach(result => {
        expect(result.domain).toBe('claude.ai');
      });
    });

    it('should handle partial queries correctly', async () => {
      await hybridEngine.initialize(testPages);
      
      // Search for partial query
      const results = await hybridEngine.search('claude c');
      
      expect(results.mergedResults.length).toBeGreaterThan(0);
      console.log(`"claude c" returned ${results.mergedResults.length} results`);
    });

    it('should merge results from both engines without duplicates', async () => {
      await hybridEngine.initialize(testPages);
      
      const results = await hybridEngine.search('code');
      
      // Check for duplicates
      const idMap = new Map<string, number>();
      results.mergedResults.forEach(result => {
        const count = idMap.get(result.id) || 0;
        idMap.set(result.id, count + 1);
      });
      
      // No ID should appear more than once
      for (const [id, count] of idMap.entries()) {
        expect(count).toBe(1);
      }
    });
  });

  describe('Index Persistence', () => {
    it('should save and load Lunr index', async () => {
      // Create index
      await lunrEngine.createIndex(testPages);
      
      // Search before reload
      const resultsBefore = await lunrEngine.search('Claude Code');
      expect(resultsBefore.length).toBeGreaterThan(0);
      
      // Create new instance and try to load
      const newLunrEngine = new LunrSearchEngine();
      const loaded = await newLunrEngine.loadIndex();
      
      // In a real environment with IndexedDB, this would be true
      // But in our mock, it will be false
      expect(loaded).toBe(false);
    });
  });

  describe('Service Integration', () => {
    it('should work through hybridSearchService', async () => {
      // Initialize service
      await hybridSearchService.init();
      
      // Perform search
      const results = await hybridSearchService.search('Claude Code');
      
      expect(results).toBeDefined();
      expect(Array.isArray(results)).toBe(true);
    });

    it('should provide search suggestions', async () => {
      await hybridSearchService.init();
      
      const suggestions = await hybridSearchService.getSuggestions('clau', 5);
      
      expect(suggestions).toBeDefined();
      expect(Array.isArray(suggestions)).toBe(true);
      expect(suggestions.length).toBeLessThanOrEqual(5);
    });

    it('should handle index updates', async () => {
      await hybridSearchService.init();
      
      // Add a new page
      const newPage: Page = {
        id: 'test-page-1',
        url: 'https://example.com/claude-code-test',
        title: 'Claude Code Test Page',
        content: 'This is a test page about Claude Code and programming.',
        domain: 'example.com',
        visitTime: Date.now(),
        lastUpdated: Date.now(),
        accessCount: 1,
        language: 'en'
      };
      
      await hybridSearchService.addPageToIndex(newPage);
      
      // Search for the new page
      const results = await hybridSearchService.search('Claude Code Test');
      const found = results.some(r => r.page.id === 'test-page-1');
      
      expect(found).toBe(true);
    });
  });

  describe('Chinese Text Support', () => {
    it('should handle Chinese text search', async () => {
      await hybridEngine.initialize(testPages);
      
      // Search for Chinese text if present in test data
      const results = await hybridEngine.search('开发');
      
      // The test data might not have Chinese content, so we just verify no errors
      expect(results).toBeDefined();
      expect(results.mergedResults).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should complete search within performance targets', async () => {
      await hybridEngine.initialize(testPages);
      
      const startTime = performance.now();
      const results = await hybridEngine.search('Claude Code');
      const elapsed = performance.now() - startTime;
      
      console.log(`Search completed in ${elapsed.toFixed(2)}ms`);
      
      // Should complete within 300ms
      expect(elapsed).toBeLessThan(300);
      expect(results.responseTime).toBeLessThan(300);
    });
  });
});

// Run the test
describe('Validate Specific Issue', () => {
  it('should find multiple results for "Claude Code" not just zhihu', async () => {
    // Load backup data
    const backupPath = path.join(__dirname, '../materials/recall-backup-2025-06-26.json');
    const backupContent = fs.readFileSync(backupPath, 'utf-8');
    const backup = JSON.parse(backupContent);
    const pages: Page[] = backup.pages;
    
    // Manual check
    const manualMatches = pages.filter(page => {
      const text = `${page.title || ''} ${page.content || ''}`.toLowerCase();
      return text.includes('claude code');
    });
    
    console.log(`\\nManual search found ${manualMatches.length} pages containing "claude code":`);
    manualMatches.forEach((page, idx) => {
      console.log(`${idx + 1}. ${page.title}`);
      console.log(`   URL: ${page.url}`);
    });
    
    // Initialize and search
    const engine = new HybridSearchEngine();
    await engine.initialize(pages);
    
    const results = await engine.search('Claude Code');
    
    console.log(`\\nHybrid engine found ${results.mergedResults.length} results`);
    results.mergedResults.forEach((result, idx) => {
      console.log(`${idx + 1}. ${result.title} (score: ${result.combinedScore.toFixed(3)})`);
      console.log(`   URL: ${result.url}`);
    });
    
    // Should find more than just 1 result
    expect(results.mergedResults.length).toBeGreaterThan(1);
    
    // Should include the zhihu page but also others
    const zhihuResults = results.mergedResults.filter(r => r.url.includes('zhihu.com'));
    const otherResults = results.mergedResults.filter(r => !r.url.includes('zhihu.com'));
    
    expect(zhihuResults.length).toBeGreaterThan(0);
    expect(otherResults.length).toBeGreaterThan(0);
  });
});