/**
 * @file Integration tests for AJAX monitoring error recovery and fallback mechanisms.
 * @description This suite verifies that AjaxMonitor, SafeInterceptor, and ErrorRecoverySystem
 * work together to handle failures gracefully, trigger fallbacks, and recover when possible.
 */

import { AjaxMonitor } from '../../src/content/AjaxMonitor';
import { ErrorRecoverySystem } from '../../src/content/ErrorRecovery';
import { SafeInterceptor } from '../../src/content/SafeInterceptor';

describe('AJAX Recovery and Fallback Integration', () => {

  // 保存原始的 XHR 和 fetch 方法
  const originalXhr = global.XMLHttpRequest;
  const originalFetch = global.fetch;

  beforeEach(() => {
    // 在每个测试前清除模拟并恢复原始方法
    jest.clearAllMocks();
    global.XMLHttpRequest = originalXhr;
    global.fetch = originalFetch;
    // @ts-ignore
    global.XMLHttpRequest.prototype.open = originalXhr.prototype.open;
    // @ts-ignore
    global.XMLHttpRequest.prototype.send = originalXhr.prototype.send;
  });

  test('AjaxMonitor should retry initialization on failure and then succeed', async () => {
    let attempt = 0;
    const onAjaxCompleted = jest.fn();

    // 模拟一个在第三次尝试之前会失败的启动过程
    const unstableStart = jest.fn(async () => {
      attempt++;
      if (attempt < 3) {
        throw new Error(`Initialization failed on attempt ${attempt}`);
      }
      // 成功时，模拟一个稳定的监控器
      const stableMonitor = new AjaxMonitor({}, { onAjaxCompleted });
      await stableMonitor.start();
      return stableMonitor;
    });
    
    // 模拟 AjaxMonitor 的构造函数返回一个不稳定的实例
    const monitor = new AjaxMonitor(
        { debug: true }, 
        { onAjaxCompleted }
    );
    
    // 强制模拟 start 方法
    monitor.start = jest.fn().mockImplementation(async () => {
        attempt++;
        if (attempt < 3) {
          throw new Error(`Initialization failed on attempt ${attempt}`);
        }
        // 在第三次尝试时，恢复到原始的 start 实现（或一个稳定的版本）
        // 为了简单起见，这里我们只让它不再抛出错误
    });

    // 为这个测试创建一个专用的恢复系统
    const recoverySystem = new ErrorRecoverySystem({
      maxRetries: 3,
      retryDelay: 10, // 缩短测试延迟
      debug: true
    });

    try {
      await monitor.start();
    } catch (e) {
      // 捕获初始失败，并移交-给恢复系统处理
      const result = await recoverySystem.handleError(e as Error, 'AjaxMonitor', {
        start: monitor.start,
      });
      
      // 验证恢复是否成功 - 可能是重试成功或降级模式
      expect(result.success).toBe(true);
      expect(['retry', 'degraded', 'fallback']).toContain(result.method);
    }
    
    // 验证 start 方法被调用了多次（至少2次，可能3次）
    expect(monitor.start).toHaveBeenCalledWith();
    expect((monitor.start as jest.Mock).mock.calls.length).toBeGreaterThanOrEqual(2);
  });
}); 