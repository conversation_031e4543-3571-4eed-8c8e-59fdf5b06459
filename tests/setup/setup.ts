/**
 * Jest 测试设置文件
 * 配置测试环境，包括 Chrome API 模拟和 IndexedDB 模拟
 */

// Reference Chrome types
/// <reference path="../../src/types/chrome.d.ts" />

// 全局的 Chrome API 模拟
Object.defineProperty(global, 'chrome', {
  value: {
    // storage API 模拟
    storage: {
      local: {
        get: jest.fn((keys, callback) => {
          const result = {};
          if (typeof keys === 'string') {
            // @ts-ignore
            result[keys] = undefined;
          } else if (Array.isArray(keys)) {
            keys.forEach(key => {
               // @ts-ignore
              result[key] = undefined;
            });
          }
          if (callback) {
            callback(result);
            return;
          }
          return Promise.resolve(result);
        }),
        set: jest.fn((_items, callback) => {
          if (callback) {
            callback();
          }
          return Promise.resolve(undefined);
        }),
        remove: jest.fn((_keys, callback) => {
          if (callback) callback();
          return Promise.resolve(undefined);
        }),
        clear: jest.fn(callback => {
          if (callback) callback();
          return Promise.resolve(undefined);
        }),
      },
      sync: {
        get: jest.fn().mockResolvedValue({}),
        set: jest.fn().mockResolvedValue(undefined),
        remove: jest.fn().mockResolvedValue(undefined),
        clear: jest.fn().mockResolvedValue(undefined),
      },
    },
    // runtime API 模拟
    runtime: {
      sendMessage: jest.fn().mockResolvedValue({}),
      onMessage: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
        hasListener: jest.fn(),
      },
      getURL: jest.fn((path) => `chrome-extension://test-id/${path}`),
      id: 'test-extension-id',
      onInstalled: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
        hasListener: jest.fn(),
      },
      onStartup: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
        hasListener: jest.fn(),
      },
      onSuspend: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
        hasListener: jest.fn(),
      },
      getManifest: jest.fn(() => ({
        manifest_version: 3,
        name: 'Recall',
        version: '3.0.0',
      })),
      onConnect: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
        hasListener: jest.fn(),
      },
      onConnectExternal: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
        hasListener: jest.fn(),
      },
      onUpdateAvailable: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
        hasListener: jest.fn(),
      },
    },
    // tabs API 模拟
    tabs: {
      query: jest.fn().mockResolvedValue([]),
      get: jest.fn().mockResolvedValue({ id: 1, url: 'https://example.com' }),
      update: jest.fn().mockResolvedValue({}),
      create: jest.fn().mockResolvedValue({}),
      remove: jest.fn().mockResolvedValue(undefined),
      sendMessage: jest.fn().mockResolvedValue({}),
      onUpdated: {
        addListener: jest.fn(),
        removeListener: jest.fn(),
        hasListener: jest.fn(),
      },
    },
    // windows API 模拟
    windows: {
      get: jest.fn().mockResolvedValue({}),
      getAll: jest.fn().mockResolvedValue([]),
      create: jest.fn().mockResolvedValue({}),
      update: jest.fn().mockResolvedValue({}),
    },
    // notifications API 模拟
    notifications: {
      create: jest.fn(),
      clear: jest.fn(),
      onClicked: {
        addListener: jest.fn(),
      },
      onClosed: {
        addListener: jest.fn(),
      },
    },
  },
  writable: true,
});

// IndexedDB Mock
class MockIDBRequest {
  result: any = null;
  error: any = null;
  onsuccess: ((event: any) => void) | null = null;
  onerror: ((event: any) => void) | null = null;
  readyState: string = 'done';

  constructor(result?: any) {
    this.result = result;
    // Simulate async completion
    setTimeout(() => {
      if (this.onsuccess) {
        this.onsuccess({ target: this });
      }
    }, 0);
  }
}

class MockIDBObjectStore {
  name: string;
  keyPath: any;
  indexNames: string[] = [];

  constructor(name: string) {
    this.name = name;
  }

  add = jest.fn().mockReturnValue(new MockIDBRequest());
  put = jest.fn().mockReturnValue(new MockIDBRequest());
  get = jest.fn().mockReturnValue(new MockIDBRequest({}));
  getAll = jest.fn().mockReturnValue(new MockIDBRequest([]));
  delete = jest.fn().mockReturnValue(new MockIDBRequest());
  clear = jest.fn().mockReturnValue(new MockIDBRequest());
  count = jest.fn().mockReturnValue(new MockIDBRequest(0));
  createIndex = jest.fn();
  index = jest.fn().mockReturnThis();
  openCursor = jest.fn().mockReturnValue(new MockIDBRequest(null));
}

class MockIDBTransaction {
  mode: string = 'readonly';
  objectStoreNames: string[];
  oncomplete: ((event: any) => void) | null = null;
  onerror: ((event: any) => void) | null = null;
  onabort: ((event: any) => void) | null = null;

  constructor(storeNames: string[], mode: string = 'readonly') {
    this.objectStoreNames = storeNames;
    this.mode = mode;
  }

  objectStore = jest.fn((name: string) => new MockIDBObjectStore(name));
  abort = jest.fn();
}

class MockIDBDatabase {
  name: string;
  version: number;
  objectStoreNames: string[] = ['pages', 'settings', 'searchIndexes'];

  constructor(name: string = 'recall-db', version: number = 1) {
    this.name = name;
    this.version = version;
  }

  transaction = jest.fn((storeNames: string | string[], mode: string = 'readonly') => {
    const names = Array.isArray(storeNames) ? storeNames : [storeNames];
    return new MockIDBTransaction(names, mode);
  });

  createObjectStore = jest.fn((name: string) => new MockIDBObjectStore(name));
  deleteObjectStore = jest.fn();
  close = jest.fn();
}

const mockIndexedDB = {
  open: jest.fn().mockImplementation((name: string, version?: number) => {
    const request = new MockIDBRequest();
    request.result = new MockIDBDatabase(name, version);
    return request;
  }),
  deleteDatabase: jest.fn().mockReturnValue(new MockIDBRequest()),
  databases: jest.fn().mockResolvedValue([]),
};

Object.defineProperty(global, 'indexedDB', {
  value: mockIndexedDB,
  writable: true,
});

Object.defineProperty(global, 'IDBKeyRange', {
  value: {
    bound: jest.fn(),
    only: jest.fn(),
    lowerBound: jest.fn(),
    upperBound: jest.fn(),
  },
  writable: true,
});

// Web Crypto API Mock
Object.defineProperty(global, 'crypto', {
  value: {
    subtle: {
      encrypt: jest.fn().mockResolvedValue(new ArrayBuffer(16)),
      decrypt: jest.fn().mockResolvedValue(new ArrayBuffer(16)),
      generateKey: jest.fn().mockResolvedValue({}),
      importKey: jest.fn().mockResolvedValue({}),
      exportKey: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
      deriveBits: jest.fn().mockResolvedValue(new ArrayBuffer(32)),
      deriveKey: jest.fn().mockResolvedValue({}),
    },
    getRandomValues: jest.fn().mockImplementation((arr: any) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    }),
  },
  writable: true,
});

// Performance API Mock
Object.defineProperty(global, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn().mockReturnValue([]),
    getEntriesByName: jest.fn().mockReturnValue([]),
  },
  writable: true,
});

// URL Mock
Object.defineProperty(global, 'URL', {
  value: class MockURL {
    href: string;
    origin: string;
    protocol: string;
    hostname: string;
    port: string;
    pathname: string;
    search: string;
    hash: string;

    constructor(url: string, base?: string) {
      // Simple URL parsing for tests
      const fullUrl = base ? new URL(url, base).href : url;
      this.href = fullUrl;
      
      const parts = fullUrl.match(/^(https?:)\/\/([^:/]+)(:(\d+))?(\/[^?#]*)?(\?[^#]*)?(#.*)?$/) || [];
      this.protocol = parts[1] || 'https:';
      this.hostname = parts[2] || 'localhost';
      this.port = parts[4] || '';
      this.pathname = parts[5] || '/';
      this.search = parts[6] || '';
      this.hash = parts[7] || '';
      this.origin = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;
    }

    toString() {
      return this.href;
    }
  },
  writable: true,
});

// Request idle callback mock
Object.defineProperty(global, 'requestIdleCallback', {
  value: jest.fn((callback: any) => {
    setTimeout(callback, 0);
  }),
  writable: true,
});

// Console mock to reduce noise in tests
const originalConsole = global.console;
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
};

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
});