{"version": "2.0.0", "timestamp": 1750960976841, "pages": [{"id": "09f9560b-81d0-434a-bf19-ca06eb1b32dc", "url": "https://linux.do/t/topic/751546", "title": "4张淘宝夜宵免单卡 - 福利羊毛 - LINUX DO", "content": "话题 关于 即将到来的活动 开发调优 资源荟萃 文档共建 非我莫属 读书成诗 扬帆起航 前沿快讯 网络记忆 福利羊毛 搞七捻三 运营反馈 所有类别 人工智能 公告 快问快答 抽奖 精华神帖 所有标签 Default 真诚、友善、团结、专业，共建你我引以为荣之社区。《常见问题解答》 ❤️ 再忍不住，也不要做这种事啊 ❤️ 福利羊毛 您已选择 0 个帖子。 全选 取消选择 xjw一元复始3 分钟快来抢夜宵免单卡，请通过以下任一方式抢免单 ①去淘宝App搜索提取码：402K99 ②点击链接领取https://m.tb.cn/h.hVgRgrE?tk=ti44VDN0ThG ③完整复制这条信息，打开淘宝App自动识别 路过的佬友们麻烦点个赞吧 只差单日7个赞升3级了 1 此话题将在最后一个回复的1 个月后关闭。 您好！看起来您很喜欢讨论，但您还没有注册帐户。 厌倦了滚动浏览相同的帖子？当您创建帐户后，您将始终回到您离开的地方。使用帐户，您还可以收到新回复通知、保存书签，以及使用“赞”来感谢他人。我们可以共同努力，使这个社区变得更好。 不，谢谢 👕 岂曰无衣？与子同袍 👕️ 相关话题 话题列表，带有按钮的列标题可以排序。 话题 回复 浏览量 活动 淘宝免单卡！ 福利羊毛 5 456 27 天 免单卡，晚上喝一杯吧 福利羊毛 6 535 5 天 【已抢完】淘宝免单卡, 手快有手慢无, 5抢1 福利羊毛 8 337 29 天 淘宝免单卡自取～ 福利羊毛 7 608 3 天 两张闪购粽子免单卡 福利羊毛 AFF 3 203 26 天", "domain": "linux.do", "visitTime": 1750957065475, "accessCount": 3, "lastUpdated": 1750957070828, "contentStatus": "extracted", "lastExtractionAttempt": 1750957070303}, {"id": "0bd9cff6-bdeb-4af0-b802-424b2c907cb1", "url": "https://github.com/ruanyf/weekly", "title": "ruanyf/weekly: 科技爱好者周刊，每周五发布", "content": "Open in github.dev Open in a new github.dev tab Open in codespace 1 Branch320 TagsAdd fileAdd fileFolders and filesNameNameLast commit messageLast commit dateLatest commitruanyfMerge pull request #7137 from junminhong/fix/353-meta-typoJun 25, 2025d13c574 · Jun 25, 2025History728 Commitsdocsdocsfix(docs/issue-353.md): meta word typoJun 25, 2025.gitignore.gitignoredocs: release issue 204Apr 22, 2022README.mdREADME.mddocs: release issue 353Jun 20, 2025Repository files navigationREADME科技爱好者周刊 记录每周值得分享的科技内容，周五发布。 欢迎投稿，推荐或自荐文章/软件/资源，请提交 issue 。 P.S. 讨论区的《谁在招人》，是一个免费的程序员招聘帖，提供大量就业信息，欢迎访问或发布工作/实习岗位。 如何搜索 周刊已经沉淀了大量内容，可以使用下面的几种方法进行搜索。 1、使用 GitHub 自带的网页搜索。 2、使用 Sourcegraph.com 进行搜索。 3、将这个仓库克隆到本地，然后在仓库目录使用下面的命令。 $ grep -nri [搜索词] docs | cat --number 比如，搜索 CSS 相关内容。 $ grep -nri css docs | cat --number 2025 六月 第 353 期：苹果的“液态玻璃”是为了 AR 第 352 期：Bug 追踪系统的正确样子 第 351 期：GitHub Issues（几乎）是最好的笔记应用 五月 第 350 期：Java 三十周年 第 349 期：神经网络算法的发明者 第 348 期：李飞飞，从移民到 AI 明星 第 347 期：冷启动的破解之道 四月 第 346 期：未来就是永恒感的丧失 第 345 期：HDMI 2.2 影音可能到头了 第 344 期：制造业正在“零工化” 三月 第 343 期：如何阻止 AI 爬虫 第 342 期：面试的 AI 作弊——用数字人去面试 第 341 期：低代码编程，恐怕不会成功 第 340 期：技术炒作三十年 二月 第 339 期：代币是什么 第 338 期：重新思考 6G 第 337 期：互联网创业几乎没了 第 336 期：面对 AI，互联网正在衰落 一月 第 335 期：年底的未来已来 第 334 期：年终笔记四则 第 333 期：一切都要支付两次 第 332 期：西蒙·威利森的年终总结，梁文锋的访谈 2024 十二月 第 331 期：你可能是一个 NPC 第 330 期：李开复梳理人工智能 第 329 期：示意图利器 D2 第 328 期：AI 模型不是一门好生意 十一月 第 327 期：没有链接的互联网 第 326 期：世界没有那么多财富 第 325 期：VS Code 编辑器的下一站是 Zed？ 第 324 期：人类已知的最大质数 第 323 期：技术公司的口号比拼 十月 第 322 期：内容行业的内幕 第 321 期：傅盛回忆录 第 320 期：乒乓仓 九月 第 319 期：如何拍出爆款视频 第 318 期：创业咖啡馆的记忆 第 317 期：驴子，老虎和狮子的寓言 第 316 期：你一生的故事 八月 第 315 期：一份谷歌离职报告 第 314 期：《黑神话：悟空》可以产业化吗？ 第 313 期：如果新加坡没有空调 第 312 期：从英特尔看“美国制造” 第 311 期：低利率与长期项目 七月 第 310 期：内容农场的 AI 赚钱术 第 309 期：无人驾驶出租车的双面刃 第 308 期：工作找不到，博士能读吗？ 第 307 期：不要看重 Product Hunt 六月 第 306 期：信息就像糖一样上瘾 第 305 期：随机数，这是一个问题 第 304 期：最受欢迎的颜色 第 303 期：技术封建主义 五月 第 302 期：创业虽然好，不敢推荐了 第 301 期：OpenAI 的图书馆工位 第 300 期：三十年，解决人生三大问题 第 299 期：AI 的关键是语料 四月 第 298 期：轮到硬件工程师吃香了 第 297 期：饮水鸟玩具 第 296 期：xz 后门的作者 Jia Tan 是谁？ 三月 第 295 期：巧妙的灯泡钟 第 294 期：崖门海战的感想 第 293 期：一周是一年的2% 第 292 期：所有代码都是技术债 第 291 期：AI 没有护城河 二月 第 290 期：苹果头盔的最大问题 第 289 期：宽容从何而来 一月 第 288 期：技术写作的首要诀窍 第 287 期：禄丰恐龙谷记行 第 286 期：蓝色指示灯的解决方案 第 285 期：为什么 PPT 不如备忘录 2023 十二月 第 284 期：YouTube 有多少个视频？ 第 283 期：[年终感想] 没有目的地，向前走 第 282 期：电动皮卡 Cybertruck 的 48V 供电 第 281 期：新基建的政策选择 第 280 期：机器点餐与宅文化 十一月 第 279 期：网络社区的悲剧 第 278 期：棘手的 AI 版权 第 277 期：工作台副屏的最佳选择 第 276 期：内容行业的衰落 十月 第 275 期：彼得·蒂尔的实验 第 274 期：加密通信的最后一块拼图 第 273 期：任正非的三篇最新谈话 九月 第 272 期：Unity 的安装费，游戏业的缩影 第 271 期：非线性的世界，线性的你 第 270 期：“精益开发”的精益是什么？ 第 269 期：为什么英雄不使用炸药 八月 第 268 期：生产力是形容机器，不是形容人 第 267 期：5G 的春天要来了 第 266 期：自己做双语 EPUB 电子书 第 265 期：WiFi 的后面是 LiFi 七月 第 264 期：Elasticsearch 的启示 第 263 期：开源软件如何赚钱？ 第 262 期：告别密码 第 261 期：黑客马拉松的正确方式 六月 第 260 期：你的旅程不会停在 Day 1 第 259 期：如何免费使用 ChatGPT 第 258 期：卡马克的猫 第 257 期：黄仁勋的 Nvidia 故事 五月 第 256 期：最酷的乐高作品 第 255 期：对待 AI 的正确态度 第 254 期：人生是一个长板问题 第 253 期：训练材料用完之日 四月 第 252 期：互联网创业变难了 第 251 期：国产单板机值得推荐 第 250 期：新技术的最大风险 第 249 期：最成功的软件企业家 三月 第 248 期：不要夸大 ChatGPT 第 247 期：扎克伯克的裁员信 第 246 期：永不丢失的网络身份 第 245 期：摩天大楼是反人类的 第 244 期：大数据已死 二月 第 243 期：与孔子 AI 聊天 第 242 期：一次尴尬的服务器被黑 第 241 期：中国的增长动力在内陆 第 240 期：教育年限可以缩短吗？ 一月 第 239 期：未来两种人会增加 第 238 期：停止寻找的最佳时间 2022 十二月 第 237 期：真实方位是如何暴露的？ 第 236 期：中国的阳光地带 第 235 期：青年失业率与选择创业 第 234 期：AI 聊天有多强 第 233 期：生活就像一个鱼缸 十一月 第 232 期：好用的平面设计软件 第 231 期：互联网公司需要多少员工？ 第 230 期：电子产品的用电量 第 229 期：手机充电问题的解决 十月 第 228 期：人类和人生的意义 第 227 期：脸书的公司入职教育 第 226 期：谷歌出了什么问题？ 九月 第 225 期：游戏 NPC 也是一种职业 第 224 期：Figma 为什么赢了 Sketch 第 223 期：程序员需要担心裁员吗？ 第 222 期：四十年编程感想 第 221 期：全世界最繁荣的行业 八月 第 220 期：人工智能的机会在哪里 第 219 期：如何防止帐号被黑 第 218 期：葡萄酒，樱花，全球变暖 第 217 期：沙特的新未来城 七月 第 216 期：极简主义的胜利 第 215 期：互联网最喜欢的行为模式 第 214 期：你的地图是错的 第 213 期：知识孤岛，知识软件 第 212 期：人生不短 六月 第 211 期：虚拟商品可以拉动 GDP 第 210 期：为什么软件变得复杂 第 209 期：程序员是怎样的人 五月 第 208 期：晋升制度的问题 第 207 期：汽车行业的顶峰可能过去了 第 206 期：如何走出失望和怀疑 第 205 期：互联网风口过去了吗？ 四月 第 204 期：如何度过疫情、裁员、还有战争 第 203 期：英国的名校签证，伯克利的计算机教育 第 202 期：三个有启发的学习方法 第 201 期：中国需要成立半导体部 三月 第 200 期：低期望，多尝试 第 199 期：俄罗斯的 HTTPS 证书问题 第 198 期：美国制造是否可能 第 197 期：如果这个世界有快乐机 二月 第 196 期：掌机的未来 第 195 期：你做过不在乎结果的项目吗？ 第 194 期：悲观者正确，乐观者成功 一月 第 193 期：前端与后端，谁更难？ 第 192 期：最大的机会来自新技术 第 191 期：一个程序员的财务独立之路 2021 十二月 第 190 期：产品化思维 第 189 期：下一个内卷的行业 第 188 期：音乐是反社交 第 187 期：元宇宙会成功吗 第 186 期：低纬度，高海拔，气候优势 十一月 第 185 期：美国宪法拍卖，一个区块链案例 第 184 期：政府的存储需求有多大？ 第 183 期：腾讯的员工退休福利 第 182 期：新人优惠的风险 十月 第 181 期：移动支付应该怎么设计？ 第 180 期：你想住在中国哪里？ 第 179 期：AR 技术的打开方式 第 178 期：家庭太阳能发电的春天 九月 第 177 期：iPad 的真正用途 第 176 期：中国法院承认 GPL 吗？ 第 175 期：知识广度 vs 知识深度 第 174 期：全能程序员 vs 特长程序员 八月 第 173 期：网络收音机的设计 第 172 期：我们会死于气候灾难吗？ 第 171 期：云服务流量有多贵？ 第 170 期：软件订阅制的胜利 七月 第 169 期：五菱汽车的产品设计 第 168 期：游戏《底特律：变人》 第 167 期：广告拦截器太过分了 第 166 期：视频学习胜过读书吗？ 第 165 期：全端 App 的时代 六月 第 164 期：培训班 vs 大学，求职成功率比较 第 163 期：你的城市有多少张病床？ 第 162 期：生活就像《吃豆人》游戏 第 161 期：再见了，学术硕士 五月 第 160 期：中年码农的危机 第 159 期：游戏开发者的年薪 第 158 期：内容渠道的贬值 第 157 期：KK 给年轻人的建议 四月 第 156 期：显卡缺货与异业竞争 第 155 期：数字货币是打破美元霸权的武器吗？ 第 154 期：1982年的信息社会预言 第 153 期：机器翻译是对译者的侮辱吗？ 第 152 期：从北大到技校 三月 第 151 期：NFT 是什么，听说能赚钱 第 150 期：当音乐还是稀缺的时候 第 149 期：新能源汽车，谁会是赢家？ 第 148 期：微增长时代 二月 第 147 期：寻找你愿意忍受的痛苦 第 146 期：网课应该怎么上？ 第 145 期：大家不出门，经济怎么办？ 一月 第 144 期：提高收入的根本途径 第 143 期：世界尽头与冷酷仙境 第 142 期：2020年才是21世纪元年 第 141 期：封闭系统的胜利 2020 十二月 第 140 期：印度人的工资是多少？ 第 139 期：生物学的可怕进展 第 138 期：失业难以避免，重构人生规划 第 137 期：Slack 被收购，以及企业的技术选型 第 136 期：利特伍德奇迹定律 十一月 第 135 期：什么行业适合创业？ 第 134 期：未来的游戏业比现在大100倍 第 133 期：贵州变瑞士，有没有可能？ 第 132 期：快能力和慢能力 十月 第 131 期：你的头脑是二值逻辑，还是三值逻辑？ 第 130 期：低龄化的互联网 第 129 期：创业的凸函数与凹函数 第 128 期：这个社会是否正在变成“赛博朋克”？ 九月 第 127 期：未来人人开发软件，几乎没人编码 第 126 期：内卷化是什么？ 第 125 期：数字人民币要取代谁 第 124 期：华为如何考核员工 第 123 期：互联网公司与湘军的军制 八月 第 122 期：谈谈互联网公司的高估值 第 121 期：为什么人类没有越来越闲？ 第 120 期：只有开放才能打败封锁 第 119 期：降雨量和保险博弈 七月 第 118 期：高考志愿怎么填 第 117 期：我不想让你记住我的脸 第 116 期：世界的未来就是一个火药桶 第 115 期：保护你的 DNA，不要泄漏 第 114 期：U 盘化生存和 Uber-job 六月 第 113 期：暴力犯罪为什么越来越少？ 第 112 期：如何培养领导力 第 111 期：智能电视的误区 第 110 期：如果不能去美国上市 五月 第 109 期：播客的价值 第 108 期：阵地战与奇袭战 第 107 期：致富与杠杆 第 106 期：数字游民 四月 第 105 期：线上行业会赢 第 104 期：语音合成的用途 第 103 期：信息的半衰期 第 102 期：工作热情从何而来？ 第 101 期：互联网不再稀缺 三月 第 100 期：零利率时代 第 99 期：疫情导致的研究生扩招 第 98 期：怎样清晰地表达自己的观点？ 第 97 期：那些为了考试拼搏的年轻人 二月 第 96 期：在线教育不等于录制视频 第 95 期：远程办公暴露冗余岗位 第 94 期：既懂得制造，又懂得销售 第 93 期：漫游类的游戏，将会越来越多 一月 第 92 期：听觉暂留 第 91 期：印度孟买的房价，为什么跟北京一样高？ 第 90 期：管人和技术是两种不同的能力 第 89 期：不下雨的地方，不要去卖伞 2019 十二月 第 88 期：如果你遇到一条蛇 第 87 期：新人要为团队写文档 第 86 期：千万不要当完美主义者 第 85 期：美国为什么不是乱哄哄？ 十一月 第 84 期：一次性工作招聘，用完你就丢 第 83 期：技术解决不了人类的对立 第 82 期：就业要选发展最快的行业 第 81 期：子辈能大幅超越父辈吗？ 第 80 期：企业软件创业，为什么在中国不容易成功？ 十月 第 79 期：我们的生活越来越依赖机器 第 78 期：下一个风口是什么行业？ 第 77 期：韩剧《阿尔布拉罕宫的回忆》 第 76 期：任何爱好都能变成职业，只要你会拍视频 九月 第 75 期：电子取代机械，对就业有何影响？ 第 74 期：信息的商业模式为什么不是收费 第 73 期：数据统计的威力 第 72 期：当代人不再有手稿 八月 第 71 期：名校毕业，不容易创业 第 70 期：世界进入负利率时代，这意味什么 第 69 期：做得好 vs 做得快 第 68 期：关注能力的成长，胜于关注待遇 第 67 期：复杂系统无法维护，侏罗纪公园必定失败 七月 第 66 期：创业不是零和游戏 第 65 期：周刊开设“谁在招人”的招聘服务 第 64 期：新人如何进入互联网行业？ 第 63 期：互联网市场的集中化趋势 六月 第 62 期：日本电影《编舟记》 第 61 期：转行前端越来越难 第 60 期：一本介绍人类起源的学术自传 第 59 期：互联网时代很难交朋友 五月 第 58 期：软件推广可以像化妆品那样吗？ 第 57 期：分享知识是否违反人性？ 第 56 期：文科生为什么不容易就业？ 第 55 期：不是反对 996，而要提倡远程办公 第 54 期：可扩展性最好的活动 四月 第 53 期：DNA 相亲会 第 52 期：人脸识别与课堂监控 第 51 期：为什么过去10年，笔记本硬件发展缓慢？ 第 50 期：\"时间换收入\"是一个陷阱 三月 第 49 期：学会有所不为 第 48 期：著名程序员 Bill Joy 的人生启示 第 47 期：吃播算不算正式工作？ 第 46 期：推荐算法的副作用 第 45 期：阿西莫夫回忆录《人生舞台》 二月 第 44 期：高校“唯论文”导向的后果 第 43 期：一篇好玩的论文 第 42 期：什么领域，你可以做到出类拔萃？ 一月 第 41 期：如何看待互联网公司裁员？ 第 40 期：手动咖啡不属于电子时代 第 39 期：苹果公司的两封公开信 第 38 期：使用越方便，技术实现越复杂 2018 十二月 第 37 期：小说家的时代，永远地过去了 第 36 期：程序员将来会不会过剩？ 第 35 期：“一人份”的服务越来越多 第 34 期：身份证的最终解决方案：人体植入芯片 十一月 第 33 期：现场投票不如网络投票 第 32 期：砌砖头的三种角度 第 31 期：程序员的退休信号 第 30 期：为什么谷歌做不好社交软件？ 第 29 期：公司的组织架构，决定了软件的复杂性 十月 第 28 期：软件开发是真正的知识吗？ 第 27 期：乔布斯的“热情假设”对不对？ 第 26 期：DHH 的新书《工作何必疯狂》 第 25 期：安卓手机十周年的感想 九月 第 24 期：新人进入软件行业的建议 第 23 期：统计学上的人生最大决定因素 第 22 期：猴子自拍，版权归谁 第 21 期：人生的水平运动和垂直运动 八月 第 20 期：不读大学的替代方案 第 19 期：电影《头号玩家》描绘未来的虚拟世界 第 18 期：无人机攻击，难以防范 第 17 期：全球变暖，在劫难逃 第 16 期：科技改变死亡的模式 七月 第 15 期：周刊的内容从何而来？ 第 14 期：马斯克的人生才是梦想家的人生 第 13 期：周刊为什么只谈技术？ 第 12 期：人口老龄化，养老金不够 六月 第 11 期：编程语言越发复杂 第 10 期：30岁以后谨慎转行前端 第 9 期：身份证可以植入人体 第 8 期：实验室会生产人吗？ 第 7 期：垃圾填埋不是解决办法 五月 第 6 期：未来还需要苦学外语吗？ 第 5 期：互联网时代，做一个好人是划算的 第 4 期：马克思研究的问题 第 3 期：周刊的风格 四月 第 2 期：为什么写周刊？ 第 1 期：创刊号 About 科技爱好者周刊，每周五发布 Resources Readme Activity Stars 69.5k stars Watchers 3.1k watching Forks 3.5k forks Report repository Releases 320 tags Packages No packages published Contributors 62 + 48 contributors", "domain": "github.com", "visitTime": *************, "accessCount": 2, "lastUpdated": *************, "contentStatus": "extracted", "lastExtractionAttempt": *************}, {"id": "0d268dc2-392b-4405-8450-dc8127e4d30d", "url": "https://www.reddit.com/r/ClaudeAI/comments/1fziibo/what_does_the_actual_workflow_look_like_for_using/", "title": "What does the actual workflow look like for using <PERSON> to help a beginner code?", "content": "I have 3 different Claude accounts that I rotate through. I use projects for everything. A very important part before you start is to ask what you need to set up first in terms of the software you’re using in order to make the app you’re trying to build possible. This will save you headaches down the road. For example, I learned there are different memory types for iOS apps for different things by doing that. Now when I start building something I’ll ask which is the best for what I’m trying to do. Here are some loose and unorganized notes to help you get started: AI Dev Notes Use Claude for solving functions and troubleshooting. Use ChatGPT for design and details like animations. Ask if there is a better way to do it or if the new files it asks you to make are needed or is there a simpler way to do it. This helps avoid unnecessary modifications. Tell it to add markup to the code if you want to manually adjust. Tell it to act as a dev for the thing you’re building, app/chrome extension. When doing linear designs tell it to follow iOS app dev best practices for design and function. Do onboarding and animations last to reduce AI credit usage. Use “branches” in Xcode! When building a page, build core functions first. Reduces code usage faster, then work on fine-tuning design. Be specific. Don’t use “it”. Ex:. Make it smaller. (Wrong). Make the button at the bottom of the page smaller.(Correct) (Was using “it” but it fixed when “button” was used. Many examples of this.). If it asks to set up a bunch of shit in Xcode, ask if there is a simpler way to do it. Ask step-by-step for how to set up in Xcode. When you get the “X messages remaining,” message don’t do anything important or anything that requires it to make a lot of code. It can run out of characters. When starting a new project in Claude upload all files and tell it to explain the app functions and give a brief of what it’s used for. (Claude) If you max out response length tell it the page you have and tell it to now make the rest of the pages for the update. When troubleshooting or working on a detail try to fix one problem at a time if it’s giving issues in understanding. If Claude is running in circles or repeating the same message start a new project. When asking for code to copy/paste tell it to “write the full updated code for me to copy/paste.” Clean the build folder if there are persistent errors. Sometimes just restarting can clear them from memory. Get icons together first. When fine tuning, instead of having it remake all of the code, ask it “where in the code can I [task]”. This helps speed and saves response credits. Xcode has Command+F to find the code you need for fine tuning/manual adjustment -If working on something small, instruct it to not make “sweeping changes”. -If it messes up everything and you’re only working with a few files, grab the original files out of claude and copy/paste them to start fresh again so you don’t need to restore a branch. -When troubleshooting, only upload the files that might be involved. This includes the main app file, any response stores, etc. Anything that could be affecting the function you’re trying to fix. Use this site https://www.diffchecker.com/text-compare/ or another to compare code before making the updates to see the changes being made and to notice other lines of code that were removed or “forgotten” when the new code was being generated. Use XML markup when prompting to help Claude and chat gpt better understand everything. Submitting to Apple: If submitting as company the company needs a D&B number. More info here https://www.dnb.com/duns/get-a-duns.html (check first to make sure your company doesn’t already have one) When making screenshots for the graphics for the app store, use iPhone 8 plus for the 5.5” screenshots. Have to submit full app for review in order to activate paid products. Make sure when submitting your first in app purchase that you also submit a new VERSION of the app, not just a new BUILD. Misc: Lotti files are how you can play short videos with low file sizes in the app as part of the design. Animated SVGs are also very low file size solutions. (There is an after effects plugin that will export as animated SVG) If on a mac and syncing with iCloud you can work on the app while on a main computer and then easily open it in it’s current state on a laptop. Download SF icons. It lets you search icons and will show color variations or animations if you choose to use those options. Reminders for next app: Set up core first - memory, colors, core functions. This ensures the important parts are set up and the app can be built around them. Know what the paid/free features will be before building so AI can be instructed to use matching naming conventions so it all works together. Memory - UserData is great for small loads. CoreData is great for more complex apps. Databases (using Firebase or similar) are best for complex apps with a lot of data. Things I got flagged on after submission: My upgrade logo (for in-app purchase) was too similar to my app logo so I had to update it. Message from Apple: “We noticed that your promotional image to be displayed on the App Store does not sufficiently represent the associated promoted in-app purchase. Specifically, we found the following issue with your promotional image: – Your promotional image is the same as your app’s icon.“ The app I made to get those notes is called Creator Keyboard if you want to look it up in the App Store.", "domain": "www.reddit.com", "visitTime": 1750960781669, "accessCount": 2, "lastUpdated": 1750960781674, "contentStatus": "extracted", "lastExtractionAttempt": 1750960781637}, {"id": "200ab62f-d08a-4263-a92a-a70c1de78922", "url": "https://zhuanlan.zhihu.com/p/1895522767971014513", "title": "数字IC NPU 22nm流片项目", "content": "IC修真院全流程流片项目上线以来，经历过期待、质疑、挑战、信任、感谢，到现在的成功回测，学员拿到芯片、上岸入职企业……反而说明了行则必至，步步生花。行业在不断发展，AI浪潮激发了更加多元的需求和产品。IC修真院经历多轮深度调研，重磅推出：《数字IC NPU 芯片全流程设计与流片实战项目》流片工艺：TSMC 22nmNPU（Neural Processing Unit，神经网络处理器）是一种专门为人工智能任务设计的处理器，尤其擅长加速神经网络的计算，比如矩阵乘法、卷积运算等。 它通过并行计算和优化内存访问，高效处理深度学习模型的推理（预测）任务，比传统CPU和GPU更省电且速度更快。CPU擅长的是通用任务，比如刷网页；GPU擅长的是图形和复杂计算，比如打游戏；而NPU只专注于处理AI任务，比如语音识别、图像识别。在人工智能的浪潮下，AI芯片类的产品大有可为。摩根士丹利预测AI ASIC的总可用市场将从2024年的120亿美元增长到2027年的300亿美元。现在NPU领域的主要玩家，都是英特尔、AMD、高通、英伟达、苹果、华为这类头部DesignHouse/手机厂商。这是未来AI技术发展的一大趋势，对于从业或即将从业的诸君来说，NPU无疑是一个黄金机会和加分筹码。NPU全流程项目实战课学什么？采用实战式培训，结合实际项目完成主流AI算法框架介绍，推理/训练方法介绍、RTL设计、仿真自测、验证策略、验证方法制定、测试点提取、验证平台搭建、验证执行、覆盖率检查、floorplan、place、cts、route以及寄生参数提取、静态时序分析、物理验证等全部开发过程，实现数字芯片开发综合能力全覆盖。 流片过程是一个综合性的工程实践，涵盖从设计到验证到后端等多环节的协同。举个例子，在验证环节发现的问题可能需要追溯到设计源头进行修正，这就促使学员去深入了解设计原理；与后端环节的沟通协作可以让学员明白芯片物理实现对验证策略的要求。不是“为了流片而流片”，而是要真切帮助学员完成从“技能训练”到“解决实际问题能力”的转变。这样全面的培养目标有助于培养出具有全局视野和跨环节协作能力的综合性芯片人才。详情可见下方课程大纲。课程大纲：课程亮点：从AI技术的基础知识和算法入手，一步步深入到神经网络的训练、推理，直至NPU芯片的架构设计与实际流片开发。课程覆盖了从算法、前端设计、数字验证，到芯片后端实现的整个产业链流程。 1、全局把控：覆盖全产业链技能AI算法基础：掌握AI算法核心，如算子、经典神经网络等，了解深度学习框架和大模型的开发、优化技巧，为后续芯片开发奠定理论基础。芯片设计流程：涵盖数字芯片设计流程，包括前端设计、数字验证，体验从零到一的完整开发过程，掌握跨时钟域电路设计等关键技巧。芯片后端实现：学习静态时序分析、布局布线等芯片后端设计核心内容，熟练使用相关工具，实现芯片从逻辑网表到物理实现的全过程。2、理论+实战：无缝对接企业需求真实流片项目贯穿：课程全程以实际NPU芯片项目为载体，每个阶段都有对应的项目实操，让学员在实践中掌握技能。贴合企业标准：课程内容完全基于真实企业项目设计，所涉及的技术和流程与企业实际需求高度一致，确保学员入职后能立刻上手。技能同步提升：在项目实操过程中，学员不仅能巩固理论知识，还能同步提升解决实际问题的能力，实现理论与实战的无缝对接。3、职业适配：多岗位发展路径AI算法工程师：通过课程学习，学员能够掌握AI算法的核心知识和开发技巧，具备成为AI算法工程师的能力，负责算法的研究和优化。数字IC设计工程师：熟悉数字芯片设计流程和相关技术，能够独立完成芯片前端设计工作，成为数字芯片设计工程师，参与芯片的架构设计和电路实现。数字IC验证工程师：学习系统级验证方法和工具，掌握芯片验证流程和技巧，可胜任验证工程师岗位，确保芯片功能的正确性和稳定性。数字IC后端工程师：掌握芯片后端设计的核心技术，如静态时序分析、布局布线等，能够完成芯片的物理实现，成为后端工程师。NPU全流程项目优势是什么？IC修真院开创了业内【全流程项目+直播课程+真实流片】的先锋，多年来积累的雄厚师资、完备的课程体系、细致的课程服务，是我们的底气，更是大家的保障！ NPU项目本身在业内就有很高的认可度和含金量，对于学员和希望进入IC行业的同学来说，这是一门绝佳的课程和上好的机会。1、掌握AI加速核心技术：NPU作为AI芯片的核心组件，参与其设计可深入理解神经网络加速、低功耗优化、并行计算等关键技术。2、跨领域技术融合能力：NPU常应用于手机、汽车、物联网等领域，项目经验可帮助学员理解不同场景下的设计需求，提升跨行业适应能力。3、22nm真实流片：体验企业中真实芯片设计与开发的过程，并参与真实流片。众所周知，IC企业通常更青睐有实际流片经验或参与过复杂模块设计的候选人。4、资深导师团队：IC修真院拥有众多经验丰富的行业导师，每位讲师都是行业一线的技术专家与资深工程师。5、实战经验分享：导师带来的不仅是知识，更是他们在实际项目中多年积累的经验与技巧，让学员学到实用技能，提升解决实际问题的能力。6、贴合企业需求：课程内容完全与当前企业实际需求相结合，确保学员所学即企业所需。职业跃迁的黄金机会：快速把控行业脉络，明确职业定位，迅速适应企业岗位要求。已有学员在完成课程后，成功入职知名芯片企业，实现了职业的快速跃迁。讲师介绍讲师Paul背景：美国乔治梅森大学博士学位经验：15年数字设计、架构工作经验履历：曾就职于高通，担任架构工程师。对于AXI高性能设计、高级HDL设计、函数式编程、Serdes高性能设计、PCIe、低功耗设计有深入的理解，精通数字设计全流程设计。讲师Kevin背景：中国科学院大学硕士学位经验：10年数字前端设计工作经验履历：曾就职于中兴微电子研究院，担任专家级前端设计工程师。具有丰富的项目经验,长期从事模块设计、架构设计等工作。参与多款5G基站核心芯片的设计；对于前端设计、验证、时序优化以及功耗分析有深入的理解。讲师Bob背景：电子科技大学硕士学位经验：10年数字前端设计、架构工作经验履历：曾就职于英伟达，担任数字设计高级工程师。参与多款芯片的架构设计,对于数模混合设计、数字前端以及数字后端、综合STA、低功耗设计有深入的了解。讲师溪林背景：北京大学微电子硕士学位 经验：10年以上数字IC验证工作经验履历：曾就职于华为，担任专家级验证工程师。获多项专利。负责IOC领域智能网卡芯片的验证工作，具有多款大型芯片成功流片经验。精通Fiber Channel协议，ARM AMBA总线协议，缓存空间管理实现；精通半导体功率器件设计流程，芯片工艺流程；精通Perl、VBA等脚本语言。讲师陈老师背景：西安交通大学硕士学位经验：10年以上数字IC验证工作经验履历：曾就职于华为海思，担任高级数字验证工程师。参与处理器-核心cache一致性验证工作以及PCIe Vip集成验证，负责AI芯片-卷积神经网络模块验证；精通Cache一致性验证、SoC验证、AI芯片验证、PCIe/AMBA等总线验证。讲师Nancy背景：西安电子科技大学微电子专业硕士经验：10年以上数字IC验证工作经验履历：曾就职于中兴通信、Intel，担任高级芯片验证工程师。负责基带处理芯片和手机芯片等验证工作，具有多款芯片成功流片经验，精通视频解码，音频处理等模块验证、SOC验证、USB和AMBA总线等验证，精通perl、python等脚本语言。讲师魏老师背景：西北大学硕士学位、西安电子科技大学企业导师经验：20年以上的数字IC后端经验履历：曾就职于英飞凌、航空航天研究所，担任专家级数字后端工程师。拥有丰富的7/5nm的流片经验。参与多款芯片的签核工作；对于后端时序收敛、逻辑综合、布局布线、物理验证有深入的研究，具有丰富的后端全流程设计经验。讲师唐老师背景：新西兰奥克兰大学集成电路硕士学位经验：8年数字后端设计经验履历：曾就职于微电子研究所、新思科技，担任高级数字后端设计工程师。负责ICC2的产品验证，负责EDA工具的评估以及流程设计开发。精通脚本语言，开发自动化脚本以提高IC设计流程效率。讲师小 Y背景：西安电子科技大学集成电路设计博士学位经验：10年数字后端设计经验履历：曾就职于三星半导体，担任数字后端高级工程师。参与多个先进工艺的超大型数字芯片顶层STA工作，多个模块级物理全流程实现，涉及floorplan至route，以及时序签核、PV验证、LEC验证、PA验证。丰富的约束编写、时序分析能力。开课详情课程周期：3个月 适合人群：科班硕士（微电子、集成电路、计算机、通信、自动化、电子等相关专业）或有数电模电基础、Verilog基础者详细了解：点击下方链接，了解流片详情。NPU赋能AI，乘上东风之前和已就业两年多的学员聊天，大家给到的反馈也是很多工程师面临的一个困境。入职就只专注于做某种类型的芯片或某个模块，想跳槽发现还是只能找同类型/同业务的岗位，很难拓宽技术视野。这时候全流程的优势就体现出来了：不同环节的讲师做过的项目是不同的，十几年的工作经验和项目经验凝结成“最精华的部分”，融汇在不同的课程阶段教授给大家。这种资源其实是很多已经入行的工程师都接触不到的。另一方面，大家对于岗位的认知是相对有限的。我们被人生的齿轮推着往前走，实习、毕业论文、找工作全都已经提前规划进了日程表里。大家没有机会去尝试不同岗位的内容，有同学在入职之后才发现不喜欢设计更喜欢验证，但是转岗也并非一件容易的事，所以择岗的试错成本非常高。对于还没步入职场的这一部分同学来说，通过一个能给求职加分的流片项目，跑通芯片设计流程，找到适合自己的技术方向，对于长期职业发展规划是性价比很高的一件事。简言之，我们需要拥有一个深度体验、重新思考、慎重考虑的机会，也需要一个在职业生涯中能够有效试错的机会，更加需要一个能写在简历上的22nm流片项目。最后，愿诸君能乘上人工智能的“东风”。", "domain": "zhuanlan.zhihu.com", "visitTime": 1750957004014, "accessCount": 2, "lastUpdated": 1750957004023, "contentStatus": "extracted", "lastExtractionAttempt": 1750957004015}, {"id": "27408a7b-f372-48bf-80d6-bbf1367fdacb", "url": "https://github.com/charmbracelet/bubbletea", "title": "charmbracelet/bubbletea: A powerful little TUI framework 🏗", "content": "Bubble Tea The fun, functional and stateful way to build terminal apps. A Go framework based on The Elm Architecture. Bubble Tea is well-suited for simple and complex terminal applications, either inline, full-window, or a mix of both. Bubble Tea is in use in production and includes a number of features and performance optimizations we’ve added along the way. Among those is a framerate-based renderer, mouse support, focus reporting and more. To get started, see the tutorial below, the examples, the docs, the video tutorials and some common resources. By the way Be sure to check out Bubbles, a library of common UI components for Bubble Tea. Tutorial Bubble Tea is based on the functional design paradigms of The Elm Architecture, which happens to work nicely with Go. It's a delightful way to build applications. This tutorial assumes you have a working knowledge of Go. By the way, the non-annotated source code for this program is available on GitHub. Enough! Let's get to it. For this tutorial, we're making a shopping list. To start we'll define our package and import some libraries. Our only external import will be the Bubble Tea library, which we'll call tea for short. package main // These imports will be used later on the tutorial. If you save the file // now, Go might complain they are unused, but that's fine. // You may also need to run `go mod tidy` to download bubbletea and its // dependencies. import ( \"fmt\" \"os\" tea \"github.com/charmbracelet/bubbletea\" ) Bubble Tea programs are comprised of a model that describes the application state and three simple methods on that model: Init, a function that returns an initial command for the application to run. Update, a function that handles incoming events and updates the model accordingly. View, a function that renders the UI based on the data in the model. The Model So let's start by defining our model which will store our application's state. It can be any type, but a struct usually makes the most sense. type model struct { choices []string // items on the to-do list cursor int // which to-do list item our cursor is pointing at selected map[int]struct{} // which to-do items are selected } Initialization Next, we’ll define our application’s initial state. In this case, we’re defining a function to return our initial model, however, we could just as easily define the initial model as a variable elsewhere, too. func initialModel() model { return model{ // Our to-do list is a grocery list choices: []string{\"Buy carrots\", \"Buy celery\", \"Buy kohlrabi\"}, // A map which indicates which choices are selected. We're using // the map like a mathematical set. The keys refer to the indexes // of the `choices` slice, above. selected: make(map[int]struct{}), } } Next, we define the Init method. Init can return a Cmd that could perform some initial I/O. For now, we don't need to do any I/O, so for the command, we'll just return nil, which translates to \"no command.\" func (m model) Init() tea.Cmd { // Just return `nil`, which means \"no I/O right now, please.\" return nil } The Update Method Next up is the update method. The update function is called when ”things happen.” Its job is to look at what has happened and return an updated model in response. It can also return a Cmd to make more things happen, but for now don't worry about that part. In our case, when a user presses the down arrow, Update’s job is to notice that the down arrow was pressed and move the cursor accordingly (or not). The “something happened” comes in the form of a Msg, which can be any type. Messages are the result of some I/O that took place, such as a keypress, timer tick, or a response from a server. We usually figure out which type of Msg we received with a type switch, but you could also use a type assertion. For now, we'll just deal with tea.KeyMsg messages, which are automatically sent to the update function when keys are pressed. func (m model) Update(msg tea.Msg) (tea.Model, tea.Cmd) { switch msg := msg.(type) { // Is it a key press? case tea.KeyMsg: // Cool, what was the actual key pressed? switch msg.String() { // These keys should exit the program. case \"ctrl+c\", \"q\": return m, tea.Quit // The \"up\" and \"k\" keys move the cursor up case \"up\", \"k\": if m.cursor > 0 { m.cursor-- } // The \"down\" and \"j\" keys move the cursor down case \"down\", \"j\": if m.cursor < len(m.choices)-1 { m.cursor++ } // The \"enter\" key and the spacebar (a literal space) toggle // the selected state for the item that the cursor is pointing at. case \"enter\", \" \": _, ok := m.selected[m.cursor] if ok { delete(m.selected, m.cursor) } else { m.selected[m.cursor] = struct{}{} } } } // Return the updated model to the Bubble Tea runtime for processing. // Note that we're not returning a command. return m, nil } You may have noticed that ctrl+c and q above return a tea.Quit command with the model. That’s a special command which instructs the Bubble Tea runtime to quit, exiting the program. The View Method At last, it’s time to render our UI. Of all the methods, the view is the simplest. We look at the model in its current state and use it to return a string. That string is our UI! Because the view describes the entire UI of your application, you don’t have to worry about redrawing logic and stuff like that. Bubble Tea takes care of it for you. func (m model) View() string { // The header s := \"What should we buy at the market?\\n\\n\" // Iterate over our choices for i, choice := range m.choices { // Is the cursor pointing at this choice? cursor := \" \" // no cursor if m.cursor == i { cursor = \">\" // cursor! } // Is this choice selected? checked := \" \" // not selected if _, ok := m.selected[i]; ok { checked = \"x\" // selected! } // Render the row s += fmt.Sprintf(\"%s [%s] %s\\n\", cursor, checked, choice) } // The footer s += \"\\nPress q to quit.\\n\" // Send the UI for rendering return s } All Together Now The last step is to simply run our program. We pass our initial model to tea.NewProgram and let it rip: func main() { p := tea.NewProgram(initialModel()) if _, err := p.Run(); err != nil { fmt.Printf(\"Alas, there's been an error: %v\", err) os.Exit(1) } } What’s Next? This tutorial covers the basics of building an interactive terminal UI, but in the real world you'll also need to perform I/O. To learn about that have a look at the Command Tutorial. It's pretty simple. There are also several Bubble Tea examples available and, of course, there are Go Docs. Debugging Debugging with Delve Since Bubble Tea apps assume control of stdin and stdout, you’ll need to run delve in headless mode and then connect to it: # Start the debugger $ dlv debug --headless --api-version=2 --listen=127.0.0.1:43000 . API server listening at: 127.0.0.1:43000 # Connect to it from another terminal $ dlv connect 127.0.0.1:43000 If you do not explicitly supply the --listen flag, the port used will vary per run, so passing this in makes the debugger easier to use from a script or your IDE of choice. Additionally, we pass in --api-version=2 because delve defaults to version 1 for backwards compatibility reasons. However, delve recommends using version 2 for all new development and some clients may no longer work with version 1. For more information, see the Delve documentation. Logging Stuff You can’t really log to stdout with Bubble Tea because your TUI is busy occupying that! You can, however, log to a file by including something like the following prior to starting your Bubble Tea program: if len(os.Getenv(\"DEBUG\")) > 0 { f, err := tea.LogToFile(\"debug.log\", \"debug\") if err != nil { fmt.Println(\"fatal:\", err) os.Exit(1) } defer f.Close() } To see what’s being logged in real time, run tail -f debug.log while you run your program in another window. Libraries we use with Bubble Tea Bubbles: Common Bubble Tea components such as text inputs, viewports, spinners and so on Lip Gloss: Style, format and layout tools for terminal applications Harmonica: A spring animation library for smooth, natural motion BubbleZone: Easy mouse event tracking for Bubble Tea components ntcharts: A terminal charting library built for Bubble Tea and Lip Gloss Bubble Tea in the Wild There are over 10,000 applications built with Bubble Tea! Here are a handful of ’em. Staff favourites chezmoi: securely manage your dotfiles across multiple machines circumflex: read Hacker News in the terminal gh-dash: a GitHub CLI extension for PRs and issues Tetrigo: Tetris in the terminal Signls: a generative midi sequencer designed for composition and live performance Superfile: a super file manager In Industry Microsoft Azure – Aztify: bring Microsoft Azure resources under Terraform Daytona – Daytona: open source dev environment manager Cockroach Labs – CockroachDB: a cloud-native, high-availability distributed SQL database Truffle Security Co. – Trufflehog: find leaked credentials NVIDIA – container-canary: a container validator AWS – eks-node-viewer: a tool for visualizing dynamic node usage within an EKS cluster MinIO – mc: the official MinIO client Ubuntu – Authd: an authentication daemon for cloud-based identity providers Charm stuff Glow: a markdown reader, browser, and online markdown stash Huh?: an interactive prompt and form toolkit Mods: AI on the CLI, built for pipelines Wishlist: an SSH directory (and bastion!) There’s so much more where that came from For more applications built with Bubble Tea see Charm & Friends. Is there something cool you made with Bubble Tea you want to share? PRs are welcome! Contributing See contributing. Feedback We’d love to hear your thoughts on this project. Feel free to drop us a note! Twitter The Fediverse Discord Acknowledgments Bubble Tea is based on the paradigms of The Elm Architecture by Evan Czaplicki et alia and the excellent go-tea by TJ Holowaychuk. It’s inspired by the many great Zeichenorientierte Benutzerschnittstellen of days past. License MIT Part of Charm. Charm热爱开源 • Charm loves open source • نحنُ نحب المصادر المفتوحة", "domain": "github.com", "visitTime": 1750960774611, "accessCount": 1, "lastUpdated": 1750960774611, "contentStatus": "extracted", "lastExtractionAttempt": 1750960774566}, {"id": "2da66225-61c2-4bdd-9418-1f3def3a0703", "url": "https://www.reddit.com/r/ClaudeAI/comments/1lhgdbd/dev_jobs_are_about_to_get_a_hard_reset_and/", "title": "Dev jobs are about to get a hard reset and nobody’s ready", "content": "As an architect level engineer the best take I heard was this: “90% of my skills just went to zero dollars and 10% of my skills went up 1000x. […] Having a vision, being able to set milestones towards that vision, keep track of a design to maintain levels or control the levels of complexity as you go forward; those are hugely leveraged skills now compared to ‘I know where to put the ampersands and the stars and the brackets in Rust’”. - <PERSON> I'm an old dev and I remember back in the day we had a tool that allowed you to plan software at a design level (TogetherSoft) and then sync your code with your designs. You obviously still had to fill in the code, but you could easily refactor the skeleton. It felt revolutionary at the time but fell out of favour when UML's popularity waned. Working with <PERSON> recently has felt like a re-invention of that idea. I've stopped having to think too hard about the code and have started thinking at that design level again. I'm excited about how I can spend most of the time focusing on design and simply reviewing code. It has convinced me that I just have to accept the old ways are now over. I'm relieved you still need to know what the heck you're doing though. Yep, it has felt like a movement of “code first, think and ask questions later” took over the narrative during the past decade \"If you aren't breaking things, you aren't shipping fast enough\" More replies More replies This all said - I love Claude Code but it produces a lot of fluff and has poor context awareness in large code bases. Don’t get it twisted - it might be doing all the code but it’s not doing it all that well yet. Working and production ready are no where even remotely comparable, particularly at scale. Dev/architect of 30 years here. This is not a problem if you get used to managing context and keep tasks in manageable size. I have a project I've been building for months with CC and the code is super clean and production ready. Yeah I use <PERSON> with a Gemini MCP and get them to plan The next feature together and write all the details and task list to an .MD file which is then referenced in the Todo.md. works beautifully And how large is your biggest production code base by line count? More replies Remember, Google developed A2A. It works better for me. You can pull out more tooling from the API. 500t/s works wonderfully for handoff tasks what's a2a More replies More replies More replies More replies I think the main point I'm taking away from your guys convo is you still need to know code and what Claude is doing. This is from someone who knows nothing about code. More replies Brother, I run a tight ship and it still absolutely shits itself and breaks features occasionally and you know it. Don’t act like this is an experience thing - it genuinely fucks your codebase up occasionally on a SINGLE instruction if in auto-edit. Id also say that someone with your experience ought to know you’re just borrowing from Peter to pay Paul at that point of the project. How many times have you had to have it go clean up files or tests it randomly creates or fails to move/sort? Even with a precise directory structure and literally telling it not to it will litter shit in your repo. Source also an architect at a f500. Without looking at your codebase, I’d argue that your instructions are either too broad in scope, the patterning of your codebase is too inconsistent, or a combination of both. I don’t mention this to be critical so I hope you don’t take it that way. Source: 3+ year codebase where the code was written by over 25 developers across 6 regions, 3+ native languages throughout the group (not programming languages), and we’re facing the same challenges I just pointed out to you, but have tailored prompts to tackle these challenges at a granular level. More replies More replies I've been a dev for about 25 years and I've been using LLMs at a pretty basic level. Do you recommend any ai courses for Claude or resources for learning how to use them more effectively? If you are working with Claude Code use sub-agents. They significantly help with context management. For a solid overview you can refer to this guide: https://www.anthropic.com/engineering/claude-code-best-practices Tips: Create a feedback loop. Define clear expectations on how the agent should iterate on your code in your CLAUDE.md: Write and run tests. Run linting / type checking. Store learnings at the end of each session into Claude Code's memory to avoid repeating mistakes. Lint only to detect type, logic and syntax issues, not style or formatting: Avoid using linting tools for style or formatting detection / fixes mid-session. This wastes context and can negatively affect diff and search updates. Instead, use linting only to identify type, logic and syntax issues. Use tools like Prettier for fixing style and formatting at the end of a session, not during. If your work spans several repositories: Set up an overarching repo with git worktrees. Create a root-level CLAUDE.md that links to repo-specific CLAUDE.md files (and vice versa) using relative Markdown links. Use types as much as possible and enable type checking: Strong typing improves safety and Claude Code's ability to reason about the code. Iterate with multiple passes. Do not try to do everything at once, but once in a while let Claude Code: Review and suggest improvements Deduplicate or refactor code Split logic into smaller reusable functions or files Perform security scans and code quality reviews You can create Claude commands for each of them. Replace traditional helper scripts with Claude commands. Instead of writing shell scripts define Claude commands in .claude/commands. These: Can perform common operations Can adapt themselves to the situation unlike traditional scripts Use template projects. Set up base projects with proper tooling and best practices. Then: Create a create-app Claude command that acts like a Cookiecutter for consistent project scaffolding. Mention the templates in CLAUDE.md so it knows it can always refer to them if needed. Be cautious with learning resources. Many AI-related YouTube videos, books and courses offer little substance and end in a sales pitch. Instead: Read official documentation Try the https://huggingface.co/learn/agents-course/en/unit0/introduction, which seems promising Keep Claude Code grounded and up-to-date: Leverage MCPs to provide the context with the latest versions of libraries and up to date syntax (context7 etc.) Include versioning and dependency info in CLAUDE.md. Let the AI write your content. For content like news articles: Let it use web search to find the latest news on a certain topic and let it write static content (md files with frontmatter) and come up with suggestions. You can run this from inside a cron once a day for ex. Update: Fixed spelling and sentence structure More replies One of the best sources I've found has been AI Foundations on YouTube. The host looks and sounds like a \"hype beast influencer\" but actually gives very actionable insights and walk throughs. I am looking to deep dive on this topic and share what I learn. Id be happy to keep you updated. Are there any specific mediums you'd prefer? Ie. Substack, LinkedIn, email newsletter, reddit forum? P.s Not an AI course but AI for Humans is my go to source for weekly news on AI. Very informative More replies More replies I think people have made far too much of the 200K context window. I wish it was larger in Claude, but context size management (through file memory storage) is not merely working around LLMs weaknesses, but leveraging the best strengths of LLMs. You can't possibly generate & manage a complex project by one-shotting it in a 5X larger context. That's asking LLMs to manage too much within the LLM itself, which degrades the quality of what they produce. And you will ALWAYS find one slightly bigger problem that overflows the context of the largest LLM. The active, useful context size of the human brain, for solving any single problem in our active, minute-to-minute thoughts, cannot be large. But when we solve a problem, extract key lessons, write it down, and move through an a To Do list guided by an plan, we can design fighter jets. No single human brain in a defense contractor keeps every bit of design context for the entire jet design. The cycle of plan, delegate, plan actions, make an action list, delegate actions, work through actions, summarize/document, review, etc... it is powerful, but only requires many small contexts. More replies More replies More replies Not to be a dick but this is just a skill issue. Once you learn how to properly use CC and other coding agents, it can easily build very clean code More replies I fully expect over the next year that it will be an arms race of tokens for context. Gemini has 1m. OpenAI will come out with 1.2m. Anthropic 1.5 etc. the hardware and energy to support all of this will be insane. Eh, gemini has had 2m and tested 10m. It will likely scale much faster then you think Was gonna say, just a few weeks ago a senior researcher from Gemini said we'd be bringing 10M context and cheap, usable 1M context later this year. More replies More replies More replies More replies Absolutely. I spend DAYS on planning with and without llms. Then I hit a coding button. I do a rapid prototype first so I can spot weaknesses in my high level design. Coding LLMs are good enough now that I can spend a day building something, figure out that 'what I said wasn't actually what I wanted', then I can go away and design the real system. Even a year ago, it would have been completely unfeasable to spend several weeks writing something just to throw it away but now its spending $50 and a day to save me pain downstream. It kind of feels like I'm getting most of the benefits of agile without having to use agile. Prototyping is something covered in engineering classes. And you’re 100% correct! Prototyping, UMLs, sequence diagrams, api documentation, agile crap: all that planning stuff is about to become 10x’s more important. Absolutely. Good software delivery methodology is more important than ever. More replies More replies More replies More replies And 99% of these vibe coders are giving up completely architectural decision making to the model. Half the time, Claude “does 100% of the work” is Claude A) taking stupid code short cuts B) outright lying about what it said it did C) ridiculously over engineering basic requirements D) often all of the above None of these guys will have a clue what to do when their Claude created and architected app breaks the first time more than 5 people try to use it at a time and their total lack of knowledge of what’s under the hood mean they burn through thousands of dollars in tokens spraying and praying that Claude fixes it. In a year, all of these guys will be talking about technical debt like they discovered it themselves Omg yes anything slightly complicated it will fail and lie to you about it even fucking test! And its a yes man llm meaning your always right even though your completely wrong More replies I have exactly the same feeling as a physicist working in a fundamental research lab. AIs have rendered whatever limited skills I had to code in Python absolutely useless; however, knowing how a physical system operates, and knowing how I want my data to be either handled, or generated, has become even more invaluable and allowed me to build from scratch a 20,000 lines software which is going to transform not only my ability to produce science, but also my scientific career as a whole as this software could become a reference in my field. I had those ideas at the back of my head for a while, but I would never have found the time, the dedication, and I would say the ability, to actually make them into something useable. AIs made this possible. Civil engineer here, not a coder. Similar - using CC to develop Python apps to automate so much of the design process, plus my own personal projects. It's pretty amazing really. More replies I've worked on two major projects using Claude (keep in mind, I'm a SysAdmin, not a Dev). The first one, was a personal project for myself (FIRST Robotics Scouting app), and I had a strong vision for what it should do, how it should behave and look. It took about $1,200 in API credits fro October to February, and even though I only had 3.5 and 3.7 (4 had not released yet), it was very successful and I consider it worth it. The second one was a work project that was for another department, and they were never really able to convey exactly what they wanted, and had no clear vision for it. It did not turn out well. Having the skills to plan, guide, correct, and tweak are going to be in big demand, while memorizing syntax is not. More replies I’ve been using it extensively and agree it does incredible work. However, it’s most effective when used by someone who understands what it’s doing and knows how to guide it properly. For example, a backend developer at our company got tired of waiting for UI work and decided to build it themselves using AI. While the result was functionally working, it was an absolute mess - no accessibility considerations, no localization support, multiple responsibilities crammed into single unmaintainable components, and several other fundamental issues. The developer had no idea these problems existed. I’ve also encountered situations where I needed to make corrections that were relatively straightforward to me, but Claude Code kept getting stuck in loops trying to solve them. The key difference is expertise - it amplifies what you already know. Without that foundation, you might be able to create a simple app, but in a large, complex codebase, you’d likely introduce issues you wouldn’t even know how to identify, let alone debug.​​​​​​​​​​​​​​​​ This is the correct take. If you could do it in 6 months given time and focus Claude will get you there in a week. Many vibers would need years of foundational education in computer science, programming, design, deployments etc etc. It’s going to be harder and harder to be disciplined enough to do it when AI seemingly does it for you. Experts will always know the big picture, and right now AI generates code within a scope that “works” but is dangerously naive in large codebases. And then it aliases an import for no reason, renames three variables that other functions use, and blows up every test you had because it randomly changes shit for no reason other than vibes. Then it creates two new store methods that are almost exactly the same as one you already have, reworks an API call in a way that breaks it, writes a new backend function that copies one you already had with a trivial difference, duplicates a utility file to bypass writing to your database correctly, and saves files directly to your store instead of using your file manager tool, which means the file is lost forever... All because it makes a shitload of incorrect assumptions and refuses to read a single fucking file before writing a dozen breaking changes to your app. edit: \"Why fix two lines of linter errors to match your type when I can rewrite the entire file six fucking times just to avoid having to spend a millisecond looking at the type?\" More replies Dangerously naive is a good way to describe it. We have a phrase we use internally: “they have just enough knowledge to be dangerous”. For example, I’m mostly a backend dev, but I do have some experience with React so I could technically support on a React project if I had to. But, I’d describe myself as knowing just enough React to be dangerous, and I’m probably not the best person for the job. I sometimes see AI code gen in a similar way, depending on the complexity of the task. It can be dangerous on large complex codebases. More replies This. I dunno man maybe I’m gonna eat my words but fuck junior devs are fucked. If you can steer a sota model with your own domain and codebase knowledge you can become a productivity monster with these tools. Right now, in this moment, if you can read this, this is the exact moment you have to become indispensable. With MCPs, proper security and access, you can do A LOT of shit from business inteligente to analytics with LLMs. While your production grade stuff needs to be seriously and carefully checked when using this tools, you can start vibe coding analytic dashboards, create a tool for the product team to query GA and analytics with NLP, etc. This is unfortunately true, at least in the short term - junior developers are in a precarious position. Leadership sees them as easily replaceable and is betting that senior developers will eventually follow suit. I think they’re wrong about that. LLMs are fundamentally pattern generators, not true problem solvers. They excel at eliminating tedious, repetitive work, which allows experienced developers to focus on genuinely novel and complex problems. But solving those harder challenges - the ones that require real creativity and deep understanding - will likely require an entirely different technology than current LLMs. Whether or when we’ll develop that kind of technology remains an open question. So if junior devs are out of the loop going forward.... how are new senior devs created then without the experience and trials of being a junior? More replies More replies More replies Yeah, exactly this. I'm a designer (very experienced) and have been going really deep into all the design/UI generation tools. They can all produce something that looks good on the surface. But no matter how much I try to use AI to do my own job quicker or better, it can't actually produce shippable work that's higher than \"janky template\" quality. Also don't forget that the vast majority of design work is about adding features to existing products. AI can kinda handle creating a low-quality greenfield design but it can't take into account a entire product, can't align perfectly with an existing brand, can't understand all the stakeholders and requirements and messy internal histories. Let alone users. More replies Time is the x variable here. All the things you mentioned can be added to a checklist for a given stack for some nextgen AI to checkoff each time a noob asks it to do something. That feedback loop will be fixed in x time also with another smart fallback or handoff. In x Time what was a large complex codebase to us will probably be low level to it. This is AI coding at its lowest level. Really we are just getting started. More replies You can just ask the LLM to address those things. Your experience is just someone who doesn’t know how to use the tools More replies \"Full production-grade desktop app\" is so oddly unspecific. Which I find is true of a lot of these hype posts. Claude Code tends to add “production-ready” and “enterprise-grade” marketing language into the task summary, commit messages, and documentation, especially when using it with Zen MCP. Well shit... I am going to start doing that too To be fair, I also use the same language when asked for planning 😂 More replies More replies Yeah, pics or it didn’t happen applies here. Show us the full production-grade desktop app and let us judge the claim for ourselves. For real. You'd think a single one of these people would post some proof on GitHub or a link to their application, but it's all just hype. I was watching for a comment like this. I would like to see a GitHub repository to back up claims like \"production ready\", \"fully reviewed\", and full application in a week with full history. Fully reviewed by who? A lot of code cranked out very quickly is still a lot for any human to understand well. Production ready is a vague statement. What Claude Code can do is very impressive, I'm still learning how to get it to produce better code. I can get it to produce code that works, but I personally would not call it \"production ready\". Are we just lowering our standards for what production ready code means so we can accept the pace that llms can produce it? When shit goes wrong is where the pain will come from. More replies More replies They never will. More replies I think it wouldn't be outrageous to say that many if not most of these post are AI generated. Take it from a professional - llm assisted coding is not ready to replace anything I’m a principal engineer and tech lead in a financial software company, and I can say the things it has done with me to refactor old code are insane. We have a handful of microservice ingress components that have gotten big, unwieldy, and problematic to touch, we have been able to write tests, convert to typescript, and start breaking them out systematically into hooks. I’ve gotten to the point where our only underperformers are the remaining AI deniers who think it’s garbage and doesn’t work, and we might have to let them go if they can’t learn new tooling. It’s the job now, it’s like trying to work without the internet, eventually you need to learn how to use it effectively or just get left behind More replies More replies More replies Just out of curiosity: you're not a developer, are you? He’s not a designer, either! 😂 He’s a plumber. It’s ova for us devs- he can make a production grade desktop app and fix leaky pipes, too. More replies More replies Yeah anybody that has worked with LLMs on large codebases, existing projets knows they're definitely not magic lmao it's like working with a jr dev that can't even test anything More replies 100% lmao More replies sounded like someone who never work with an enterprise codebase. blank canvas is easy, it's empty, you can draw anything on it. More replies Yeah, the moment i threw a complex architecture and ux issue at it, it melted like butter at a sunday breakfast. Design work is safe ;) Whenever one of these \"software engineering is so over\" posts begins with \"I've been using Claude Code\" I know it's safe to ignore lol More replies OP as other brainrots clone same texts that developers are going to lose jobs, funny, so go on then, create and monetize some apps, it's easy The idea of a “Python dev” or “React dev” is outdated. Going forward, I won’t be hiring for languages, I’ll hire devs who can solve problems, no matter the stack. The language barrier is completely gone. In general, it was already like that in big companies But also it doesn't work well for C / CPP. In general you'll struggle if you don't understand what you're doing More replies I think this is a guy with a hard on about replacing employees, the first thing he wanted to make was crappy employee tracking software. Toxic as fuck. We don't do that in Europe. Delusional startup bauss Ceo founder cringe Lord. More replies Glad you finally caught up. Now, who exactly will be doing the code reviews? The CEOs? NO. A developer who knows their shit. No sane company is going to blindly let an AI commit code to their main branch. The implications for disaster are tremendous. The company has a responsibility to its shareholders to manage responsibly and not risk their clients' data with AI. Think things through, your entire post is only 50% of the equation. More replies I’m currently building a product and I’m using Claude Code a lot. It really is very helpful, but I disagree. It still can’t do basics that a mid to senior dev can easily do. For example a QuickBooks Online integration. I had to jump in and start telling it what was wrong and why the requests to QuickBooks APIs were failing …etc I’m sure it’ll get better a few years from now, but definitely not there yet. As for UIs, I’ve only tested a few things and they’ve been all been really bad, so can’t comment much on that. Really though, how much time did it still save you vs having to write all the related API functionality by hand? I bet it still accelerated your speed of development around 10x For sure, that’s why I’m a big fan 👍 More replies Give it the docs. You have to provide information. More replies More replies Nice ad Claude More replies But can you solve this stack. IEC61131 st on Siemens , Allen Bradly controls. Embedded with a opc server and frontend react with csharp computer backend All production, time critical and safety circuits More replies Just stop with the astroturfing already. Seen similar advertisement post since yesterday for the same $200/mo plan. You won't be making a good case with these, only cast doubt about the real progress behind the scene. Its worth every penny, but your assessment and this stupid astroturfing campaign worries me more than the \"mythical future with zero job\". 🤦‍♂️ God bless any company that will hire you though. It doesn't sounds like you can produce anything meaningful even with a $1000/mo plan. How much does anthropic pay per post? More replies Not even close Doing 100% coding? Bullshit. Some simple stuff maybe. But working with existing big codebase it constantly needs corrections and make mistakes. More replies Sure, app devs are about to get destroyed. But then there are the 99% who actually work in a team on enterprise grade software, needing to comply with all kinds of business requirements and infrastructure topics. I use cursor + claude code and it's very helpful, but I have to micromanage everything otherwise it will completely wreak havoc More replies lol okay Mostly wrong tbh. You are 100% correct and timely. Over the weekend mostly in one 6hr stretch I was a highly technical product manager and Claude did all the coding, documentation and testing. I had to keep nudging it to do the full documentation of what it had done so it could pick up the work later. Here is what “we” created https://github.com/nborwankar/aishell I’d hate to call it “vibe coding” it was more likely “intentional coding”. More replies This is either an ad of sorts or you guys are writing very simple apps. Claude keeps hallucinating, overreaching, adding stuff you never wanted, breaking scope, running your servers, getting stuck in error solving loops, DESPITE heavy handed project rule files being present. The code it writes is completely unstructured, doesnt even attempt to use inheritance or interfaces even when they would be absolutely good in a given situation. Anything that slightly resembles a product is still beyond the reach. Unless perhaps its a simple to do app or sth like that. Exactly. The day you start worrying is when Google starts rolling out features and new products at a very fast pace, thanks to the supposed x10 productivity boost. But somehow they are not doing that, despite having one of the best AI and the best hardware. Strange... More replies AI coding agents are the new compiler optimizations. They amplify whatever engineering discipline you already have. Teams that invest in architecture, review automation, and clear specifications will ship faster than ever. Teams that skip those steps will create unmaintainable spaghetti at lightspeed. Choose which side of that divide you want to be on. More replies Working code for a hobby project isn't the same as maintainable, secure, performant, bug-free enterprise code. This is like the offshoring fad all over again. Yes, they/AI might be able to cobble some bullshit together for cheap but good luck trying to maintain it. I use Claude Code daily and yes, it gives massive productivity gains, but it is so ridiculously far off being able to replace even the most junior of developers. 99% of the time the \"one shot\" attempt it gives you is shit and you need to refactor it (or tell Claude what's wrong and how to refactor). Give it another 10 years or so and it might be at a junior dev level. More replies had this almost-dead monolith that was practically frozen due to 5 years of poorly interwoven dependencies across features - any update risked breaking everything. While it took significant mental effort to visualize the migration process and create proper instructions and guardrails, Claude Code has been incredibly effective at reading through complex legacy code to isolate and upgrade features systematically. Truth is, such task would have been extremely complex and risky This is something I wouldn’t have dared attempt without AI assistance. We’re a little more than halfway through now and it’s mostly on autopilot, though large features still need close supervision since the AI can lose focus and get “exhausted” on complex contexts. I suspect bringing legacy apps up to LLM-ready codebases might be a new job - at least until this process itself gets automated? More replies I'm curious where this is going to go as there's one part of this that it seems no one is taking into consideration. People who can successfully and accurately use these tools are those who have deep knowledge of what their doing, in their hands this is just speeding it up. This approach is currently eliminating junior positions and thus discouraging people to even learn those skills, yet those skills are exactly what enables seniors to use it so effectively. Do you understand what Dev jobs are about. We are always fixing your bugs that you created :D I am right there with you. Had a software for a family real estate holding I was putting off to get started for months because it would take me so much time to get it done, then I decided to do it with Claude Code to learn how it works and I got a decent working MVP done in a single day, mobile in flutter. Haven't deployed yet because now I want the users (the other owners and me) to use it and point feedbacks they suggest. But that is it, I calculate it would take me at least 3 months if I would monkey-code it from scratch, I agree with the part of system design, devops, architecture and cloud, that's whats going to separate men from boys from now on. Yet I still think it's very valid for anyone to learn how to code from the scratch, do small portfolio projects by hand to know the inner sides so that they can manage CC better. More replies I am not so sure about this, but will refrain from claiming you're wrong, and focus on sharing my experience. I too am using claude code, but the 5x package. I am making heavy use of claude.md, memories, and doing lots of planning up front. I found at first it was super impressive and it had a really good grounding in what I needed and how to approach it. You then start the application and it works, seems good. Eventually though I hit bugs and dive into the code. it's a mess. I asked it for JSON-RPC, well it tells me, I went with a REST API. I then reply, \"I told you, the specification states Server Side Events / JSON RPC is mandatory. I then apologises and jumps into fixing. Instead of actually migrating to JSON RPC and streaming, it just starts mocking this by hardcoding the application string and json payload to look like RPC. From there I went deeper into the code, lots of hard coded crap everywhere. Tons and mean tons of duplication, just massive volumes of code, modules of 1000 lines plus. There was so much to fix, I just found the cognitive load too high and it would be better to just cleanly write it myself. As aside to the shitfest state of the code. I also really disliked how out of the loop I felt, but that's a personal thing. My take is, software engineers (good ones) are going to have plenty of work over the coming years, fixing all this AI slosh. Gone are the days of doing a bootcamp for 12 weeks and getting a six figure salary, but engineers who know how to deal with large codebases and scale applications are going to make a mint. More replies I’ve been doing a similar thing for the past year. Launched three mobile apps and two SaaS, me alone with Copilot and Cursor. A dozen or more homebrew projects (that I always wanted to try). My 10x nature got a 100x boost and it still feels like a dream! My observation is that the semantics of the code became much more important now. The structure and wording of the code now has much more impact on agents’ ability to maintain and extend the code when it outgrows the comfortable context window (which is still less than the promised 128k, let alone 1M). Coincidentally, I also speak four foreign languages fluently and over time, the importance of natural language skill became clear: coherence and idiomatic code are now absolute kings when it comes to AI coding, and without it, you’ll just hit a wall at some point. For years, I had to work with some engineers who weren’t really good at coding but were actually good problem solvers. What can be seen now, is that lack of coherence and idioms in their coding prevents them from using AI coding tools to the max. I agree. I’ve already built and tested apps in most languages, and now I’m seriously studying what I don’t know, because I hate not understanding 100% of the generated code. But yeah I’ve turned language agnostic. More replies Only someone that has not a clue would write such a nonsense.. Good luck with your \"vibing\" lol. More replies Delusion. lol oh lol, let me guess you’re mid to junior or have no real dev experience at all. I use Claude daily, no it’s not as good as you make it out to be. Yes it can solve some bottlenecks but at the end of the day two things are of issue here. 1) if you don’t write it you don’t truly understand it and Claude makes you damn lazy sometimes 2) you want mediocre code no problem, context size is limited, architectural decisions will always suffer. Code bases are growing exponentially. I have a solo project in a mono repo with 11 packages over a variety of frameworks. If you don’t have domain expertise you will be led down the wrong path. Good luck with code maintenance. And don’t even get me started on hallucinations, not being up to date with latest framework specs, and using shitty deprecated code. More replies This is yet another AI hype post. \"Going forward, I won’t be hiring for languages, I’ll hire devs who can solve problems, no matter the stack. The language barrier is completely gone.\" That would be the case if AI was 100% accurate writing code and came up with reliably good abstractions. Neither of these things are true. Try debugging a memory issue in C++ if you don't even know the language. Hype, hype, hype... LLMs turn the problem of engineering into reviewing often bad code. Reviewing code is painful. Most of the people writing these posts have never got to a really high level in programming. More replies Software engineers are not going away, its just changing. More replies The sad truth is that AI is currently unregulated/ untaxed. The problem is that while on paper the AI improvements are really impressive - the reality is that if it will start affecting the economical situation in significant way - it is going to be regulated or taxed so all your investments will go to waste. The reality is that me or you have no power to change it. If I were you - I would still invest into AI but I would be aware that status quo can change any time and you need to have backups (if all your designers / developers etc are gone - your business will be forked). More replies I have a max CC sub and that shit has changed my life. No longer are different tech stacks barriers for me. I’m a ruby dev that works server side in mostly ruby repos I just applied for my first iOS App Store Release. I have like 4 hours of iOS training lol I’m trying to push out indie projects now to secure a residual income in case my job gets cut lol More replies Im skeptical because it looks like it works as you go through it but unless you spend time prepping, just like anything in life, I guess, then you arent actually getting legitimate production ready code. If you try to pound through it then it will make significant amount of mistakes and you will accumulate tech debt and it hallucinates and lies. You don’t know these until you’ve launched a few projects and actually learn how to code. It sounds like you dont have these issues, considering your statements of success with it but I’ve also seen way too many people believe everything it says. They are designed to flatter you, don’t believe the LLM’s. But until you launch it and test it at full scale you wont really know. I’m mostly speaking for everyone else having issues, learn from my mistakes. Project summary + tech stack + architecture structure + logic flow chart + very detailed plan + check list and implement one task at a time, have it double check as you go or use another model to check. If you dont have any of these documents then you are not ready to start coding. This is what I do anyway, don’t have issues anymore and don’t use tools. I’ve built 3 products now that I use in production and make money with currently, FWIW. More replies I agree with you on most of this except 4. Figma make is not doing brand identity, it’s barfing our generic logos. If you were going to pay someone on fiver to do it, you don’t have to, but there is a lot more to real, professional design than name of company in font with icon. Brand is about standing out, and AI can only give you the median result. UI design I think is about to be upended. But like hiring devs who understand devops and architecture, designers who understand how people interact with brands and products are about to become a lot more productive. More replies Hey everyone I think what he’s trying to say is that Claude code in of itself has reached a point that if paired with a true enterprise workflow you can accomplish the same result of a dev team without just 2 or 3 people. More replies In 20 years, someone who can still develop their own business application by hand will either be considered a genius or stupid 😂", "domain": "www.reddit.com", "visitTime": 1750960782617, "accessCount": 2, "lastUpdated": 1750960782844, "contentStatus": "empty", "lastExtractionAttempt": 1750960782619}, {"id": "34cea5fd-c932-4984-bb99-3c40c9c55bd1", "url": "https://www.reddit.com/r/ClaudeAI/comments/1lhmts3/i_present_superclaude/", "title": "I Present : <PERSON><PERSON><PERSON><PERSON> !", "content": "Meet SuperClaude – the missing power-up for Claude Code This is primarily a set of rules for Claude Code that applies software engineering principles.No CODE, No external tools or setup requirement. A lightweight, drop-in framework that plugs the gaps--from checkpoint/history control to automated docs--while squeezing more context into every token. And... it's FREE. https://github.com/NomenAK/SuperClaude Why you’ll love it Git-based Checkpoints & Session History Navigate back to any point in a conversation or debugging session using a Git-integrated checkpoint system — no more losing context or digging through logs. Token-Optimized Documentation Documentation is automatically generated using a token-reduction strategy. Code examples, API references, and usage notes follow a structured, repeatable template — ensuring docs evolve alongside your work. Project documentation is saved in the /docs directory, while Claude-specific usage notes and context are organized under /.claudedocs. Lean Context, Larger Scope Our 70% token-reduction pipeline keeps prompts compact and efficient, allowing <PERSON> to manage larger, more complex projects without slowing down. Context reduction strategy already applied to SuperClaude to make it lightweight. Built-in intelligence Evidence-Based Decisions → No more \"this is better\" without proof Smart model routing – picks the right Claude variant for the job 🧠 Intelligent Tool Integration Auto Documentation Lookup → Context7 finds library docs instantly Complex Analysis → Sequential thinking for deep problems UI Generation → Magic creates React components Browser Testing → Puppeteer validates your work 💡 Why SuperClaude? Before: Generic AI assistanceAfter: Specialized, context-aware development partner ✅ Consistent workflows across all projects ✅ Research-first approach → Always finds official docs ✅ Quality standards → Built-in best practices ✅ Cognitive specialization → Right mindset for each task Zero-friction installgit clone https://github.com/NomenAK/SuperClaude.git cd SuperClaude ./install.sh Auto-Backup integrated !Should be compatible with any of your MCPs.That’s it—no databases, no extra services. Installs to ~/.claude/ and works in every Claude Code project. 18 ready-made commands Typed shortcuts for the chores you run every day. With extensive flag system: /user:build --react # spin up a React app with best practices /user:analyze --security # full security sweep /user:troubleshoot --prod # production fire-fighting /user:design --api --ddd # DDD-driven API blueprint /persona:architect # enter system-design mode9 on-demand personas Switch Claude’s mindset in a heartbeat—manually or automatically: Persona Focus architect big-picture system design frontend React & UX polish backend API reliability & scale security threat modeling & secure code analyzer deep-dive debugging qa test strategy & coverage performance speed tuning refactorer code clarity & cleanup mentor guided learning & coaching Open source & community-driven MIT-licensed, with contribution guides, issue templates, and a welcoming discussion board. Got an idea for a new persona or command? Jump in! SuperClaude turns generic AI help into a specialized, context-aware teammate—without the overhead. What personas or commands would level-up your workflow? Let us know!", "domain": "www.reddit.com", "visitTime": 1750960783544, "accessCount": 2, "lastUpdated": 1750960783717, "contentStatus": "extracted", "lastExtractionAttempt": 1750960783542}, {"id": "3c036e8d-0ecc-4a7a-ab2f-07dcf49212eb", "url": "https://www.zhihu.com/question/1921330600498959141/answer/1921634533415053027", "title": "能否对比一下Claude Code和Gemini CLI，你的选择建议是？", "content": "说实话，Cursor/Augment Code/ Claude Code用久了最头疼的就是费用问题，每个月美元账单看得心疼。Google这次推出的Gemini CLI直接解决了这个痛点。你只需要用个人Google账户登录，就能获得免费的Gemini Code Assist许可证，每分钟60次调用，每天1000次调用，完全免费使用Gemini 2.5 Pro的完整版本这个额度对个人开发者来说真的很够用了。鄙人不才，昨天下午就开始调试，走了很多弯路。至少有人可以避免我的错误，也值得了。Gemini CLI 是 Google 推出的命令行AI编程助手，作为 Claude Code 的免费替代方案：也有人说gemini cli是在抄袭/致敬claude code,也有人说谷歌gemini这个更像个半成品 匆匆上线抢市场，不如Claude Code内部用了好久才分享出来但是免费真香。您好，我是阿里巴巴AI专家，专注于LLM大模型&Agent 能力开发，10年互联网经验，今天开始免费给 大家分享【vibe coding解决100个问题】AI编程完全手册2025版，欢迎参与从0到1的提升之路。AI代理设置使用美国的模型和软件，一定要VPN。我自己使用的是monocloud。能使用美国和欧洲的IP就可以（香港IP代理貌似不可以），尽量开global模式。我们使用cursor访问模型没问题，但是一定要设置终端Terminal变量。Windows距离： cmd设置代理的环境变量set HTTP_PROXY=http://127.0.0.1:7078 set HTTPS_PROXY=http://127.0.0.1:7078端口号就是你的代理端口，一定要设置以下。lineus和macos,windows cmd和powershell方式都不一样，一定要注意格式问题。# 在 Linux 或 macOS 中:export HTTP_PROXY=\"xxxxxxx\" # 在 Windows (CMD) 中:set HTTP_PROXY=\"xxxxxxx\" # 在 Windows (PowerShell) 中:$env:HTTP_PROXY=\"xxxxxxx\"curl --v http://google.com 这一步一定要测试一下。在chrome能访问google不行，一定要在终端terminal中可访问，这是关键问题。Gemini CLI Workspace 报错解决指南访问 Google Cloud 控制台【1】一定要设置：set GOOGLE_CLOUD_PROJECT=xx , id就是google cloud控制台的项目ID。以上设置的环境变量，windows cmd窗口关闭失效了，需要写个脚本每次记载一下。启动方案注意，发起启动命令，先切换到使用的代码仓，或者新建一个空的文件。gemini给了两个启动方式npx https://github.com/google-gemini/gemini-cli或者npm install -g @google/gemini-cli建议一定要使用前一个，npm方式安装的包可能有bug没有修复。第一次启动鉴权的时候，需要登录google账号。新用户需要注册一个google的账号，使用账号的时候VPN需要美国或者欧洲IP。 **至关重要：**``您在这里登录的 Google 账户，``**必须**``与您在第一步中访问 Google Cloud 控制台并获取项目 ID 时所用的账户``**完全一致**``。经历将近6个小时的踩坑，总算走到了最后,不需要Gemini模型的API key，直接开始跑项目。让Claude给我分析一下，当前项目的架构。从gemini画图看，不美观，而且没有对齐，gemini模型能力相比claude差一些。使用体验：cli方式仿佛又回到了vim时代，习惯了Cursor的鼠标点击来回切换，termianl方式有复古了，要不是免费，真的不想用。项目开发实验gemini给了一些开发例子， 比如描述项目的架构，探索项目的安全机制，执行一个issue代码修复，移植codebase版本，自动化工作流，比如批量png文件转换成jpg等。在Cursor、cline中使用的案例，都可以拿来试一下。核心特性对比性能表现响应速度：相比 Claude Code，Gemini CLI 在复杂任务处理上存在显著差距Claude Code：6分钟完成任务Gemini CLI：30分钟完成同样任务代码质量：文章生成能力优秀，但UI设计能力逊色于 Claude 4工具生态：工具数量明显少于成熟的AI编程工具使用限制免费额度：每分钟最多60次调用超出后切换至 Gemini 2.5 Flash每日最多1000次调用付费选项：支持API付费使用，适合多窗口并行工作技术架构我让gemini帮我画了代码仓的核心架构，可能prompt没来得及调整，gemini有些随心所欲了命令行参数参数功能说明-m指定模型选择特定的AI模型-p传递提示非交互模式直接执行-s沙箱模式启用安全执行环境-d调试模式提供详细输出信息-a全文件上下文包含当前目录所有文件--yolo自动批准自动批准所有工具调用--verbose标志获取详细输出类似log的详细调试功能使用最佳实践Google 员工推荐的工作流计划优先：复杂任务先让AI制定详细计划分层配置：创建多个层次的 .gemini.md 文件避免 YOLO 模式：倾向于拦截并重新提示代码测试提交：确保代码质量上下文管理使用 @ 符号引用特定文件或目录通过 /memory refresh 强制重新加载上下文利用 /memory show 显示当前上下文三类内置命令**/**** 命令**：对话管理和界面控制**@**** 命令**：文件和目录引用**!**** 命令**：系统交互和命令执行使用总结：谷歌gemini这个更像个半成品，匆匆上线抢市场，不如Claude Code内部用了好久才分享出来。但是他就是免费的！免费真香！我们是谁，在做什么您好，我是阿里巴巴AI专家，专注于LLM大模型&Agent 能力开发，10年互联网经验，今天开始免费给大家分享【vibe coding解决100个问题】AI编程完全手册2025版，欢迎参与从0到1的提升之路。如果你也想在AI时代开发一个小项目，也许你完全不懂编程？也许你学过编程但是不懂如何从0到1搭建一个MVP项目？欢迎订阅我公众号，这是一个以Augment/Cursor实战为主，以实战案例的形式开发几十个项目，包括：网站、微信小程序、浏览器插件、App应用、Dify+Coze智能体。以上就是今天的全部内容，如果你觉得不错，欢迎点赞、在看、转发三连，帮助更多的朋友，这对我也很重要。参考文献：【1】http://console.cloud.google.com【2】https://github.com/google-gemini/gemini-cli", "domain": "www.zhihu.com", "visitTime": 1750957023328, "accessCount": 6, "lastUpdated": 1750957034681, "contentStatus": "extracted", "lastExtractionAttempt": 1750957034671}, {"id": "842cce8e-5fb0-44b0-9f4b-755555cfe803", "url": "https://linux.do/", "title": "LINUX DO - 新的理想型社区", "content": "跳到主要内容 话题列表，带有按钮的列标题可以排序。 话题 发帖人 关于优化交易、推广需求的变更公告 运营反馈 公告 有的佬友已经注意到了，论坛的 跳蚤市场 跳蚤市场版块不见了。那么它究竟去哪了呢？ 直接说重点：加入交易分组可见。 社区将推广类信息从 深海幽域 分拆至专门的版块，是对交易、推广需求的一次优化。 在加入交易分组后，跳蚤市场的帖子会出现在信… 阅读更多 268 8.1k 28 分钟 请不要把互联网上的戾气带来这里！ 运营反馈 公告 是的，L站目前每天都有不少各色各样的佬友加入。对于一个在线社区来说，不断壮大和涌入新的血液是一件好事。 但我每天都要问问自己，这里面有没有问题？真的完全是好事吗？在这个过程中我嗅到了一丝危险的气息：有人试图同质化这里，把这里当作互联网上另… 阅读更多 1.9k 124k 8 小时 欢迎来到 LINUX DO！ 运营反馈 我们非常高兴看到您加入我们。 LINUX DO Where possible begins. 以下是一些可以帮助您入门的事情： 在个人资料中添加您的照片和关于您自己和您的兴趣的信息，介绍您自己。您希… 阅读更多 6.5k 288k 2 小时 佬友们，可否推荐一下自学Python的教程 搞七捻三 快问快答 9 167 1 分钟 小米yu7~目前销量天花板 前沿快讯 9 308 2 分钟 应该是最稳的team了 搞七捻三 纯水 42 1.8k 2 分钟 推荐一个安卓下载器 搞七捻三 纯水,Android 1 13 3 分钟 4张淘宝夜宵免单卡 福利羊毛 0 11 3 分钟 有佬友用过Authy 这款2FA的吗 开发调优 快问快答,纯水 6 88 3 分钟 数标标API- 专注Google Gemini API - 全网Gemini系列最全、最性价比供应源头 - 留言抽1K美金！ 福利羊毛 人工智能,Gemini,抽奖,高级推广 1.9k 12.4k 3 分钟 Gemini CLI 配置完整教程Windows 10/11 开发调优 人工智能,软件开发,Cursor,Gemini 37 1.3k 5 分钟 马上升级3级抽奖福利第三弹：一个季度多国家解锁梯子订阅（月流量300g） 福利羊毛 抽奖 191 550 5 分钟 媲美Claude code的AI终端Warp，现在可领一个月Pro会员 开发调优 人工智能 52 1.0k 6 分钟 水一水，站内收藏的cursor rules和提示词 资源荟萃 人工智能,ChatGPT,软件开发,Cursor 39 1.6k 6 分钟 【公益API】AI API公益站 deepseek/gork3/claude/gemini/gpt 福利羊毛 273 4.0k 7 分钟 我用AI搓了个Outlook邮箱管理程序 开发调优 人工智能 79 2.9k 7 分钟 【影视资源】用Tg的频道搜索做了个影视资源的聚合搜索 资源荟萃 12 102 8 分钟 没用过谷歌云，谷歌云权限被禁了 搞七捻三 纯水 2 42 9 分钟 谈了将近五年的初恋女友把我甩了 搞七捻三 纯水,树洞 55 826 9 分钟 摸鱼游戏，支持pc，安卓。有内购系统使劲玩就行了 资源荟萃 纯水,AFF,软件开发 94 1.1k 9 分钟 Deep Research 新用法，竟可以用来辅助高考志愿填报 开发调优 人工智能,OpenAI,Gemini 29 583 11 分钟 giffgaff 使用方法 文档共建 giffgaff 177 3.3k 12 分钟 TV软件全合集(安装即用，无付费，更至最新版)（听歌、直播、市场、娱乐） 资源荟萃 AFF,夸克网盘,软件开发,作品集 6 113 12 分钟 Epic 喜加一：《沙贝》游戏免费领取 Epic 喜加一：《沙贝》游戏免费领取 福利羊毛 游戏,薅羊毛,白嫖 9 97 13 分钟 谷歌文档和Office有什么区别 开发调优 快问快答,职场 9 79 16 分钟 【讲个八卦】我老婆的一个朋友跟她男朋友分手了 搞七捻三 纯水,吃瓜 184 2.0k 16 分钟 ’苏超‘抢票求问！ 搞七捻三 快问快答 1 28 17 分钟 LINUX DO 社区抽奖规则 运营反馈 抽奖,公告 564 128k 17 分钟 Linux do站长你TMD要点逼脸吧！！！ 运营反馈 快问快答,纯水 668 20.6k 19 分钟 claw cloud一直在waiting 搞七捻三 纯水 9 141 20 分钟", "domain": "linux.do", "visitTime": 1750957060513, "accessCount": 2, "lastUpdated": 1750957060529, "contentStatus": "extracted", "lastExtractionAttempt": 1750957060512}, {"id": "c34e01c0-caad-4137-8eaf-dcb778453797", "url": "https://www.zhihu.com/question/658661680", "title": "修仙文明可能以怎样的方式碾压星际文明？", "content": "`“我草，这颗星球真美啊！”“是啊，而且大的让人心生畏惧。”太空中，一艘泰坦级太空母舰漂浮璀璨星辰里。船舷右侧挤满了人。透过玻璃从太空中俯瞰面前这颗巨大无比的星球。赞叹无比。舰长室内，卡司帝国最年轻的少将，正通过面前的屏幕，迷恋的看着这颗刚刚发现的星球。正当舰长沉迷于这美景时。从外间走进一名上校。面色有些难看，不敢去打扰舰长，只是对着舰长面前，同自己一样穿着上校军服的人打着眼色。那人静悄悄的后退几步，二人小声的退到角落。“皮特，扫描不出来。”叫皮特的就是那原本在屋内的上校。“什么？什么扫描不出来？”皮特有些没搞清状况。“这颗星球好像有什么我们不理解的科技，让我们的扫描激光穿透不了大气层。”皮特听懂了，但有点懵。“舰载扫描仪坏了？”“没有，全新的，科亚光科集团的最新品。”科亚光科是一家遍布帝国十七颗行星的大集团，专门做光学雷达设备的。这艘太空母舰的舰长，就是这个家族的嫡系。皮特扭头看了眼还在细致观察的少将舰长。又回头对着这人说道。“我帮你拖一天，派一个登陆舰下去，带着扫描仪，一定要给我摸清楚，这颗星球有什么！”“谢了，兄弟！”上校感激的轻拍皮特的臂膀，又悄悄的退了出去。将舰长室的大门轻轻关上，上校示意跟着自己来的几个军官和自己离开。等离开了舰长室范围，身后一少校军衔的军官就迫不及待问道：“长官，如何说的？”“让伊森带特战队下去，我们只有一天时间。”其实本来也没啥大事，扫描不出来直接说就是，可这舰载扫描仪新装的，而且装的还是舰长自家的产品。你当众说出来，岂不是打了舰长的脸。在帝国混，人情世故搞不清楚，走不了多远。泰坦舰下方，气动门缓缓打开，一艘蓝色的小型战舰带出绚烂的尾翼，冲向这颗星球。说是小型，那是和泰坦对比来说的。如果单拎出来，也是个庞大无比的家伙。战舰长千米，宽二百米，高九十米，是帝国天使级战舰的改进型号。舰船大厅内，各式各样的机甲装备，从固定间内推出。坐着最后的调试。“机甲小队报告，四十台重型机甲DO-44 \"地狱犬\"，四十台机动机甲NSF-07 \"幽灵\"，四十台特战机甲T-Doll \"女武神\"已整装完毕，随时可以出击！”伊森走在前面，视察着各个小队的情况，身后几人依次汇报着情况。“战机小队报告，闪电隼\"（F-99 Lightning Hawk）十二架，剃刀\"（RAZOR-X）十五架已整装完毕，随时可以出击！”随着一个个小队汇报完成，伊森少校点了头。“进入隐身模式，我们这次只有一天时间，巡航速度定在三十马赫，我们首要任务是扫描这颗星球！”“少校，我们进大气层了，目前没有任何武器向我们攻击！”“没有最好，这么颗美丽的星球，希望别被这上面的土著糟蹋了！”西域，西南州一座万米的高山上。正在闭关的陈飞白睁开了眼，警觉的盯着上方的屋顶。眼神锐利，看破千里。手指一挥，一枚传讯符箓飞出这僻静之地。乾元道宫掌教宋时元，接到了这枚传讯。听了之后，眉头微微皱起。这是哪个仙门不打招呼，直接将飞舟开到自家地盘上！都惊动了闭关的老祖，真是该死！当我乾元道宫无人吗？站起身，向前疾走两步，消失在大殿中。“见过掌教！”乾元道宫的飞舟停舟广场内，宋时元身形突然出现。吓的几个留守修士一哆嗦。还没等这些修士缓过来。这广场上如下饺子一般，出现一个有一个身影。都是平时不得见的大人物。道宫的上层战力。“一刻钟，将飞舟整备，用极品灵石！速去。”飞舟管理人员修为不高，堂主不过是个紫府，哪见过这种阵仗，吓的直接亲自去处理。“掌教，西南方向那个飞的很慢的飞舟是哪家仙门！”一红袍修士才将现身，就咋咋呼呼的喊出来。引的四周修士侧目。“我也不知，你看到了？”掌教宋时元问道，“我刚就从那过来的，本想直接上去找他麻烦，但接到掌门急讯，就先来了此处。”宋时元没说话，只是点了点头，看了眼人到齐了，便身形一动，消失在原地，再出现已是在飞舟上了。拿出掌门令牌，将飞舟启动，解开禁制，广场上的十多个修士纷纷化为一道烟，瞬移在船上。齐聚在甲板上。宋时元袖袍一震，飞舟开始急速升高，朝着西边飞去。“少校！四点方向有一飞船急速飞来，速度四十七马赫！！！请求是否打击！”伊森少校眉头皱起。心底念头不断挣扎，能飞四十七马赫，说明这颗星球的科技实力不弱，可为什么太空中没有卫星呢？到底是打还是跑。“请求接通信号。”伊森决定先稳一稳。“少校，他们没有信号！”伊森一愣，这是什么意思，没有信号，是谈都不想谈吗？“少校，他们的飞船没有电！”你要不要听听你再说什么胡话，没有电，他们的船是木头做的啊！“少校，他们的船是木头做的！”伊森一时间不知道自己应该下什么命令了。“发射导弹，先打下来再说！”伊森大声的喊出来。天使级登陆舰搭载的是热核导弹，射程十万公里，威力巨大。“掌教！那飞舟放炮了！”其实乾元道宫这艘飞舟也有炮，不过是用灵石驱动的，打出能量的。这种射出来弹头的炮，说实话这些个修士还是第一次见到。宋时元，顿时心头火起。金丹后期威势显现，你到我地头不打招呼就算了，现在我带人来主动和你打招呼，你用炮射我！真当我乾元道宫脾气好嘛！宋时元准备动手还没动时，身边一开始说话那红袍修士已然出手。手掌一翻，一枚山印祭出，转瞬变大，化为一座真山，挡在前面。手指再掐诀，真山颜色化为金色，由土转金，防御力在上一层。轰隆！！！一声巨响，天使级战舰打出的热核导弹，在这山盾前炸开。瞬间产生的高温，让数千米的地面化为焦土。红袍修士面前这座山盾也融化大半，颜色变的暗淡起来。噗的一声，红袍修士吐出一大口血。面如金纸，受了重伤。宋时元这时也到了，塞了颗丹药进红袍修士嘴里。随后手一挥，将其扔到飞舟甲板上。甲板上一擅长治疗的金丹女修接住 ，立马给其疗伤。飞舟甲板上其余修士则是各自祭出自己法宝，跟着掌教宋时元朝着敌人飞去！伊森惊呆了！那是什么山吗？怎么变出一个山的？人怎么能硬抗热核导弹的？但没等他多想，就见十几个人漂浮在空中，朝着他们飞来！人怎么会飞的？这是个什么星球？———更新太空，泰坦级太空母舰-蔷薇号舰长室内。所有人都默不作声，站在原地一动不动。“我要三百亿克朗有什么用！”一声怒吼传出，这是尊贵的舰长咆哮的声音。从舰长室的全息通讯间传出。门没关严，导致在外间的军官们隐约能听到。但又听不真切。不过能知道他们的顶头上司现在很火大，这就够了。副官小跑，将门关严。“父亲！这颗星球是我发现的！”“我知道！德克，但这就是政治，用这颗星球的利益换取你伯父的更进一步。”“父亲你知道这颗星球有多大吗？他比首都星还要大！”全息投影显示的老者，明显有些惋惜。随后叹息一声。“他再好，也不属于我们，孩子，这些东西我们最终都会拿回来的”年轻的少将不说话，眼里充满着愤怒。“这次回来，家族会推你进军事委员会的，你做好准备。”全息投影关闭，舰长独自在房间内沉默了好久。这才打开通讯间的门走出。“资料呢？这颗星球的资料呢？”声音还和以往一样，随和，充满磁性。“皮特上校，你看下朱利安上校在这里吗？”“报告！司令阁下，我在！”称呼上司，永远称呼他的最高职位。德克少将除了是这艘泰坦级的母舰舰长外，还兼着帝国少年军副司令的职位。“朱利安上校，从我们发现这颗星球开始，已经是十四个小时了，消息都从这里传到帝都了，你连这颗星球的具体信息都没搞清楚吗？”“报告！司令阁下，我为了更加详细的了解这颗星球的情况，我派遣了一个登陆兵团，前往星球内部考察！”德克少将眉头皱起，他隐约觉得有点问题。但不知道问题出在哪里。“派遣的是那支登陆兵团？”德克少将看着朱利安上校。“报告！第四十七登陆兵团！”“哦，我记得他们团长，叫伊森，很不错的一个人。”泰坦级的太空母舰上有整个集团军，这种小规模派遣都到不了舰长耳朵里。“皮特上校，让信息部接一下“文莱郡号”的摄像头，我们看看这颗星球内部到底长什么样。”“司令阁下，接不通，自“文莱郡号”进入大气层后，就失去了信息联系。”德克少将来了精神。这颗星球有秘密啊。不过又想到与自己无关，心情顿时一落千丈。“算了，算了，让伊森回来了，我们要回帝都星了。”———-伊森感觉自己四十多年接受的教育，受到了冲击。不过容不得伊森思考，双方已经越来越近。乾元道宫的飞舟本就是斜着拦截，宋时元此时已经能用神识感知到这艘天使级登陆舰了。“凡人？”不止宋时元，身后跟着到道宫金丹也是感知到这艘战舰上的人，毫无修为，全是凡人。“打开量子护盾！准备拉升，这里古怪的很！”伊森从屏幕里早就看清了飞过来的一群人，下达了自己的命令。宋时元刚准备用神识将舰船上的人控制之时，战舰上出现一摸乳白色的光幕，阻隔了神识探查。伊森当然不知道，整条船刚刚在鬼门关走了一圈。“再打三枚热核导弹，对着那群人打！然后给我全程录下来，我倒要看看是不是还能挡住。登陆舰船底部，三枚橙红的导弹激射而出，冲着宋时元飞来。速度极快。宋时元灵机狂跳。这三枚好像比刚刚那枚厉害些。到底是什么东西！来不及多想。宋时元作为掌教自是不会退到后面。手指飞快掐诀。周身空气变的极度粘稠，乳白色的雾气在空中凭空浮现。以宋时元为起点，朝着前方飞速蔓延。身后的一白袍金丹，站在掌教宋时元身后，手掌一翻，一柄伞出现在手中。手并起剑指，伞如面前这导弹一般，朝着前方疾射。进入掌教所施法区域里，飞速变大旋转开来。后方跑的满的金丹，也是各自基础防御法器，做着准备。刚刚大家都见识了这奇怪法术的威力，相当于金丹后期全力一击，那火老鬼要不有个极品法宝《不周浑天印》，估计是死多生少。三枚热核导弹，当量比之前那枚其实大了不少。眨眼功夫，三枚导弹进了掌教所施法区域。速度竟是肉眼可见的慢了下来。粘稠近乎实质的空气，一点一点的消耗着三枚导弹的势能，直到导弹停滞在空中微微旋转，不得寸进。“掌教师兄，领域之力已经掌握到这种地步了吗？看来掌教离着元婴不远了啊”落在最后的两个金丹修士说着闲话，笑看这三枚飞弹被定住的样子。轰隆！轰隆！轰隆！连续三声巨大的闷响，三枚热核导弹在空中爆开。但并没有任何动静。宋时元感到有点不对。自己领域内似乎积蓄了极大的威能，自己快要压制不住了。“将面前这片区域打出虚空！”宋时元大声喝到。额头渐渐渗出汗水。自己恐怕是小看这群人了。原本就在宋时元身边的白袍修士第一个出手，发丝飞舞，仙气飘飘，双手合十自头顶斩下。一炳巨刃虚影浮现。向着前方斩去。身后十多个金丹也都是祭出自己最强一击，眨眼间朝着一片区域进攻。区域内受不得如此密集的灵气波动，先是出现个黑点，而后黑点渐渐变大。吸收着一切能量。宋时元在这千钧一发之际，放开了自己对领域的控制。朝着前方激射出掌教令牌。令牌转瞬变大护住众人。轰隆！一声宋时元这辈子听过最大的声响响彻在着山谷之上。领域控制不住的能量还是爆裂开来。冲击的气浪掀飞了一层地皮。原本岑峦叠障的山谷变为一片平地。这还是在这能量大部分被虚空吸走的情况下。还能有这么大威力。伊森在见到那导弹被控制住时，就下令拉升，朝着太空而去。乾元道宫一帮金丹，此时都是懵逼的。对面船上是有个元婴吗？白袍修士的上品法宝，《紫苏流云伞》被炸没了，宋时元的掌门令倒是没什么事。眼见着对面要跑，宋时元想去追，似乎感知到什么，就停在半空看着。伊森见对面没追，也是松了口气。将动力推到极限。达到三百马赫。眼看着就要冲破大气层，飞船各处的警示灯疯狂闪烁着。速度从三百马赫飞速下降。伊森刚才看到过自己的导弹是如何被拦截，知道自己似乎遇到了同样的事情。“启动”曲速越迁”！”伊森大声命令着。曲速越迁启动中，54受到未知影响，目的地已随机。32“想走？问过老夫了吗！”此处已经接近外太空，一只大手凭空浮现，速度极快，眼看飞船就要被紧紧握住，不过，最后还是抓空了。扑哧一声，笑声在这高空中传出。“人家没问你，不还是走了。”“那你们怎么不出手，看我笑话又有何好处。”“谁看你笑话，就你来的最早，你都赶不及，我们如何能留的下”“这些人是天外来的？通知圣地了没有？”“黄老鬼第一时间就去了，舔狗一个”几人听了这话，都是不接话。“这些天外之人，器物甚是精妙，以凡人之躯比肩金丹，不容易啊！”“是啊，是啊。走了，陈老弟，你那弟子快进元婴了吧。”一灰衣老者，就是一开始没留住飞船的元婴。“对，有何指教，田兄”陈飞白想着也没什么好瞒的，就直接说了。“没啥，诶，你乾元道宫已有六个元婴，问鼎西域第一大派，可喜可贺啊。”田老头没头没尾的说了几句，就消失不见，陈飞白也没多想。身形闪动间出现在宋时元一众道宫高层面前。“见过陈师叔/师祖”众人对着陈飞白行礼，陈飞白摆了摆手。“此间事不算完，你们警觉些。”又对着宋时元说道“回去将护山大阵打开，不要舍不得灵石，已经到了关键时候，门派的积累就是用在这种时候的。”“遵命”宋时元带头行礼。“对了，把仓库里资源找个理由给小弟子们发下去。提升提升实力。”陈飞白说完这句，这才消失不见。众人刚准备登上飞舟，返回门派时，陈飞白又出现在众人面前。“对了，这个给你，小王。”说这扔了个扇子给一白袍金丹。这白袍金丹就是之前挤出伞装法宝的金丹修士，王文。“你那破伞我早看不顺眼了，一个大男的，整天拿着个紫伞，像什么样子！”王文不敢说话，接过师叔递过来的扇子法宝。口中称谢。“回头让小火把他那印拿我那去，我看看能不能修。”说完这话，陈飞白真准备走了。但最后又对着王文吼了一句。“少和那些川都宫的接触！”———-（北京时间6月22日0点26分，我坐在客厅里继续码字）太空-蔷薇号（别看不起着这舰船名。这可是以帝国最美的行星蔷薇星命名的。）朱利安上校压力有点大。伊森的天使级-文莱郡号登陆舰失联了。作为此次行动的军事主官，他负有不可推卸的责任。上校有资格调动兵团的同时，也完全要对自己作出的决定负责。权利和义务是对等的。“皮特，可是你要我派遣一个兵团下去的！这会出了事情，你想要我一个人扛？”朱利安压抑着情绪，对着皮特上校说到。“朱利安上校，您和我军衔一样，都是上校，甚至职位上，你作为陆战第七军军长，比我这个办公室参谋还要尊贵些，你说我指挥你，朱利安上校，你是不是在开玩笑？”朱利安上校的呼吸渐渐加重。“我出事了对你有什么好处！”“朱利安上校，我已经正式请求军纪部展开对你的调查！，请你不要在胡乱攀附交情！”说完，皮特上校管都没管被军纪部架起来的朱利安，朝着舰长室走去。“司令阁下，第四十七登陆兵团被朱利安·古斯塔夫，乱命派遣至陌生星球，现在踪迹全无。”德尔少将放在手中的文件，叹了口气。扭了扭自己的眼睛。“朱利安为什么要做出这种事情？”皮特上校痛心惋惜的说。“朱利安·古斯塔夫丧失信念，对陛下丧失忠诚，为了一己私利，命令自己管辖兵团，强行出击，造成巨大损失”“所以啊，这调兵权还是得在参谋部手里，才是最安全的，不会因某个人的私欲，而枉送性命！”“司令阁下说的在理，您看要不要组织一次学习会，就这件事情召集各军事主官来学习？”德尔少将有些诧异，抬起头，盯着皮特上校。皮特上校站的笔直，目光灼灼的和自己的上司对视。“这件事，你来定！另外命令回转帝都星，这里的一切都不必管了，全部交给即将到来的帝国第一舰队！”———“曲速越迁完成。”伊森此时整个后背都是湿的，太刺激了，这个星球居然有超人，只要自己弄到成为超人的办法，自己回到帝国岂不是走上了人生巅峰。“伊森少校，我们越迁了快四十万公里了。”“四十万公里？这颗星球到底有多大。”伊森惊讶的长大了嘴，不过又摇了摇头。这颗星球有超人，不大一点，怎么能够超人飞行的。“如果还是联系不上总舰，我们先撤回太空去。”“少校，我们可能回不去了。”伊森皱眉。“飞船出了问题？”“少校，我们，我们，动力系统坏了。现在还能最后飞行五。。”“只能飞行五天了！？”伊森感到有点绝望。还没等那船员说话，合成的电子音就在一遍又一遍的提醒。‘五分钟后飞船自动降落，请做好准备。’‘五分钟后飞船自动降落，请做好准备。’伊森看着汇报的船员。说了一句很脏的家乡话。飞船缓缓降落在一片密林之中。四周安静无比。登陆舰的舱门打开。一台白红色的机甲一步一步谨慎的走出来。机甲高度17米,外部全由‘镀振合金’覆盖。伊森驾驶着这台帝国的尖端科技制造的特战机甲T-Doll 女武神。小心翼翼的走出战舰。身后跟着百多台机甲，看着压迫感十足。“动作轻点，现在我们没有退路了！”伊森在频道内再次强调。等机甲走出，舱门再次关闭。“所有机甲，开启量子护盾，保持静默。探索这片区域。”距离飞船降落点三里地的一个高坡上。趴着两个修士。“那是个什么玩意？”“不知道啊，没见过，是不是哪家仙门的傀儡？”“诶，你别说，看着像。”“那我咋感受不到法力波动啊？”“能控制这些傀儡的最少都是筑基，你个练气中期，还要感受到人家的法力，给你脸了。”“你他娘的就不能说的委婉点。”其实在下飞船之前，伊森就通过热成像发现了这两个人。但见到这二人没动作，也没飞，就以为两个是普通人。想要悄悄的将二人围起来。控制住，拷问些情报出来。“我咋感觉他们想要把我两围起来呢？”右侧的丁一，疑惑的开口。“不会吧，我两又没得罪他们。”“我草，快跑，真是冲着我们来的！”伊森想不明白，那两个不会飞的普通人是如何发现自己这边开启了幽灵模式的机甲的。但现在目标要跑，也顾不得许多。“a小队、b小队，全速前进，拿下两个土著！”帝国一个机甲作战小队共五台机甲，两个小队十台不同型号的机甲，很快将两个练气修士围困住。（今晚再更，目标1.2w,目前七千字，此时距离我发布不到一天。）丁一和丁七兄弟两背靠着背，手上拿着上品灵剑死死盯着面前的金属傀儡。\"我二人是清河谷紫府丁家族人，无意冒犯诸位仙门前辈！还望前辈放我二人离开！”丁一冲着面前大声的喊道。其实兄弟两个，并不是那紫府丁家的族人，自己的出生仅仅是个筑基小家族，与那紫府丁家，不过凑巧同姓。若是紫府丁家也镇不住面前这些傀儡操控者。自己二人恐怕是要交代在这里了。面前的十台机甲，翻译系统都快运算冒烟了，也没翻译出这两个土著说的什么。只听的叽里咕噜一大串听不懂的声音。二人见面前这十个傀儡没动静，心里一喜。只当是紫府家族的名头唬住了这些人。可还没高兴多久，外围就传来密密麻麻的金属傀儡走路的声音。呼呼啦啦的又围了百多台傀儡。伊森一直盯着自己面前屏幕显示的\"正在翻译中”几个字，心里大骂帝国机甲采购部门的愚蠢。就他妈不能买点好货。尽是一些过时货色。丁一回头看着丁七。二人对视一眼，都没说话。对面连话都不愿和自己多说。显然是抱着杀人灭口的心思。也不打算坐以待毙，丁一率先动了。手心一转。指尖多出三张淡色符箓。朝着前方甩出。符箓在半空炸开。转瞬一片大雾就出现在这山林内。大雾浓实，且有阻碍神识探查效果。丁一放出的正是练气期的高端货，封烟符箓。这算是丁一家底之一了。\"自己小心！\"兄弟两个打算分开跑，告别一句后，丁一身形一跃朝着北边，踩着树尖飞快遁走。丁七则是钻入土里。借着土遁符逃走。“开枪吧。动静小点。”虽然山林里遍布着大雾，但伊森还是从热成像里将两个土著动作看得一清二楚。一个飞到了天上，一个钻进了土里。这是什么超人星球吗？但伊森见着人要跑了，只得下令开枪。总不能让他们回去通风报信吧。两台重型机甲地狱犬从后背亮出银光闪闪360mm枪口。自动锁定两个土著。发射出两枚拳头大的子弹。丁一脚尖轻点树尖没敢回头，但他感觉到对面朝自己放了法术，从储物袋中取出一枚铜色小盾，飞速祭练丢到身后，变大护住后背，这时那拳头大的子弹已经到了。砰的一声，子弹穿透了小盾。丁一还没来及做出防御，就已经被洞穿了胸口。重重跌落在地上。练气期的遁地符箓本就下潜的不深，约莫七八米，追着丁七去的拳头大的子弹在丁七上方钻入地下不过两米爆炸开来。炸出一个10多米的深坑，爆炸带动的冲击波将丁七活活震死在地下。直到手下人将两个土著尸体带到自己面前。屏幕面前的翻译器才显示翻译成功。“我们是清澈的河水里那个紫色房子里住的居民。我们能邀请你们去吃饭吗？”伊森看着地下的两具尸体，总觉得有点不对劲。14.25更星际时代，基本上所有势力都会将语言翻译器下放至单兵，所以此时此刻，围在周围的一百多个机甲，都看见了屏幕上的翻译。“少校，我们是不是有点过分？”伊森脸色也有点红。“抽一管血，采集点DNA送回船上研究吧。然后这两个友善的土著，就用帝国最高礼仪，风葬礼！愿他们下辈子投胎到帝国！”其余机甲听了长官这话，纷纷点了点头。帝国风葬礼，就是将死者火化成灰之后，将其骨灰撒在空中，随着风飞向世界各地。表示这个逝者如生前一样，永远在某个角落陪伴亲友。与此同时。清河谷丁家往西二千里，野牛湖筑基丁家。家族祠堂内。两盏魂灯忽然熄灭。看守祠堂的老者吓了一跳，走近一看更是老泪纵行。丁一，丁七，家族里两个筑基苗子，属于那种。家族搞到筑基丹，就只能在他二人里选择的家族下代核心。可。可这二人不过出去去找个药材，怎么就同时陨落了？连滚带爬的冲向了后山。那是家族唯一筑基丁子熙的修炼之地。“族长！族长！”老者满脸泪痕的跪在丁子熙面前。“六叔公何事，你这辈分这样哭哭啼啼成何体统啊！”“丁一，丁七死了！”蹭的一下。丁子熙站起身，双目发红。盯着老者。“魂灯灭了？！！”“是。”老者一边啜泣一边回话。说着还将魂灯递给丁子熙。丁子熙拿到魂灯，闭上眼测算。忽的睁开眼，双目通红，“二人不过离家族七百里！就是筑基出手，也能跑一个出来！”说完，带着家族传承下品宝器（练气灵器，筑基法器，紫府宝器，金丹法宝，元婴灵宝），云中剑飞射而出！朝着二人魂灭之地而去。丁子熙都快出离了愤怒。二人是家族复兴的希望！居然被人杀害！直到快飞到地方。丁子熙恢复了些理智。将一张筑基期的，敛息符箓拍在自己身上，这张符箓的制作者是个稀有的冰灵根修士，制作的所有符箓都会让使用者感到寒冷，所以除了水系法术符箓之外，其余的价格比市场稍低。丁子熙不在乎这些，冷？他的心更冷！“傀儡？”直到接近了，丁子熙吓了一跳。密密麻麻的高大傀儡围成一个圈，将丁一与丁七烧成灰。丁子熙感觉自己已经快要压制不住愤怒了。但这么高大的傀儡，最少也得筑基操控，这里估摸着百十来台，这是怎样一个大势力！又为何要对自家小辈出手？丁子熙掏出留影石，准备记录下来。去大仙门告状。他们将骨灰举起来干什么？他们将骨灰撒在空中干什么？忽的一股狂风，机甲吹出的风，将二人的骨灰吹散。有一点落在了丁子熙的脸上。丁子熙满眼是泪！挫骨扬灰！！！！我丁子熙与你们不共戴天！！！—-“吴执事，我是真有十万火急的情况要和你们掌教连线！”丁子熙跪在一个修士面前。恳切的求着方便。这吴执事穿着素净青袍，胸口绣着两芒耀日。看着是个中正之人。“丁族长，丁族长，你先起来，你跪我又有何用啊！，我不过是道宫边陲别院的一个小执事，我有何权利直接去联系掌教？”吴执事将丁子熙扶起，坐在椅子上。“到底何事？让丁族长这般模样？”丁子熙想了一瞬，这吴执事与自己相识一百多年，他要是那些贼人的同伙，那他就认栽。将留影石备份递给吴执事，并说了自己所见所闻。语气抑扬顿挫，听的闻着伤心，听者流泪。“太过分了！”吴执事手掌重重拍在椅子上，将座下椅子拍的粉碎，随后站起。“走！先和我去见掌院”说着便拉着丁子熙往内院而去。乾元道宫-南平岛别院掌院杜思凝是个女修。是从道宫天降到此处任职的一个紫府后期修士，因是受了情伤，自荐来道宫的最西部镇守。平时负责清点道宫在南平岛资源，等着道宫每十年收取一次，工作也算清闲。道宫威名遍布西域，也没哪个不长眼的来找麻烦。此时杜思凝正在品茶，听得院里吴执事求见，点了点头。片刻后，一个练气后期侍女领着两人到了掌院面前。行礼自是不提，待丁子熙又将来龙去脉说清楚之后。杜思凝陷入了沉思。早上宫里是不是发来一条掌教亲令,说的啥来着。当时没注意听，杜思凝开始回想。哦，好像是说注意一艘奇怪的飞舟。说的是这些人吗？和吴执事，丁子熙这些筑基期修士不同，杜思凝是了解整个西域所有势力的。她清楚的知道，西域六大元婴级势力，没有哪家擅长傀儡术的。甚至更远的中域也没有。至于其他域则是太远，就算有这等势力来人，宫里总会递消息来。既然不是所知势力，那就只可能是宫里提到那艘飞舟下来的人了。“你二人外面等着，我联系下宫里。”杜思凝想了想，还是和道宫联系下为好，留影石他也看了，一百多台机甲，就表示这些人最少有一百多个筑基，那肯定也有紫府，金丹不好说。自己这个南平岛别院一共才十来个筑基，拿什么和人刚。拿出传讯仪，插入极品灵石，等了片刻。杜思凝面前出现一个虚影，虚影渐渐变的凝实，就像是真的站在杜思凝面前一样。“秦师叔！？”杜思凝原本坐着，但看清是宫里金丹长老之后，立刻恭敬的站起来行礼。——-一炳巨大的合金装甲用战刀，将一只巨型的猛虎前爪砍断。随后这架红白相间特战机甲女武神号的左臂，露出的230mm口径的枪口，喷射出炙热的火舌。砰砰砰的全部打进这巨大老虎的身体里。炸出一个个血洞。让老虎转瞬没了声响。可还没等这机甲喘口气，又从天上落下只猴子，手里拿着棍子劈了下来。看着平平无奇，可这力道着实恐怖。女武神号机甲迅速抬刀格挡，这这猴子的棍子却突然一分为三，化为三节棍。棍首部分，结结实实的打在机甲肩膀上。99%的神经连接，让驾驶这台机甲的伊森，痛的快要晕过去。伊森忍着痛，硬扛了几下，最终冷热武器交替，结束了这猴子的猴命。“妈的，所有机甲小队，往后退，让文莱郡号发个热核导弹来！”伊森急了。“少校，这星球的动物为什么也会超能力！我们到了他们到动物园吗？”“我他妈哪知道！可能这是超能动物园吧。重型机甲掩护，其余机甲速度后退十公里。三十秒后重型机甲再撤！热核导弹还有62秒到这里！”战斗打的突然。一只机甲小队发现一个山谷，聚集着很多动物，想都没想，一个特战机甲拎着刀冲了进去，喊着什么今晚加餐这类的胡话。而后这只小队就在公屏里不停的喊着支援。随着加入的机甲越来越多，动物们的死伤越发的高了之后，从山洞里冲出来几个厉害的动物。一只表皮硬的像是战舰的黑色巨熊，一只会发激光的蛇。还有个哪哪都硬的穿山甲。随着这三只动物的加入，机甲军团终于出现了伤亡。多机甲配合才能限制这三只动物的行动范围。但只要被打出缺口，动物就会冲出去造成巨大伤亡，所以出现缺口，就需要立刻补上，才不会造成更大的损失。黑熊其实是这场中最气的。今日是他纳妾的好日子，附近的筑基妖兽得知这个消息之后，带着贵重的礼物，热情的过来讨杯酒水。黑熊自是高兴的，还请了邻近的两个紫府妖兽一起热闹热闹。本以为今天会是个快乐的日子。可这突然冲进来的几个傀儡，打破了这一切。像个疯子一样，见妖就杀。本以为是个小角色，没想到还越打越多。战斗就是这样莫名其妙的打响。双方谁也没说话，一方气到不想说话，一方以为不会说话。战斗结束的也快。这些傀儡说退就退，毫不恋战。本想追出去的黑熊，却被蛇妖拦住。“大力兄，不可，不可追！”熊妖其实也不想追，但碍于面子，他要是不第一个追过去，对不起底下死的弟兄。“蛇前辈是怕有埋伏？”熊妖声音巨大，响彻整个山谷。蛇妖点了点头。“大力你想想，能动用如此多筑基后期傀儡的势力能有多少？”旁边的穿山甲点了点头。就在三妖说话功夫，一枚橙色的导弹从天边飞来。速度极快。从看到开始，三妖灵机直跳。没有犹豫，直接施法遁走。可还是晚了。轰隆！又是一声巨响，这是在修真界爆炸的第五颗核弹。这次可没有金丹在场。没有法宝削减威力。实打实的星际时代为小型手术核弹在这爆开。落点中心十公里寸草不生。6.23更新（亲爱的读者们，今天周一啊。作者上午处理了工作。）卡司帝国镇国重器，一艘泰坦级的太空母舰有多少人？根据官方给出的数据，满载状态下，连士兵加起来是三十六万到四十五万之间。德尔少将的旗舰蔷薇号，稍微小些，满载是三十八万四千人。想要将这艘如同行星般大小的凶器开动起来，至少需要四万名技术人员。奥克斯就是这四万名技术人员之一，位于FC-0017区，属于F区监控系统下的维护工程师。才起床的奥克斯头发凌乱如同鸡窝。双目无神。接替了摸了一晚上鱼的同事，坐在了光幕前面。除了重要区域密密麻麻被显示在面前百多平方的光幕上。其余的百万监控区域，若是没有重大紧急情况，只会以一个弹窗跳出，提醒着工程师。弹窗也是分紧急程度的。等级最高的S级会直接跳出大屏。等级最低的G级，则是右下角一个小小的弹窗。奥克斯的任务就是处理这些系统警告。其实本来这间办公室有四个人的，但奥克斯至今没见过另外两个人长啥样。系统还算智能，按优先级排序。奥克斯点开第一条，B级弹窗。FK-0568区144号监控，显示有两个行政人员在打架。很好，奥克斯拖动弹窗，直接转给了治安所。奥克斯麻木的重复着机械的工作，不停有更高级别的弹窗顶到前面来。永远都不会有人注意一条F级的提示。”FO-0653区，能量核心放错位置。请立刻转移““师兄，你学的怎么样了？”FO区域，一间双人宿舍内，两个穿着保洁制服的帝国人交谈着。“真难，这说话像是在嗓子里含着狗屎！”“师兄！你吃过大黑的屎的吗？”另一人露出惊恐的表情。“少贫，师门让我二人潜入，是为了搞清这些天外人是哪来的，他们那有没有灵气，你有功夫和我贫嘴，不如多学两句话。”“师兄，我现在基本对话啥的，还可以，搞清规律还是很快的，我练气时学的小法术都比这难。\"师兄被噎的说不出话。“师兄，你还别说，这些天外人虽是凡人，可这器物着实精巧。”“确实，不过都是些奇技淫巧，于修炼何益？反而会分心，大道无望！”“修吧，修吧，你修成个木头！与天同寿！”“师妹，我不是那意思。我...”“我看你就是那意思！”.....其实蔷薇号第一天进入这片太空，就被修仙界几个老不死的发现了，人老成精，更何况这种老不死的。悄悄的送了两个年轻筑基修士过来，先潜入一艘小型的维修舰，搜魂两个人后，就易容成他们的样子，伪装起来。也幸亏伪装的是两个清洁工，要是伪装个技术工程师，准就暴露了。搜魂只能看到记忆，学不会东西。一开始两人冒充嗓子哑了，还算蒙混过去。所以二人一有空就要躲在房内学着帝国的通用语。\"滴滴滴”房间里的屏幕自动亮起，显示出一张地图，地图自动放大，用红色标出了一片小区域。“妈的，这天外人干活比我们杂役弟子还多！和他们一比，我们门派全是大善人！”二人看着又来了新的任务，骂骂咧咧的拿着抹布拖把向外走去，按照地图指引前往工作区域。蔷薇号，舰长室“司令，我们到了托斯卡纳。”皮特上校向着德尔少将汇报着。托斯卡纳太空基地是卡司帝国最远的一个补给基地，到了这里，就算进入了帝国势力范围。随着基地塔台的指挥，巨大的蔷薇号慢慢驶入港口。随着稳定翼的展开，漫长繁琐的停舰过程终于结束。繁琐那是对底层人员而言。此时的德尔少将已经在港口内的将军招待所里。靠着松软的沙发上，抽着雪茄。“德尔。这次的事情，他们做的确实过分了...”“麦卡伯伯，没有什么做的过不过分，我们得到我们想要的，他们得到他们想要的，这是交易，公平的交易。”德尔的声音一如既往的柔和。“倒是麦卡伯伯，这次临危受命任第一舰队司令，真是让人意外呢。”“陛下的信任而已，我老了，本来想去卫戍军区养老，奈何陛下非要钦点我来走这一趟。”肩膀上扛着五颗星的麦卡笑呵呵的喝了口杯中的酒。“那颗星球的资料，我知道的已经全部发给您了，我在那折了个登陆兵团，麦卡伯伯还是小心点为好，别阴沟里翻了车。”“这就不劳你费心了德尔，走吧，晚宴准备好了。”“不了，麦卡伯伯，我晚上有个军事会议，先走一步了。”德尔将一支只抽了一口的雪茄，按灭在烟灰缸里，头也不回的离开。皮特上校紧紧的跟着。等走远了，德尔少将回头看了眼金碧辉煌的将军招待所。“你觉得我很幼稚吗？不知道忍让吗？”皮特上校以为是和自己说话，但看着上司却没有看自己。一时也不知该不该回话。“但会哭的孩子才有奶吃。”乾元道宫，飞舟广场。一艘法宝级飞舟已经整装完成。带队的是金丹后期的王文，四个金丹中期，七个金丹初期。还跟着百多个紫府。掌教宋时元，再三嘱咐王文小心，而后看着飞舟飞速的朝着南平岛别院飞去。心里也是期盼着这些师弟师妹弟子们能安全回来。乾元道宫自研自产的宝器飞舟插入了极品灵石后，在王文金丹修为的加持下，速度用这些天外人的话说，达到了80马赫。此处距南平岛别院，正好约为百万里。随着一颗颗极品灵石灵力消耗，化为齑粉后，不到三个时辰，这艘飞舟就降落到南平岛别院。王文今日穿的是紫袍。身形一闪，转瞬来到别院内，杜思凝早早带着别院弟子等候在广场上。“见过师伯！”“不必多礼，可有派人去看过那群天外人现在何处？”“弟子亲去的，那些，那些天外人..”王文好笑，“有什么说就是了，支支吾吾的作甚。”杜思凝这紫府也算是王文看着长大的，言语里都是亲近。“他们的傀儡无缘无故大闹了红叶岭一个熊妖的纳妾聚会。然后可能是他们的金丹出手，用一古怪法术将那红叶岭夷为平地。死了三个紫府妖兽，百多筑基妖兽。”王文听杜思凝这么说，就知晓应该是那飞舟又打放出了威力巨大的炎爆术。沉吟片刻，就带着杜思凝上了飞舟。你有飞舟，我没有吗？我今日开出来的这艘金乌号，是整个西域最擅长攻坚的重炮飞舟。妈的，极品灵石今日管够。飞舟起飞，顺着杜思凝指引的方向，不到片刻就出现在文莱郡号的雷达范围里。坐在指挥室内疼的龇牙咧嘴的伊森，听到下属汇报雷达发现了飞舟。伊森想家了，他从没遇见过这样的星球，连那些动物都好像有灵智一样。“少校！他们发射了激光炮！”还没等伊森下达命令，文莱郡号登陆舰的拦截系统就自动激活。射出一枚又一枚拦截弹。王文此时格外的兴奋。十枚极品灵石才能激活的灵力大炮，此时像是不要灵石一样，一发接着一发的射出。身旁的金丹中期秦阳也是兴奋的很。“师兄，让我来，让我来打几发。”“等会的，掌教师兄给的灵石还很多，你急什么。”“他们那飞舟有大阵吗？如果没有的话，师兄你射这十几炮估计就够了。金丹被打到估计都要半条命。”另一个粉袍金丹在一旁接着话。“师兄，你看，天外人也发射了飞弹！”密集的拦截弹飞入空中，与飞来的灵能炮撞在一起。炸响的火光染红了整片天空。“师兄，那是什么东西。飞的还挺快。”王文视线看去，从火光中冲吹十多个黑漆漆，扁扁的东西，像是一只铁鸟。不过飞的还挺快。“不知道，可能是什么器物铁鸟吧。”神识探查过去，依旧感受不到一丁点的灵气。我去会会他们。一个身穿绿袍的金丹中期一跃而起，在空中手并起剑指，一柄璀璨的飞剑突然出现，稳稳的带着金丹修士飞向了那些铁鸟。“德尔塔，前面那是什么东西在天上飞？”“不知道，看起来像是个人，我雷达锁定他了，要发射导弹吗？”“打下来！”闪电隼空天战机携带的导弹是热能追踪导弹。基本上只要敌人先被雷达锁定了，就是必死的局面。两枚导弹分别从闪电隼的左右机翼弹出，冲着那绿袍金丹而去。这绿袍金丹站在剑上，见着两枚飞弹飞来，也是不慌，指间滑动，从虚空中又飞出一柄金色飞剑，飞剑一分为二，分别朝着两枚飞弹而去。飞弹似有灵性，巧妙的避开了飞来的飞剑，继续朝着金丹而去。绿袍金丹眉毛一挑。“有点意思！”被避开的两柄飞剑又飞回，二分为四，四分为八，转瞬六十四柄飞剑，成连环之势为困住两枚飞弹，说了这么多，其实就是眨眼的功夫。被飞剑围困的飞弹在空中爆开。让站在剑上的金丹，险些站不稳。王文站在飞舟上，看的真切，手一挥，身后剩下的七八个金丹，各个祭出武器向着天上的九只铁鸟攻去。飞舟上的紫府没动，一个个站在船舷，用法力维持着飞舟防护大阵。一枚飞弹穿过金丹修士的包围圈，直直的撞向飞舟。飞舟外围浮现一层光幕，不仅隔绝了飞弹威能，还将冲击波挡在外面。飞舟纹丝不动。本来看戏看的正起劲的紫府修士，被这突如其来的飞弹吓了一跳。纷纷加大法力输出，维持飞舟护盾。伊森通过光幕看到这土著飞船竟然也有能量护盾。心底莫名为帝国征伐这个星球的计划担忧起来。来不及多想，想也没有用，自己此刻飞船动力系统损毁，自己挡不住这些土著，今日就得死在这里。（先看着，我继续写。）“师兄，能不能用法力把这清理干净？”“不行，老祖交代了，能不用法力就不要用，而且这虚空中没灵气，咱俩修为全靠灵石吊着。省着点。”“行吧，我二十八岁筑基，我这样的天之骄子也有拖地的时候！”一架小型无人机从二人面前飞过。在这空间内转了一圈。随后飘到二人面前。好听的电子音响起。“FO0653-495-罗密欧，FO-0653-565朱丽叶，工作时间聊天，工作完成时效低于85%，扣除工资200克朗。”刷刷刷，一张白色的单据从无人机的肚子里吐了出来。落在师兄手上。等无人机飞走，师兄梁山伯有点懵逼的看着师妹祝英台，“刚刚说啥呢？”祝英台走近，看着白色单据上的文字，有点不确定。“好像是扣我们俸禄。”“为啥扣我们俸禄？”“好像是我两干的不好，没弄干净。”梁山伯挠了挠头，看了眼干净的地面。“扣了多少？”“200吧好像。”“那我们每天俸禄是多少？”祝英台想了想，“这些天外人好像不是按天发放俸禄，一个月发一次。我们两每个月是3600好像。”“哦，那还好，扣的不多。”“师兄，如果按天算的话，我两每天是128，这扣了200，我两今天白干，还要搭上明天大半天。”“天杀的，这些天外人怎么能这样！完全不把人当人！就是门派杂役弟子过的都比这好！”师兄妹两人累的要死，回到小小的宿舍内，躺着都不想动。“师兄，咱们这日子啥时候到头啊。”“我刚刚从食堂走的时候，往窗户外看了眼，那的窗户能看到外面，咱们好像停在一个这些天外人在虚空修的一个堡垒里。”祝英台眼睛一亮，从床上坐起来。“我们到了吗？是不是能下去了？”“没，我看这个大飞船里没有人下去。我还看到四五个和我们这个飞舟一样的大的飞舟朝着我们来时的路飞去。还跟着好多小点的飞舟。”祝英台毫不在意，生无可恋的又躺下。“估计是找我们麻烦去了，我看书上说，这些个天外人就喜欢征服星球。”“星球是什么?”“就是我们的青元界。”“那你说咱们青元界飞升的大能到哪去了？”“不知道。管他呢，我这辈子能成金丹就够了。”就在兄妹两个聊天的时候，一片庞大的战舰群从蔷薇号头顶飞过。三艘泰坦级（Titan-class）太空母舰。六艘主宰级（Sovereign-class）-次旗舰十七艘无畏级（Dreadnought-class）-主力战列舰加上上百艘各式各样的护卫舰，工程舰，补给舰。朝着帝国的未来驶去。是的，卡司帝国皇家第一舰队，全体军官都是这么认为的。自从看了蔷薇号传过来的三维照片之后，他们一致认为，帝国的未来在这颗星球上，而不是和那些虫子还有异教徒打那些无谓的局部战争。尊贵的第一舰队，就是该去完成这样伟大的使命。第一舰队，泰坦级-霍尔莫斯号。一间尊贵，典雅，威严，庄重，金碧辉煌的会议室内，齐聚着所有第一舰队的顶层。这些人肩膀上的星星加起来，比星空还要多，还要璀璨。麦卡·黎波里，作为场中肩膀上星星最多的人坐在了主位上。（前文写的三颗星，我改下，给他变成五星。）“德尔少将认为那颗美丽的星球上，有什么是我们无法抵挡的，想要我们保守作战。”“哈哈哈，哈哈哈，”会场笑声一片，气氛融洽和谐。“尊敬麦卡五星上将，德尔·科亚少将是不是在帝国一军院教书教的太久了，有点忘记什么是帝国第一舰队了，”德尔·科亚曾担任过帝国第一军事学院的政治部主任。哈哈哈哈，会场又是一片老钱风笑声。“既然诸位都认为德尔少将的建议有些保守，那么诸位以为呢？”“我第一分舰队请求主攻！”“我第二分舰队请求主攻！”“我特战登陆军团请求主攻！”......“看看，看看。”麦卡上将用手指着七八个请求主攻的中将，少将，对着场中众人说道。“这才是帝国真正的中流砥柱，国之大幸啊！”“这样吧，也别主攻，副攻了，你们从四个方向，同时进攻！我希望一周后，我视讯陛下的时候，陛下会惊讶的合不拢嘴。哈哈哈哈”（持续更新中！！多评论，讨论下剧情，我会采用比较合理的思路，另外，我是个写小说的，有些数据别太较真哈，见谅，见谅。）伊森本以为自己已经非常非常高看这颗星球了，但结果还是低估了。他只看到，自己的空天战机在天空速度突然变慢，被对面那些会飞的鸟人，用各种各样奇怪的攻击打下来。文莱郡号接近七十架的空天战机现在已经战损过半了。这是什么类型的超人？伊森仔细回想着帝国的经典超能力电影。现在伊森只想说那些导演都是废物，缺乏想象力。自己要是能回帝国，当个导演也能走上人生巅峰，可现在来不及了，自己今日怕是要死在这里了。“少校。”“洛丽塔，对不起，我们回不去了。”“不是，少校，我们可以走。”伊森眼睛一亮。“动力系统修好了？”“不是少校，是我们跃迁系统修好了。”......“小师弟，好样的！”“小师弟，那边还有，快去那边！”乾元道宫这一代金丹中，最小的金丹修士宋执，此刻正在空中用自己的领域，为师兄们创造击落这些铁鸟的机会。领域之力实际上是元婴修士专属。金丹后期能领悟三成，就已经拿到了晋升元婴的门票了。但谁都没有想到，这个年仅148岁的金丹初期小师弟，竟然这么早就领悟了领域之力。而且看起来程度还不低。修仙界从来都不缺乏惊才艳艳之辈，比宋执更早领悟领域的也不是没有。但，时间领域！还真没有！原本的宋执站在最后，王文只是想着带出来长长见识。但战况越发的焦灼，让王文有些头疼。直到身后这个小师弟站出来，说自己有办法，乾元一众金丹才发现，宫内竟是多出个这样的妖孽。宋执的加入，让战斗呈现一边倒的局面。没有什么铁鸟能承受金丹的全力一击。转瞬七十多架空天战机就被击落大半。正当众人准备再接再厉之时。天边传来一声怒吼。“乾元道宫！！你欺人太甚！”巨大且饱含愤怒的声音从海面传来。乌云如海浪翻涌一浪一浪的遮蔽了天空。雷光闪现，雷声炸响。一条巨大的蛟蛇在乌云里翻涌，后面跟着数不清的海底妖兽。转瞬就到了王文飞舟前。“王文？！！果然是你杀了我那好孙儿！”“老泥鳅，我今日没空陪你闹！哪来滚哪去！”王文在门内脾气好，在外面可不是这样。乾元道宫第一阴人的名号不是白给的。王文踏前几步，将宋执护在身后，金丹后期的威势毫不掩饰散发出。震的蛟蛇身后的海妖心底惊惧。蛟蛇身体向前游动，也是发出金丹后期威势与王文对峙。“你乾元道宫无缘无故杀我孙儿。此时还要以势压人吗！欺负我海族无元婴吗！”王文此刻一头雾水。但眼见今日就能将这些天外人消灭，实在不想与这蛇妖纠缠。身后的秦阳小声劝着王文。“师兄，稳一手，今日首要就是拿下那些天外人。”王文点了点头，给了秦阳一个你放心的眼神。而后王文凌空踏出，脚底太极虚影显现了，如水纹般荡开，又化为水墨消散。一步一步，朝着蛟蛇走去。“你他妈的，老泥鳅，老子不知道你说的孙子，是哪个孙子。但今日我乾元道宫再此办事。你来碍事，就是不给我乾元道宫面子！”秦阳脸色一黑。他妈就是这样稳一手的吗？蛟蛇双目已经彻底化为红色。“王文匹夫，欺我海族太盛！”当即带着电闪雷鸣朝着王文攻来。乾元道宫的金丹自是不会看着自家师兄被人打。一个个都是转头和海族来的金丹打了起来。金丹大战，在青元界不多见。金丹混战，那就更少了。要说修仙界哪家的脾气暴躁，那得数蛟蛇族，龙的血脉没遗传到几分，龙的脾气都是学了个十成。这蛟蛇的孙子，就那个去吃熊妖纳妾酒席的那个紫府蛇妖，算是族里头脑较为清楚的了。被视为族群的未来。好巧不巧负责看护的金丹长辈那天就没跟着，导致这小蛇殒命。附近有能力有手段，而且敢动手的就一家乾元道宫。而且此时正好就有不少金丹在南平岛。不是你乾元道宫，还能有谁！双方越打就是越是火大。而且几万年来多次冲突埋下的血海深仇也在此刻爆发出来。伊森透过光幕看呆了。这是什么？水族馆的动物跑出来了吗？“少校，可以了，马上跃迁！”伊森恋恋不舍的看着天空中的大战，点了点头。文莱郡号在倒数之后，再次消失不见。中域-紫薇仙门紫薇仙门自青元界有了史书以来，就一直是青元界第一仙门。没有起落，仙门气运一直稳定在巅峰。此时紫薇仙门最高峰叩仙峰上。几个老者正在喝茶。山顶四周一副冰封积雪的模样，可这喝茶的亭子四周却是青草盈盈，花开正艳。“我跟着那飞舟，可这些天外人似乎掌握了空间法则，空间波动之下，就直接消失了，我试着找过。可始终没找到。”“若无定标，自是难找。”“哦？空老有法子？”“我偷偷送了两个弟子带着定标去那天外飞舟里。只要到了这些天外人的地方，捏碎定标，我们自由办法过去。”“这天外人倒是聪明的很，已凡人之躯掌控空间法则。”“不，不，不，他们叫黑洞跃迁。”其余几个老者不说话，一副愿闻其详的模样。就见这叫空老的老者，手指轻点。面前茶碗飘起，钻入一黑洞之中。再出现已是在另一边。“这和我等瞬移有何不同？”“本质上并无不同，我等修的是法则，修士瞬移远近靠自身修为和对法则的领悟。而消耗则是灵力和神识，天外人是靠曲率驱动，就是主动压缩他们飞舟前方的时空，同时扩张后方的时空，制造一个移动的时空泡，天外人的飞舟本身静止在泡泡内，但时空法则带着这泡泡已一种看不见的形态移动。这种移动是看不见的，他能转瞬亿万万里。你等可明白？”其余几个老者思索片刻后，轻轻点了点头，口中呢喃着原来如此之类的话。空老一阵无语，又解释道：“就是原本是通过空间法则，但他们让空间法则通过他们！这下明白了吧。”“啊啊。啊啊。原来是这样。”“妙，当真是妙！”有一老者觉得不尽兴，又向空老发问道：“那我等消耗灵力与神识，且肉身需得承受瞬移带来的压迫感，那那些天外人如何承受的？”“这就又涉及到另一法则，因果法则。就是这些天外人说的量子。”几个老者倒吸一口凉气，似懂非懂的点了点头。空老本想细说这天外的量子，但见面前这几个千多岁的老东西，一副天真烂漫的模样。一时噎住。“你等只需准备好我说的东西就行！其余不用多管！”随后一甩袖子，消失不见。剩下的几个老者对视一眼。尴尬的互相笑了笑“喝茶，喝茶。”（脑子要炸了，你们明白了吗？）24日二更周老三是个邪修。修为还行，算是他周氏有家谱以来几百年里最高修为了。如果家族还认他的话。筑基初期。59岁卡着线晋升的筑基，到如今165岁了，还是个初期。但周老三知足了。他这辈子能筑基就已经是赚了。今日周老三向往常一样，守在一个破岛上，等着好心人来给他送资源。周老三做了很多假的寻宝图，每张都不一样，有的是似是而非的写着几句话，这是骗那些有脑子的。有的就写的很明了，直接说这里有宝藏，快来取。这是骗那些没脑子的。周老三也不急，就坐在隐蔽处等着。眼见太阳都要落山，周老三又是叹息一声，暗想着现在的修士警惕性太高了。可周老三刚想动，就听见了动静，急忙趴下，将敛息术用到极致。这么多年不死，靠的就是这个。“少校！你确定你翻译的对吗？”“我的ai翻译是比你们高级的。我翻译了很多次，上面的大致意思就是这个岛上有宝藏。“”可是少校，我们随便从海里捡到一个瓶子，里面就有个藏宝图是不是太巧了？”“你懂什么，我们这是倒霉到极致，开始转运了，我一定要拿到宝藏，成为这个星球土著一样的超能力者，为我们死去的兄弟报仇。”伊森驾驶着改进型的女武神机甲，带着两个机甲小队降落在一座荒岛上。这次伊森学聪明了，降落第一件事，就是开启隐身模式，开启量子护盾。几个隐蔽措施一弄，一般人从上面飞过还真注意不到下面有个飞船。出去采集资源，也是用非常小心谨慎的方式。就像这次来找宝藏，伊森就带了两个精锐小队。趁着天黑，才过来。“少校，热成像扫描过了，这座岛除了小动物，没有土著。”伊森点了点头。开始搜寻起来。岛不大，很快队员就发现了不对劲。一处山洞里发现了特殊的能量场。“少校，这里的形成了个能量场！自行约束成为了门。”伊森走过来，用ai扫描，片刻后，智能ai就给出了十七中打开方式。“你还别说，有时候我真的无法理解，为什么他们连最基础的力学都不明白，做出的东西，却一次又一次和宇宙超能物物理这么相似。”伊森从后背抽出一把合金战刀，一刀将山洞洞顶部削去。17米的机甲进不去矮小的山洞，削去洞顶也是破解能量场的办法之一。伊森拿到了几本薄薄的书册，和几颗发着微光的石头。“这是钻石吗？少校。”“不像，带回去在研究吧，这几本书应该是什么秘籍之类的。”“那我们都能成为土著这样的超能力者吗？”“谁知道呢，我们这次一定要在那个岛上，躲到帝国的舰队进攻这片大陆！”声音渐渐的远了，周老三依旧趴着地下一动也不敢动。直直的等了两个时辰，不见那些傀儡回来，才敢起身。那么高，那么大的傀儡。周老三这辈子是第一次见。对面是什么人不重要，重要的是周老三知道自己要立功了。一共九个傀儡，加上他们留守的撑死天十多个筑基修士。这么富裕，开这么大的傀儡，敢到邪魔群岛来，也不打听一下邪魔群岛归谁照着。青元界第一邪派，血刀门就在此处难道不知吗？等着吧，那灵石上有自己印记，这次定要说服老祖，全门派出动，拿下这些个肥羊！6.25第一更“师兄，好像到了。”梁山伯累的不想说话，他从没有想过，这些天外人为什么能把人压榨成这样。“到哪了？”“到了个什么蔷薇星了，我看书上说，蔷薇星是他们天外人国家里最美丽的星球。”“我们会在这下船吗？”梁山伯坐起来。“不知道，不如直接溜出去？”正当二人犹豫要不要提前溜下船时。那个这十几天来另两人恐惧的无人机又飞了过来。依旧是嗓音甜美的电子音。“亲爱的FO0653-495-罗密欧，FO-0653-565朱丽叶，感谢你们一直以来在蔷薇号上辛苦的付出，蔷薇号的整洁与美丽，离不开你们的努力。但F区卫生司经慎重评估，认为我们提供的岗位可能无法充分发挥二位的潜力，与才能。会严重阻碍二位的未来发展，所以不得不痛心的向帝国社会输送二位人才。希望未来你们能找到更匹配的舞台，谢谢，另，请于三个小时内离开蔷薇号，我们贴心的为您提供了地图。”“师妹，这叽里咕噜一大堆又说啥呢。”祝英台帝国语已经学的七七八八，正常交流是没问题的。但这小东西，今天说了一大堆到底是啥意思，心里也没底。“它说让我们下船。我们好像被开革了。”“开革了？为啥？”“不知道，说是我们干这个大材小用了。”梁山伯点了点头，这么多天，这个小东西就这一句还算是人话。师兄妹两人，啥也没收拾，大摇大摆的顺着地图指引，转了十几次音速列车。来到一个大广场上。此时大广场已经站满了人，都是等着下车的。不过看起来，好像不太高兴。师兄妹两个自是不会管这些天外人怎样，兴奋的等在广场上。十多分钟，侧边的镀振合金门缓缓向下打开。映入所有人面前的是一个长长的廊桥。这是接驳飞船。全密闭空间，看不见外面。想想也是，军港的位置被这些平民知道还得了。师兄妹两个这些天也算是吃过见过，没有像乡巴佬一样四处张望。跟着人流挤进了飞舟车厢。站票，没有座位，只有顶上一排排的金属拉手。所有人像是沙丁鱼一样挤在一起。被一趟又一趟的送往蔷薇星太空北港。也就一个多小时，沙丁鱼接驳车将四千多名蔷薇号优化人员送到了太空港。港口位于一颗人造星球上，大大小小的太空船坞，一眼望不到边际，每一秒都有飞船落下，又每一秒都有飞船起飞。吞吐量巨大。“师妹，好多人啊，这就是天外人的星球吗？”“不是！你看下面。”梁山伯此时站在一块透明玻璃上，从此处向下看去，除了漆黑的星空，就是一颗美丽的粉蓝星球。粉蓝交织，如梦如幻，在漆黑的夜空里如同闪耀的宝石。耀眼夺目。“师兄，别看了，正事要紧，要是没有灵气，我们还得去下一个星球。”梁山伯反应过来。点了点头，同师妹一起排队过安检买票。接待的售票员很热心，师兄妹这一路上哪有人和他们这样温柔的说过话，都在心里感慨着天外人情绪价值这一块做的确实比青元界好。而且票价才300一个人，还有座位。两人又是折腾了半天，快天黑了才落在蔷薇星首府“奥瑞利亚”的东港。其实还没落地，二人就已经感知到这是一颗没有灵气的星球。也没心思逛逛，要是找不到有灵气的星球，二人得一辈子困在这，最后沦为凡人。当即就准备买票回太空港，再去下一个星球。“什么？去太空港一个人一万五千八？”祝英台以为自己没听清。又问了一次。对面的智能机器人充满感情的又重复了一次。“师兄，咱们先去打工吧，咱两钱不够。”“啊？”——晚点还有625-二更“真美啊”即使是在三维立体里看了无数次，帝国第一舰队所有官兵，都情不自禁的发出一声感叹。确实，青元界的陆地与海洋，并非那种不变的绿色或蓝色，而是一种流动的，深邃的珐琅质感，甚至从同一个角度，不同人所看到的美还是不一样的。极地上空那缓慢旋转的大气漩涡，如同星球的冠冕，散发出月白与冰蓝的寒光。第一舰队早就在来时的路上演练过无数次，如何以微小的代价拿下帝国的未来。“以点破面！”这是五星上将麦卡·黎波里按着自己过往战无不胜的经验，想出的进攻策略。用巨大的武力震慑住敌人，摧毁敌人的中枢政权，打乱敌人的组织系统，切断敌人的重要枢纽。在演练中，这一步需要五天。然后执行第二步，“物资封锁与基础破坏”让这颗星球上没有一颗粮食能吃，没有一条路可走。这步时间长点，但十天也足够了。最后一步，“以夷制夷”拉拢投机分子，用土著去管理土著。用土著去剿灭土著，达到转移仇恨的目的。到了这里，基本上就已经宣告，这颗星球属于帝国了。这一步的时间取决于这颗星球的土著，不在帝国的控制范围内，但要是进度过慢，帝国也不介意亲自下场。“好了，战略制定的再好，也需要一步一步的去执行，诸位都是帝国百战老将，多的话我不在说，五天之后，我在旗舰准备好盛大的胜利酒会，等着诸位！”“好了，开战在即，我这里没备午餐，诸位请回。”蹭的一声，会议室里几十个将军整齐划一的站起身，向着麦卡上将敬礼，随后手托着军帽，依次而出。麦卡椅子翻转，身后巨大的玻璃上显示着青元星的全貌，麦卡眼神深邃，可手指的抖动，还是出卖了他此刻心里的紧张。只是无人看到就是。随着各部指挥官的一一就位，占据一整片星空的第一舰队开始分散，三艘泰坦级（Titan-class）太空母舰，一分为三，除了主旗舰，麦卡所在的【霍尔莫斯号】没动之外，另外两艘【拉戈那克号】【厄瑞玻斯号】各自带着自己的编队向着左右进发。左侧的【厄瑞玻斯号】庞大的腹部装甲迫不及待的滑开，十七根口径5700000mm的超能粒子主炮，从舰体内滑出，伸展，深紫色的闪电，时不时在炮口汇集。而内部的登陆弹射坞，金属的机械臂在对上百艘登陆舰坐着最后的调试。身边的三艘无畏级（Dreadnought-class）-主力战列舰侧舷的装甲板如龙鳞一般向上翻起，一根根闪着寒光的粒子矩阵炮，将炮口对准下面那美丽的星球。无数编队舰船，按着既定的命令，执行着自己该做的任务。第一舰队此时如同一台精密的仪器每一个步骤都做的完美无暇。恐怖，肃杀的气氛开始蔓延在这寂静的星空。令人胆寒。0626第一更乾元道宫。掌教宋时元闭关。代掌教李飞仙处理着道宫大小事务。此时李飞仙正在大殿中坐班。“外出弟子可曾全部回来了？”李飞仙问着下首十多个分管具体事务的紫府弟子。“禀掌教，除王师伯带出去的人之外，其余无任务弟子尽皆回归道宫。”李飞仙点了点头，毫不在意王文在外面的安危。在西域，或者说整个青元界，元婴不出手的情况下，没人能留得住王文。这么长时间不回来，定是有事耽搁，总不能和那边的海族打起来吧。“练气弟子呢，可曾全部安排进入秘境？”李飞仙又问到另一件事。“禀掌教，道宫练气弟子共七千四百二十四人，除去镇守凡间的一千四百名弟子之外，其余弟子已全部按修为分到了五个秘境内，各有一紫府执事照看。”乾元道宫是整个青元界唯一没有杂役弟子的宗门。所有事情都是弟子自己干，没有谁伺候谁一说。李飞仙点点头，又看向面前的玉简。这是师兄闭关前交给他的必须要做的几件事。“大阵呢？大阵各处阵脚可准备妥当了？”“禀掌教，【九霄玄穹乾元星斗大阵】已按照掌教谕令全力运转，365处虚阵角，皆是筑基后期弟子驻守，108处实阵角已有紫府同门带筑基弟子前往驻守，剩余36处主主阵角则各有金丹师叔带着紫府同门镇守。”李飞仙满意的看了眼座下的各个紫府，师兄调教的这些个内务长老，做事当真令人省心。又看了眼玉简。见也无旁的事，挥挥手，示意下面的紫府都退去。其实到了金丹这个层次，门内基本上已经对他没有秘密了。宋飞仙自是知道，门内如此作态是为了防范那天外人的飞舟从虚空打下的炮。可这总是被动防御，怎么能行。有大阵的地方可挡，没有大阵的地方呢。那些凡人之地怎么办？就看着被天外的飞弹打成一片片白地吗？长此以往，也不用等天外人下来了，自己就先被耗死了。其实青元界修士到了紫府时，肉身可挡罡风，就可一直往上飞，飞到虚空中。不过越往上飞，自身灵气消耗的就越快。等真正到了虚空，一身灵力剩余不过二三。且虚空中用灵石或丹药补充灵气，留存更是少的可怜。这等情况下，就是想要攻出去，也会被天外人耗死在虚空。天外人那些器物，虽是凡人操控，但以数量耗死金丹修士，李飞仙觉得问题应该不大。“也不知我青元界几个老祖想出办法没有”李飞仙叹息一声，从虚空中抓出个酒壶，对着嘴里灌了一口。还没咽下去，就见外面原本漆黑的夜空突然亮如白昼。一道十多里长的蓝色光团带着恐怖的能量，从虚空中向着乾元道宫而来。李飞仙感到一丝不妙。身形飞速的在护山大阵各处阵点闪烁。检查各处可有疏忽大意的弟子。确认一切正常后，李飞仙平静的看着天上飞来的巨大光团，这等威势，李飞仙自认挡不下。恐怕只有门内那些元婴期的师叔师伯才方可一试。想及门内，李飞仙突然心底发寒，天外人如何得知乾元道宫位置的？怎的就打的如此之准？大阵开启，就是化神也找不到，天外人是如何知道的？已经来不及多思考，那蓝色光团已到近前。轰隆一声。在天上四五里的位置炸开。就见整个乾元道宫上方一道巨大的屏障一闪而过，而后渐渐凝实。这天外来的能量就撞在了这屏障上。屏障上数亿万的符文显现，密密麻麻的从各处向着被撞之处汇聚。抵挡这狂暴却不灵动的力量。这蓝色光团很长，一眼看不到头。持续不断的对着道宫上方屏障发起着冲击。逸散出去的能量，在道宫附近的山林炸开。掘地三百丈都不止。幸亏道宫附近万里都无凡人。不然也是一场凡人浩劫。其实不光李飞仙感到惊讶，在太空中的第一舰队，第一分舰队司令普尼尔也感到惊奇无比。因这个星球扫描不了，也就无法确定哪里是这颗星球的中枢。但展示帝国不可抵挡的力量，是征服这颗星球的第一步。所以，经过司令部的研究，决定从德尔少将手下失踪那只登陆船，登陆的地方作为宣扬帝国力量的突破口。第一分舰队，就捞到了这个活。旗舰【厄瑞玻斯号】的十七门主炮，经过调试后，巨大且冰冷的炮口就瞄准了这片区域。既然看不见，那就对着大陆中心和沿海打，准没错。普尼尔下令开炮之后，【厄瑞玻斯号】共计十七门的泰坦级战舰主炮。积蓄了半天能量，打出了十五个寂寞。仅仅两门主炮瞄准的位置给出了防御反馈。其余十五炮，毛都没打着。只是在这颗星球上留下了十五个深深的峡谷。而其中就有一门歪打正着，打到了乾元道宫的护山大阵。还打的十分准。真要说起来，这得怪乾元道宫祖师，谁让他当年非要在西域大洲的正中心建造道宫呢。在太空坐镇二线的第一舰队总司令麦卡在三维光幕里看到这一幕。敏锐的发现，这会是一个机会。一个将这颗星球有生力量彻底绞杀的机会。与其分散力量，去赌一个地方有没有土著，不如集中在一起，先把已知的两个点拿下。当即便下令全军，集合起来，从这两个点全力进攻，消灭能看到的所有土著反抗者。战争的号角一旦响起，就不不会轻易的停下，第一舰队从两个方向共计一千七百艘的登陆舰，拖着蓝色的尾焰，在战列舰矩阵粒子炮的掩护下，朝着乾元道宫势力范围扑来。登陆舰进入大气层后，舱门大开，如同蜂群一般的空天战机迫不及待的冲了出来，携带着无数弹药开始消灭能看到的一切建筑。每条战线都将近拉长到五万里。如遮天蔽日的蝗虫。吞噬着所有。（晚点还有，然后，本人郑重声明！每个字都是我一个字一个敲的，不存在评论区所说，ai写的）0625第二更“卡文，我没看错吗？这居然是一个农耕文明？”“我的天！我们这次要发了！”“卡文，我们要轰炸他们吗？他们看起来好像非常恐惧！”“享受这种恐惧吧，土著们！”卡文此刻只有要即将发财后的兴奋，和一种欺负弱者的快感。剃刀（razor）空天战机之所以称为剃刀，就是因为他们轰炸过的地方，就像是剃刀剃过头皮的样子，寸草不生。无数枚动能贯穿炸弹，从机腹中落下，调整姿态后向着地面冲去。它不会管是富丽堂皇的大宅还是四面漏风的贫家，它一视同仁，所有所有，都会在炸弹撞击地面那一刻被撕的粉碎。“师兄！别去，我们打不过它！”乾元道宫治下平安郡，驻守仙师陆青远此时双目通红，却被身后两个师弟死死拦着。“师兄，你才练气后期，你连追都追不上，去不就是送死吗？我们只需在此静待道宫来人，何必要去做无谓的挣扎！”陆青远一把将两个练气中期的师弟推开。“你们他妈的没种，就在此处窝着，像个狗一样藏在地洞里。老子不愿当狗，老子是平安郡仙师，是道宫钦命的守护使，我不去！让那些凡人去吗！”陆青远说完看到不看两人一眼，向着高处飞速掠去，手指掐诀，一跃而起，而后一炳飞剑从储物袋中飞出，稳稳的带着陆青远飞上了天空。下方无数的凡人，好像看到了希望，逃跑时的气力又多了几分。“那是什么？”卡文从剃刀轰炸机的观察窗看到一个黑点渐渐的升高朝着自己飞来。“那人好像站在一个冷兵器上。”同区域的飞行员观察后在频道里说道。“打下来吗？”“打试试看吧，那是什么飞行玩具吗？帝国也有个人用的飞行装备，如民用的小型核聚变飞行马甲，便宜点的也有喷气式的撒农药飞行器，这种冷兵器形状的倒是第一次见。剃刀轰炸机自带的扫射机枪仅仅是个125mm的小口径速射机关枪，虽然口径小点，但胜在射速快。两架剃刀交替射击，制造出交叉火网向着陆青远而去。陆青远虽然年轻，才二十七岁，但这些年与邪修斗法的经验也不少，虽没见过这种打法，但到底是不慌的。手掌一翻，三张符箓出现在指尖。口中呢喃，就见原本站在剑飞行的陆青远已经不见。一张小挪移符出现在更高的空中，手上两张符箓向前一甩，化为两团火球向着前方飞去。但速度终归是慢了。”我操，这个土著真的会飞，还会喷火！”“抓活的！一定要抓活的！”“我们他妈的轰炸机怎么抓的，将画面传输给少校，让他把飞行机甲派下来！”陆青远艰难的在十几架轰炸机前周旋，要不是想要抓活的，陆青远早就被打成了筛子。从天边径直飞来一个小队机甲，一架‘穹顶巡卫，三架夜莺，和一架天袭者。标准的帝国机甲小队编制。飞行机甲属于帝国的尖端科技，目前也仅仅列装了皇家禁卫军和第一舰队。其它的帝国军队有的甚至连听都没听过。陆青远长时间的空中作战，已经有些灵气不足，被高速俯冲过来的天袭者机甲一下撞飞了数千米。本以为此生就此结束的陆青远，不甘的闭上了眼。他不忍心看到地下的凡人那绝望的呼喊。陆青远觉得时间过的好慢。慢到他的思绪都有些迟钝。时间？变慢？陆青远睁开眼，他发现自己被一个修为看不透的人用法力缓慢托举着，但这个人穿的衣服陆青远认识。金红白云仙鹤道袍，胸口绣三纹烈阳。这是乾元道宫紫府期才能穿的礼服。非重大场合不穿的。陆青远本想行礼，可浑身的阵痛让他动弹不得。“歇着吧，一个练气的小弟子能有这样的勇气与智慧，真是我道宫之幸。””谢师叔祖救命之恩！”陆青远虽不能动，但嘴巴还是能说的。“不是我救你的，是宋执师叔，哦，你要叫太师叔祖了。哈哈哈”来的这群人自是王文，在海边同海族打来好几天，最后被海族元婴阻拦。草草结束了这场金丹混战。还好，除了死了几个海族紫府，在没其他损失。王文见海族走了，那个天外飞舟也跑了，只得带着人在附近继续搜寻，以期能在找到线索。好巧不巧，泰坦主炮有一发正好落在王文附近，要不是王文飞舟开的快，估计就得翻船了。王文这才后知后觉，天外人来了，来的比预想要快。随即拼命的催动飞舟，回道宫去。路上遇到了天外人的西线部队。乾元道宫附近几万里称为东线战场，挡住第二发主炮的凡人区称为西线战场，帝国第一舰队一分为二，全力进攻。（我也想上班摸鱼，关键他事情来了啊，事情来了我不做，最后还是我的事情。明日尽量写得多一点。第一次正式碰撞要开始了）再次谢谢你们的点赞关注收藏喜欢评论", "domain": "www.zhihu.com", "visitTime": 1750960770624, "accessCount": 3, "lastUpdated": 1750960786430, "contentStatus": "extracted", "lastExtractionAttempt": 1750960786408}, {"id": "c87273ef-4d2b-4a00-a3ad-27b011d3b9ed", "url": "https://www.reddit.com/r/n8n/comments/1kt8ag5/just_closed_a_35000_deal_with_a_law_firm/?share_id=cy9PQgtFL1UCalXIaIEug&utm_content=2&utm_medium=ios_app&utm_name=ioscss&utm_source=share&utm_term=1", "title": "<PERSON> closed a $35,000 deal with a law firm", "content": "Excited to write that today i closed my biggest Ai deal yet, a $35,000 deal with a mid-sized law firm to build and deploy a fully private AI setup using LLaMA 3 70B completely self-hosted, no third-party APIs, and compliant with strict legal data policies and we’re using n8n to connect the entire thing. This will be a full blown internal system. Pretty much their own GPT4 style legal analyst, trained to process internal case law, filings, and contracts, answer complex questions, and summarize docs but with zero exposure to OpenAI or Anthropic. They needed control, privacy and automation and had no interest in hiring an internal AI team. Tech stack We’ll be using: LLaMA 3 70B (quantized + accelerated using vLLM) Hosted privately on CoreWeave using dual A100 GPUs. ChromaDB as the vector store to handle document embedding and retrieval LlamaIndex to power a RAG pipeline, enabling real-time Q&A over their case files n8n as the glue to automate everything from doc uploads to Slack/email notifications A simple but clean Streamlit-based web UI for their staff to chat with the model, ask questions, and get summaries instantly All of it wrapped in a secure setup with JWT auth, IP access controls, and full audit logging How n8n will make this 10x easier We won’t write a traditional backend for this. Instead, we’ll use n8n, which gives us/them the flexibility to: Monitor a shared Google Drive folder for new legal documents Automatically convert, chunk, and embed those docs into ChromaDB Kick off a summary job with the LLM and route results to the right paralegal via Slack or email Handle incoming staff questions (via form or chat UI) and respond with real-time LLM-generated answers Log everything for compliance, reporting, and later audit The firm’s paralegals will be able to drop in new documents and have summaries + search access within minutes, without ever calling IT or opening a support ticket. And they can also edit or extend the workflows in n8n themselves. Also, I think $35K is maybe Underpriced because this is a system that saves them dozens of hours per week. Compared to hiring even one full-time AI engineer or automating this with a dev team, $35,000 is kind of a deal. Once deployed they’ll pay ~$1,200/month in GPU hosting and have an in-house, private legal AI engine that’s fully theirs. From the law firm’s perspective, this is an easy investment that’ll pay itself back in one quarter. And few things I noticed on this deal Privacy and control are the new killer features. Many businesses can’t upload their documents to OpenAI/chatgpt due to privacy and they love the concept of a private llm and more firms are realizing they want AI power without giving up data sovereignty. LLaMA 3 70B is production-ready when deployed properly — especially for professional use cases like law. Clients don’t want to build all this themselves. They want someone to make it work and keep it simple. n8n is criminally underrated for LLM-based workflow automation. It makes this entire project modular, flexible, and fast. I plan on productizing this into a “PrivateGPT for Professionals” and will offer it for law, finance, and healthcare firms. The demand is real and growing. Has anyone else built anything at this scope? Happy to chat/answer any questions in the thread.", "domain": "www.reddit.com", "visitTime": 1750960782179, "accessCount": 2, "lastUpdated": 1750960783138, "contentStatus": "extracted", "lastExtractionAttempt": 1750960782975}, {"id": "e23dc62e-1b4c-4391-a90f-3fd363a6d33d", "url": "https://github.com/anthropics/prompt-eng-interactive-tutorial", "title": "anthropics/prompt-eng-interactive-tutorial: Anthropic's Interactive Prompt Engineering Tutorial", "content": "Welcome to Anthropic's Prompt Engineering Interactive Tutorial Course introduction and goals This course is intended to provide you with a comprehensive step-by-step understanding of how to engineer optimal prompts within <PERSON>. After completing this course, you will be able to: Master the basic structure of a good prompt Recognize common failure modes and learn the '80/20' techniques to address them Understand <PERSON>'s strengths and weaknesses Build strong prompts from scratch for common use cases Course structure and content This course is structured to allow you many chances to practice writing and troubleshooting prompts yourself. The course is broken up into 9 chapters with accompanying exercises, as well as an appendix of even more advanced methods. It is intended for you to work through the course in chapter order. Each lesson has an \"Example Playground\" area at the bottom where you are free to experiment with the examples in the lesson and see for yourself how changing prompts can change <PERSON>'s responses. There is also an answer key. Note: This tutorial uses our smallest, fastest, and cheapest model, Claude 3 Haiku. Anthropic has two other models, Claude 3 Sonnet and Claude 3 Opus, which are more intelligent than Haiku, with <PERSON> being the most intelligent. This tutorial also exists on Google Sheets using Anthrop<PERSON>'s Claude for Sheets extension. We recommend using that version as it is more user friendly. When you are ready to begin, go to 01_Basic Prompt Structure to proceed. Table of Contents Each chapter consists of a lesson and a set of exercises. Beginner Chapter 1: Basic Prompt Structure Chapter 2: Being Clear and Direct Chapter 3: Assigning Roles Intermediate Chapter 4: Separating Data from Instructions Chapter 5: Formatting Output & Speaking for Claude Chapter 6: Precognition (Thinking Step by Step) Chapter 7: Using Examples Advanced Chapter 8: Avoiding Hallucinations Chapter 9: Building Complex Prompts (Industry Use Cases) Complex Prompts from Scratch - Chatbot Complex Prompts for Legal Services Exercise: Complex Prompts for Financial Services Exercise: Complex Prompts for Coding Congratulations & Next Steps Appendix: Beyond Standard Prompting Chaining Prompts Tool Use Search & Retrieval", "domain": "github.com", "visitTime": 1750960774567, "accessCount": 2, "lastUpdated": 1750960774612, "contentStatus": "extracted", "lastExtractionAttempt": 1750960774567}, {"id": "eb7f8c24-0506-4ee0-9a54-e8918e7e1ea9", "url": "https://www.zhihu.com/", "title": "首页 - 知乎", "content": "关注推荐热榜专栏一本正经和你谈情： `“我草，这颗星球真美啊！” “是啊，而且大的让人心生畏惧。” 太空中，一艘泰坦级太空母舰漂浮璀璨星辰里。 船舷右侧挤满了人。 透过玻璃从…邓凳： 你是一名应用开发者，在旁人眼中，你动动指尖、敲几段代码，就能改变世界，迅速实现财富自由，走上人生巅峰。但关起门来只有自己才知道，背后是凌晨三点工位上泡面与咖啡齐飞，是米诺地尔都拯救不了的地中海发…莫也： 这事吧，其中隐含兔子的理政经验、治国传统。 不止是禁酒，而是对于几乎所有重要问题，兔子的治理逻辑都有规律可循，具体拆解就是分为三个阶段： 1，放任…大型动物： 看了一遍其他人的回答，我有个不同的看法，那就是之所以能火的原因就是复盘。 因为中国人是喜欢复盘的，这次吃了亏，下次就想着找补回来，在外面和人…马大白话Max： 陆奇刚来百度的时候，我还是一个百度外包员工。虽然是外包，但工作环境，工位都是和正式员工一起。 那会儿他弄了一个“新风会”，其中有一个环…Agent在落地应用中技术经验分享YWTAI： 一、概述 2025年被称为Agent元年，很多与Agent相关的应用、框架和协议在如火如荼的迭代升级。但我的感受是，市面上很多ToC的Agent应用看起来效果很惊艳，但真正落地ToB场景产生…某bit： 这个问题下，估计没有比我这个用法更逆天的…… 如果你是个视频创作者，有个歪门邪道： 某些时候，你可以用折叠屏替代运动相机…… 额，贵价的手机替代便宜的运动相机，这个提案有点颠，但它真的有实用场景，…Unbelos-K： 英雄被写成诗 只剩爱与勇敢 诗凝成画 少了悲欢 …… 故事被谱成曲 全是分合聚散 曲唱成歌 不许平凡 …… 他们被说成书 区区八十一难 书刻成…一根藤上七朵花： 没发育的男童，是没有性能力的，放在女人堆里，无论是对女人还是对男童都没有危害。 反而女童进男浴室就不行了。女童具有完整的性能力（伤不伤…123写好故事，赚高收益！已有100万+作者入驻去投稿​推荐关注123", "domain": "www.zhihu.com", "visitTime": 1750957000517, "accessCount": 28, "lastUpdated": 1750960761481, "contentStatus": "extracted", "lastExtractionAttempt": 1750960761450}, {"id": "f40fddf3-670c-4e4d-80f7-5abefb1a128b", "url": "https://zhuanlan.zhihu.com/p/1921588886511018952", "title": "2025香港高才通计划申请审批时间要多久？附diy申请全攻略！", "content": "2025香港高才申请审批时间要多久？附diy申请全攻略！根据香港入境事务处最新政策，2025年香港高才通计划的审批时间已进一步优化。官方数据显示，在材料完整且无需补件的情况下，最快可在4周内完成审批，而一般审批周期为1-3个月左右。从银河集团的申请获批案例来看，A类申请（年收入达250万港元或以上）因材料的复杂性，审批速度普遍在1-3个月左右。而B类（全球名校毕业+3年工作经验）和C类（全球名校毕业+工作经验不足3年，限额1万/年）申请人，若材料齐全，通常在1个月左右可完成审批。需注意的是，审批时间受材料完整性、政策变动及申请高峰期影响。例如，2025年6月因续签高峰，部分申请人的审批周期延长至3个月。因此，建议有意向申请高才的朋友，可以提前准备好相关材料。如果你对香港身份感兴趣，可以先做个自测，看看自己适合哪种申请方式（优才/高才/专才/留学），后续还会有规划师教你如何申请↓↓↓一、香港高才申请条件香港高才通计划分为A、B、C三类，分别针对高收入人群、名校毕业生及年轻潜力人才。以下为三类申请的核心条件及材料要求：1、A类：高收入人群——年收入250万港元门槛在紧接申请前一年，全年收入达港币250万元或以上（或等值外币）的人士。收入要求：在提交申请前12个月内，合法收入达到港币250万元或以上。收入类型：包括薪金、津贴、股票期权和业务利润等，但个人投资所得不计入内。适用人群：企业高管、创业者、高薪专业人士等。2、B类：名校毕业生——合资格名校本科生+3年工作经验获合资格大学颁授学士学位、并在紧接申请前五年内累积至少三年工作经验的人士。学历要求：持有入境处指定的合资格大学的学士学位。工作经验：在申请前5年内，累计至少3年工作经验。适用人群：拥有名校背景并具备一定工作经验的专业人士。3、C类：年轻潜力人才——合资格名校本科生+工作经验不足3年在紧接申请前五年内获合资格大学颁授学士学位，但工作经验少于三年的人士。学历要求：同样需持有合资格大学的学士学位。工作经验：在申请前5年内，工作经验少于3年，包括应届毕业生。名额限制：每年1万个名额，先到先得。适用人群：刚毕业或工作经验不足的名校毕业生。注意事项：C类申请不适用于在香港修读全日制课程获得学士学位的非本地学生；需提供学位证书、学信网认证及实习证明（如有）。全球合资格院校一共199所，具体如下：如果你对香港身份感兴趣，可以先做个自测，看看自己适合哪种申请方式（优才/高才/专才/留学），后续还会有规划师教你如何申请↓↓↓二、香港高才材料清单1、主申请人资料准备①赴港工作同意书：从香港入境事务处官网下载ID(C)1026表格，打印最后一页，即赴港工作同意书，找单位盖章后制成电子版。【注意】该同意书是申请流程中最大的卡点，如果现公司不愿意提供盖章，可以尝试联系当地人才市场盖章（视各地人才市场操作流程不同，难度不同）以深圳为例，可在“粤省事”公众号搜索“档案所在地查询”，实名认证后找到档案存放地，然后通过“深圳人社”公众号预约业务办理，建议预约前先打电话询问是否能盖章。②港澳通行证/身份证（正反面）：电子版。若没有通行证，请尽快办理。③证件照：准备一张电子版近照（JEPG格式，无底色要求，无尺寸要求）。④学位证书（全球百强）：电子版证书。中国院校：毕业证、学位证、学位认证报告国外院校：成绩单、学位证、中留服学位认证报告。⑤在职证明/离职证明（B类&C类）：加盖公章，并制成电子版。⑥完税证明（A类）：以深圳为例：选择常用业务 -> 纳税记录开具 -> 打开特色应用频道 -> 选择深圳 -> 纳税清单打印（深圳）选择月份区间，比如202301-202312，可查询总收入和缴税明细，下载即可。2、受养人（配偶和子女）资料准备【注意】强烈建议跟主申请人一并提交申请，否则后续走补申请流程非常麻烦。港澳通行证：受养人的正反面电子版。户口本：受养人的户口本电子版。出生证：小孩的出生证电子版。结婚证：配偶的结婚证电子版。三、香港高才申请流程1、评估自己是否符合申请条件确定自己符合以上三类申请条件中的一类，确保在提交申请前满足相关要求。如果你对香港身份感兴趣，可以先做个自测，看看自己适合哪种申请方式（优才/高才/专才/留学），后续还会有规划师教你如何申请↓↓↓2、准备文件和材料判断自己符合高才通条件之后，可以着手准备申请材料，申请材料的齐全与否是决定申请通过的决定性因素。3、网上递交申请确定符合高才通申请要求并准备好相关的材料后，可以在线上递交申请，如果你是找中介公司协助的话，一般由中介递交申请。4、获得批准和缴费香港入境处审核人才递交的申请材料，没有问题后会发放获批函，申请人需要在一定时间内进行缴费，缴费后获得【电子签证】。5、办理逗留签注(D签)拿到【电子签证】后还需要办理逗留D签证，这个后续赴港激活身份有用，您可以在入境前或入境后到入境事务处办理，办理时需要携带个人身份证、电子签、港澳通行证等相关证件。6、领取小白条/激活身份申请人赴港的时候，入境处会发放一张小白条，上面有你的个人信息和入境日期，7年时间从这一天开始计算，记住需要妥善保管小白条，后续续签也还需要用到，赴港后，就代表已经激活签证了。7、办理香港居民身份证高才通要求申请人在首次登陆入境香港的30天内办理香港身份证，一般需要先预约抢号，抢到号之后再赴港办理，一般10个工作日内就能领取（可自己领取也可以找人代领）。四、香港高才diy还是中介？1、自己diy申请可能面临的挑战①对政策的了解不够深入：香港入境事务处的政策随时可能有变化，申请者可能无法迅速掌握最新信息，导致提交的材料不符合要求。②准备材料不够全面：申请过程需要准备大量证明文件，如收入证明、学历认证等。如果材料不齐全，或不符合规定格式，可能导致申请被拒或需要多次补充。③时间成本较高：独自研究政策、准备相关材料和处理突发问题，会消耗大量时间和精力。④缺乏专业建议：无法准确评估自己的条件，可能错失最佳的申请方案。2、中介代办4大核心优势①政策敏感性与风险规避政策更新频繁：2025年起，香港入境处引入AI风控系统，严查“薪资卡线”（如月薪仅2万港元）及空壳公司。例如，某A类申请人因公司利润与持股比例不匹配被拒，中介可提前通过模拟审核规避风险。材料合规性：中介熟悉材料格式、翻译公证及补件要求，避免因细节疏漏导致拒批。②时间成本与效率优化材料准备周期：DIY申请人需自行研究政策、整理材料，耗时1-3个月；中介可提供标准化模板，将周期缩短至1-2周。突发情况处理：如入境处要求补充材料，中介可代为沟通并协助准备，避免申请人因工作繁忙延误进度。③续签与永居规划续签策略：中介可根据申请人背景设计续签路径，如通过自雇创业（需雇佣本地员工或年利得税≥10万港元）或在香港盘店铺做轻生意，省时省事，续签无忧！永居申请：中介可协助准备“通常性居住”证明，如租房合同、水电费账单、子女在港入学记录等。④一站式服务与情感支持落地服务：中介可协助办理香港银行卡、租房、子女入学等，帮助申请人快速适应香港生活。心理支持：定居香港的过程充满不确定性，中介可通过客户案例分享缓解焦虑，提供全程陪伴。银河集团能提供“一站式”服务，拥有完整的配套后续服务。很多申请人正是看中了后续的续签规划及在港生活服务而选择银河。毕竟拿到香港身份后，如何让香港身份的优势最大化，充分利用香港香港身份的福利，才是最终的目的。银河为不仅仅为客户提供香港身份办理业务，还提供如规划子女教育、开香港公司、续签、办理驾照、房产、赴港产子、就业等赴港工作生活服务。重点是，银河集团拥有丰富的高才申请经验，获批案例丰富。毕竟公司成立了这么久，也具备一定客户资源，只有优秀的服务才能吸引到老客户介绍，在高才申请上，各种类型的客户银河都有服务过，感兴趣的朋友记得滴滴Ruby了解更多高才申请细节！银河集团专业提供香港身份申请、香港创业、做生意、香港公司注册、香港身份续签、香港驾照、香港房产等“一站式”服务，如果你需要专业人士定制你的香港身份申请全流程方案，或有香港创业、做生意需求，欢迎扫码添加微信咨询：", "domain": "zhuanlan.zhihu.com", "visitTime": 1750957020297, "accessCount": 2, "lastUpdated": 1750957020308, "contentStatus": "extracted", "lastExtractionAttempt": 1750957020295}], "blacklist": [], "metadata": {"totalPages": 14, "totalBlacklist": 0, "exportedBy": "Recall v2.0.0"}}