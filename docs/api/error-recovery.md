# Error Recovery System Documentation

## Overview

The Error Recovery System provides comprehensive error handling, retry mechanisms, and fallback strategies for the AJAX detection enhancement. It ensures system stability and graceful degradation when components fail.

## Architecture

### Core Components

1. **ErrorRecoverySystem** - Main error recovery coordinator
2. **SafeInterceptor** - Safe method interception with error boundaries
3. **Integration Modules** - Enhanced AjaxMonitor and EnhancedContentWatcher

## ErrorRecoverySystem API

### Configuration

```typescript
interface ErrorRecoveryConfig {
  debug?: boolean;              // Enable debug logging
  autoRecover?: boolean;        // Enable automatic recovery
  maxRetries?: number;          // Maximum retry attempts (default: 3)
  baseDelayMs?: number;         // Base delay for retry (default: 1000ms)
  maxDelayMs?: number;          // Maximum retry delay (default: 30000ms)
  circuitBreakerThreshold?: number; // Failure threshold for circuit breaker (default: 5)
}
```

### Methods

#### `handleError(error, component, context)`

Handles errors with automatic recovery strategies.

**Parameters:**
- `error` (Error) - The error to handle
- `component` (string) - Component name for logging
- `context` (any) - Additional context and recovery options

**Returns:** `Promise<RecoveryResult>`

**Recovery Strategies:**
1. **Exponential Backoff** - For transient failures
2. **Linear Retry** - For consistent failures  
3. **Fixed Interval** - For stable retry patterns
4. **Circuit Breaker** - Prevents cascade failures
5. **Degraded Mode** - Fallback functionality

#### `getErrorStats()`

Returns comprehensive error statistics.

**Returns:**
```typescript
{
  totalErrors: number;
  errorsByComponent: Map<string, number>;
  circuitBreakerStatus: Map<string, 'open' | 'closed' | 'half-open'>;
  degradedComponents: Set<string>;
}
```

#### `checkHealth()`

Performs system health check.

**Returns:**
```typescript
{
  healthy: boolean;
  issues: string[];
  recommendations: string[];
}
```

#### `exportErrorReport()`

Exports detailed error report for debugging.

**Returns:** JSON string with complete error history

## SafeInterceptor API

### Methods

#### `intercept(config)`

Safely intercepts methods with error boundaries.

**Parameters:**
```typescript
interface InterceptionConfig {
  target: any;                  // Target object
  methodName: string;           // Method to intercept
  interceptor: Function;        // Interceptor function
  fallback?: Function;          // Fallback if interception fails
  preserveOriginal?: boolean;   // Keep original method reference
  onError?: (error: Error) => void; // Error handler
}
```

**Returns:** `InterceptionResult`

#### `restore(key)` / `restoreAll()`

Restores intercepted methods to their original state.

#### `getStats()`

Returns interception statistics and error counts.

## Helper Functions

### `createSafeXHRInterceptor(onRequest)`

Creates safe XMLHttpRequest interceptor.

**Parameters:**
- `onRequest` - Callback for request events

**Returns:** InterceptionResult with restore function

### `createSafeFetchInterceptor(onRequest, onCompletion)`

Creates safe Fetch API interceptor.

**Parameters:**
- `onRequest` - Callback for request start
- `onCompletion` - Callback for request completion

**Returns:** InterceptionResult with restore function

## Integration Examples

### AjaxMonitor Integration

```typescript
// Error recovery is automatically initialized
const ajaxMonitor = new AjaxMonitor({
  enableXHR: true,
  enableFetch: true,
  debug: true
});

// Start with error recovery
await ajaxMonitor.start();

// Check health status
const health = ajaxMonitor.getHealthStatus();
if (!health.healthy) {
  console.warn('AjaxMonitor health issues:', health.issues);
}

// Export error report if needed
const errorReport = ajaxMonitor.exportErrorReport();
```

### EnhancedContentWatcher Integration

```typescript
// Error recovery is built-in
const contentWatcher = new EnhancedContentWatcher({
  enableMutationObserver: true,
  enableIframeMonitoring: true,
  debug: true
});

// Safe start with automatic recovery
await contentWatcher.start();

// Monitor error statistics
const errorStats = contentWatcher.getErrorStats();
console.log('Error stats:', errorStats);
```

## Error Recovery Strategies

### 1. Retry Strategies

#### Exponential Backoff
- **Use Case**: Network errors, temporary failures
- **Pattern**: 1s, 2s, 4s, 8s, 16s...
- **Max Delay**: 30 seconds

#### Linear Retry
- **Use Case**: Resource constraints, consistent failures
- **Pattern**: 1s, 2s, 3s, 4s, 5s...
- **Progressive**: Steady increase

#### Fixed Interval
- **Use Case**: Stable environments, known recovery times
- **Pattern**: 2s, 2s, 2s, 2s...
- **Consistent**: Same delay between attempts

### 2. Circuit Breaker Pattern

Prevents system overload by "opening" when failure threshold is reached.

**States:**
- **Closed**: Normal operation
- **Open**: All requests fail immediately
- **Half-Open**: Test requests allowed

**Thresholds:**
- **Failure Count**: 5 consecutive failures
- **Timeout**: 30 seconds before half-open
- **Success Required**: 3 successful calls to close

### 3. Degraded Mode

When critical components fail, the system operates with reduced functionality:

- **AJAX Monitoring**: Falls back to periodic content checks
- **Mutation Observer**: Uses timer-based content detection
- **Iframe Monitoring**: Disables iframe content tracking
- **Shadow DOM**: Skips shadow DOM observation

## Error Types and Handling

### Common Error Scenarios

1. **Method Interception Failures**
   - Original method undefined/modified
   - Permission denied in strict contexts
   - **Recovery**: Safe fallback to original behavior

2. **Observer Creation Failures**
   - MutationObserver not supported
   - Security restrictions
   - **Recovery**: Timer-based polling fallback

3. **Cross-Origin Access**
   - iframe content access denied
   - Cross-domain restrictions
   - **Recovery**: External frame monitoring only

4. **Memory/Resource Constraints**
   - Too many observers active
   - High memory usage
   - **Recovery**: Selective monitoring disable

### Error Logging

All errors are logged with:
- **Timestamp**: When error occurred
- **Component**: Which component failed
- **Error Type**: Classification of error
- **Context**: Additional debugging information
- **Recovery Action**: What was attempted
- **Success**: Whether recovery succeeded

## Performance Considerations

### Error Recovery Overhead

- **Memory**: ~50KB for error tracking
- **CPU**: <1% additional overhead
- **Storage**: Error history limited to 100 entries

### Optimization Features

- **Error Deduplication**: Prevents spam logging
- **Smart Retry Delays**: Adaptive based on error patterns
- **Circuit Breaker**: Prevents resource waste
- **Cleanup**: Automatic old error purging

## Monitoring and Debugging

### Health Check Recommendations

Regular health checks help identify issues:

```typescript
// Check every 5 minutes
setInterval(() => {
  const health = ajaxMonitor.getHealthStatus();
  
  if (!health.healthy) {
    console.warn('System health issues detected:', health.issues);
    
    // Take corrective action
    health.recommendations.forEach(action => {
      console.log('Recommendation:', action);
    });
  }
}, 5 * 60 * 1000);
```

### Error Report Analysis

Error reports contain:
- **Error Patterns**: Most common failure types
- **Component Health**: Which components fail most
- **Recovery Success**: Effectiveness of strategies
- **Performance Impact**: Error handling overhead

### Debug Mode

Enable debug mode for detailed logging:

```typescript
const config = {
  debug: true,  // Enables verbose error logging
  autoRecover: true,
  maxRetries: 5
};
```

Debug output includes:
- Error occurrence timestamps
- Recovery strategy selection
- Retry attempt details
- Circuit breaker state changes
- Degraded mode activations

## Best Practices

### Implementation Guidelines

1. **Always Use Async Start**
   ```typescript
   // Good
   await ajaxMonitor.start();
   
   // Avoid
   ajaxMonitor.start(); // May miss recovery opportunities
   ```

2. **Monitor Health Regularly**
   ```typescript
   const health = component.checkHealth();
   if (!health.healthy) {
     // Take action based on recommendations
   }
   ```

3. **Handle Degraded Mode**
   ```typescript
   if (errorStats.degradedComponents.has('MutationObserver')) {
     // Adjust expectations or provide alternative UI
   }
   ```

4. **Export Reports for Issues**
   ```typescript
   // When users report problems
   const report = component.exportErrorReport();
   // Send report for analysis
   ```

### Configuration Recommendations

#### Development Environment
```typescript
{
  debug: true,
  autoRecover: true,
  maxRetries: 5,
  baseDelayMs: 100,  // Faster for testing
  circuitBreakerThreshold: 3
}
```

#### Production Environment
```typescript
{
  debug: false,
  autoRecover: true,
  maxRetries: 3,
  baseDelayMs: 1000,
  circuitBreakerThreshold: 5
}
```

#### High-Reliability Environment
```typescript
{
  debug: false,
  autoRecover: true,
  maxRetries: 7,
  baseDelayMs: 2000,
  maxDelayMs: 60000,
  circuitBreakerThreshold: 10
}
```

## Troubleshooting

### Common Issues

1. **High Error Rates**
   - Check browser compatibility
   - Verify extension permissions
   - Review website security policies

2. **Circuit Breaker Frequently Open**
   - Reduce failure threshold
   - Increase retry delays
   - Check for systematic issues

3. **Degraded Mode Always Active**
   - Review error patterns
   - Check browser console for details
   - Verify environment compatibility

4. **Memory Usage Growing**
   - Error history may be too large
   - Check for error logging loops
   - Consider reducing retention period

### Support Information

For technical support:
1. Export error report
2. Include browser version and extension details
3. Provide reproduction steps
4. Share health check results

The error recovery system is designed to be self-healing and provide maximum uptime even when individual components encounter issues.