### 第一部分：已澄清的决策点 (原歧义列表)**

根据我们的沟通，以下关键技术决策已经明确，并成为本开发计划的基石：

- **1. 页面正文提取策略:**
  - **已确认方案:** 我们将采用 Mozilla 的 `Readability.js` 库作为正文提取的核心实现，以确保从复杂的网页中高效、准确地分离出主要内容。
- **2. 核心搜索引擎库:**
  - **已确认方案:** 我们将采用轻量级的客户端模糊搜索库 `Fuse.js`。这将作为实现本地全文搜索和模糊匹配功能的技术核心。
- **3. 单页应用 (SPA) 内容更新机制:**
  - **已确认方案:** 为了兼容 SPA，系统将通过监听浏览器 `history` API 的变化（如 `pushState`）来触发对新视图内容的抓取和索引，并会加入防抖（debounce）机制以优化性能。
- **4. 功能范围限定:**
  - **已确认方案:** 本次开发计划**严格限制在纯本地功能**。所有与后端服务器、用户账户系统、云同步及付费订阅相关的功能（如 PRD 中的 REQ-007 云同步、商业指标等）均不在本次迭代范围内。

------

### **第二部分：结构化任务列表 (项目仪表盘)**

| **任务ID**   | **任务名称**                           | **状态 (Status)** | **所属功能/模块** | **任务类型** | **任务描述**                                                 | **完成标准/验证方法**                                        | **前置依赖**           | **预期输出/交付物**                       | **复杂度** | **风险点/注意事项**                                          |
| ------------ | -------------------------------------- | ----------------- | ----------------- | ------------ | ------------------------------------------------------------ | ------------------------------------------------------------ | ---------------------- | ----------------------------------------- | ---------- | ------------------------------------------------------------ |
| **INIT-001** | **创建并配置Git代码仓库**              | **To Do**         | 项目初始化        | DevOps       | 创建Git仓库，配置main分支保护规则和`.gitignore`文件。        | 仓库可被克隆，`.gitignore`能正确忽略`node_modules`等文件。   |                        | Git仓库地址                               | S          |                                                              |
| **INIT-002** | **搭建Chrome扩展项目脚手架**           | **To Do**         | 项目初始化        | Frontend     | 使用Vite或Webpack搭建支持React和TypeScript的Chrome Manifest V3项目结构。 | `npm run dev`能启动开发模式并加载扩展，`npm run build`能生成生产环境包。 |                        | 包含`manifest.json`的基础项目代码         | M          | Manifest V3对后台脚本(Service Worker)和内容脚本的执行环境有严格限制。 |
| **INIT-003** | **配置`manifest.json`核心权限**        | **Pending**       | 项目初始化        | Frontend     | 声明扩展所需的核心权限，如`storage`, `scripting`, `history`。 | 扩展能正确请求并使用声明的权限，无权限错误。                 | INIT-002               | `manifest.json`文件                       | S          | 遵循最小权限原则，`history`权限用于监听URL变化以支持SPA。    |
| **DB-001**   | **设计IndexedDB数据表结构**            | **To Do**         | 数据存储层        | Database     | 设计用于存储页面信息的`pages`表（id, url, title, content, visitTime, accessCount）和用户设置的`settings`表。 | 数据结构定义文档完成，字段类型、索引明确。                   |                        | 数据模型定义文件                          | S          | `content`字段需要建立索引以便于后续检索，但IndexedDB本身不支持全文索引。 |
| **DB-002**   | **实现IndexedDB服务模块**              | **Pending**       | 数据存储层        | Frontend     | 封装一个`IndexedDBService`，提供对`pages`表的CRUD（增删改查）操作。 | 编写单元测试，确保`addPage`, `getPage`, `deletePage`, `getAllPages`等方法正常工作。 | DB-001                 | `src/services/db.service.ts`              | M          | IndexedDB的API是异步的，需要妥善处理Promise。                |
| **IDX-001**  | **实现内容抓取脚本(Content Script)**   | **Pending**       | 内容索引          | Frontend     | 使用`Readability.js`从当前页面DOM中提取主要文本内容和标题。  | 编写单元测试，使用模拟的HTML片段验证脚本能正确提取文章正文。 | INIT-002               | `src/content/scraper.ts`                  | L          | 网站结构千差万别，`Readability.js`并非100%准确。             |
| **IDX-002**  | **实现后台服务(Service Worker)**       | **Pending**       | 内容索引          | Frontend     | 创建后台Service Worker，用于接收Content Script发送的数据并调用DB服务进行存储。 | Service Worker能在扩展安装后持续运行，并能响应消息。         | INIT-003               | `src/background/index.ts`                 | M          | Manifest V3中Service Worker是事件驱动的，非持久存在。        |
| **IDX-003**  | **建立Content Script与Background通信** | **Pending**       | 内容索引          | Frontend     | 实现当页面加载完成或URL变化时，Content Script抓取内容并通过`chrome.runtime.sendMessage`发送给Background。 | Background能正确接收到Content Script发送的{url, title, content}数据。 | IDX-001, IDX-002       | `src/content/index.ts`                    | M          | 需要处理好消息发送和接收的时机，以及SPA的URL变化监听。       |
| **IDX-004**  | **实现页面数据入库逻辑**               | **Pending**       | 内容索引          | Frontend     | 在Background中，接收到数据后，查重（基于URL），然后调用`IndexedDBService`将新页面数据存入数据库。 | 新访问的页面在短暂延迟后，能在IndexedDB中被查到。通过E2E测试验证。 | DB-002, IDX-003        | `src/background/index.ts`中的消息处理逻辑 | M          | 需要考虑索引的性能，避免阻塞其他浏览器操作。                 |
| **SE-001**   | **集成并配置Fuse.js**                  | **To Do**         | 搜索引擎          | Frontend     | 将`Fuse.js`库集成到项目中，并创建`SearchService`。           | `Fuse.js`能够被成功导入和初始化。                            | INIT-002               | `src/services/search.service.ts`          | S          |                                                              |
| **SE-002**   | **实现Fuse.js搜索索引构建**            | **Pending**       | 搜索引擎          | Frontend     | 在`SearchService`中，实现从IndexedDB加载所有页面数据，并构建`Fuse.js`的搜索实例。 | 单元测试验证`Fuse.js`的索引能被正确创建。                    | DB-002, SE-001         | `src/services/search.service.ts`          | M          | 索引10万+页面数据可能消耗较多内存和时间，需要优化加载时机。  |
| **SE-003**   | **实现核心搜索API**                    | **Pending**       | 搜索引擎          | Frontend     | 在`SearchService`中封装一个`search(query)`方法，调用`Fuse.js`执行搜索并返回格式化的结果。 | 单元测试验证输入关键词能返回预期的结果列表，包括模糊匹配。   | SE-002                 | `src/services/search.service.ts`          | M          | `Fuse.js`的参数（如`threshold`, `keys`）需要精细调优以平衡准确率和召回率。 |
| **UI-001**   | **设计并实现Popup基础UI布局**          | **To Do**         | 用户界面          | Frontend     | 使用React和CSS/SCSS实现Popup窗口的主体布局，包括搜索框、结果区域、状态区域。 | Popup界面能被打开，布局符合设计稿，包含各主要UI元素的占位符。 | INIT-002               | `src/popup/App.tsx`                       | M          | 需考虑明暗两种主题模式。                                     |
| **UI-002**   | **实现搜索输入框组件**                 | **Pending**       | 用户界面          | Frontend     | 开发带有实时输入（debounce处理）、清除按钮的搜索框组件。     | 输入时能触发搜索事件，响应流畅。                             | UI-001                 | `src/popup/components/SearchBar.tsx`      | S          |                                                              |
| **UI-003**   | **实现搜索结果列表组件**               | **Pending**       | 用户界面          | Frontend     | 开发用于展示搜索结果的列表组件，每个列表项显示页面标题、URL、内容片段高亮和访问时间。 | 组件能根据传入的数据数组正确渲染列表，支持虚拟列表以优化性能。 | UI-001                 | `src/popup/components/ResultList.tsx`     | M          | 结果中的关键词高亮和内容片段生成是重点。                     |
| **UI-004**   | **连接UI与搜索引擎服务**               | **Pending**       | 用户界面          | Frontend     | 在Popup主组件中，监听搜索框输入，调用`SearchService`，并将返回结果传递给结果列表组件。 | 在搜索框输入关键词后，结果列表能实时更新并显示匹配的页面。   | SE-003, UI-002, UI-003 | `src/popup/App.tsx`                       | M          | 状态管理是关键，需要处理加载中、无结果、错误等多种UI状态。   |
| **UI-005**   | **实现按时间和站点过滤的UI**           | **Pending**       | 用户界面          | Frontend     | 在UI上添增过滤选项，允许用户按预设时间范围（今天、本周等）和指定网站域名进行筛选。 | 选中过滤条件后，搜索结果能被正确过滤。                       | UI-001                 | `src/popup/components/Filters.tsx`        | M          |                                                              |
| **SE-004**   | **在SearchService中实现过滤逻辑**      | **Pending**       | 搜索引擎          | Frontend     | 在`SearchService`中，扩展`search`方法以接收过滤参数，在执行`Fuse.js`搜索前先对数据源进行过滤。 | 单元测试验证传入过滤参数后，搜索范围被正确限定。             | SE-003, UI-005         | `src/services/search.service.ts`          | M          |                                                              |
| **TEST-001** | **编写核心服务单元测试**               | **Pending**       | 测试              | Test         | 为`IndexedDBService`和`SearchService`编写全面的单元测试。    | 测试覆盖率达到80%以上，所有核心逻辑路径都被测试。            | DB-002, SE-003         | `src/services/*.test.ts`                  | L          |                                                              |
| **TEST-002** | **搭建并编写E2E集成测试**              | **Pending**       | 测试              | Test         | 使用Puppeteer或Playwright编写端到端测试脚本，模拟用户完整操作流程。 | E2E测试脚本能自动完成：打开浏览器 -> 访问测试页面 -> 等待索引 -> 打开popup -> 输入搜索 -> 验证结果 的流程。 | UI-004, IDX-004        | `e2e/search.spec.ts`                      | L          | E2E测试环境配置复杂，运行较慢。                              |

------

### **第三部分：详细任务定义 (JSON数组 - 工作流源文件)**

JSON

```
[
  {
    "task_id": "INIT-001",
    "module": "项目初始化",
    "status": "To Do",
    "description": "创建并配置Git代码仓库",
    "task_type": "DevOps",
    "dependencies": [],
    "complexity": "S",
    "deliverables": ["Git仓库地址", ".gitignore文件"],
    "spec": {
      "input": "项目名称 'Recall'",
      "output": "一个配置了main分支保护和标准Node.js .gitignore的远程Git仓库"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "仓库可被克隆，`node_modules`等目录被正确忽略。",
      "script_hint": ""
    },
    "notes": "初始提交应包含README.md。"
  },
  {
    "task_id": "INIT-002",
    "module": "项目初始化",
    "status": "To Do",
    "description": "搭建Chrome扩展项目脚手架",
    "task_type": "Frontend",
    "dependencies": [],
    "complexity": "M",
    "deliverables": ["包含manifest.json和构建脚本的项目目录结构"],
    "spec": {
      "input": "技术栈要求：React, TypeScript, Manifest V3",
      "output": "一个可以通过`npm run build`命令打包成Chrome可加载的`dist`目录的项目结构。"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "`npm run dev`启动的开发模式能实现热重载，`dist`目录能成功加载到Chrome中作为扩展。",
      "script_hint": ""
    },
    "notes": "推荐使用Vite配合`vite-plugin-crx-manifest`插件。"
  },
  {
    "task_id": "INIT-003",
    "module": "项目初始化",
    "status": "Pending",
    "description": "配置`manifest.json`核心权限",
    "task_type": "Frontend",
    "dependencies": ["INIT-002"],
    "complexity": "S",
    "deliverables": ["manifest.json"],
    "spec": {
      "input": "功能需求：需要在活动页面执行脚本、存储数据、监听历史记录。",
      "output": "配置了 'permissions': ['storage', 'scripting', 'history'] 和 'background': {'service_worker': '...'} 的`manifest.json`。"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "在扩展管理页面重新加载扩展时不出现manifest权限相关的错误。",
      "script_hint": ""
    },
    "notes": "`history`权限用于支持SPA路由变化监听。"
  },
  {
    "task_id": "DB-001",
    "module": "数据存储层",
    "status": "To Do",
    "description": "设计IndexedDB数据表结构",
    "task_type": "Database",
    "dependencies": [],
    "complexity": "S",
    "deliverables": ["`src/models/db.model.ts`"],
    "spec": {
      "input": "PRD中的数据存储需求",
      "output": "TypeScript接口定义，包括Page（id, url, title, content, visitTime, accessCount）和Setting。"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "数据模型定义通过了团队的代码审查。",
      "script_hint": ""
    },
    "notes": "URL应作为主键或唯一索引，以防止重复记录。"
  },
  {
    "task_id": "DB-002",
    "module": "数据存储层",
    "status": "Pending",
    "description": "实现IndexedDB服务模块",
    "task_type": "Frontend",
    "dependencies": ["DB-001"],
    "complexity": "M",
    "deliverables": ["`src/services/db.service.ts`"],
    "spec": {
      "input": "DB-001中定义的数据模型",
      "output": "一个封装了IndexedDB操作的异步服务类，例如 `idb` 库的包装器。"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "增、删、改、查操作的单元测试全部通过。",
      "script_hint": "`src/services/db.service.test.ts`"
    },
    "notes": "该服务将成为一个被多处依赖的核心模块。"
  },
  {
    "task_id": "IDX-001",
    "module": "内容索引",
    "status": "Pending",
    "description": "实现内容抓取脚本(Content Script)",
    "task_type": "Frontend",
    "dependencies": ["INIT-002"],
    "complexity": "L",
    "deliverables": ["`src/content/scraper.ts`"],
    "spec": {
      "input": "当前页面的 `document` 对象",
      "output": "一个包含 { title: string, content: string } 的对象"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "在给定的模拟HTML字符串上运行时，能提取出正文文本，且准确率高于90%。",
      "script_hint": "`src/content/scraper.test.ts`"
    },
    "notes": "将使用 `Readability.js` 库作为核心实现。"
  },
  {
    "task_id": "IDX-002",
    "module": "内容索引",
    "status": "Pending",
    "description": "实现后台服务(Service Worker)",
    "task_type": "Frontend",
    "dependencies": ["INIT-003"],
    "complexity": "M",
    "deliverables": ["`src/background/index.ts`"],
    "spec": {
      "input": "来自 `chrome.runtime` 的消息事件和来自 `chrome.history` 的导航事件",
      "output": "对数据库的调用"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "当接收到特定格式的消息或URL变化时，能够触发预期的动作序列。",
      "script_hint": "E2E测试的一部分"
    },
    "notes": "主要作为消息路由和任务协调中心。"
  },
  {
    "task_id": "IDX-003",
    "module": "内容索引",
    "status": "Pending",
    "description": "建立Content Script与Background通信",
    "task_type": "Frontend",
    "dependencies": ["IDX-001", "IDX-002"],
    "complexity": "M",
    "deliverables": ["`src/content/index.ts`"],
    "spec": {
      "input": "抓取到的页面内容对象",
      "output": "通过 `chrome.runtime.sendMessage` 发送的消息"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "在测试页面上，后台能成功接收到由Content Script发送的页面内容。",
      "script_hint": "E2E测试的一部分"
    },
    "notes": "此处的逻辑将被后台的SPA监听逻辑所触发。"
  },
  {
    "task_id": "IDX-004",
    "module": "内容索引",
    "status": "Pending",
    "description": "实现页面数据入库逻辑",
    "task_type": "Frontend",
    "dependencies": ["DB-002", "IDX-003"],
    "complexity": "M",
    "deliverables": ["`src/background/index.ts` 中的消息监听器"],
    "spec": {
      "input": "包含页面内容的消息对象",
      "output": "无直接输出，副作用是数据被写入IndexedDB"
    },
    "verification": {
      "method": "E2E Test",
      "criteria": "访问一个新页面后，该页面的数据出现在IndexedDB中。",
      "script_hint": "`e2e/indexing.spec.ts`"
    },
    "notes": ""
  },
  {
    "task_id": "SE-001",
    "module": "搜索引擎",
    "status": "To Do",
    "description": "集成并配置Fuse.js",
    "task_type": "Frontend",
    "dependencies": ["INIT-002"],
    "complexity": "S",
    "deliverables": ["`src/services/search.service.ts`"],
    "spec": {
      "input": "无",
      "output": "一个可以被实例化的`SearchService`类"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "`Fuse.js`能够被成功导入和初始化。",
      "script_hint": ""
    },
    "notes": ""
  },
  {
    "task_id": "SE-002",
    "module": "搜索引擎",
    "status": "Pending",
    "description": "实现Fuse.js搜索索引构建",
    "task_type": "Frontend",
    "dependencies": ["DB-002", "SE-001"],
    "complexity": "M",
    "deliverables": ["`src/services/search.service.ts`"],
    "spec": {
      "input": "从IndexedDB获取的页面数据数组",
      "output": "一个可供搜索的`Fuse`实例"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "给定模拟数据，`Fuse.js`的索引能被正确创建，并且内存占用在合理范围内。",
      "script_hint": "`src/services/search.service.test.ts`"
    },
    "notes": "索引构建可能是一个耗时操作，需要考虑在后台空闲时进行或在搜索时动态构建。"
  },
  {
    "task_id": "SE-003",
    "module": "搜索引擎",
    "status": "Pending",
    "description": "实现核心搜索API",
    "task_type": "Frontend",
    "dependencies": ["SE-002"],
    "complexity": "M",
    "deliverables": ["`src/services/search.service.ts`"],
    "spec": {
      "input": "query: string, options: SearchOptions",
      "output": "Promise<SearchResultItem[]>"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "输入关键词能返回预期的结果列表，结果包含高亮片段和相关性分数。",
      "script_hint": "`src/services/search.service.test.ts`"
    },
    "notes": ""
  },
  {
    "task_id": "UI-001",
    "module": "用户界面",
    "status": "To Do",
    "description": "设计并实现Popup基础UI布局",
    "task_type": "Frontend",
    "dependencies": ["INIT-002"],
    "complexity": "M",
    "deliverables": ["`src/popup/App.tsx`"],
    "spec": {
      "input": "无",
      "output": "渲染了主布局的React组件"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "Popup界面能被打开，布局符合设计稿，包含各主要UI元素的占位符。",
      "script_hint": ""
    },
    "notes": ""
  },
  {
    "task_id": "UI-002",
    "module": "用户界面",
    "status": "Pending",
    "description": "实现搜索输入框组件",
    "task_type": "Frontend",
    "dependencies": ["UI-001"],
    "complexity": "S",
    "deliverables": ["`src/popup/components/SearchBar.tsx`"],
    "spec": {
      "input": "props: { onSearch: (query: string) => void }",
      "output": "一个受控的输入框组件"
    },
    "verification": {
      "method": "Component Test",
      "criteria": "输入时能触发`onSearch`回调，并应用了debounce。",
      "script_hint": "`src/popup/components/SearchBar.test.tsx`"
    },
    "notes": ""
  },
  {
    "task_id": "UI-003",
    "module": "用户界面",
    "status": "Pending",
    "description": "实现搜索结果列表组件",
    "task_type": "Frontend",
    "dependencies": ["UI-001"],
    "complexity": "M",
    "deliverables": ["`src/popup/components/ResultList.tsx`"],
    "spec": {
      "input": "props: { results: SearchResultItem[] }",
      "output": "一个渲染了搜索结果的列表"
    },
    "verification": {
      "method": "Component Test",
      "criteria": "组件能根据传入的`results`数组正确渲染列表项。",
      "script_hint": "`src/popup/components/ResultList.test.tsx`"
    },
    "notes": "应使用`react-window`或类似库实现虚拟滚动以优化大量结果的性能。"
  },
  {
    "task_id": "UI-004",
    "module": "用户界面",
    "status": "Pending",
    "description": "连接UI与搜索引擎服务",
    "task_type": "Frontend",
    "dependencies": ["SE-003", "UI-002", "UI-003"],
    "complexity": "M",
    "deliverables": ["`src/popup/App.tsx`"],
    "spec": {
      "input": "用户在搜索框的输入",
      "output": "更新后的搜索结果列表UI"
    },
    "verification": {
      "method": "E2E Test",
      "criteria": "在popup中输入关键词后，结果列表能实时更新并显示正确的匹配页面。",
      "script_hint": "`e2e/search.spec.ts`"
    },
    "notes": "管理好加载、空、错误等UI状态。"
  },
  {
    "task_id": "UI-005",
    "module": "用户界面",
    "status": "Pending",
    "description": "实现按时间和站点过滤的UI",
    "task_type": "Frontend",
    "dependencies": ["UI-001"],
    "complexity": "M",
    "deliverables": ["`src/popup/components/Filters.tsx`"],
    "spec": {
      "input": "props: { onFilterChange: (filters) => void }",
      "output": "过滤选项组件"
    },
    "verification": {
      "method": "Component Test",
      "criteria": "选择不同的过滤条件时，能正确触发`onFilterChange`回调。",
      "script_hint": "`src/popup/components/Filters.test.tsx`"
    },
    "notes": ""
  },
  {
    "task_id": "SE-004",
    "module": "搜索引擎",
    "status": "Pending",
    "description": "在SearchService中实现过滤逻辑",
    "task_type": "Frontend",
    "dependencies": ["SE-003", "UI-005"],
    "complexity": "M",
    "deliverables": ["`src/services/search.service.ts`"],
    "spec": {
      "input": "扩展`search`方法的参数，增加`filters`对象",
      "output": "经过滤后的搜索结果"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "单元测试验证传入过滤参数后，搜索的数据源被正确预先过滤。",
      "script_hint": "`src/services/search.service.test.ts`"
    },
    "notes": ""
  },
  {
    "task_id": "TEST-001",
    "module": "测试",
    "status": "Pending",
    "description": "编写核心服务单元测试",
    "task_type": "Test",
    "dependencies": ["DB-002", "SE-003"],
    "complexity": "L",
    "deliverables": ["`src/services/*.test.ts`"],
    "spec": {
      "input": "核心服务模块",
      "output": "Jest/Vitest测试报告"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "测试覆盖率达到80%以上，所有核心逻辑路径都被测试。",
      "script_hint": ""
    },
    "notes": ""
  },
  {
    "task_id": "TEST-002",
    "module": "测试",
    "status": "Pending",
    "description": "搭建并编写E2E集成测试",
    "task_type": "Test",
    "dependencies": ["UI-004", "IDX-004"],
    "complexity": "L",
    "deliverables": ["`e2e/search.spec.ts`"],
    "spec": {
      "input": "已构建的扩展包",
      "output": "Playwright/Puppeteer测试报告"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "E2E测试脚本能稳定运行并通过。",
      "script_hint": ""
    },
    "notes": "E2E测试是验证整个工作流正确性的关键。"
  }
]
```

------

### **第四部分：任务依赖关系图 (Mermaid)**

代码段

```mermaid
graph TD
    subgraph "模块0: 项目初始化 (DevOps)"
        INIT-001["INIT-001: 创建Git仓库"]
        INIT-002["INIT-002: 搭建项目脚手架"]
        INIT-003["INIT-003: 配置manifest.json"]
    end

    subgraph "模块1: 数据存储层 (Database)"
        DB-001["DB-001: 设计DB结构"]
        DB-002["DB-002: 实现DB服务"]
    end

    subgraph "模块2: 内容索引 (Backend Logic)"
        IDX-001["IDX-001: 实现内容抓取脚本"]
        IDX-002["IDX-002: 实现后台服务"]
        IDX-003["IDX-003: 建立通信"]
        IDX-004["IDX-004: 数据入库"]
    end
    
    subgraph "模块3: 搜索引擎 (Backend Logic)"
        SE-001["SE-001: 集成Fuse.js"]
        SE-002["SE-002: 构建搜索索引"]
        SE-003["SE-003: 实现核心搜索API"]
        SE-004["SE-004: 实现过滤逻辑"]
    end

    subgraph "模块4: 用户界面 (Frontend)"
        UI-001["UI-001: 实现基础UI布局"]
        UI-002["UI-002: 实现搜索框"]
        UI-003["UI-003: 实现结果列表"]
        UI-004["UI-004: 连接UI与服务"]
        UI-005["UI-005: 实现过滤UI"]
    end

    subgraph "模块5: 测试 (QA)"
        TEST-001["TEST-001: 核心服务单元测试"]
        TEST-002["TEST-002: E2E集成测试"]
    end

    %% 依赖关系
    INIT-002 --> INIT-003
    
    DB-001 --> DB-002

    INIT-002 --> IDX-001
    INIT-003 --> IDX-002
    IDX-001 --> IDX-003
    IDX-002 --> IDX-003
    DB-002 --> IDX-004
    IDX-003 --> IDX-004

    INIT-002 --> SE-001
    DB-002 --> SE-002
    SE-001 --> SE-002
    SE-002 --> SE-003
    SE-003 --> SE-004

    INIT-002 --> UI-001
    UI-001 --> UI-002
    UI-001 --> UI-003
    UI-001 --> UI-005
    SE-003 --> UI-004
    UI-002 --> UI-004
    UI-003 --> UI-004
    UI-005 --> SE-004

    DB-002 --> TEST-001
    SE-003 --> TEST-001
    UI-004 --> TEST-002
    IDX-004 --> TEST-002
```