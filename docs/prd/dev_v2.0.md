### **第二部分：结构化任务列表 (项目仪表盘)**

| **任务ID** | **任务名称** | **状态 (Status)** | **所属功能/模块** | **任务类型** | **任务描述** | **完成标准/验证方法** | **前置依赖** | **预期输出/交付物** | **复杂度** | **风险点/注意事项** |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| **PREP-001** | **升级核心依赖并验证V1兼容性** | **To Do** | 项目初始化 | DevOps | 升级`React`, `Vite`, `Fuse.js`等核心依赖到最新稳定版。 | `npm install`无错误, `npm run build`成功, V1的搜索和索引功能通过手动回归测试。 | | `package.json` | S | |
| **BE-001** | **实现高级搜索查询解析器** | **Pending** | 高级搜索 | Frontend | 创建一个纯函数模块，用于解析用户输入的字符串，分离出关键词、精确匹配短语、排除词和`site`过滤器。 | `Unit Test`: `parseQuery('A "B" -C site:D')` 返回 `{keywords:['A'], exact:['B'], exclude:['C'], site:'D'}`。 | PREP-001 | `src/services/query-parser.ts` | M | 需要处理各种边缘情况，如不匹配的引号。 |
| **BE-002** | **在SearchService中集成查询解析与过滤逻辑** | **Pending** | 高级搜索 | Frontend | 修改`SearchService`，使其先使用`BE-001`的解析器，然后根据`site:`和`-`逻辑对数据源进行预/后过滤，最后再执行`Fuse.js`搜索。 | `Integration Test`: 确保`search()`方法能正确处理`site:`和`-`过滤，返回预期的子集。 | BE-001 | `src/services/search.service.ts` | M | 过滤逻辑的性能是关键，避免全量数据多次循环。 |
| **FE-001** | **实现Popup的语法帮助UI** | **Pending** | 高级搜索 | Frontend | 创建一个`React`组件，用于在`Popup`中以`Tooltip`或`Popover`形式展示搜索语法帮助。 | `Component Test`: 组件能被正常触发显示，内容符合PRD要求。 | PREP-001 | `src/popup/components/SyntaxHelp.tsx` | S | |
| **FE-002** | **配置Options页面路由** | **Pending** | 配置中心 | Frontend | 修改`manifest.json`，添加`options_page`字段，并配置项目路由以支持该独立页面。 | 在浏览器扩展管理中，点击“扩展选项”能成功在新标签页打开`options.html`。 | PREP-001 | `manifest.json`, `src/options/index.html` | S | |
| **FE-003** | **实现配置中心主布局与导航** | **Pending** | 配置中心 | Frontend | 创建配置页面的主`React`组件，包含左侧导航栏和右侧内容区的基础布局。 | `Component Test`: 导航链接能正确切换右侧内容区的视图。 | FE-002 | `src/options/App.tsx` | M | |
| **DB-001** | **为历史管理页实现分页/虚拟加载数据接口** | **Pending** | 配置中心 | Database | 在`IndexedDBService`中添加一个支持分页或按`cursor`加载数据的新方法，以应对大量历史记录。 | `Unit Test`: `getHistory({page: 2, limit: 50})`能正确返回第二页的数据。 | PREP-001 | `src/services/db.service.ts` | M | `IndexedDB`原生分页性能可能不佳，需仔细设计。 |
| **FE-004** | **实现索引历史管理UI** | **Pending** | 配置中心 | Frontend | 开发历史记录列表视图，使用`react-window`或类似库实现虚拟滚动，并集成`BE-002`的搜索功能。 | `E2E Test`: 页面能渲染10k+条记录而不卡顿，搜索功能正常工作，可多选并触发删除。 | FE-003, DB-001, BE-002 | `src/options/views/HistoryManager.tsx` | L | 虚拟列表与复杂`item`的集成是难点。 |
| **DB-002** | **创建黑名单DB表并实现服务** | **Pending** | 配置中心 | Database | 在`IndexedDB`中创建`blacklist`表 (`store`)，并封装一个`BlacklistService`提供`CRUD`操作。 | `Unit Test`: `addDomain()`, `removeDomain()`, `isBlacklisted()`等方法测试通过。 | PREP-001 | `src/services/blacklist.service.ts` | M | |
| **FE-005** | **实现黑名单管理UI** | **Pending** | 配置中心 | Frontend | 开发黑名单管理的`React`组件，包含输入框、列表展示和删除按钮。 | `Component Test`: 能正确添加、显示和删除黑名单中的域名。 | FE-003, DB-002 | `src/options/views/BlacklistManager.tsx` | S | |
| **BE-003** | **在后台服务中集成黑名单逻辑** | **Pending** | 配置中心 | Frontend | 修改后台`Service Worker`的页面索引触发逻辑，在调用`Readability.js`前，先通过`BlacklistService`检查当前页面域名是否在黑名单内。 | `Integration Test`: 访问一个已加入黑名单的网站，`IndexedDB`中不会新增该页面的记录。 | FE-005, DB-002 | `src/background/index.ts` | M | |
| **BE-004** | **实现数据导出服务** | **Pending** | 数据备份 | Frontend | 在一个新`DataService`中，实现从`IndexedDB`读取所有数据并序列化为`.json`字符串的功能。 | `Unit Test`: 导出的`JSON`字符串结构正确，能被`JSON.parse()`解析。 | PREP-001 | `src/services/data.service.ts` | M | 处理大数据量时可能消耗较多内存。 |
| **FE-006** | **实现数据备份恢复UI** | **Pending** | 数据备份 | Frontend | 开发备份恢复的UI组件，包括导出按钮、导入按钮（触发文件选择）及相关提示信息。 | `E2E Test`: 点击导出能下载文件，点击导入选择文件后能触发相应的服务调用。 | FE-003, BE-004 | `src/options/views/BackupManager.tsx` | M | |
| **BE-005** | **实现数据导入服务** | **Pending** | 数据备份 | Frontend | 在`DataService`中，实现解析`.json`文件并将其中的数据写入`IndexedDB`的功能，需处理“合并”与“覆盖”逻辑。 | `Integration Test`: 导入一个有效的备份文件后，`IndexedDB`中的数据被正确更新。 | BE-004 | `src/services/data.service.ts` | L | 事务处理和错误回滚是关键，防止数据损坏。 |
| **FE-007** | **实现搜索结果高亮组件** | **Pending** | 结果高亮 | Frontend | 创建一个`Highlight`组件，接收文本和关键词作为`props`，返回带有`<mark>`标签高亮的`HTML`。 | `Component Test`: 能正确高亮单个、多个关键词，并处理特殊字符。 | PREP-001 | `src/popup/components/Highlight.tsx` | M | 需注意防止`XSS`注入，正确处理`dangerouslySetInnerHTML`。 |
| **FE-008** | **在结果列表中应用高亮** | **Pending** | 结果高亮 | Frontend | 修改`Popup`的结果列表组件，使用`FE-007`来展示页面标题和内容摘要。 | `E2E Test`: 在`Popup`中搜索后，返回结果的关键词被正确高亮。 | FE-007, BE-002 | `src/popup/components/ResultList.tsx` | S | |
| **TEST-001**| **编写高级搜索E2E测试** | **Pending** | 测试 | Test | 使用`Playwright`或`Puppeteer`编写端到端测试，覆盖所有高级搜索语法组合。 | `Test Script`: 自动化脚本能成功验证各种搜索语法的正确性。 | FE-001, BE-002 | `e2e/advanced-search.spec.ts` | L | |

---

### **第三部分：详细任务定义 (JSON数组 - 工作流源文件)**

```json
[
  {
    "task_id": "PREP-001",
    "module": "项目初始化",
    "status": "To Do",
    "description": "升级核心依赖并验证V1兼容性",
    "task_type": "DevOps",
    "dependencies": [],
    "complexity": "S",
    "deliverables": ["package.json"],
    "spec": {
      "input": "项目当前的package.json",
      "output": "更新了主要依赖版本的package.json"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "V1的核心功能（基础搜索、自动索引）在依赖升级后仍能正常工作。",
      "script_hint": ""
    },
    "notes": "这是V2开发的起点，确保环境稳定。"
  },
  {
    "task_id": "BE-001",
    "module": "高级搜索",
    "status": "Pending",
    "description": "实现高级搜索查询解析器",
    "task_type": "Frontend",
    "dependencies": ["PREP-001"],
    "complexity": "M",
    "deliverables": ["src/services/query-parser.ts", "src/services/query-parser.test.ts"],
    "spec": {
      "input": "queryString: string",
      "output": "{ keywords: string[], exact: string[], exclude: string[], site: string | null }"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "测试用例覆盖正常、混合、边缘（如连续空格、未闭合引号）等所有情况，并通过测试。",
      "script_hint": "src/services/query-parser.test.ts"
    },
    "notes": "此模块是高级搜索的逻辑核心，应无任何外部依赖。"
  },
  {
    "task_id": "BE-002",
    "module": "高级搜索",
    "status": "Pending",
    "description": "在SearchService中集成查询解析与过滤逻辑",
    "task_type": "Frontend",
    "dependencies": ["BE-001"],
    "complexity": "M",
    "deliverables": ["src/services/search.service.ts"],
    "spec": {
      "input": "query: string",
      "output": "Promise<SearchResult[]>"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "使用模拟数据，调用search(query)方法，验证返回结果是否同时满足了模糊搜索和高级语法的过滤条件。",
      "script_hint": "src/services/search.service.test.ts"
    },
    "notes": "注意执行顺序：site过滤 -> Fuse.js模糊搜索 -> exclude过滤。"
  },
  {
    "task_id": "DB-002",
    "module": "配置中心",
    "status": "Pending",
    "description": "创建黑名单DB表并实现服务",
    "task_type": "Database",
    "dependencies": ["PREP-001"],
    "complexity": "M",
    "deliverables": ["src/services/blacklist.service.ts"],
    "spec": {
      "input": "domain: string",
      "output": "Promise<boolean> for isBlacklisted, Promise<void> for others"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "针对该服务的所有公开方法（add, remove, getAll, isBlacklisted）编写单元测试并通过。",
      "script_hint": "src/services/blacklist.service.test.ts"
    },
    "notes": "需要处理IndexedDB的异步事务。"
  },
  {
    "task_id": "BE-003",
    "module": "配置中心",
    "status": "Pending",
    "description": "在后台服务中集成黑名单逻辑",
    "task_type": "Frontend",
    "dependencies": ["DB-002"],
    "complexity": "M",
    "deliverables": ["src/background/index.ts"],
    "spec": {
      "input": "A page navigation event",
      "output": "Either proceeds to indexing or stops silently"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "模拟后台消息或导航事件，验证当URL的域名在黑名单中时，后续的索引流程（如调用db.service）不会被触发。",
      "script_hint": "E2E test will cover this."
    },
    "notes": "这是保护用户隐私的关键逻辑点。"
  }
]
```

---

### **第四部分：任务依赖关系图 (Mermaid)**

```mermaid
graph TD
    subgraph "Phase 0: 准备工作"
        PREP-001["PREP-001: 升级依赖"]
    end

    subgraph "Phase 1: 高级搜索功能"
        BE-001["BE-001: 实现查询解析器"]
        BE-002["BE-002: 集成解析与过滤"]
        FE-001["FE-001: 实现语法帮助UI"]
        FE-007["FE-007: 实现高亮组件"]
        FE-008["FE-008: 应用高亮到结果列表"]
        TEST-001["TEST-001: 编写高级搜索E2E测试"]
    end

    subgraph "Phase 2: 配置中心与数据管理"
        FE-002["FE-002: 配置Options页面路由"]
        FE-003["FE-003: 实现配置中心主布局"]
        DB-001["DB-001: 实现DB分页接口"]
        FE-004["FE-004: 实现历史管理UI"]
        DB-002["DB-002: 创建黑名单DB与服务"]
        FE-005["FE-005: 实现黑名单管理UI"]
        BE-003["BE-003: 集成黑名单到后台"]
        BE-004["BE-004: 实现数据导出服务"]
        FE-006["FE-006: 实现备份恢复UI"]
        BE-005["BE-005: 实现数据导入服务"]
    end

    %% Dependencies
    PREP-001 --> BE-001
    PREP-001 --> FE-001
    PREP-001 --> FE-002
    PREP-001 --> DB-001
    PREP-001 --> DB-002
    PREP-001 --> BE-004
    PREP-001 --> FE-007

    BE-001 --> BE-002
    BE-002 --> FE-004
    BE-002 --> FE-008
    BE-002 --> TEST-001
    FE-001 --> TEST-001

    FE-002 --> FE-003
    FE-003 --> FE-004
    FE-003 --> FE-005
    FE-003 --> FE-006

    DB-001 --> FE-004
    
    DB-002 --> FE-005
    DB-002 --> BE-003
    FE-005 --> BE-003

    BE-004 --> FE-006
    BE-004 --> BE-005
    FE-006 --> BE-005

    FE-007 --> FE-008

```