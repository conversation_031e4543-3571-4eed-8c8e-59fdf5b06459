### **产品需求文档 (Product Requirements Document - PRD)**
**产品名称**: Recall
**文档版本**: 3.0.1
**文档状态**: 已更新 (Updated - Post Multi-Agent Review)
**撰写人**: Senior Product Manager
**最后更新**: 2025年6月22日
**更新内容**: TensorFlow.js迁移、性能优化、UX改进、技术架构更新

---

#### **# 角色 (Role)**
我将扮演一名世界顶级的资深产品经理 (`Senior Product Manager`)。我的任务是为`Recall`的V3版本撰写一份全面、专业且可执行的产品需求文档 (`PRD`)。这份 `PRD` 足够清晰，以便工程师 (`engineers`)、设计师 (`designers`) 和其他利益相关者 (`stakeholders`) 能够准确理解产品的“做什么 (What)、为何做 (Why)、为谁做 (Whom)”，而不会产生重大歧义。

#### **# 产品概念 (Product Concept)**
* **产品名称与一句话定位 (Product Name & One-Liner):** **Recall**，一款将浏览器历史升级为个人专属知识与行为洞察引擎的AI插件。
* **目标用户 (Target Audience):** 知识工作者，特别是技术研发人员、研究人员/学者、内容创作者。
* **核心待解决问题 (Core Problem to Solve):**
    1.  传统浏览器历史只能按标题和URL搜索，无法检索页面正文，导致看过的宝贵信息难以找回。
    2.  随着浏览信息爆炸式增长，用户难以有效组织、连接和回顾自己的知识资产，造成重复劳动和知识流失。
    3.  用户缺乏对自己信息获取行为的“元认知”，无法洞察自己的学习模式和兴趣演变。
* **核心功能构想 (Core Feature Ideas):**
    1.  本地AI驱动的自然语言语义搜索。
    2.  AI对话式历史探索与总结。
    3.  自动化知识中心与个性化知识简报。
    4.  交互式知识图谱可视化。
    5.  “自带密钥”(BYOK)模式与云端LLM集成。

#### **# PRD 结构与要求 (PRD Structure and Requirements)**

### **1. 背景与问题陈述 (Background and Problem Statement)**
* **市场背景 (Market Background):** 在信息爆炸的今天，知识工作者每天面临海量信息的冲击。 Chrome浏览器作为市场占有率超过65%的主流平台，其原生历史记录功能已远不能满足深度信息检索的需求。 同时，随着前端AI技术的成熟（如 `Transformers.js`和WebGPU加速）和用户对数据隐私的日益重视，为纯本地、智能化的个人知识管理工具创造了巨大的市场机会。
* **问题定义 (Problem Definition):**
    1.  **信息检索低效**: 用户常常只记得看过某个内容，却忘记了出处（网站/URL）和精确标题，传统的关键词搜索几乎无效，导致大量时间浪费在重复搜索上。
    2.  **知识资产流失**: 用户的浏览历史是一个潜在的、巨大的个人知识库。但由于缺乏有效的组织和回顾工具，这些知识是碎片化的、无序的，无法被有效激活和利用，最终慢慢流失。
    3.  **自我洞察缺失**: 用户无法从宏观视角审视自己的信息消费习惯和知识探索路径，即缺乏“元认知”工具。他们不知道自己时间的去向，也难以发现隐藏的兴趣关联和知识盲区。
* **竞品格局 (Competitive Landscape):**
    * **Falcon**: 曾经是该领域的优秀解决方案，但已停止维护，留下了未被满足的用户需求。
    * **Memex**: 功能强大但过于复杂，要求用户主动进行保存和组织，学习成本高，未能实现自动化和智能化。
    * **核心弱点**: 现有竞品普遍缺乏由本地AI驱动的、兼顾隐私与智能的全文内容搜索和自动化洞察能力。`Recall` V3将以此为核心差异点切入市场。

### **2. 目标用户与场景 (Target Users and Scenarios)**
* **用户画像 (User Personas):**
    * **Persona 1: 技术研发人员 - 李哲**
        * **描述**: 30岁，资深前端工程师，每天需要查阅大量技术文档、博客和`Stack Overflow`。
        * **目标**: 快速解决工作中遇到的技术难题，提升开发效率。
        * **痛点**: “我清楚地记得几周前看过一篇关于`React Hook`性能优化的文章，里面有个绝妙的代码片段，但现在死活找不到。浏览器历史里搜`React`有几百条记录，太浪费时间了！”
        * **动机**: 渴望拥有一个能“听懂”他意图的工具，帮他从自己的知识库中快速精准地找到解决方案。
    * **Persona 2: 研究人员/学者 - 王静**
        * **描述**: 28岁，经济学博士生，正在撰写关于“全球供应链”的毕业论文。
        * **目标**: 高效地管理参考文献，并能追溯之前研究过的数据和观点。
        * **痛点**: “为了写一个章节，我可能在几天内看了上百个网页。当需要引用某个具体数据时，我完全想不起来是在哪个PDF报告或新闻网站上看到的了，只能重新再找一遍。”
        * **动机**: 需要一个能自动整理研究资料，并能让她直观理解不同研究主题之间关联的智能助理。

* **用户场景或用户故事 (User Scenarios / User Stories):**
    * **李哲**: "As a `developer`, I want to ask my browser history 'show me the article about react hook performance optimization I saw last month' so that I can quickly solve my current bug without re-searching from Google."
    * **王静**: "As a `researcher`, I want to see a visual graph of my research on 'supply chain logistics' so that I can understand how different topics are connected and discover new research angles for my thesis."

### **3. 产品目标与成功指标 (Product Goals and Success Metrics)**
* **产品愿景 (Product Vision):** 成为每一位知识工作者不可或缺的“第二大脑”，将他们被动的浏览历史，转化为一个主动、智能且绝对私密的个人知识资产。
* **业务与用户目标 (Business & User Goals):**
    * **目标1 (用户增长与采纳)**: 在V3版本发布后的6个月内，通过提供业界领先的免费本地语义搜索体验，实现50,000的月活跃用户 (`MAU`)。
    * **目标2 (商业化验证)**: 在V3版本发布后的9个月内，通过提供无与伦比的自动化洞察和对话式探索功能，实现3%的`Pro`版本付费转化率。
* **关键绩效指标 (Key Performance Indicators, KPIs):**
    * **对应目标1**:
        * 月活跃用户数 (`MAU`)
        * 日活/月活比 (`DAU/MAU Ratio`): > 40%
        * 新用户次周留存率: > 60%
    * **对应目标2**:
        * 付费转化率 (`Conversion Rate`, Free to Pro)
        * Pro用户月流失率 (`Churn Rate`)
        * 每用户平均收入 (`ARPU`)

### **4. 功能性需求 (Functional Requirements) - 使用 MoSCoW 方法**

#### **Must-Have (必要需求, V3.0 免费版MVP范围)**

* **需求ID**: FEAT-001
* **用户故事**: "As a `user`, I want the extension to automatically index the full text of pages I visit so that I can search my history based on content, not just titles."
* **功能描述**: 扩展在后台自动抓取用户访问页面的主要文本内容，使用轻量级本地AI模型 (`Transformers.js`配合WebGPU/WASM加速) 生成384维向量嵌入 (`Embeddings`)，并将文本和向量一并存储在本地的 `IndexedDB` 中。用户在搜索框输入自然语言，系统使用HNSW算法进行高效向量相似度搜索，实现即时、离线的语义搜索。
* **技术架构**: 
    * **AI引擎**: Transformers.js v3 + Xenova/all-MiniLM-L6-v2模型
    * **加速**: WebGPU优先，WASM降级支持
    * **向量搜索**: HNSW算法，支持10万+文档的快速检索
    * **存储**: IndexedDB V3架构，500MB容量限制
* **验收标准**:
    * [ ] 能够成功索引超过95%的常见静态网站和`SPA`应用。
    * [ ] 索引过程在后台进行，对用户前台浏览的`CPU`和内存影响可忽略不计。
    * [ ] 语义搜索的平均响应时间应小于250ms（已优化目标）。
    * [ ] 文本向量化处理速度小于45ms每1000字符。
    * [ ] 支持10,000+页面的语义搜索，响应时间<75ms。
    * [ ] 在离线状态下，搜索功能完全可用。

* **需求ID**: FEAT-002
* **用户故事**: "As a `user`, I want to securely add my own OpenAI/Anthropic/DeepSeek/Google API key so that I can use advanced AI features without my data going through your servers."
* **功能描述**: 在扩展的设置页面提供一个安全的API Key管理界面。用户可以输入、切换和移除自己的LLM服务商的API Key。支持OpenAI、Anthropic、DeepSeek、Google、Azure、LiteLLM等6种主流服务商。所有使用该Key的请求，都由客户端直接发送到对应的API服务商，`Recall`的服务器不参与此过程。
* **安全实现**:
    * **加密存储**: AES-256加密，PBKDF2密钥派生（10万次迭代）
    * **直连通信**: 客户端直接访问LLM API，无中间代理
    * **权限控制**: 最小权限原则，仅申请必要的Chrome Extension权限
* **验收标准**:
    * [ ] 用户可以成功保存一个加密的API Key。
    * [ ] 用户可以随时更新或移除已保存的Key。
    * [ ] 界面需明确告知用户数据将通过其个人Key进行传输，以确保透明度。

* **需求ID**: FEAT-003
* **用户故事**: "As a `user`, when I'm dwelling on a long and complex page, I want the extension to proactively ask if I need a summary so that I can quickly grasp the key points."
* **功能描述**: 当系统检测到用户在某个页面停留时间超过一个可配置的阈值（默认为3分钟）时，会在页面角落弹出一个非侵入式的通知，询问用户是否需要生成AI摘要。如果用户同意，则调用`FEAT-002`中配置的BYOK能力来生成并展示摘要。
* **验收标准**:
    * [ ] 通知功能可以被用户在设置中开启或关闭。
    * [ ] 触发逻辑准确，且通知UI不会打断用户的核心阅读流。
    * [ ] 点击“同意”后能成功调用BYOK流程并展示摘要。

#### **Should-Have (期望需求, V3.1 Pro版核心功能)**

* **需求ID**: FEAT-004
* **用户故事**: "As a `Pro user`, I want to have a conversation with my Browse history, asking complex questions like 'what were the main arguments in the articles I read about AI ethics last week?' so that I can get synthesized insights instead of just a list of links."
* **功能描述**: 将搜索框升级为对话界面。当用户输入一个问题后，系统首先使用本地语义搜索召回最相关的一组历史页面（Top-K），然后将这些页面的文本内容整合到一个精心设计的`Prompt`中，通过用户的BYOK Key提交给LLM，最终将LLM返回的总结性答案呈现给用户。
* **验收标准**:
    * [ ] 对话系统能够理解用户的多轮追问。
    * [ ] 返回的答案必须基于用户的真实浏览历史，并能提供来源链接。
    * [ ] 整个流程的端到端响应时间应在5秒以内。

* **需求ID**: FEAT-005
* **用户故事**: "As a `Pro user`, I want the extension to automatically group my Browse history into projects or topics like '[Project] Q3 Market Research' so that my knowledge is organizedeffortlessly."
* **功能描述**: 一个在本地运行的后台AI服务，持续分析用户的浏览行为（如URL、关键词共现、访问时间密度等），自动将相关的页面聚合成“知识中心”。用户可以在一个专门的视图中查看、重命名、合并或手动调整这些“知识中心”。
* **验收标准**:
    * [ ] 自动创建的主题准确率 > 80%。
    * [ ] 用户可以对知识中心进行有效的增删改查管理。
    * [ ] 该后台分析进程资源占用低，不影响浏览器性能。

* **需求ID**: FEAT-006
* **用户故事**: "As a `Pro user`, I want to be able to select a 'Knowledge Hub' and generate a structured summary report or a daily digest so that I can efficiently review my research progress."
* **功能描述**: 在“知识中心”视图中，用户可以选择一个或多个中心，或一个时间范围，点击“生成报告”按钮。系统将调用`FEAT-004`的对话式总结能力，生成一份包含关键要点、相关链接和AI洞察的结构化报告。
* **验收标准**:
    * [ ] 用户可以自定义报告的输出格式（如要点、摘要、问答）。
    * [ ] 生成的报告内容连贯、准确，且格式清晰。

#### **Could-Have (锦上添花, V3.2+ Pro版增强功能)**

* **FEAT-007: 知识图谱可视化**: 将用户的浏览历史构建成可交互的知识图谱，节点是页面，边代表跳转关系或内容相似度，让用户直观看到自己的“思维地图”。
* **FEAT-008: 智能平台与论坛推荐**: 在生成的总结报告末尾，基于内容推荐用户尚未访问过的高质量、专业的平台或论坛。
* **FEAT-009: 一键发送到Notion等**: 允许`Pro`用户将生成的报告或知识中心一键发送到Notion, Obsidian等主流笔记工具。

#### **Won't-Have (本次不做)**

* **团队协作与数据共享功能**: 这需要中心化服务器，违背了产品的核心隐私原则。
* **移动端App或浏览器扩展**: 保持专注，先在桌面端Chrome生态中做到极致。
* **主动式网络爬虫**: 所有数据必须来源于用户的真实浏览行为。

### **5. 非功能性需求 (Non-Functional Requirements)**
* **性能 (Performance)** (基于TensorFlow.js优化后的目标):
    * **语义搜索响应**: < 250ms（端到端，包含查询向量化+HNSW搜索+结果排序）
    * **文本向量化**: < 45ms每1000字符（使用WebGPU加速）
    * **模型加载时间**: < 2秒（首次加载，含缓存后<100ms）
    * **大规模索引性能**: 10,000+页面搜索响应 < 75ms
    * **页面索引过程**: 后台异步调度，不影响用户浏览体验
    * **内存占用**: 扩展后台峰值 < 120MB（包含WebGPU缓存）
    * **存储管理**: 500MB总容量，LRU清理 < 10ms单次操作
* **安全 (Security)**:
    * **核心原则**: 所有用户浏览历史、页面文本和生成的AI数据（如向量）100%存储于用户本地`IndexedDB`，绝不上传至任何`Recall`的服务器。
    * `BYOK`的`API Key`在本地存储时必须使用强加密（如`AES-256`）保护。
    * 所有与第三方`LLM`的通信均由客户端直接发起，`Recall`不作代理。
* **可用性 (Usability)** (基于UX评估的改进目标):
    * **新手引导**: 新用户在有引导情况下，可以在60秒内完成首次语义搜索
    * **界面设计**: 弹窗尺寸400x600px，符合Chrome扩展设计规范
    * **防障亲和性**: 符合WCAG 2.1 AA标准，支持键盘导航和屏幕阅读器
    * **用户引导**: Pro功能的引导清晰，提供渐进式入门体验
    * **非侵入性**: 所有主动功能提供关闭选项，且交互方式不打断用户流程
* **兼容性 (Compatibility)** (技术架构更新):
    * **浏览器**: Chrome 88+ 和 Microsoft Edge 88+ (Manifest V3兼容)
    * **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)
    * **硬件支持**: 
        * WebGPU支持设备(推荐): RTX 3050+, M1 Mac+, 现代集成显卡
        * WASM降级支持: 所有现代浏览器设备
    * **网站类型**: 95%+主流网站兼容，包括静态、SPA、PWA等

### **6. 技术架构 (Technical Architecture)** (新增章节)

#### **核心架构组件**
* **AI引擎层**:
    * `TransformersJSEngine`: 主力AI引擎，支持WebGPU/WASM加速
    * `AIEngineManager`: 智能引擎选择和资源管理
    * `BatchProcessor`: 高效的批量向量化处理

* **存储层**:
    * `IndexedDBService`: 主存储服务，支持事务和版本迁移
    * `LRUCacheManager`: 智能存储空间管理，500MB自动清理
    * `VectorStorage`: 优化的向量数据存储和压缩

* **搜索引擎层**:
    * `HNSWIndex`: 高性能近似最近邻搜索算法
    * `SemanticSearch`: 统一的语义搜索接口和结果排序
    * `QueryProcessor`: 查询意图识别和预处理

* **安全层**:
    * `CryptoService`: AES-256加密服务
    * `KeyManager`: BYOK密钥管理和轮转
    * `LLMProxy`: 多服务商API直连代理

#### **数据流架构**
```
网页内容 → ContentScript → 后台处理 → AI向量化 → 存储索引
    ↓              ↓              ↓             ↓            ↓
浏览器       内容抽取      任务调度       嵌入生成    向量存储
    ↑              ↑              ↑             ↑            ↑
搜索界面 ← 结果展示 ← 语义搜索 ← HNSW索引 ← 向量检索
```

### **7. 风险与限制 (Risks and Constraints)**
* **核心假设 (Assumptions)**:
    * 用户愿意为了获得10倍的效率提升，而授权插件访问其浏览历史。
    * 我们的目标用户中，有足够比例的人拥有或愿意为了高级功能而获取自己的LLM `API Key`。
* **限制条件 (Constraints)**:
    * 严格遵守`Chrome Manifest V3`规范进行开发。
    * 无中心化服务器，所有智能和分析必须在客户端完成。
    * 开发资源有限，需要对功能优先级进行严格管理。
* **风险评估 (Risk Assessment)** (更新基于技术评估结果):
    * **技术风险**: 低配置设备上性能不佳，WebGPU兼容性问题
        * **缓解策略**: WebGPU/WASM智能降级，多模型选择，设备能力检测和自适应优化
        * **现状**: 已实现技术架构，性能表现优秀，风险降低至 **中级**
    * **UX交互风险**: 用户学习成本高，60秒首次搜索目标难以实现
        * **缓解策略**: 渐进式引导流程，上下文提示和智能推荐，键盘快捷键优化
        * **现状**: UX评估发现关键问题，需要立即改进，风险级别 **高**
    * **市场竞争风险**: Pro版价值主张不清晰，转化率不达预期
        * **缓解策略**: 强化BYOK价值体验，提供清晰的价值阶梯和成本效益分析
        * **现状**: 产品定位清晰，技术实现优秀，风险级别 **中级**
    * **政策与兼容性风险**: 浏览器厂商政策变化，Manifest V3权限收紧
        * **缓解策略**: 严格遵守Manifest V3，最小权限原则，多浏览器支持
        * **现状**: 技术验证通过，全面兼容，风险级别 **低**

### **8. 里程碑与路线图 (Milestones and Roadmap)** (更新基于多智能体评估)

#### **Phase 1: V3.0 - "AI Foundation" (目标 Q4 2025)**
* **目标**: 发布技术先进的免费版本，以本地语义搜索体验领跑市场
* **核心发布内容**: FEAT-001, FEAT-002, FEAT-003
* **技术重点**: TensorFlow.js引擎、WebGPU加速、HNSW搜索、AES-256安全
* **可交付指标**:
    * 语义搜索响应时间 < 250ms
    * 支持10,000+页面索引
    * 95%+网站兼容性
    * 符合WCAG 2.1 AA标准

#### **Phase 1.5: UX优化补充 (目标 Q4 2025末)**
* **目标**: 解决UX评估中发现的关键问题
* **关键任务**: 
    * 调整弹窗尺寸到400x600px符合PRD規范
    * 实现渐进式引导流程实现60秒首次搜索
    * 完善防障亲和性和键盘导航
* **风险缓解**: 高优先级，影响用户采用率

#### **Phase 2: V3.1 - "Pro Insight Engine" (目标 Q1 2026)**
* **目标**: 推出Pro订阅服务，验证商业模式可行性
* **核心发布内容**: FEAT-004, FEAT-005, FEAT-006
* **商业重点**: 对话式搜索、知识中心自动组织、AI報告生成
* **成功指标**: 3%付费转化率，50,000 MAU

#### **Phase 3: V3.2+ - "Ecosystem & Intelligence" (目标 Q2 2026+)**
* **目标**: 构建知识生态，建立竞争壁垒
* **扩展功能**: FEAT-007, FEAT-008, FEAT-009
* **战略重点**: 知识图谱可视化、智能推荐、生态集成

### **9. 更新总结 (Update Summary)**

#### **主要更新内容**:
1. **技术架构迁移**: 从 ONNX Runtime 完全迁移到 TensorFlow.js + WebGPU
2. **性能目标优化**: 基于技术评估结果调整性能指标
3. **UX设计改进**: 解决弹窗尺寸、引导流程、防障亲和性问题
4. **安全增强**: 新增技术架构章节，明确安全实现细节
5. **风险更新**: 基于多智能体评估结果更新风险级别

#### **下一步行动**:
1. **立即执行** (1-2周): UX改进优先级任务
2. **技术完善** (2-4周): 优化算法和性能调优
3. **测试验证** (4-6周): 全面测试和用户验证
4. **发布准备** (6-8周): 最终优化和上线准备

---

**文档更新日志**:
- 2025-06-22: 基于多智能体评估结果全面更新PRD
- 添加技术架构章节和更新总结
- 优化性能目标和UX设计要求
- 更新风险评估和缓解策略