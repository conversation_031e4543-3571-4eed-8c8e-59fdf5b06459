感谢您的澄清！我现在将继续完成基于TDD驱动的开发计划拆解。

## 测试策略概览

### 测试金字塔设计
- **单元测试 (70%)**: 覆盖所有核心业务逻辑、数据处理函数、加密算法、搜索算法等
- **集成测试 (20%)**: 测试Chrome API交互、IndexedDB操作、多模块协作等
- **端到端测试 (10%)**: 测试完整的用户流程，如配置流程、搜索流程、导入导出流程

### 测试框架选择
- **单元测试**: Jest + @testing-library/react
- **集成测试**: Jest + Chrome Extension Testing API
- **E2E测试**: Puppeteer + Jest
- **性能测试**: Jest + performance API
- **测试覆盖率**: Istanbul (目标: 整体85%，核心模块95%)

### 测试覆盖率目标
- **整体覆盖率**: ≥ 85%
- **核心模块覆盖率**:
  - 加密模块: ≥ 95%
  - 搜索引擎: ≥ 90%
  - 数据导入导出: ≥ 95%
  - API交互: ≥ 85%

### 关键测试场景
1. **混合搜索准确性**: 语义搜索与字符串匹配的结果融合
2. **数据完整性**: 导入导出过程中的时间戳保持
3. **加密安全性**: API密钥的加密存储和使用
4. **性能边界**: 大数据量下的搜索和渲染性能
5. **多语言切换**: 实时语言切换的完整性
6. **降级机制**: WebGPU不可用时的WASM降级

## TDD结构化任务列表 (任务总览)

| 任务ID | 任务名称 | 所属功能/模块 | 任务类型 | TDD阶段 | 任务描述 | 完成标准/验证方法 | 前置依赖 | 预期输出/交付物 | 复杂度 | 测试覆盖要求 |
|--------|----------|---------------|----------|----------|----------|-------------------|----------|------------------|---------|--------------|
| TEST-ENV-001 | 搭建测试环境基础设施 | 测试基础设施 | DevOps | N/A | 配置Jest、React Testing Library、Puppeteer等测试框架 | 所有测试框架可正常运行示例测试 | - | 测试配置文件、CI配置 | M | N/A |
| TEST-ENV-002 | 配置测试覆盖率工具 | 测试基础设施 | DevOps | N/A | 集成Istanbul，设置覆盖率阈值和报告生成 | 覆盖率报告正确生成，阈值检查生效 | TEST-ENV-001 | 覆盖率配置、报告模板 | S | N/A |
| TEST-ENV-003 | 创建测试数据fixtures | 测试基础设施 | Test-Design | N/A | 准备各类测试数据，包括浏览历史、用户配置等 | 测试数据结构完整，可被所有测试使用 | - | fixtures目录结构和数据文件 | M | N/A |
| TEST-401-001 | 设计数据导出时间戳测试用例 | 数据导入导出 | Test-Design | Red | 设计验证导出数据包含完整时间戳的测试场景 | 测试用例覆盖正常、边界和异常情况 | TEST-ENV-003 | 测试用例文档 | M | N/A |
| TEST-401-002 | 编写数据导出时间戳单元测试 | 数据导入导出 | Test-Implementation | Red | 实现验证visitTime、lastVisitTime、visitCount的测试 | 测试运行失败（功能未实现） | TEST-401-001 | export.test.js | M | 95% |
| IMPL-401-001 | 实现带时间戳的数据导出功能 | 数据导入导出 | Feature-Implementation | Green | 修改导出逻辑，确保包含所有时间戳信息 | 所有时间戳测试通过 | TEST-401-002 | export.js更新 | M | N/A |
| TEST-401-003 | 设计数据导入兼容性测试用例 | 数据导入导出 | Test-Design | Red | 设计V3.0和V4.0格式兼容性测试 | 测试用例覆盖各版本格式 | TEST-ENV-003 | 兼容性测试用例文档 | M | N/A |
| TEST-401-004 | 编写数据导入兼容性测试 | 数据导入导出 | Test-Implementation | Red | 实现版本检测和数据合并逻辑的测试 | 测试运行失败 | TEST-401-003 | import.test.js | L | 95% |
| IMPL-401-002 | 实现向后兼容的导入功能 | 数据导入导出 | Feature-Implementation | Green | 实现自动识别版本和合并策略 | 兼容性测试全部通过 | TEST-401-004 | import.js更新 | L | N/A |
| TEST-401-005 | 编写导入导出集成测试 | 数据导入导出 | Test-Implementation | Red | 测试完整的导出-导入流程 | 数据完整性100% | TEST-401-002,TEST-401-004 | import-export.integration.test.js | M | 90% |
| REF-401-001 | 重构数据导入导出模块 | 数据导入导出 | Refactoring | Refactor | 优化代码结构，提取公共逻辑 | 所有测试保持通过 | IMPL-401-002 | 重构后的模块代码 | M | N/A |
| TEST-402-001 | 设计混合搜索引擎测试策略 | 混合搜索引擎 | Test-Design | Red | 设计语义搜索和字符串匹配的测试场景 | 测试策略文档完成 | TEST-ENV-003 | 搜索测试策略文档 | L | N/A |
| TEST-402-002 | 编写字符串匹配算法单元测试 | 混合搜索引擎 | Test-Implementation | Red | 测试标题和URL的精确/模糊匹配 | 测试定义完成，运行失败 | TEST-402-001 | stringMatch.test.js | M | 90% |
| IMPL-402-001 | 实现字符串匹配搜索算法 | 混合搜索引擎 | Feature-Implementation | Green | 实现高效的字符串匹配算法 | 字符串匹配测试通过，响应<50ms | TEST-402-002 | stringMatch.js | M | N/A |
| TEST-402-003 | 编写搜索结果融合算法测试 | 混合搜索引擎 | Test-Implementation | Red | 测试两种搜索结果的融合和去重 | 测试运行失败 | TEST-402-001 | searchFusion.test.js | L | 90% |
| IMPL-402-002 | 实现搜索结果融合算法 | 混合搜索引擎 | Feature-Implementation | Green | 实现权重融合和去重逻辑 | 融合测试通过，去重率100% | TEST-402-003 | searchFusion.js | L | N/A |
| TEST-402-004 | 编写搜索性能测试 | 混合搜索引擎 | Test-Implementation | Red | 测试大数据量下的搜索性能 | 性能基准建立 | TEST-402-002,TEST-402-003 | search.performance.test.js | M | N/A |
| IMPL-402-003 | 优化搜索性能 | 混合搜索引擎 | Feature-Implementation | Green | 实现搜索缓存和并行处理 | 响应时间<300ms | TEST-402-004 | 性能优化后的搜索模块 | L | N/A |
| TEST-402-005 | 编写搜索降级机制测试 | 混合搜索引擎 | Test-Implementation | Red | 测试性能降级的触发和恢复 | 降级机制测试完成 | TEST-402-004 | degradation.test.js | M | 85% |
| IMPL-402-004 | 实现搜索降级机制 | 混合搜索引擎 | Feature-Implementation | Green | 实现超时检测和自动降级 | 降级测试通过 | TEST-402-005 | 降级机制实现 | M | N/A |
| TEST-403-001 | 设计配置页面防抖测试 | 配置体验优化 | Test-Design | Red | 设计输入防抖和验证延迟的测试场景 | 测试场景完整 | - | 配置测试设计文档 | S | N/A |
| TEST-403-002 | 编写配置输入防抖测试 | 配置体验优化 | Test-Implementation | Red | 测试500ms防抖延迟的正确性 | 测试运行失败 | TEST-403-001 | debounce.test.js | S | 85% |
| IMPL-403-001 | 实现配置输入防抖机制 | 配置体验优化 | Feature-Implementation | Green | 实现500ms的输入防抖 | 防抖测试通过 | TEST-403-002 | 防抖工具函数 | S | N/A |
| TEST-403-003 | 编写Chrome通知API测试 | 配置体验优化 | Test-Implementation | Red | 测试通知的显示和自动消失 | Chrome API mock完成 | TEST-403-001 | notification.test.js | M | 85% |
| IMPL-403-002 | 实现通知反馈系统 | 配置体验优化 | Feature-Implementation | Green | 使用Chrome notification API | 通知测试通过 | TEST-403-003 | notification.js | M | N/A |
| TEST-404-001 | 设计多语言切换测试 | 国际化支持 | Test-Design | Red | 设计语言切换的完整测试场景 | 测试场景涵盖5种语言 | - | i18n测试设计文档 | M | N/A |
| TEST-404-002 | 编写语言切换功能测试 | 国际化支持 | Test-Implementation | Red | 测试实时切换和持久化 | 测试运行失败 | TEST-404-001 | languageSwitch.test.js | M | 85% |
| IMPL-404-001 | 实现语言切换功能 | 国际化支持 | Feature-Implementation | Green | 实现语言选择器和切换逻辑 | 语言切换测试通过 | TEST-404-002 | 语言切换组件 | M | N/A |
| TEST-404-003 | 编写i18n资源加载测试 | 国际化支持 | Test-Implementation | Red | 测试动态语言资源加载 | 资源加载测试完成 | TEST-404-001 | i18nLoader.test.js | M | 85% |
| IMPL-404-002 | 实现i18n资源管理系统 | 国际化支持 | Feature-Implementation | Green | 实现JSON格式的多语言资源管理 | 资源加载测试通过 | TEST-404-003 | i18n资源系统 | M | N/A |
| TEST-405-001 | 设计域名屏蔽选择器测试 | 域名屏蔽管理 | Test-Design | Red | 设计域名列表加载和过滤的测试 | 测试设计完成 | TEST-ENV-003 | 域名选择器测试设计 | M | N/A |
| TEST-405-002 | 编写域名选择器性能测试 | 域名屏蔽管理 | Test-Implementation | Red | 测试1000+域名的渲染性能 | 性能测试基准建立 | TEST-405-001 | domainSelector.performance.test.js | M | N/A |
| IMPL-405-001 | 实现域名选择器组件 | 域名屏蔽管理 | Feature-Implementation | Green | 使用虚拟列表优化大量数据渲染 | 性能测试通过 | TEST-405-002 | DomainSelector组件 | L | N/A |
| TEST-405-003 | 编写通配符规则测试 | 域名屏蔽管理 | Test-Implementation | Red | 测试*.example.com等通配符规则 | 测试运行失败 | TEST-405-001 | wildcard.test.js | M | 90% |
| IMPL-405-002 | 实现通配符匹配逻辑 | 域名屏蔽管理 | Feature-Implementation | Green | 实现高效的通配符匹配算法 | 通配符测试通过 | TEST-405-003 | 通配符匹配模块 | M | N/A |
| TEST-406-001 | 编写菜单重组测试 | 配置中心重构 | Test-Implementation | Red | 测试新的菜单顺序和导航 | 测试运行失败 | - | menuReorg.test.js | S | 85% |
| IMPL-406-001 | 实现菜单重组 | 配置中心重构 | Feature-Implementation | Green | 按新顺序重组配置菜单 | 菜单测试通过 | TEST-406-001 | 重组后的菜单组件 | S | N/A |
| TEST-407-001 | 设计对话式搜索测试策略 | Pro功能-对话搜索 | Test-Design | Red | 设计多轮对话和上下文理解的测试 | 测试策略完成 | - | 对话搜索测试策略 | L | N/A |
| TEST-407-002 | 编写对话上下文管理测试 | Pro功能-对话搜索 | Test-Implementation | Red | 测试10轮对话的上下文保持 | 测试运行失败 | TEST-407-001 | conversationContext.test.js | L | 85% |
| IMPL-407-001 | 实现对话上下文管理 | Pro功能-对话搜索 | Feature-Implementation | Green | 实现对话历史和上下文追踪 | 上下文测试通过 | TEST-407-002 | 对话管理模块 | L | N/A |
| TEST-407-003 | 编写流式响应测试 | Pro功能-对话搜索 | Test-Implementation | Red | 测试SSE流式响应展示 | 测试运行失败 | TEST-407-001 | streamResponse.test.js | M | 85% |
| IMPL-407-002 | 实现流式响应处理 | Pro功能-对话搜索 | Feature-Implementation | Green | 实现流式数据处理和UI更新 | 流式响应测试通过 | TEST-407-003 | 流式响应处理器 | M | N/A |
| TEST-407-004 | 编写BYOK API集成测试 | Pro功能-对话搜索 | Test-Implementation | Red | 测试与OpenAI等API的集成 | API mock完成 | TEST-407-001 | apiIntegration.test.js | M | 85% |
| IMPL-407-003 | 实现BYOK API调用 | Pro功能-对话搜索 | Feature-Implementation | Green | 实现安全的API调用和错误处理 | API集成测试通过 | TEST-407-004 | API集成模块 | M | N/A |
| TEST-408-001 | 设计报告生成测试策略 | Pro功能-报告生成 | Test-Design | Red | 设计多种报告格式的测试场景 | 测试策略完成 | - | 报告生成测试策略 | L | N/A |
| TEST-408-002 | 编写报告模板引擎测试 | Pro功能-报告生成 | Test-Implementation | Red | 测试自定义模板的解析和渲染 | 测试运行失败 | TEST-408-001 | templateEngine.test.js | L | 90% |
| IMPL-408-001 | 实现报告模板引擎 | Pro功能-报告生成 | Feature-Implementation | Green | 实现灵活的模板系统 | 模板测试通过 | TEST-408-002 | 模板引擎 | L | N/A |
| TEST-408-003 | 编写Notion API集成测试 | Pro功能-报告生成 | Test-Implementation | Red | 测试Notion页面创建API调用 | Notion API mock完成 | TEST-408-001 | notionExport.test.js | M | 85% |
| IMPL-408-002 | 实现Notion导出功能 | Pro功能-报告生成 | Feature-Implementation | Green | 集成Notion API创建页面 | Notion导出测试通过 | TEST-408-003 | Notion导出模块 | M | N/A |
| TEST-408-004 | 编写PDF生成测试 | Pro功能-报告生成 | Test-Implementation | Red | 测试PDF格式的报告生成 | 测试运行失败 | TEST-408-001 | pdfGeneration.test.js | M | 85% |
| IMPL-408-003 | 实现PDF导出功能 | Pro功能-报告生成 | Feature-Implementation | Green | 使用PDF库生成格式化报告 | PDF生成测试通过 | TEST-408-004 | PDF生成模块 | M | N/A |
| TEST-409-001 | 编写Pro功能开关测试 | Pro功能-基础设施 | Test-Implementation | Red | 测试功能开关的控制逻辑 | 测试运行失败 | - | featureToggle.test.js | M | 90% |
| IMPL-409-001 | 实现功能开关系统 | Pro功能-基础设施 | Feature-Implementation | Green | 实现灵活的功能控制 | 功能开关测试通过 | TEST-409-001 | 功能开关模块 | M | N/A |
| TEST-409-002 | 编写使用量统计测试 | Pro功能-基础设施 | Test-Implementation | Red | 测试API调用次数统计 | 测试运行失败 | - | usageTracking.test.js | M | 85% |
| IMPL-409-002 | 实现使用量追踪 | Pro功能-基础设施 | Feature-Implementation | Green | 实现精确的使用量统计 | 使用量测试通过 | TEST-409-002 | 使用量追踪模块 | M | N/A |
| TEST-SEC-001 | 编写API密钥加密测试 | 安全模块 | Test-Implementation | Red | 测试AES-256-GCM加密和PBKDF2密钥派生 | 测试运行失败 | - | encryption.test.js | L | 95% |
| IMPL-SEC-001 | 实现API密钥加密存储 | 安全模块 | Feature-Implementation | Green | 实现安全的密钥存储方案 | 加密测试通过 | TEST-SEC-001 | 加密模块 | L | N/A |
| TEST-PERF-001 | 编写WebGPU检测和降级测试 | 性能优化 | Test-Implementation | Red | 测试WebGPU可用性检测和WASM降级 | 测试运行失败 | - | gpuDetection.test.js | M | 85% |
| IMPL-PERF-001 | 实现WebGPU检测和降级 | 性能优化 | Feature-Implementation | Green | 实现自动的性能方案选择 | 降级测试通过 | TEST-PERF-001 | GPU检测模块 | M | N/A |
| TEST-E2E-001 | 编写完整配置流程E2E测试 | 端到端测试 | Test-Implementation | Red | 测试从安装到首次搜索的完整流程 | E2E测试环境搭建完成 | 所有功能实现 | configuration.e2e.test.js | L | N/A |
| TEST-E2E-002 | 编写搜索流程E2E测试 | 端到端测试 | Test-Implementation | Red | 测试各种搜索场景的完整流程 | 测试运行失败 | 所有功能实现 | search.e2e.test.js | L | N/A |
| TEST-E2E-003 | 编写数据迁移E2E测试 | 端到端测试 | Test-Implementation | Red | 测试导出-导入的完整流程 | 测试运行失败 | 所有功能实现 | dataMigration.e2e.test.js | M | N/A |
| REF-FINAL-001 | 整体代码重构和优化 | 代码质量 | Refactoring | Refactor | 基于所有测试进行最终的代码优化 | 所有测试保持通过，覆盖率达标 | 所有测试通过 | 优化后的代码库 | L | N/A |

## 详细任务定义 (JSON数组 - 自动化处理源文件)

```json
[
  {
    "task_id": "TEST-ENV-001",
    "module": "测试基础设施",
    "description": "搭建完整的测试环境，配置Jest、React Testing Library、Puppeteer等",
    "task_type": "DevOps",
    "tdd_phase": "N/A",
    "dependencies": [],
    "complexity": "M",
    "deliverables": ["jest.config.js", "test-setup.js", ".github/workflows/test.yml"],
    "spec": {
      "frameworks": ["Jest@29", "React Testing Library@14", "Puppeteer@21"],
      "config_requirements": ["支持ES6模块", "Chrome扩展环境mock", "并行测试执行"]
    },
    "verification": {
      "method": "Manual Test Run",
      "criteria": "示例测试成功运行",
      "command": "npm test -- --version && npm run test:sample"
    },
    "notes": "确保测试环境能模拟Chrome扩展的特殊API"
  },
  {
    "task_id": "TEST-401-002",
    "module": "数据导入导出",
    "description": "编写验证导出数据包含完整时间戳的单元测试",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": ["TEST-401-001"],
    "complexity": "M",
    "deliverables": ["tests/unit/export.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_export_includes_timestamps",
          "input": {"pages": [{"url": "https://example.com", "visitTime": 1234567890, "lastVisitTime": 1234567900, "visitCount": 5}]},
          "expected_output": {"format": "v4.0", "data": [{"includes": ["visitTime", "lastVisitTime", "visitCount"]}]},
          "description": "验证导出数据包含所有时间戳字段"
        },
        {
          "name": "test_export_handles_missing_timestamps",
          "input": {"pages": [{"url": "https://example.com"}]},
          "expected_output": {"format": "v4.0", "data": [{"visitTime": "number", "lastVisitTime": "number", "visitCount": 1}]},
          "description": "验证缺失时间戳时的默认处理"
        }
      ],
      "coverage_target": 95
    },
    "verification": {
      "method": "Automated Test Execution",
      "criteria": "测试定义完成但运行失败（功能未实现）",
      "command": "jest tests/unit/export.test.js"
    },
    "notes": "测试应覆盖各种边界情况"
  },
  {
    "task_id": "IMPL-401-001",
    "module": "数据导入导出",
    "description": "实现带时间戳的数据导出功能",
    "task_type": "Feature-Implementation",
    "tdd_phase": "Green",
    "dependencies": ["TEST-401-002"],
    "complexity": "M",
    "deliverables": ["src/modules/export.js"],
    "spec": {
      "export_format": {
        "version": "4.0",
        "schema": {
          "url": "string",
          "title": "string",
          "content": "string",
          "visitTime": "number",
          "lastVisitTime": "number",
          "visitCount": "number"
        }
      },
      "backward_compatibility": true
    },
    "verification": {
      "method": "Unit Test Pass",
      "criteria": "TEST-401-002中的所有测试用例通过",
      "command": "jest tests/unit/export.test.js"
    },
    "notes": "确保导出格式与V3.0兼容"
  },
  {
    "task_id": "TEST-401-004",
    "module": "数据导入导出",
    "description": "编写数据导入兼容性测试，验证V3.0和V4.0格式的处理",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": ["TEST-401-003"],
    "complexity": "L",
    "deliverables": ["tests/unit/import.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_import_v3_format",
          "input": {"format": "v3.0", "data": [{"url": "https://example.com", "title": "Example"}]},
          "expected_output": {"pages": [{"url": "https://example.com", "visitTime": "generated", "visitCount": 1}]},
          "description": "验证V3.0格式导入时自动生成时间戳"
        },
        {
          "name": "test_import_v4_format",
          "input": {"format": "v4.0", "data": [{"url": "https://example.com", "visitTime": 1234567890}]},
          "expected_output": {"pages": [{"url": "https://example.com", "visitTime": 1234567890}]},
          "description": "验证V4.0格式完整保留时间戳"
        },
        {
          "name": "test_import_conflict_merge",
          "input": {"existing": {"url": "https://example.com", "visitTime": 1000}, "new": {"url": "https://example.com", "visitTime": 2000}},
          "expected_output": {"url": "https://example.com", "visitTime": 1000, "lastVisitTime": 2000},
          "description": "验证冲突数据的合并策略"
        }
      ],
      "coverage_target": 95
    },
    "verification": {
      "method": "Automated Test Execution",
      "criteria": "测试运行失败（功能未实现）",
      "command": "jest tests/unit/import.test.js"
    },
    "notes": "重点测试合并策略的正确性"
  },
  {
    "task_id": "TEST-402-002",
    "module": "混合搜索引擎",
    "description": "编写字符串匹配算法的单元测试",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": ["TEST-402-001"],
    "complexity": "M",
    "deliverables": ["tests/unit/stringMatch.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_exact_title_match",
          "input": {"query": "React optimization", "pages": [{"title": "React optimization guide"}, {"title": "Vue performance"}]},
          "expected_output": [{"title": "React optimization guide", "score": 1.0}],
          "description": "验证标题精确匹配"
        },
        {
          "name": "test_fuzzy_url_match",
          "input": {"query": "github.com/facebook", "pages": [{"url": "https://github.com/facebook/react"}]},
          "expected_output": [{"url": "https://github.com/facebook/react", "score": 0.8}],
          "description": "验证URL模糊匹配"
        },
        {
          "name": "test_performance_large_dataset",
          "input": {"query": "test", "page_count": 10000},
          "expected_output": {"response_time": "<50ms"},
          "description": "验证大数据集下的性能"
        }
      ],
      "coverage_target": 90
    },
    "verification": {
      "method": "Automated Test Execution",
      "criteria": "测试运行失败（功能未实现）",
      "command": "jest tests/unit/stringMatch.test.js"
    },
    "notes": "使用高效的字符串匹配算法"
  },
  {
    "task_id": "TEST-402-003",
    "module": "混合搜索引擎",
    "description": "编写搜索结果融合算法测试",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": ["TEST-402-001"],
    "complexity": "L",
    "deliverables": ["tests/unit/searchFusion.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_result_fusion_weights",
          "input": {
            "semantic_results": [{"url": "a.com", "score": 0.9}],
            "string_results": [{"url": "b.com", "score": 0.8}],
            "weights": {"semantic": 0.6, "string": 0.4}
          },
          "expected_output": [{"url": "a.com", "final_score": 0.54}, {"url": "b.com", "final_score": 0.32}],
          "description": "验证权重融合计算"
        },
        {
          "name": "test_duplicate_removal",
          "input": {
            "semantic_results": [{"url": "a.com", "score": 0.9}],
            "string_results": [{"url": "a.com", "score": 0.8}]
          },
          "expected_output": [{"url": "a.com", "final_score": "combined", "sources": ["semantic", "string"]}],
          "description": "验证重复结果去重"
        },
        {
          "name": "test_time_decay_factor",
          "input": {"results": [{"url": "a.com", "lastVisitTime": "recent"}, {"url": "b.com", "lastVisitTime": "old"}]},
          "expected_output": {"order": ["a.com", "b.com"]},
          "description": "验证时间衰减因子的影响"
        }
      ],
      "coverage_target": 90
    },
    "verification": {
      "method": "Automated Test Execution",
      "criteria": "测试运行失败（功能未实现）",
      "command": "jest tests/unit/searchFusion.test.js"
    },
    "notes": "确保融合算法的准确性和效率"
  },
  {
    "task_id": "IMPL-402-003",
    "module": "混合搜索引擎",
    "description": "优化搜索性能，实现缓存和并行处理",
    "task_type": "Feature-Implementation",
    "tdd_phase": "Green",
    "dependencies": ["TEST-402-004"],
    "complexity": "L",
    "deliverables": ["src/modules/search/performance.js"],
    "spec": {
      "optimizations": ["LRU缓存", "Web Worker并行处理", "索引预构建"],
      "cache_size": "100MB",
      "worker_count": 4
    },
    "verification": {
      "method": "Performance Test Pass",
      "criteria": "搜索响应时间<300ms",
      "command": "jest tests/unit/search.performance.test.js"
    },
    "notes": "注意内存使用和缓存失效策略"
  },
  {
    "task_id": "TEST-403-003",
    "module": "配置体验优化",
    "description": "编写Chrome通知API测试",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": ["TEST-403-001"],
    "complexity": "M",
    "deliverables": ["tests/unit/notification.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_success_notification_auto_dismiss",
          "input": {"type": "success", "message": "配置已保存"},
          "expected_output": {"displayed": true, "auto_dismiss": "3s"},
          "description": "验证成功通知3秒后自动消失"
        },
        {
          "name": "test_error_notification_manual_dismiss",
          "input": {"type": "error", "message": "配置错误"},
          "expected_output": {"displayed": true, "requires_action": true},
          "description": "验证错误通知需要手动关闭"
        }
      ],
      "coverage_target": 85
    },
    "verification": {
      "method": "Automated Test with Chrome API Mock",
      "criteria": "Chrome API正确调用",
      "command": "jest tests/unit/notification.test.js"
    },
    "notes": "需要mock Chrome notifications API"
  },
  {
    "task_id": "TEST-407-002",
    "module": "Pro功能-对话搜索",
    "description": "编写对话上下文管理测试",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": ["TEST-407-001"],
    "complexity": "L",
    "deliverables": ["tests/unit/conversationContext.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_context_preservation_10_rounds",
          "input": {"conversations": ["问题1", "回答1", "问题2基于回答1", "..."]},
          "expected_output": {"context_maintained": true, "round_count": 10},
          "description": "验证10轮对话的上下文保持"
        },
        {
          "name": "test_context_relevance_search",
          "input": {"question": "刚才提到的那篇文章", "context": ["之前搜索React性能优化"]},
          "expected_output": {"search_query": "React性能优化", "context_used": true},
          "description": "验证基于上下文的搜索查询生成"
        }
      ],
      "coverage_target": 85
    },
    "verification": {
      "method": "Automated Test Execution",
      "criteria": "测试运行失败（功能未实现）",
      "command": "jest tests/unit/conversationContext.test.js"
    },
    "notes": "需要设计高效的上下文存储结构"
  },
  {
    "task_id": "TEST-408-003",
    "module": "Pro功能-报告生成",
    "description": "编写Notion API集成测试",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": ["TEST-408-001"],
    "complexity": "M",
    "deliverables": ["tests/unit/notionExport.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_notion_page_creation",
          "input": {"report": {"title": "周报", "content": "markdown内容"}},
          "expected_output": {"status": 200, "page_id": "string"},
          "description": "验证Notion页面创建API调用"
        },
        {
          "name": "test_notion_auth_handling",
          "input": {"api_key": "invalid"},
          "expected_output": {"status": 401, "error": "Authentication failed"},
          "description": "验证API认证错误处理"
        }
      ],
      "coverage_target": 85
    },
    "verification": {
      "method": "API Mock Test",
      "criteria": "Notion API mock正确响应",
      "command": "jest tests/unit/notionExport.test.js"
    },
    "notes": "需要完整mock Notion API响应"
  },
  {
    "task_id": "TEST-SEC-001",
    "module": "安全模块",
    "description": "编写API密钥加密测试",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": [],
    "complexity": "L",
    "deliverables": ["tests/unit/encryption.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_aes_256_gcm_encryption",
          "input": {"plaintext": "sk-1234567890", "password": "userPassword"},
          "expected_output": {"encrypted": "string", "iv": "string", "salt": "string", "tag": "string"},
          "description": "验证AES-256-GCM加密"
        },
        {
          "name": "test_pbkdf2_key_derivation",
          "input": {"password": "userPassword", "salt": "32bytes", "iterations": 100000},
          "expected_output": {"key_length": 32, "time": "<100ms"},
          "description": "验证PBKDF2密钥派生参数"
        },
        {
          "name": "test_decryption_integrity",
          "input": {"encrypted_data": "tampered"},
          "expected_output": {"error": "Authentication failed"},
          "description": "验证篡改数据的完整性检查"
        }
      ],
      "coverage_target": 95
    },
    "verification": {
      "method": "Automated Test Execution",
      "criteria": "所有加密测试失败（功能未实现）",
      "command": "jest tests/unit/encryption.test.js"
    },
    "notes": "安全模块必须达到最高测试覆盖率"
  },
  {
    "task_id": "TEST-PERF-001",
    "module": "性能优化",
    "description": "编写WebGPU检测和降级测试",
    "task_type": "Test-Implementation",
    "tdd_phase": "Red",
    "dependencies": [],
    "complexity": "M",
    "deliverables": ["tests/unit/gpuDetection.test.js"],
    "test_spec": {
      "test_framework": "jest",
      "test_cases": [
        {
          "name": "test_webgpu_availability_check",
          "input": {"navigator": {"gpu": "available"}},
          "expected_output": {"use_webgpu": true},
          "description": "验证WebGPU可用时的检测"
        },
        {
          "name": "test_wasm_fallback",
          "input": {"navigator": {"gpu": null}},
          "expected_output": {"use_webgpu": false, "fallback": "wasm"},
          "description": "验证自动降级到WASM"
        },
        {
          "name": "test_performance_mode_switch",
          "input": {"runtime_switch": true},
          "expected_output": {"switch_time": "<50ms"},
          "description": "验证运行时性能模式切换"
        }
      ],
      "coverage_target": 85
    },
    "verification": {
      "method": "Automated Test Execution",
      "criteria": "测试运行失败（功能未实现）",
      "command": "jest tests/unit/gpuDetection.test.js"
    },
    "notes": "确保降级对用户透明"
  }
]
```

## TDD任务依赖关系图 (Mermaid)

```mermaid
graph TD
    subgraph "测试基础设施"
        TEST-ENV-001[搭建测试环境] --> TEST-ENV-002[配置覆盖率工具]
        TEST-ENV-001 --> TEST-ENV-003[创建测试fixtures]
    end

    subgraph "数据导入导出 TDD流程"
        TEST-401-001[设计时间戳测试] --> TEST-401-002[编写导出测试-Red]
        TEST-401-002 --> IMPL-401-001[实现导出功能-Green]
        IMPL-401-001 --> TEST-401-003[设计导入兼容性测试]
        TEST-401-003 --> TEST-401-004[编写导入测试-Red]
        TEST-401-004 --> IMPL-401-002[实现导入功能-Green]
        IMPL-401-002 --> TEST-401-005[编写集成测试]
        TEST-401-005 --> REF-401-001[重构模块-Refactor]
    end

    subgraph "混合搜索引擎 TDD流程"
        TEST-402-001[设计搜索测试策略] --> TEST-402-002[编写字符串匹配测试-Red]
        TEST-402-002 --> IMPL-402-001[实现字符串匹配-Green]
        TEST-402-001 --> TEST-402-003[编写融合算法测试-Red]
        TEST-402-003 --> IMPL-402-002[实现融合算法-Green]
        IMPL-402-001 --> TEST-402-004[编写性能测试]
        IMPL-402-002 --> TEST-402-004
        TEST-402-004 --> IMPL-402-003[优化性能-Green]
        IMPL-402-003 --> TEST-402-005[编写降级测试-Red]
        TEST-402-005 --> IMPL-402-004[实现降级机制-Green]
    end

    subgraph "配置体验优化 TDD流程"
        TEST-403-001[设计防抖测试] --> TEST-403-002[编写防抖测试-Red]
        TEST-403-002 --> IMPL-403-001[实现防抖-Green]
        TEST-403-001 --> TEST-403-003[编写通知测试-Red]
        TEST-403-003 --> IMPL-403-002[实现通知系统-Green]
    end

    subgraph "国际化支持 TDD流程"
        TEST-404-001[设计i18n测试] --> TEST-404-002[编写语言切换测试-Red]
        TEST-404-002 --> IMPL-404-001[实现语言切换-Green]
        TEST-404-001 --> TEST-404-003[编写资源加载测试-Red]
        TEST-404-003 --> IMPL-404-002[实现资源管理-Green]
    end

    subgraph "域名屏蔽 TDD流程"
        TEST-405-001[设计选择器测试] --> TEST-405-002[编写性能测试-Red]
        TEST-405-002 --> IMPL-405-001[实现选择器-Green]
        TEST-405-001 --> TEST-405-003[编写通配符测试-Red]
        TEST-405-003 --> IMPL-405-002[实现通配符匹配-Green]
    end

    subgraph "Pro功能-对话搜索 TDD流程"
        TEST-407-001[设计对话测试策略] --> TEST-407-002[编写上下文测试-Red]
        TEST-407-002 --> IMPL-407-001[实现上下文管理-Green]
        TEST-407-001 --> TEST-407-003[编写流式响应测试-Red]
        TEST-407-003 --> IMPL-407-002[实现流式处理-Green]
        TEST-407-001 --> TEST-407-004[编写API集成测试-Red]
        TEST-407-004 --> IMPL-407-003[实现API调用-Green]
    end

    subgraph "Pro功能-报告生成 TDD流程"
        TEST-408-001[设计报告测试策略] --> TEST-408-002[编写模板引擎测试-Red]
        TEST-408-002 --> IMPL-408-001[实现模板引擎-Green]
        TEST-408-001 --> TEST-408-003[编写Notion测试-Red]
        TEST-408-003 --> IMPL-408-002[实现Notion导出-Green]
        TEST-408-001 --> TEST-408-004[编写PDF测试-Red]
        TEST-408-004 --> IMPL-408-003[实现PDF生成-Green]
    end

    subgraph "安全与性能"
        TEST-SEC-001[编写加密测试-Red] --> IMPL-SEC-001[实现加密存储-Green]
        TEST-PERF-001[编写GPU检测测试-Red] --> IMPL-PERF-001[实现检测降级-Green]
    end

    subgraph "端到端测试"
        TEST-E2E-001[配置流程E2E测试]
        TEST-E2E-002[搜索流程E2E测试]
        TEST-E2E-003[数据迁移E2E测试]
    end

    %% 最终依赖
    REF-401-001 --> TEST-E2E-003
    IMPL-402-004 --> TEST-E2E-002
    IMPL-403-002 --> TEST-E2E-001
    IMPL-404-002 --> TEST-E2E-001
    IMPL-405-002 --> TEST-E2E-001
    IMPL-407-003 --> TEST-E2E-002
    IMPL-408-003 --> TEST-E2E-002
    
    TEST-E2E-001 --> REF-FINAL-001[整体重构优化]
    TEST-E2E-002 --> REF-FINAL-001
    TEST-E2E-003 --> REF-FINAL-001

    %% 样式定义
    classDef testDesign fill:#FFE4B5,stroke:#FF8C00,stroke-width:2px
    classDef testImpl fill:#FFB6C1,stroke:#DC143C,stroke-width:2px
    classDef feature fill:#98FB98,stroke:#228B22,stroke-width:2px
    classDef refactor fill:#87CEEB,stroke:#4682B4,stroke-width:2px
    classDef devops fill:#DDA0DD,stroke:#8B008B,stroke-width:2px

    class TEST-401-001,TEST-402-001,TEST-403-001,TEST-404-001,TEST-405-001,TEST-407-001,TEST-408-001 testDesign
    class TEST-401-002,TEST-401-004,TEST-402-002,TEST-402-003,TEST-402-005,TEST-403-002,TEST-403-003,TEST-404-002,TEST-404-003,TEST-405-002,TEST-405-003,TEST-407-002,TEST-407-003,TEST-407-004,TEST-408-002,TEST-408-003,TEST-408-004,TEST-SEC-001,TEST-PERF-001,TEST-E2E-001,TEST-E2E-002,TEST-E2E-003 testImpl
    class IMPL-401-001,IMPL-401-002,IMPL-402-001,IMPL-402-002,IMPL-402-003,IMPL-402-004,IMPL-403-001,IMPL-403-002,IMPL-404-001,IMPL-404-002,IMPL-405-001,IMPL-405-002,IMPL-407-001,IMPL-407-002,IMPL-407-003,IMPL-408-001,IMPL-408-002,IMPL-408-003,IMPL-SEC-001,IMPL-PERF-001 feature
    class REF-401-001,REF-FINAL-001 refactor
    class TEST-ENV-001,TEST-ENV-002,TEST-ENV-003 devops
```

这份TDD驱动的开发计划严格遵循了测试优先的原则，确保每个功能实现前都有相应的测试定义。计划涵盖了PRD v4.0中的所有核心功能，并按照Red-Green-Refactor循环组织任务流程。每个任务都有明确的验收标准和测试覆盖率要求，确保代码质量和可维护性。