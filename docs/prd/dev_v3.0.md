# Recall V3.0 AI开发计划

## **第一部分：歧义与问题列表**

所有主要问题已通过沟通得到澄清：

1. ✅ **本地AI模型选型**：确认使用轻量级的ONNX Runtime + MiniLM模型
2. ✅ **API Key加密存储**：确认使用Chrome Extension的chrome.storage.local API配合Web Crypto API进行AES-256加密，不需要导入导出
3. ✅ **IndexedDB存储策略**：确认500MB存储上限，采用LRU策略清理旧数据
4. ✅ **语义搜索技术方案**：确认使用384维向量，采用HNSW算法进行近似最近邻搜索
5. ✅ **Toast通知UI设计**：确认为右下角可关闭的Toast组件，滚动时自动隐藏
6. ✅ **Pro版本授权机制**：待定，将采用第三方付费/订阅服务商方案
7. ✅ **知识中心分组算法**：确认使用DBSCAN聚类算法基于向量相似度和时间邻近性
8. ✅ **BYOK支持范围**：确认支持OpenAI、Anthropic、DeepSeek、Google、LiteLLM、Azure
9. ✅ **内容抓取策略**：确认抓取主要文本内容，PDF/视频等仅提取标题和可见文本

## **第二部分：结构化任务列表 (任务总览)**

| 任务ID | 任务名称 | 所属功能/模块 | 任务类型 | 任务描述 | 完成标准/验证方法 | 前置依赖 | 预期输出/交付物 | 复杂度 | 风险点/注意事项 |
|--------|----------|---------------|----------|----------|-------------------|----------|------------------|--------|------------------|
| INIT-001 | 项目初始化与环境配置 | 项目基础 | DevOps | 创建Chrome Extension项目结构，配置Manifest V3，设置TypeScript开发环境 | Extension可在Chrome开发者模式下成功加载，TypeScript编译无错误 | - | manifest.json, 项目目录结构, package.json | S | 严格遵循Manifest V3权限要求 |
| INIT-002 | 配置构建工具链 | 项目基础 | DevOps | 配置Webpack构建系统，支持TypeScript、React、Web Worker和WASM | 构建产物大小<5MB，构建时间<30s | INIT-001 | webpack.config.js, tsconfig.json | M | 注意代码分割，避免单文件过大 |
| INIT-003 | 配置CI/CD流程 | 项目基础 | DevOps | 设置GitHub Actions自动化测试、构建和发布流程 | PR自动触发测试，main分支自动构建 | INIT-002 | .github/workflows/ci.yml | S | 保护API密钥等敏感信息 |
| INIT-004 | 配置开发环境Mock | 项目基础 | DevOps | 创建开发环境的Mock数据和测试页面 | 可模拟各种网页类型进行本地测试 | INIT-002 | mock-server/, test-pages/ | S | 覆盖SPA、静态页面等场景 |
| BE-001 | 实现IndexedDB存储层 | 数据存储 | Backend | 创建IndexedDB的封装层，支持页面数据、向量、索引的CRUD操作 | 单元测试覆盖率>90%，支持500MB存储限制 | INIT-002 | src/storage/IndexedDBService.ts | M | 处理好版本迁移和错误恢复 |
| BE-002 | 实现LRU缓存清理策略 | 数据存储 | Backend | 实现基于LRU的存储空间管理，自动清理旧数据 | 存储接近上限时自动清理，保持10%空闲空间 | BE-001 | src/storage/LRUCacheManager.ts | M | 避免清理用户标记的重要页面 |
| BE-003 | 实现内容抓取服务 | 内容索引 | Backend | 创建Content Script，智能抓取页面主要文本内容 | 准确抓取>95%的主流网站内容，过滤广告和导航 | INIT-002 | src/content/ContentExtractor.ts | L | 处理动态加载内容和iframe |
| BE-004 | 集成ONNX Runtime | AI模型 | Backend | 在Extension中集成ONNX Runtime WebAssembly版本 | 成功加载运行MiniLM模型，推理延迟<100ms | INIT-002 | src/ai/ONNXRuntime.ts | L | 优化WASM加载性能 |
| BE-005 | 实现文本向量化服务 | AI模型 | Backend | 使用MiniLM模型将文本转换为384维向量 | 批量处理1000字符文本<50ms，向量维度正确 | BE-004 | src/ai/TextEmbedding.ts | M | 处理长文本分块和聚合 |
| BE-006 | 实现HNSW向量索引 | 搜索引擎 | Backend | 实现HNSW算法的向量索引，支持快速相似度搜索 | 10万向量中搜索Top-10延迟<50ms，召回率>90% | BE-001, BE-005 | src/search/HNSWIndex.ts | XL | 平衡索引构建时间和搜索精度 |
| BE-007 | 实现语义搜索引擎 | 搜索引擎 | Backend | 整合向量化和索引，实现自然语言语义搜索 | 端到端搜索延迟<300ms，相关性评分准确 | BE-005, BE-006 | src/search/SemanticSearch.ts | L | 优化查询向量化和结果排序 |
| BE-008 | 实现后台索引调度器 | 内容索引 | Backend | 创建后台Service Worker，调度页面内容的异步索引 | CPU占用<5%，不影响用户浏览体验 | BE-003, BE-007 | src/background/IndexScheduler.ts | M | 处理并发索引和错误重试 |
| BE-009 | 实现API Key安全存储 | 安全 | Backend | 使用Web Crypto API实现API Key的AES-256加密存储 | 加密密钥安全生成，存储数据无法逆向 | INIT-002 | src/security/KeyStorage.ts | M | 确保密钥派生的安全性 |
| BE-010 | 实现BYOK请求代理 | AI集成 | Backend | 创建客户端直连LLM API的请求服务，支持多服务商 | 支持6种LLM服务商，错误处理完善 | BE-009 | src/ai/LLMProxy.ts | L | 处理各服务商API差异 |
| BE-011 | 实现知识中心聚类服务 | 知识管理 | Backend | 使用DBSCAN算法自动聚类相关页面形成知识中心 | 聚类准确率>80%，性能满足实时要求 | BE-007 | src/knowledge/ClusteringService.ts | L | 平衡聚类质量和性能 |
| BE-012 | 实现阅读时长检测 | 阅读助手 | Backend | 监测用户在页面的停留时间和活跃状态 | 准确检测用户阅读行为，误触发率<5% | BE-008 | src/content/ReadingDetector.ts | S | 区分活跃阅读和后台标签 |
| FE-001 | 实现Extension弹出界面 | 用户界面 | Frontend | 创建React弹出窗口UI，包含搜索框和基础导航 | UI响应流畅，符合Chrome Extension设计规范 | INIT-002 | src/popup/Popup.tsx | S | 保持界面简洁高效 |
| FE-002 | 实现语义搜索界面 | 搜索功能 | Frontend | 创建搜索输入框、结果列表、高亮显示等UI组件 | 搜索结果实时展示，支持键盘导航 | FE-001, BE-007 | src/popup/SearchView.tsx | M | 优化大量结果的虚拟滚动 |
| FE-003 | 实现设置页面 | 配置管理 | Frontend | 创建Extension选项页，管理API Key、通知设置等 | 设置即时生效，UI友好直观 | FE-001 | src/options/Options.tsx | M | 提供清晰的功能说明 |
| FE-004 | 实现Toast通知组件 | 阅读助手 | Frontend | 创建非侵入式Toast组件，用于阅读时长提醒 | 右下角显示，自动隐藏，动画流畅 | BE-012 | src/content/ToastNotification.tsx | S | 确保不影响页面交互 |
| FE-005 | 实现AI摘要展示界面 | 阅读助手 | Frontend | 创建摘要内容的展示面板，支持收起/展开 | 摘要清晰展示，支持复制和分享 | FE-004, BE-010 | src/content/SummaryPanel.tsx | M | 适配各种页面布局 |
| FE-006 | 实现API Key管理界面 | 配置管理 | Frontend | 创建支持多服务商的API Key配置界面 | 支持6种LLM服务商配置，界面直观 | FE-003, BE-009 | src/options/APIKeyManager.tsx | M | 清晰标识各服务商 |
| TEST-001 | 编写单元测试套件 | 测试 | Test | 为所有核心模块编写单元测试 | 测试覆盖率>80%，关键路径100% | BE-007, FE-002 | tests/unit/ | M | 重点测试AI和搜索模块 |
| TEST-002 | 编写集成测试 | 测试 | Test | 测试Extension完整工作流程 | 覆盖主要用户场景，自动化执行 | TEST-001 | tests/integration/ | L | 模拟真实浏览环境 |
| TEST-003 | 性能基准测试 | 测试 | Test | 测试搜索延迟、内存占用、索引性能 | 生成性能报告，标记性能瓶颈 | TEST-002 | tests/performance/ | M | 使用真实数据集测试 |
| TEST-004 | 兼容性测试 | 测试 | Test | 测试主流网站和SPA应用的兼容性 | 覆盖Top 100网站，兼容率>95% | TEST-002 | tests/compatibility/ | M | 包含国内外主流网站 |
| DOC-001 | 编写用户使用文档 | 文档 | Docs | 创建用户指南、FAQ、功能介绍 | 文档清晰完整，配有截图说明 | FE-005 | docs/user-guide.md | S | 面向非技术用户 |
| DOC-002 | 编写开发者文档 | 文档 | Docs | 创建API文档、架构说明、贡献指南 | 文档规范，示例代码完整 | TEST-003 | docs/developer-guide.md | M | 保持与代码同步更新 |
| DOC-003 | 编写隐私政策文档 | 文档 | Docs | 详细说明数据处理和隐私保护措施 | 符合法规要求，用户易理解 | DOC-001 | docs/privacy-policy.md | S | 强调本地处理特性 |

## **第三部分：详细任务定义 (JSON数组)**


### **项目规模统计**
- **总任务数**: 31个
- **任务类型分布**:
  - DevOps: 4个
  - Backend: 12个
  - Frontend: 6个
  - Test: 4个
  - Docs: 3个
- **复杂度分布**:
  - S (小): 10个
  - M (中): 14个
  - L (大): 6个
  - XL (特大): 1个

### **关键路径分析**
最长关键路径为：
1. INIT-001 → INIT-002 → BE-004 → BE-005 → BE-006 → BE-007 → BE-008 → BE-012 → FE-004 → FE-005 → DOC-001

### **里程碑建议**
1. **M1 - 基础设施完成** (第2周)
   - 完成: INIT-001, INIT-002, INIT-003, INIT-004
   - 可交付: 可构建的项目框架

2. **M2 - 核心搜索功能** (第6周)
   - 完成: BE-001至BE-007, FE-001, FE-002
   - 可交付: 基础语义搜索功能

3. **M3 - MVP版本** (第8周)
   - 完成: BE-008至BE-012, FE-003至FE-005
   - 可交付: 完整的V3.0免费版功能

4. **M4 - 生产就绪** (第10周)
   - 完成: 所有测试和文档
   - 可交付: 可发布的Chrome Extension

### **风险缓解建议**
1. **技术风险**: HNSW算法实现(BE-006)是最复杂的任务，建议提前开始原型验证
2. **性能风险**: 持续监控ONNX Runtime在低配置设备上的表现
3. **兼容性风险**: 尽早开始主流网站的兼容性测试json

## **开发计划总结**
```json
[
  {
    "task_id": "INIT-001",
    "module": "项目基础",
    "description": "创建Chrome Extension项目结构，配置Manifest V3",
    "task_type": "DevOps",
    "dependencies": [],
    "complexity": "S",
    "deliverables": ["manifest.json", "src/", "package.json", "tsconfig.json"],
    "spec": {
      "input": "Chrome Extension开发规范",
      "output": "符合Manifest V3的项目结构"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "Chrome开发者模式可成功加载Extension",
      "script_hint": "npm run build && chrome://extensions"
    },
    "notes": "确保申请必要的权限：storage, tabs, activeTab, webNavigation"
  },
  {
    "task_id": "INIT-002",
    "module": "项目基础",
    "description": "配置Webpack构建系统，支持多入口和WASM",
    "task_type": "DevOps",
    "dependencies": ["INIT-001"],
    "complexity": "M",
    "deliverables": ["webpack.config.js", "webpack.common.js", "webpack.dev.js", "webpack.prod.js"],
    "spec": {
      "input": "TypeScript源码、React组件、WASM模块",
      "output": "优化后的Extension构建产物"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "构建产物总大小<5MB，无构建错误",
      "script_hint": "npm run build:prod"
    },
    "notes": "配置代码分割，分离content script和background script"
  },
  {
    "task_id": "INIT-003",
    "module": "项目基础",
    "description": "设置GitHub Actions CI/CD流程",
    "task_type": "DevOps",
    "dependencies": ["INIT-002"],
    "complexity": "S",
    "deliverables": [".github/workflows/ci.yml", ".github/workflows/release.yml"],
    "spec": {
      "input": "Git push事件",
      "output": "自动化测试报告和构建产物"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "PR自动运行测试，main分支自动发布",
      "script_hint": "查看GitHub Actions运行日志"
    },
    "notes": "使用GitHub Secrets管理敏感信息"
  },
  {
    "task_id": "INIT-004",
    "module": "项目基础",
    "description": "创建开发环境Mock服务器和测试页面",
    "task_type": "DevOps",
    "dependencies": ["INIT-002"],
    "complexity": "S",
    "deliverables": ["mock-server/", "test-pages/", "scripts/dev-server.js"],
    "spec": {
      "input": "测试场景定义",
      "output": "本地Mock服务器和测试HTML页面"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "可访问各类测试页面，Mock API正常响应",
      "script_hint": "npm run dev:mock"
    },
    "notes": "包含SPA、静态页面、长文章、PDF、视频等测试场景"
  },
  {
    "task_id": "BE-001",
    "module": "数据存储",
    "description": "创建IndexedDB存储服务封装层",
    "task_type": "Backend",
    "dependencies": ["INIT-002"],
    "complexity": "M",
    "deliverables": ["src/storage/IndexedDBService.ts", "src/storage/types.ts", "src/storage/migrations.ts"],
    "spec": {
      "input": "页面数据、向量数据、索引数据",
      "output": "Promise<存储结果>"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "CRUD操作正确，事务处理可靠，500MB限制生效",
      "script_hint": "tests/unit/storage/IndexedDBService.test.ts"
    },
    "notes": "设计好数据库版本迁移策略，处理配额超限"
  },
  {
    "task_id": "BE-002",
    "module": "数据存储",
    "description": "实现基于LRU的存储空间管理器",
    "task_type": "Backend",
    "dependencies": ["BE-001"],
    "complexity": "M",
    "deliverables": ["src/storage/LRUCacheManager.ts", "src/storage/StorageMonitor.ts"],
    "spec": {
      "input": "存储使用情况、访问记录",
      "output": "清理决策和执行结果"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "存储达到450MB时触发清理，保持10%空闲",
      "script_hint": "tests/unit/storage/LRUCacheManager.test.ts"
    },
    "notes": "支持用户标记重要页面，避免被清理"
  },
  {
    "task_id": "BE-003",
    "module": "内容索引",
    "description": "实现智能页面内容抓取Content Script",
    "task_type": "Backend",
    "dependencies": ["INIT-002"],
    "complexity": "L",
    "deliverables": ["src/content/ContentExtractor.ts", "src/content/DOMParser.ts", "src/content/SpecialContentHandler.ts"],
    "spec": {
      "input": "DOM树",
      "output": "清洗后的页面主要文本内容"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "准确抓取95%+主流网站，PDF/视频仅提取标题",
      "script_hint": "tests/integration/ContentExtractor.test.ts"
    },
    "notes": "优先提取article、main标签，处理SPA路由变化"
  },
  {
    "task_id": "BE-004",
    "module": "AI模型",
    "description": "集成ONNX Runtime WebAssembly",
    "task_type": "Backend",
    "dependencies": ["INIT-002"],
    "complexity": "L",
    "deliverables": ["src/ai/ONNXRuntime.ts", "src/ai/ModelLoader.ts", "models/minilm.onnx"],
    "spec": {
      "input": "ONNX模型文件路径",
      "output": "初始化的推理Session"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "模型加载<2s，推理Session创建成功",
      "script_hint": "tests/integration/ONNXRuntime.test.ts"
    },
    "notes": "优化WASM初始化，考虑使用Web Worker隔离"
  },
  {
    "task_id": "BE-005",
    "module": "AI模型",
    "description": "实现MiniLM文本向量化服务",
    "task_type": "Backend",
    "dependencies": ["BE-004"],
    "complexity": "M",
    "deliverables": ["src/ai/TextEmbedding.ts", "src/ai/Tokenizer.ts", "src/ai/BatchProcessor.ts"],
    "spec": {
      "input": "原始文本字符串",
      "output": "384维Float32Array向量"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "1000字符文本处理<50ms，向量维度=384",
      "script_hint": "tests/unit/ai/TextEmbedding.test.ts"
    },
    "notes": "实现文本分块策略，支持批量处理"
  },
  {
    "task_id": "BE-006",
    "module": "搜索引擎",
    "description": "实现HNSW向量索引算法",
    "task_type": "Backend",
    "dependencies": ["BE-001", "BE-005"],
    "complexity": "XL",
    "deliverables": ["src/search/HNSWIndex.ts", "src/search/VectorMath.ts", "src/search/PriorityQueue.ts"],
    "spec": {
      "input": "向量数据和元数据",
      "output": "构建的HNSW索引结构"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "10万向量搜索<50ms，召回率>90%",
      "script_hint": "tests/unit/search/HNSWIndex.test.ts"
    },
    "notes": "参数调优：M=16, efConstruction=200, ef=50"
  },
  {
    "task_id": "BE-007",
    "module": "搜索引擎",
    "description": "实现端到端语义搜索引擎",
    "task_type": "Backend",
    "dependencies": ["BE-005", "BE-006"],
    "complexity": "L",
    "deliverables": ["src/search/SemanticSearch.ts", "src/search/ResultRanker.ts", "src/search/QueryProcessor.ts"],
    "spec": {
      "input": "自然语言查询",
      "output": "排序的搜索结果列表"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "端到端延迟<300ms，结果相关性高",
      "script_hint": "tests/integration/SemanticSearch.test.ts"
    },
    "notes": "实现混合评分：0.7*向量相似度+0.3*时间权重"
  },
  {
    "task_id": "BE-008",
    "module": "内容索引",
    "description": "创建后台索引调度Service Worker",
    "task_type": "Backend",
    "dependencies": ["BE-003", "BE-007"],
    "complexity": "M",
    "deliverables": ["src/background/IndexScheduler.ts", "src/background/TaskQueue.ts", "src/background/ResourceMonitor.ts"],
    "spec": {
      "input": "页面访问事件",
      "output": "异步索引任务执行"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "CPU占用<5%，索引延迟<30s",
      "script_hint": "tests/integration/IndexScheduler.test.ts"
    },
    "notes": "实现任务优先级队列，监控资源使用"
  },
  {
    "task_id": "BE-009",
    "module": "安全",
    "description": "实现API Key的AES-256加密存储",
    "task_type": "Backend",
    "dependencies": ["INIT-002"],
    "complexity": "M",
    "deliverables": ["src/security/KeyStorage.ts", "src/security/CryptoService.ts", "src/security/KeyManager.ts"],
    "spec": {
      "input": "明文API Key",
      "output": "加密存储的Key和解密方法"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "加密强度AES-256，密钥安全派生",
      "script_hint": "tests/unit/security/KeyStorage.test.ts"
    },
    "notes": "使用PBKDF2派生密钥，随机盐值，迭代10万次"
  },
  {
    "task_id": "BE-010",
    "module": "AI集成",
    "description": "实现BYOK模式的多服务商LLM请求代理",
    "task_type": "Backend",
    "dependencies": ["BE-009"],
    "complexity": "L",
    "deliverables": ["src/ai/LLMProxy.ts", "src/ai/providers/OpenAIProvider.ts", "src/ai/providers/AnthropicProvider.ts", "src/ai/providers/DeepSeekProvider.ts"],
    "spec": {
      "input": "用户查询和API配置",
      "output": "LLM响应结果"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "支持6种服务商，统一错误处理",
      "script_hint": "tests/integration/LLMProxy.test.ts"
    },
    "notes": "抽象统一接口，处理各服务商API差异"
  },
  {
    "task_id": "BE-011",
    "module": "知识管理",
    "description": "实现基于DBSCAN的知识中心自动聚类",
    "task_type": "Backend",
    "dependencies": ["BE-007"],
    "complexity": "L",
    "deliverables": ["src/knowledge/ClusteringService.ts", "src/knowledge/DBSCAN.ts", "src/knowledge/ClusterManager.ts"],
    "spec": {
      "input": "页面向量和访问时间数据",
      "output": "聚类后的知识中心列表"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "聚类准确率>80%，处理1000页面<1s",
      "script_hint": "tests/unit/knowledge/ClusteringService.test.ts"
    },
    "notes": "eps=0.3, minPts=3，结合时间窗口聚类"
  },
  {
    "task_id": "BE-012",
    "module": "阅读助手",
    "description": "实现页面阅读时长检测服务",
    "task_type": "Backend",
    "dependencies": ["BE-008"],
    "complexity": "S",
    "deliverables": ["src/content/ReadingDetector.ts", "src/content/ActivityMonitor.ts"],
    "spec": {
      "input": "页面活动事件（滚动、点击、可见性）",
      "output": "阅读时长和活跃状态"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "准确检测活跃阅读，误触发<5%",
      "script_hint": "tests/unit/content/ReadingDetector.test.ts"
    },
    "notes": "使用Page Visibility API和用户交互检测"
  },
  {
    "task_id": "FE-001",
    "module": "用户界面",
    "description": "创建Extension弹出窗口React界面",
    "task_type": "Frontend",
    "dependencies": ["INIT-002"],
    "complexity": "S",
    "deliverables": ["src/popup/Popup.tsx", "src/popup/App.tsx", "src/popup/index.html", "src/popup/styles.css"],
    "spec": {
      "input": "用户点击Extension图标",
      "output": "渲染React弹出界面"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "界面加载<100ms，布局响应式",
      "script_hint": "手动测试弹出窗口"
    },
    "notes": "宽度400px，高度600px，深色/浅色主题"
  },
  {
    "task_id": "FE-002",
    "module": "搜索功能",
    "description": "实现语义搜索UI组件",
    "task_type": "Frontend",
    "dependencies": ["FE-001", "BE-007"],
    "complexity": "M",
    "deliverables": ["src/popup/SearchView.tsx", "src/popup/ResultList.tsx", "src/popup/SearchBox.tsx", "src/popup/ResultItem.tsx"],
    "spec": {
      "input": "用户输入查询词",
      "output": "实时展示搜索结果"
    },
    "verification": {
      "method": "E2E Test",
      "criteria": "输入防抖300ms，虚拟滚动流畅",
      "script_hint": "tests/e2e/search.test.ts"
    },
    "notes": "支持Ctrl+K快捷键，上下键导航结果"
  },
  {
    "task_id": "FE-003",
    "module": "配置管理",
    "description": "创建Extension选项页面",
    "task_type": "Frontend",
    "dependencies": ["FE-001"],
    "complexity": "M",
    "deliverables": ["src/options/Options.tsx", "src/options/SettingsForm.tsx", "src/options/components/"],
    "spec": {
      "input": "用户配置更改",
      "output": "保存设置到chrome.storage"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "设置即时生效，表单验证完善",
      "script_hint": "手动测试选项页面"
    },
    "notes": "分组显示设置项，提供详细说明"
  },
  {
    "task_id": "FE-004",
    "module": "阅读助手",
    "description": "实现Toast通知组件",
    "task_type": "Frontend",
    "dependencies": ["BE-012"],
    "complexity": "S",
    "deliverables": ["src/content/ToastNotification.tsx", "src/content/toast.css", "src/content/animations.ts"],
    "spec": {
      "input": "显示通知的触发事件",
      "output": "右下角Toast提示"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "动画流畅，自动隐藏，不影响页面",
      "script_hint": "在测试页面触发通知"
    },
    "notes": "使用CSS动画，z-index处理层级问题"
  },
  {
    "task_id": "FE-005",
    "module": "阅读助手",
    "description": "实现AI摘要展示面板",
    "task_type": "Frontend",
    "dependencies": ["FE-004", "BE-010"],
    "complexity": "M",
    "deliverables": ["src/content/SummaryPanel.tsx", "src/content/panel.css", "src/content/MarkdownRenderer.tsx"],
    "spec": {
      "input": "AI生成的摘要文本",
      "output": "可折叠的摘要面板"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "面板定位正确，内容格式美观",
      "script_hint": "tests/e2e/summary.test.ts"
    },
    "notes": "支持Markdown渲染，适配各种页面布局"
  },
  {
    "task_id": "FE-006",
    "module": "配置管理",
    "description": "实现API Key管理界面",
    "task_type": "Frontend",
    "dependencies": ["FE-003", "BE-009"],
    "complexity": "M",
    "deliverables": ["src/options/APIKeyManager.tsx", "src/options/ProviderCard.tsx", "src/options/KeyInput.tsx"],
    "spec": {
      "input": "API Key输入和服务商选择",
      "output": "加密保存的API配置"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "支持6种服务商，密钥验证功能",
      "script_hint": "tests/integration/APIKeyManager.test.ts"
    },
    "notes": "显示服务商Logo，提供配置向导"
  },
  {
    "task_id": "TEST-001",
    "module": "测试",
    "description": "编写核心模块单元测试套件",
    "task_type": "Test",
    "dependencies": ["BE-007", "FE-002"],
    "complexity": "M",
    "deliverables": ["tests/unit/", "jest.config.js", "tests/setup.ts"],
    "spec": {
      "input": "源代码模块",
      "output": "Jest测试套件"
    },
    "verification": {
      "method": "Unit Test",
      "criteria": "覆盖率>80%，CI集成通过",
      "script_hint": "npm run test:unit"
    },
    "notes": "Mock浏览器API，隔离测试环境"
  },
  {
    "task_id": "TEST-002",
    "module": "测试",
    "description": "编写端到端集成测试",
    "task_type": "Test",
    "dependencies": ["TEST-001"],
    "complexity": "L",
    "deliverables": ["tests/integration/", "tests/e2e/", "playwright.config.ts"],
    "spec": {
      "input": "用户场景定义",
      "output": "Playwright自动化测试"
    },
    "verification": {
      "method": "E2E Test",
      "criteria": "覆盖核心用户流程，稳定可重复",
      "script_hint": "npm run test:e2e"
    },
    "notes": "使用真实Chrome环境，测试完整流程"
  },
  {
    "task_id": "TEST-003",
    "module": "测试",
    "description": "执行性能基准测试",
    "task_type": "Test",
    "dependencies": ["TEST-002"],
    "complexity": "M",
    "deliverables": ["tests/performance/", "benchmarks/results.json", "benchmarks/report.html"],
    "spec": {
      "input": "性能测试场景",
      "output": "性能指标报告"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "识别性能瓶颈，生成优化建议",
      "script_hint": "npm run test:perf"
    },
    "notes": "测试10万+页面数据，模拟真实负载"
  },
  {
    "task_id": "TEST-004",
    "module": "测试",
    "description": "执行兼容性测试",
    "task_type": "Test",
    "dependencies": ["TEST-002"],
    "complexity": "M",
    "deliverables": ["tests/compatibility/", "compatibility-report.json"],
    "spec": {
      "input": "Top 100网站列表",
      "output": "兼容性测试报告"
    },
    "verification": {
      "method": "Integration Test",
      "criteria": "兼容率>95%，标记问题网站",
      "script_hint": "npm run test:compat"
    },
    "notes": "包含国内外主流网站，SPA应用"
  },
  {
    "task_id": "DOC-001",
    "module": "文档",
    "description": "编写用户使用指南",
    "task_type": "Docs",
    "dependencies": ["FE-005"],
    "complexity": "S",
    "deliverables": ["docs/user-guide.md", "docs/faq.md", "docs/screenshots/", "docs/quick-start.md"],
    "spec": {
      "input": "功能说明和使用流程",
      "output": "Markdown格式用户文档"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "文档完整清晰，配图准确",
      "script_hint": "文档评审"
    },
    "notes": "使用简单语言，提供视频教程链接"
  },
  {
    "task_id": "DOC-002",
    "module": "文档",
    "description": "编写开发者技术文档",
    "task_type": "Docs",
    "dependencies": ["TEST-003"],
    "complexity": "M",
    "deliverables": ["docs/developer-guide.md", "docs/api-reference.md", "docs/architecture.md", "docs/contributing.md"],
    "spec": {
      "input": "代码结构和API设计",
      "output": "技术文档和架构图"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "文档准确完整，示例代码可运行",
      "script_hint": "技术评审"
    },
    "notes": "使用JSDoc生成API文档"
  },
  {
    "task_id": "DOC-003",
    "module": "文档",
    "description": "编写隐私政策和数据处理说明",
    "task_type": "Docs",
    "dependencies": ["DOC-001"],
    "complexity": "S",
    "deliverables": ["docs/privacy-policy.md", "docs/data-handling.md", "docs/security.md"],
    "spec": {
      "input": "产品数据处理流程",
      "output": "隐私政策文档"
    },
    "verification": {
      "method": "Manual Verification",
      "criteria": "符合GDPR/CCPA，表述清晰",
      "script_hint": "法务评审"
    },
    "notes": "强调100%本地处理，数据不上传"
  }
]
```

## **第四部分：任务依赖关系图 (Mermaid)**
```mermaid
graph TD
    %% 项目初始化
    INIT-001[项目初始化与环境配置]
    INIT-002[配置构建工具链]
    INIT-003[配置CI/CD流程]
    INIT-004[配置开发环境Mock]
    
    %% 后端核心模块
    BE-001[实现IndexedDB存储层]
    BE-002[实现LRU缓存清理策略]
    BE-003[实现内容抓取服务]
    BE-004[集成ONNX Runtime]
    BE-005[实现文本向量化服务]
    BE-006[实现HNSW向量索引]
    BE-007[实现语义搜索引擎]
    BE-008[实现后台索引调度器]
    BE-009[实现API Key安全存储]
    BE-010[实现BYOK请求代理]
    BE-011[实现知识中心聚类服务]
    BE-012[实现阅读时长检测]
    
    %% 前端模块
    FE-001[实现Extension弹出界面]
    FE-002[实现语义搜索界面]
    FE-003[实现设置页面]
    FE-004[实现Toast通知组件]
    FE-005[实现AI摘要展示界面]
    FE-006[实现API Key管理界面]
    
    %% 测试模块
    TEST-001[编写单元测试套件]
    TEST-002[编写集成测试]
    TEST-003[性能基准测试]
    TEST-004[兼容性测试]
    
    %% 文档模块
    DOC-001[编写用户使用文档]
    DOC-002[编写开发者文档]
    DOC-003[编写隐私政策文档]
    
    %% 定义依赖关系
    INIT-001 --> INIT-002
    INIT-002 --> INIT-003
    INIT-002 --> INIT-004
    
    %% 后端依赖
    INIT-002 --> BE-001
    INIT-002 --> BE-003
    INIT-002 --> BE-004
    INIT-002 --> BE-009
    
    BE-001 --> BE-002
    BE-004 --> BE-005
    BE-001 --> BE-006
    BE-005 --> BE-006
    BE-005 --> BE-007
    BE-006 --> BE-007
    BE-003 --> BE-008
    BE-007 --> BE-008
    BE-009 --> BE-010
    BE-007 --> BE-011
    BE-008 --> BE-012
    
    %% 前端依赖
    INIT-002 --> FE-001
    FE-001 --> FE-002
    FE-001 --> FE-003
    BE-007 --> FE-002
    BE-012 --> FE-004
    FE-004 --> FE-005
    BE-010 --> FE-005
    FE-003 --> FE-006
    BE-009 --> FE-006
    
    %% 测试依赖
    BE-007 --> TEST-001
    FE-002 --> TEST-001
    TEST-001 --> TEST-002
    TEST-002 --> TEST-003
    TEST-002 --> TEST-004
    
    %% 文档依赖
    FE-005 --> DOC-001
    TEST-003 --> DOC-002
    DOC-001 --> DOC-003
    
    %% 样式定义
    classDef init fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef backend fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef frontend fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef test fill:#e8f5e9,stroke:#1b5e20,stroke-width:2px
    classDef doc fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    
    %% 应用样式
    class INIT-001,INIT-002,INIT-003,INIT-004 init
    class BE-001,BE-002,BE-003,BE-004,BE-005,BE-006,BE-007,BE-008,BE-009,BE-010,BE-011,BE-012 backend
    class FE-001,FE-002,FE-003,FE-004,FE-005,FE-006 frontend
    class TEST-001,TEST-002,TEST-003,TEST-004 test
    class DOC-001,DOC-002,DOC-003 doc
```