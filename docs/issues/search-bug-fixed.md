# Lunr搜索问题修复报告

## 问题描述
用户报告在搜索 "Claude Code" 时只能搜到一个结果，但搜索 "claude c" 可以搜到多个结果。实际备份文件中包含4个页面含有 "claude code" 文本。

## 根本原因

### 主要问题：Lunr API使用错误
- **错误代码**: `this.index.search((q: lunr.Query) => {...})`
- **正确代码**: `this.index.query((q: lunr.Query) => {...})`

`search()` 方法期望接收一个字符串参数，而 `query()` 方法才接收一个查询构建函数。这个错误导致了 `TypeError: this.str.charAt is not a function` 异常。

### 次要问题：结果合并使用URL作为key
之前已修复的问题：`HybridSearchEngine.mergeSearchResults` 使用URL而不是ID作为唯一标识，导致同一URL的多次访问记录被合并成一条。

## 修复方案

### 1. 修复Lunr API调用
文件：`src/search/fulltext/LunrSearchEngine.ts` 第226行

```typescript
// 错误的调用方式
const searchResults = this.index.search((q: lunr.Query) => {...});

// 正确的调用方式  
const searchResults = this.index.query((q: lunr.Query) => {...});
```

### 2. 之前已修复的URL去重问题
文件：`src/search/hybrid/HybridSearchEngine.ts`

使用页面ID代替URL作为合并时的唯一标识符。

## 测试结果

### 修复前
- Lunr搜索任何查询都返回0个结果
- 错误日志：`Lunr search failed: TypeError: this.str.charAt is not a function`

### 修复后
- 手动搜索 "claude code"（精确短语）：4个页面
- Lunr搜索 "Claude Code"：8个结果
- Lunr搜索 "claude c"：6个结果

## 重要说明

### Lunr的OR逻辑
Lunr将 "Claude Code" 分解为两个词 ["claude", "code"]，并使用OR逻辑搜索。这意味着：
- 返回包含 "claude" 或 "code" 任一词的页面
- 因此搜索结果数量（8个）大于精确包含 "claude code" 短语的页面数（4个）

### 搜索结果示例
1. "能否对比一下Claude Code和Gemini CLI..." - 同时包含两个词
2. "What does the actual workflow look like for using Claude to help a beginner code?" - 同时包含两个词
3. "Dev jobs are about to get a hard reset..." - 文中提到 "Claude Code"
4. 其他包含 "claude" 或 "code" 的页面

## 结论
搜索功能已正常工作。Lunr返回的结果数量符合其OR搜索逻辑的预期行为。