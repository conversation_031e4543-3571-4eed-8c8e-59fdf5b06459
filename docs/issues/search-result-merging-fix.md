# 搜索结果合并问题修复

## 问题描述

用户报告：
- 搜索 "Claude Code" 只返回一个结果
- 搜索 "claude c" 返回多个结果
- 确认文本中包含 "claude code"（忽略大小写）

## 根本原因分析

### 1. Lunr 分词行为
- "Claude Code" 被分成 `["claude", "code"]` 两个词
- "claude c" 被分成 `["claude", "c"]` 两个词
- Lunr 使用 OR 逻辑，返回包含任一词的页面

### 2. URL 去重问题（主要原因）
- `HybridSearchEngine.mergeSearchResults` 使用 URL 作为唯一标识符
- 当多个页面具有相同 URL（如同一页面的多次访问）时，合并过程只保留最后一个
- 这导致搜索结果数量减少

示例：
```typescript
// 原始实现
mergedMap.set(result.url, { ...result });  // 使用 URL 作为 key

// 如果有 3 个相同 URL 的页面，只会保留最后一个
```

## 修复方案

修改 `HybridSearchEngine.mergeSearchResults` 方法，使用页面 ID 而不是 URL 作为唯一标识符：

```typescript
// 修复后的实现
mergedMap.set(result.id, { ...result });  // 使用 ID 作为 key
```

### 具体更改

文件：`src/search/hybrid/HybridSearchEngine.ts`

1. 第 1006 行：`mergedMap.set(result.url, ...)` → `mergedMap.set(result.id, ...)`
2. 第 1014 行：`mergedMap.get(result.url)` → `mergedMap.get(result.id)`
3. 第 1033 行：`mergedMap.set(result.url, ...)` → `mergedMap.set(result.id, ...)`
4. 添加了二级排序逻辑（按访问时间）

## 修复效果

### 修复前
- 4 个结果（2 个具有相同 URL）→ 合并后只有 2 个结果
- 丢失了历史访问记录

### 修复后
- 4 个结果 → 合并后保留全部 4 个结果
- 保留了所有历史访问记录
- 每次访问可以有不同的相关性分数

## 测试验证

创建了以下测试文件：
1. `tests/issues/search-result-merging.test.ts` - 问题重现测试
2. `scripts/test-search-issue.ts` - 问题分析脚本
3. `scripts/analyze-search-issue.ts` - 根本原因分析
4. `scripts/verify-search-fix.ts` - 修复验证脚本

## 其他建议

未来可以考虑的改进：
1. 添加选项来控制是否去重
2. 按 URL 分组显示结果（类似浏览器历史记录）
3. 实现精确短语匹配（用引号包围的查询）
4. 改进中文分词支持

## 相关文件

- 修复的文件：`src/search/hybrid/HybridSearchEngine.ts`
- 补丁文件：`patches/fix-search-merging.patch`
- 问题分析：`scripts/analyze-search-issue.ts`
- 修复验证：`scripts/verify-search-fix.ts`