# AJAX功能测试计划

## 测试概述
本文档定义了AJAX检测机制的测试流程和预期结果。

## 测试环境设置
1. 确保Recall扩展已安装并启用
2. 打开Chrome开发者工具的Console面板
3. 访问测试页面: `test-pages/ajax-test.html`

## 测试用例

### 1. XHR拦截测试

#### 测试用例 1.1: 基础XHR请求
**步骤**:
1. 点击"发送XHR请求"按钮
2. 在Console中查看日志输出

**预期结果**:
- Console显示: `[Recall Content Script] AJAX request completed`
- 包含请求URL和方法信息
- AjaxMonitor应该捕获到请求

#### 测试用例 1.2: JSON XHR请求
**步骤**:
1. 点击"发送JSON XHR请求"按钮
2. 检查Console输出

**预期结果**:
- 请求被正确拦截
- Content-Type被识别为application/json
- 请求体大小被记录

#### 测试用例 1.3: 大数据XHR请求
**步骤**:
1. 点击"发送大数据XHR请求"按钮
2. 验证性能影响

**预期结果**:
- 大数据请求被捕获
- 无明显性能延迟
- 响应大小超过minResponseSize阈值

### 2. Fetch API拦截测试

#### 测试用例 2.1: 基础Fetch请求
**步骤**:
1. 点击"发送Fetch请求"按钮
2. 查看Console输出

**预期结果**:
- Fetch请求被成功拦截
- 请求信息被记录

#### 测试用例 2.2: JSON Fetch请求
**步骤**:
1. 点击"发送JSON Fetch请求"按钮
2. 验证请求处理

**预期结果**:
- POST请求被正确捕获
- JSON数据被识别

#### 测试用例 2.3: 流式Fetch测试
**步骤**:
1. 点击"测试流式Fetch"按钮
2. 观察渐进式加载

**预期结果**:
- 流式响应被正确处理
- 内容监测器检测到变化

### 3. 动态内容检测测试

#### 测试用例 3.1: 基础动态内容
**步骤**:
1. 点击"加载动态内容"按钮
2. 等待内容加载完成
3. 检查Console输出

**预期结果**:
- EnhancedContentWatcher检测到DOM变化
- 触发内容重新提取
- Console显示: `Enhanced content change detected`

#### 测试用例 3.2: 无限滚动模拟
**步骤**:
1. 点击"模拟无限滚动"按钮多次
2. 观察新内容加载

**预期结果**:
- 每次加载触发内容检测
- SmartDebouncer防止频繁提取
- 适当的延迟后触发内容更新

#### 测试用例 3.3: 论坛评论模拟
**步骤**:
1. 点击"模拟论坛评论加载"按钮
2. 观察评论逐个加载

**预期结果**:
- ForumAdapter检测到论坛内容
- 评论被正确识别和处理
- 触发针对论坛的优化处理

### 4. 调试助手验证

#### 测试用例 4.1: AJAX监听器状态
**步骤**:
1. 点击"检查AJAX监听器状态"按钮
2. 查看输出信息

**预期结果**:
```json
{
  "isActive": true,
  "stats": {
    "totalRequests": N,
    "relevantRequests": N,
    "xhrRequests": N,
    "fetchRequests": N
  },
  "config": { ... }
}
```

#### 测试用例 4.2: 内容监测器状态
**步骤**:
1. 点击"检查内容监测器状态"按钮

**预期结果**:
- 显示活跃的观察器数量
- 显示配置信息
- 确认iframe和shadow DOM监测启用

#### 测试用例 4.3: 防抖器性能
**步骤**:
1. 快速点击多个动态内容按钮
2. 点击"检查防抖器状态"按钮

**预期结果**:
- 显示防抖任务队列
- 确认优先级正确排序
- 验证执行延迟符合配置

### 5. 性能测试

#### 测试用例 5.1: CPU使用率
**步骤**:
1. 打开Chrome任务管理器（Shift+Esc）
2. 记录基准CPU使用率
3. 执行所有AJAX测试
4. 对比CPU使用率

**预期结果**:
- CPU增加 < 5%
- 无明显卡顿

#### 测试用例 5.2: 内存使用
**步骤**:
1. 记录初始内存使用
2. 执行大量动态内容加载
3. 检查内存增长

**预期结果**:
- 内存增长 < 20MB
- 无内存泄漏迹象

### 6. 错误处理测试

#### 测试用例 6.1: 网络错误
**步骤**:
1. 断开网络连接
2. 尝试发送AJAX请求
3. 查看错误处理

**预期结果**:
- 错误被优雅处理
- 不影响扩展功能
- 适当的错误日志

#### 测试用例 6.2: 大页面处理
**步骤**:
1. 创建包含1000+元素的页面
2. 触发大量DOM变化
3. 监控性能

**预期结果**:
- 防抖机制有效工作
- 无过度资源消耗
- 内容提取正常完成

## 测试结果记录

| 测试用例 | 状态 | 备注 |
|---------|------|------|
| 1.1 基础XHR | ⏳ | - |
| 1.2 JSON XHR | ⏳ | - |
| 1.3 大数据XHR | ⏳ | - |
| 2.1 基础Fetch | ⏳ | - |
| 2.2 JSON Fetch | ⏳ | - |
| 2.3 流式Fetch | ⏳ | - |
| 3.1 动态内容 | ⏳ | - |
| 3.2 无限滚动 | ⏳ | - |
| 3.3 论坛评论 | ⏳ | - |
| 4.1 AJAX状态 | ⏳ | - |
| 4.2 监测器状态 | ⏳ | - |
| 4.3 防抖器性能 | ⏳ | - |
| 5.1 CPU测试 | ⏳ | - |
| 5.2 内存测试 | ⏳ | - |
| 6.1 错误处理 | ⏳ | - |
| 6.2 大页面 | ⏳ | - |

## 已知问题
- [ ] 待发现...

## 下一步
1. 执行所有测试用例
2. 记录测试结果
3. 识别性能瓶颈
4. 优化问题区域