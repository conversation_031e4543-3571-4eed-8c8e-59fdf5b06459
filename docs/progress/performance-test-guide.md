# 性能测试指南

## 概述
本指南说明如何使用性能基准测试工具评估AJAX拦截对性能的影响。

## 测试环境准备

### 1. 构建并加载扩展
```bash
# 构建扩展
npm run build

# 在Chrome中加载
1. 打开 chrome://extensions/
2. 启用开发者模式
3. 点击"加载已解压的扩展程序"
4. 选择 dist 目录
```

### 2. 打开测试页面
在浏览器中访问: `test-pages/performance-test.html`

## 测试类型

### 完整基准测试
- **用途**: 全面评估性能影响
- **时长**: 约2-3分钟
- **测试项目**:
  - 请求延迟（XHR和Fetch）
  - 内存使用增长
  - CPU开销
  - 请求吞吐量
  - DOM操作性能

### 快速测试
- **用途**: 快速验证基本性能
- **时长**: 约30秒
- **测试项目**: 简化版的所有测试

## 测试配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 迭代次数 | 100 | 每个测试的重复次数 |
| 预热运行 | 10 | 排除初始化影响 |
| 批量请求数 | 50 | 内存测试的请求数 |
| 压力测试时长 | 30000ms | 吞吐量测试持续时间 |

## 性能指标解读

### 🎯 目标指标
- **请求延迟**: < 5ms 额外开销
- **内存增长**: < 20MB
- **CPU开销**: < 5%
- **吞吐量损失**: < 10%

### 状态标识
- 🟢 **PASS**: 符合性能目标
- 🟡 **WARNING**: 需要关注，可能需要优化
- 🔴 **FAIL**: 性能问题，需要立即优化

## 测试流程

### 1. 基础功能验证
```javascript
// 在Console中运行
RecallDebug.checkAjaxMonitor()
RecallDebug.checkEnhancedWatcher()
RecallDebug.checkSmartDebouncer()
```

### 2. 运行性能测试
1. 点击"运行完整基准测试"按钮
2. 等待测试完成（进度条显示）
3. 查看结果卡片和图表

### 3. 分析结果
- **延迟分析**: 查看XHR和Fetch的平均延迟增加
- **内存分析**: 检查内存增长是否在合理范围
- **CPU分析**: 评估计算开销
- **吞吐量**: 确认请求处理能力

## 常见问题

### Q: 测试显示"Recall扩展未检测到"
**A**: 确保：
1. 扩展已正确加载
2. 刷新测试页面
3. 等待页面完全加载

### Q: 内存测试结果为null
**A**: Chrome可能限制了内存API访问：
1. 使用 `--enable-precise-memory-info` 标志启动Chrome
2. 或使用Chrome任务管理器手动监控

### Q: 性能结果波动很大
**A**: 
1. 关闭其他标签页和应用
2. 多次运行测试取平均值
3. 使用"快速测试"进行初步评估

## 优化建议

### 如果延迟过高
1. 检查防抖配置
2. 优化请求过滤规则
3. 减少拦截器处理逻辑

### 如果内存增长过快
1. 检查是否有内存泄漏
2. 限制缓存的请求信息
3. 实现定期清理机制

### 如果CPU开销过大
1. 优化正则表达式匹配
2. 减少同步操作
3. 使用Web Worker处理

## 测试报告示例

```
📊 性能对比报告
=====================================
📍 请求延迟影响:
  XHR平均延迟: 2.34ms → 2.89ms
  Fetch平均延迟: 1.98ms → 2.41ms
  XHR开销: +23.50%
  Fetch开销: +21.72%

📍 内存使用影响:
  内存增长: 5.23MB → 8.45MB
  额外内存开销: +3.22MB

📍 CPU影响:
  CPU开销: 0% → 2.8%

📍 吞吐量影响:
  请求/秒: 523.45 → 498.23
  吞吐量损失: 4.82%

整体评估: PASS
```

## 进阶测试

### 自定义测试场景
```javascript
// 在Console中运行自定义测试
PerformanceBenchmark.config = {
    iterations: 200,
    warmupRuns: 20,
    requestsPerBatch: 100,
    testDuration: 60000
};
PerformanceBenchmark.runBenchmark();
```

### 特定功能测试
```javascript
// 仅测试延迟
await PerformanceBenchmark.testRequestLatency();

// 仅测试内存
await PerformanceBenchmark.testMemoryUsage();

// 仅测试吞吐量
await PerformanceBenchmark.testThroughput();
```

## 导出测试结果
测试完成后，可以在Console中获取详细数据：
```javascript
// 获取完整结果
PerformanceBenchmark.results

// 导出为JSON
JSON.stringify(PerformanceBenchmark.results, null, 2)
```