# Contributing to <PERSON><PERSON><PERSON>

Thank you for your interest in contributing to Recall! This document provides guidelines and information for contributors.

## Development Setup

### Prerequisites
- Node.js 18+ 
- npm (comes with Node.js)
- Git

### Quick Start
1. Clone the repository
2. Install dependencies: `npm ci`
3. Set up development environment: `npm run hooks:install`
4. Start development: `npm run dev`

## Code Quality & Pre-commit Hooks

We use automated code quality checks to maintain high standards. These run automatically before each commit.

### Automatic Setup
```bash
npm run hooks:install
```

This will install and configure:
- **Code Formatting**: Prettier for consistent code style
- **Code Quality**: ESLint for catching common issues
- **Type Safety**: TypeScript type checking
- **Architecture Compliance**: Chrome Extension context validation
- **Security**: Dependency vulnerability scanning
- **Test Quality**: Unit tests must pass before commit

### Manual Hook Commands
```bash
# Run all hooks manually
npm run hooks:run

# Update hooks to latest versions
npm run hooks:update

# Run specific checks
npm run lint
npm run type-check
npm run check:architecture
npm run test:unit
```

## Testing Strategy

### Test Types
- **Unit Tests**: `npm run test:unit`
- **Integration Tests**: `npm run test:integration`
- **E2E Tests**: `npm run test:e2e`
- **Performance Tests**: `npm run test:performance`
- **Architecture Compliance**: `npm run check:architecture`

### Running All Tests
```bash
# Complete test suite
npm run test:all

# Watch mode for development
npm run test:watch
```

## Development Workflow

1. **Create Feature Branch**: `git checkout -b feature/your-feature-name`
2. **Make Changes**: Follow our coding standards (enforced by hooks)
3. **Test Locally**: `npm run test:all`
4. **Commit Changes**: Pre-commit hooks will automatically run
5. **Push to Remote**: `git push origin feature/your-feature-name`
6. **Create Pull Request**: GitHub Actions will run full test suite

## Architecture Guidelines

### Chrome Extension Context Rules
- **Background (Service Worker)**: No DOM access, no WebAssembly, no dynamic imports
- **Content Scripts**: Full DOM access, AI operations allowed
- **Popup/Options**: Full functionality, immediate user interaction

### AI & Performance
- Vector generation: <45ms per 1000 characters
- Search response: <250ms end-to-end
- Extension build: <5MB total size

## Code Standards

### TypeScript
- Strict mode enabled
- No `any` types (use `unknown` if needed)
- Prefer interfaces over types for object shapes
- Use proper error handling with try/catch

### React Components
- Functional components with hooks
- TypeScript props interfaces
- CSS modules for styling
- Accessibility-first design

### Testing
- Test files: `*.test.ts` or `*.spec.ts`
- Coverage requirement: 80%+ for critical modules
- Mock external dependencies
- Test both happy path and error cases

## Commit Messages

We use conventional commits for clear history:

```
type(scope): description

feat(search): add semantic vector search
fix(ui): resolve popup sizing issue
test(ai): add performance benchmarks
docs(readme): update installation guide
```

Types: `feat`, `fix`, `docs`, `test`, `refactor`, `style`, `chore`

## Pull Request Process

1. **Pre-checks**: Ensure all tests pass locally
2. **Description**: Clear description of changes and motivation
3. **Testing**: Include test cases for new features
4. **Documentation**: Update docs if needed
5. **Review**: Address reviewer feedback promptly

## Performance Considerations

### AI Operations
- Always use batch processing for multiple items
- Implement proper error handling for AI failures
- Monitor memory usage in background scripts
- Use Web Workers for heavy computations when possible

### Chrome Extension Best Practices
- Minimize permissions in manifest.json
- Use message passing between contexts
- Implement proper cleanup in background scripts
- Optimize for low-memory devices

## Getting Help

- **Issues**: Check existing issues first, then create new ones
- **Discussions**: Use GitHub Discussions for questions
- **Documentation**: Check docs/ directory for detailed guides
- **Architecture**: Review existing code patterns before implementing

## Development Tools

### Recommended VSCode Extensions
- ESLint
- Prettier
- TypeScript Hero
- Chrome Extension Tools
- GitLens

### Browser Testing
- Load unpacked extension from `dist/` directory
- Use Chrome DevTools for debugging
- Test in incognito mode for isolation
- Verify on different Chrome versions

## Release Process

1. All tests must pass in CI
2. Performance benchmarks within thresholds
3. Security audit clean
4. Architecture compliance verified
5. Manual testing completed
6. Documentation updated

Thank you for contributing to Recall! 🚀