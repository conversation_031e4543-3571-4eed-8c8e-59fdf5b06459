# AJAX Detection Enhancement - Technical Integration Guide

## Overview

This guide provides comprehensive technical details for integrating and using the enhanced AJAX detection system in the Recall browser extension.

## Architecture Summary

The AJAX detection enhancement consists of several integrated components:

```
┌─────────────────────────────────────────────────────────────┐
│                    Content Script Main                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌────────────────────────────────────┐ │
│  │   AjaxMonitor   │  │    EnhancedContentWatcher         │ │
│  │                 │  │                                    │ │
│  │ • XHR Intercept │  │ • MutationObserver                │ │
│  │ • Fetch API     │  │ • iframe Monitoring               │ │
│  │ • Error Recovery│  │ • Shadow DOM Support              │ │
│  └─────────────────┘  │ • Error Recovery                  │ │
│           │            └────────────────────────────────────┘ │
│           │                           │                       │
│  ┌─────────────────┐  ┌────────────────────────────────────┐ │
│  │ SmartDebouncer  │  │      ForumAdapter                 │ │
│  │                 │  │                                    │ │
│  │ • Intelligent   │  │ • Platform Detection              │ │
│  │   Delay Control │  │ • Site-specific Optimization      │ │
│  │ • Event Merging │  │ • Generic Forum Support           │ │
│  └─────────────────┘  └────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
              │                           │
    ┌─────────────────┐         ┌─────────────────┐
    │ ErrorRecovery   │         │ SafeInterceptor │
    │     System      │         │                 │
    └─────────────────┘         └─────────────────┘
```

## Component Integration

### 1. AjaxMonitor Integration

#### Basic Setup

```typescript
import { AjaxMonitor } from './AjaxMonitor';

const ajaxMonitor = new AjaxMonitor({
  enableXHR: true,
  enableFetch: true,
  contentCheckDelay: 300,
  minResponseSize: 100,
  debug: true
}, {
  onAjaxCompleted: (requestInfo) => {
    console.log('AJAX request completed:', requestInfo);
  },
  onRelevantContentLoaded: (requestInfo) => {
    // Trigger content extraction
    extractNewContent();
  }
});

// Start monitoring with error recovery
await ajaxMonitor.start();
```

#### Advanced Configuration

```typescript
const advancedConfig = {
  enableXHR: true,
  enableFetch: true,
  contentCheckDelay: 500,
  minResponseSize: 200,
  urlPatterns: [
    /\/api\//,
    /\/ajax\//,
    /\/json\//
  ],
  ignorePatterns: [
    /\/analytics\//,
    /\/tracking\//,
    /\.png$|\.jpg$|\.gif$/
  ],
  debug: false
};

const ajaxMonitor = new AjaxMonitor(advancedConfig, {
  onAjaxCompleted: (requestInfo) => {
    // Advanced processing
    if (requestInfo.isRelevant) {
      scheduleContentExtraction(requestInfo);
    }
  }
});
```

### 2. EnhancedContentWatcher Integration

#### Basic Setup

```typescript
import { EnhancedContentWatcher } from './EnhancedContentWatcher';

const contentWatcher = new EnhancedContentWatcher({
  enableMutationObserver: true,
  enableIframeMonitoring: true,
  enableShadowDOMMonitoring: true,
  minContentSize: 50,
  periodicCheckInterval: 10000,
  debug: true
}, {
  onContentChanged: (changeInfo) => {
    console.log('Content changed:', changeInfo);
  },
  onSignificantContentAdded: (changeInfo) => {
    // Extract significant content
    extractSignificantContent(changeInfo);
  }
});

// Start with error recovery
await contentWatcher.start();
```

#### Custom Content Selectors

```typescript
const customConfig = {
  enableMutationObserver: true,
  customContentSelectors: [
    // Forum-specific
    '.post-content',
    '.comment-body',
    '.reply-text',
    
    // Social media
    '.tweet-text',
    '.status-content',
    '.feed-item',
    
    // News sites
    '.article-body',
    '.story-content',
    '.news-text'
  ],
  ignoreSelectors: [
    '.advertisement',
    '.social-buttons',
    '.navigation',
    '.sidebar'
  ]
};
```

### 3. SmartDebouncer Integration

#### Basic Usage

```typescript
import { SmartDebouncer } from './SmartDebouncer';

const debouncer = new SmartDebouncer({
  baseDelay: 300,
  maxDelay: 2000,
  urgentThreshold: 5,
  batchSize: 10,
  adaptiveMode: true
});

// Use with content changes
debouncer.debounce('content-extraction', () => {
  extractPageContent();
}, {
  priority: 'normal',
  context: { source: 'mutation-observer' }
});

// Use with AJAX responses
debouncer.debounce('ajax-processing', () => {
  processAjaxResponse();
}, {
  priority: 'urgent',
  context: { source: 'fetch-api' }
});
```

#### Adaptive Configuration

```typescript
const adaptiveDebouncer = new SmartDebouncer({
  baseDelay: 200,
  maxDelay: 3000,
  urgentThreshold: 3,
  adaptiveMode: true,
  learningEnabled: true,  // Enable ML-based optimization
  debug: true
});

// The debouncer will automatically adjust delays based on:
// - Event frequency patterns
// - User interaction timing
// - Content loading characteristics
```

### 4. ForumAdapter Integration

#### Platform Detection

```typescript
import { ForumAdapter } from './ForumAdapter';

const forumAdapter = new ForumAdapter({
  enablePlatformDetection: true,
  enableGenericDetection: true,
  debug: true
});

// Get current platform info
const platformInfo = forumAdapter.detectPlatform();
console.log('Detected platform:', platformInfo);

// Apply platform-specific optimizations
if (platformInfo.platform !== 'unknown') {
  const optimization = forumAdapter.getOptimization(platformInfo.platform);
  applyOptimization(optimization);
}
```

#### Custom Platform Configuration

```typescript
const customPlatforms = {
  'custom-forum': {
    selectors: {
      posts: '.forum-post',
      comments: '.comment-item',
      threads: '.thread-container'
    },
    ajaxPatterns: [/\/forum\/api\//],
    debounceConfig: {
      baseDelay: 400,
      maxDelay: 1500
    }
  }
};

forumAdapter.registerCustomPlatforms(customPlatforms);
```

## Complete Integration Example

### Main Content Script Integration

```typescript
// content/index.ts
import { AjaxMonitor } from './AjaxMonitor';
import { EnhancedContentWatcher } from './EnhancedContentWatcher';
import { SmartDebouncer } from './SmartDebouncer';
import { ForumAdapter } from './ForumAdapter';

class ContentScriptMain {
  private ajaxMonitor: AjaxMonitor;
  private contentWatcher: EnhancedContentWatcher;
  private debouncer: SmartDebouncer;
  private forumAdapter: ForumAdapter;
  private isInitialized = false;

  constructor() {
    this.initializeComponents();
  }

  private initializeComponents() {
    // Initialize forum adapter first
    this.forumAdapter = new ForumAdapter({
      enablePlatformDetection: true,
      enableGenericDetection: true,
      debug: false
    });

    // Get platform-specific configuration
    const platformInfo = this.forumAdapter.detectPlatform();
    const optimization = this.forumAdapter.getOptimization(platformInfo.platform);

    // Initialize debouncer with platform optimization
    this.debouncer = new SmartDebouncer({
      ...optimization.debounceConfig,
      adaptiveMode: true,
      debug: false
    });

    // Initialize AJAX monitor
    this.ajaxMonitor = new AjaxMonitor({
      enableXHR: true,
      enableFetch: true,
      contentCheckDelay: optimization.debounceConfig.baseDelay,
      urlPatterns: optimization.ajaxPatterns,
      debug: false
    }, {
      onRelevantContentLoaded: (requestInfo) => {
        this.handleContentLoaded('ajax', requestInfo);
      }
    });

    // Initialize content watcher
    this.contentWatcher = new EnhancedContentWatcher({
      enableMutationObserver: true,
      enableIframeMonitoring: true,
      enableShadowDOMMonitoring: true,
      customContentSelectors: optimization.selectors.posts,
      debug: false
    }, {
      onSignificantContentAdded: (changeInfo) => {
        this.handleContentLoaded('mutation', changeInfo);
      }
    });
  }

  public async start(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Start all components with error recovery
      await Promise.all([
        this.ajaxMonitor.start(),
        this.contentWatcher.start()
      ]);

      this.isInitialized = true;
      console.log('AJAX detection enhancement started successfully');
    } catch (error) {
      console.error('Failed to start AJAX detection enhancement:', error);
      throw error;
    }
  }

  public stop(): void {
    if (!this.isInitialized) {
      return;
    }

    try {
      this.ajaxMonitor.stop();
      this.contentWatcher.stop();
      this.debouncer.clear();
      
      this.isInitialized = false;
      console.log('AJAX detection enhancement stopped');
    } catch (error) {
      console.error('Error stopping AJAX detection enhancement:', error);
    }
  }

  private handleContentLoaded(source: string, info: any): void {
    // Use smart debouncing for content extraction
    this.debouncer.debounce('content-extraction', () => {
      this.extractContent(source, info);
    }, {
      priority: source === 'ajax' ? 'urgent' : 'normal',
      context: { source, info }
    });
  }

  private extractContent(source: string, info: any): void {
    // Implement content extraction logic
    console.log(`Extracting content from ${source}:`, info);
    
    // Get platform-specific extraction rules
    const platformInfo = this.forumAdapter.detectPlatform();
    const optimization = this.forumAdapter.getOptimization(platformInfo.platform);
    
    // Extract using platform-optimized selectors
    const content = this.extractUsingSelectors(optimization.selectors);
    
    // Process extracted content
    this.processExtractedContent(content, source);
  }

  private extractUsingSelectors(selectors: any): any {
    // Implementation depends on your content extraction system
    // This is where you'd integrate with your existing extraction logic
    return {};
  }

  private processExtractedContent(content: any, source: string): void {
    // Send to your content processing pipeline
    // This would integrate with your existing content processing
  }

  public getHealthStatus(): any {
    return {
      ajaxMonitor: this.ajaxMonitor.getHealthStatus(),
      contentWatcher: this.contentWatcher.checkHealth(),
      isInitialized: this.isInitialized
    };
  }

  public getStats(): any {
    return {
      ajaxMonitor: this.ajaxMonitor.getStats(),
      contentWatcher: this.contentWatcher.getStats(),
      debouncer: this.debouncer.getStats()
    };
  }
}

// Initialize and start
const contentScript = new ContentScriptMain();
contentScript.start().catch(console.error);

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  contentScript.stop();
});

// Health monitoring
setInterval(() => {
  const health = contentScript.getHealthStatus();
  if (!health.ajaxMonitor.healthy || !health.contentWatcher.healthy) {
    console.warn('Health check failed:', health);
  }
}, 5 * 60 * 1000); // Check every 5 minutes
```

## Configuration Management

### Environment-Specific Configurations

#### Development Configuration

```typescript
const devConfig = {
  ajaxMonitor: {
    debug: true,
    contentCheckDelay: 100,
    minResponseSize: 10
  },
  contentWatcher: {
    debug: true,
    minContentSize: 10,
    periodicCheckInterval: 5000
  },
  debouncer: {
    debug: true,
    baseDelay: 100,
    maxDelay: 1000
  }
};
```

#### Production Configuration

```typescript
const prodConfig = {
  ajaxMonitor: {
    debug: false,
    contentCheckDelay: 300,
    minResponseSize: 100
  },
  contentWatcher: {
    debug: false,
    minContentSize: 50,
    periodicCheckInterval: 10000
  },
  debouncer: {
    debug: false,
    baseDelay: 300,
    maxDelay: 2000
  }
};
```

### Dynamic Configuration Updates

```typescript
class ConfigurationManager {
  private static instance: ConfigurationManager;
  private currentConfig: any;

  public static getInstance(): ConfigurationManager {
    if (!ConfigurationManager.instance) {
      ConfigurationManager.instance = new ConfigurationManager();
    }
    return ConfigurationManager.instance;
  }

  public updateConfig(newConfig: any): void {
    this.currentConfig = { ...this.currentConfig, ...newConfig };
    
    // Apply to running components
    this.applyConfigToComponents();
  }

  private applyConfigToComponents(): void {
    // Update component configurations
    if (window.contentScript) {
      window.contentScript.updateConfiguration(this.currentConfig);
    }
  }
}
```

## Performance Considerations

### Memory Management

```typescript
// Monitor memory usage
function monitorMemoryUsage() {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    console.log('Memory usage:', {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB',
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB',
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
    });
  }
}

// Check memory every minute
setInterval(monitorMemoryUsage, 60000);
```

### Performance Optimization

```typescript
class PerformanceOptimizer {
  private static readonly MAX_OBSERVERS = 10;
  private static readonly MAX_MEMORY_MB = 50;

  public static optimizeForDevice(): any {
    const deviceInfo = this.getDeviceInfo();
    
    if (deviceInfo.isLowEnd) {
      return {
        ajaxMonitor: {
          contentCheckDelay: 500,
          minResponseSize: 200
        },
        contentWatcher: {
          enableIframeMonitoring: false,
          enableShadowDOMMonitoring: false,
          periodicCheckInterval: 20000
        }
      };
    }
    
    return {}; // Use default config for high-end devices
  }

  private static getDeviceInfo(): any {
    const deviceMemory = (navigator as any).deviceMemory || 4;
    const hardwareConcurrency = navigator.hardwareConcurrency || 2;
    
    return {
      isLowEnd: deviceMemory <= 2 || hardwareConcurrency <= 2,
      memory: deviceMemory,
      cores: hardwareConcurrency
    };
  }
}
```

## Error Handling and Debugging

### Comprehensive Error Handling

```typescript
class ErrorHandler {
  private static errorLog: any[] = [];

  public static handleError(error: Error, context: string): void {
    const errorInfo = {
      timestamp: new Date().toISOString(),
      context,
      message: error.message,
      stack: error.stack,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    this.errorLog.push(errorInfo);
    
    // Keep only last 100 errors
    if (this.errorLog.length > 100) {
      this.errorLog.shift();
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('AJAX Enhancement Error:', errorInfo);
    }

    // Send to error reporting service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(errorInfo);
    }
  }

  private static reportError(errorInfo: any): void {
    // Implement error reporting to your service
    // Example: send to background script for logging
    chrome.runtime.sendMessage({
      type: 'ERROR_REPORT',
      data: errorInfo
    });
  }

  public static exportErrorLog(): string {
    return JSON.stringify(this.errorLog, null, 2);
  }
}

// Global error handler
window.addEventListener('error', (event) => {
  ErrorHandler.handleError(event.error, 'Global Error Handler');
});

window.addEventListener('unhandledrejection', (event) => {
  ErrorHandler.handleError(
    new Error(event.reason), 
    'Unhandled Promise Rejection'
  );
});
```

### Debug Tools

```typescript
class DebugTools {
  public static createDebugPanel(): void {
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    const panel = document.createElement('div');
    panel.id = 'ajax-debug-panel';
    panel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 300px;
      background: rgba(0,0,0,0.8);
      color: white;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      font-size: 12px;
      z-index: 10000;
      max-height: 400px;
      overflow-y: auto;
    `;

    document.body.appendChild(panel);

    // Update panel every second
    setInterval(() => {
      this.updateDebugPanel(panel);
    }, 1000);
  }

  private static updateDebugPanel(panel: HTMLElement): void {
    const stats = window.contentScript?.getStats();
    const health = window.contentScript?.getHealthStatus();

    panel.innerHTML = `
      <h3>AJAX Enhancement Debug</h3>
      <div><strong>Status:</strong> ${health?.isInitialized ? 'Active' : 'Inactive'}</div>
      <div><strong>AJAX Requests:</strong> ${stats?.ajaxMonitor?.totalRequests || 0}</div>
      <div><strong>Active Requests:</strong> ${stats?.ajaxMonitor?.activeRequests || 0}</div>
      <div><strong>Content Changes:</strong> ${stats?.contentWatcher?.totalChanges || 0}</div>
      <div><strong>Health:</strong> ${health?.ajaxMonitor?.healthy ? '✅' : '❌'}</div>
      <div><strong>Memory:</strong> ${this.getMemoryUsage()}</div>
    `;
  }

  private static getMemoryUsage(): string {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const used = Math.round(memory.usedJSHeapSize / 1024 / 1024);
      return `${used} MB`;
    }
    return 'Unknown';
  }
}

// Initialize debug tools in development
if (process.env.NODE_ENV === 'development') {
  DebugTools.createDebugPanel();
}
```

## Testing Integration

### Unit Testing Setup

```typescript
// test-utils.ts
export class TestUtils {
  public static createMockAjaxResponse(url: string, content: string): Response {
    return new Response(content, {
      status: 200,
      statusText: 'OK',
      headers: { 'Content-Type': 'application/json' }
    });
  }

  public static createMockMutationRecord(type: string, target: Element): MutationRecord {
    return {
      type: type as any,
      target,
      addedNodes: new NodeList(),
      removedNodes: new NodeList(),
      attributeName: null,
      attributeNamespace: null,
      oldValue: null,
      nextSibling: null,
      previousSibling: null
    };
  }

  public static waitForDebounce(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### Integration Testing

```typescript
// integration.test.ts
describe('AJAX Detection Integration', () => {
  let contentScript: ContentScriptMain;

  beforeEach(async () => {
    contentScript = new ContentScriptMain();
    await contentScript.start();
  });

  afterEach(() => {
    contentScript.stop();
  });

  it('should detect AJAX content changes', async () => {
    // Simulate AJAX request
    const mockResponse = TestUtils.createMockAjaxResponse(
      '/api/posts',
      JSON.stringify({ posts: ['new post'] })
    );

    // Trigger fetch
    global.fetch = jest.fn().mockResolvedValue(mockResponse);
    
    fetch('/api/posts');
    
    // Wait for processing
    await TestUtils.waitForDebounce();
    
    const stats = contentScript.getStats();
    expect(stats.ajaxMonitor.totalRequests).toBeGreaterThan(0);
  });

  it('should handle mutation observer changes', async () => {
    // Add new content to DOM
    const newElement = document.createElement('div');
    newElement.className = 'post-content';
    newElement.textContent = 'New forum post content';
    
    document.body.appendChild(newElement);
    
    // Wait for processing
    await TestUtils.waitForDebounce();
    
    const stats = contentScript.getStats();
    expect(stats.contentWatcher.totalChanges).toBeGreaterThan(0);
  });
});
```

## Migration Guide

### From Basic to Enhanced AJAX Detection

#### Step 1: Update Imports

```typescript
// Before
import { basicAjaxMonitor } from './basic-ajax';

// After
import { AjaxMonitor } from './AjaxMonitor';
import { EnhancedContentWatcher } from './EnhancedContentWatcher';
```

#### Step 2: Update Configuration

```typescript
// Before
const monitor = new basicAjaxMonitor({
  watchXHR: true,
  watchFetch: true
});

// After
const ajaxMonitor = new AjaxMonitor({
  enableXHR: true,
  enableFetch: true,
  contentCheckDelay: 300,
  minResponseSize: 100
});

const contentWatcher = new EnhancedContentWatcher({
  enableMutationObserver: true,
  enableIframeMonitoring: true
});
```

#### Step 3: Update Event Handling

```typescript
// Before
monitor.onContentLoaded = (data) => {
  processContent(data);
};

// After
ajaxMonitor.events.onRelevantContentLoaded = (requestInfo) => {
  debouncer.debounce('content-processing', () => {
    processContent(requestInfo);
  });
};

contentWatcher.events.onSignificantContentAdded = (changeInfo) => {
  debouncer.debounce('mutation-processing', () => {
    processMutation(changeInfo);
  });
};
```

## Support and Maintenance

### Monitoring Health

```typescript
// Set up health monitoring
setInterval(() => {
  const health = contentScript.getHealthStatus();
  
  if (!health.ajaxMonitor.healthy) {
    console.warn('AJAX monitor unhealthy:', health.ajaxMonitor.issues);
  }
  
  if (!health.contentWatcher.healthy) {
    console.warn('Content watcher unhealthy:', health.contentWatcher.issues);
  }
}, 5 * 60 * 1000);
```

### Performance Monitoring

```typescript
// Monitor performance metrics
class PerformanceMonitor {
  private static metrics: any = {};

  public static startTiming(operation: string): void {
    this.metrics[operation] = performance.now();
  }

  public static endTiming(operation: string): number {
    const start = this.metrics[operation];
    if (start) {
      const duration = performance.now() - start;
      delete this.metrics[operation];
      return duration;
    }
    return 0;
  }

  public static measureOperation<T>(operation: string, fn: () => T): T {
    this.startTiming(operation);
    try {
      return fn();
    } finally {
      const duration = this.endTiming(operation);
      if (duration > 100) { // Log slow operations
        console.warn(`Slow operation ${operation}: ${duration}ms`);
      }
    }
  }
}
```

This integration guide provides comprehensive coverage of how to implement and use the enhanced AJAX detection system. Follow the examples and patterns provided to ensure robust, performant integration with your existing content extraction pipeline.