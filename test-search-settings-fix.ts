/**
 * Test Script for Search Settings Fix
 * 
 * This script tests the search settings integration fix to verify that:
 * 1. SearchSettings and SearchConfigPanel use unified storage
 * 2. HybridSearchEngine reads user configuration
 * 3. Settings changes are applied in real-time
 * 
 * Run this manually to verify the fixes work correctly.
 */

import { searchConfigService } from './src/services/search-config.service';
import { searchConfigListener } from './src/services/search-config-listener';
import { HybridSearchEngine } from './src/search/hybrid/HybridSearchEngine';

async function testSearchSettingsIntegration() {
  console.log('=== Search Settings Integration Test ===');
  
  try {
    // Test 1: Load unified configuration
    console.log('\n1. Testing unified configuration loading...');
    const config = await searchConfigService.getUnifiedConfig();
    console.log('✅ Unified config loaded:', {
      engines: config.engines,
      maxResults: config.behavior.maxResults,
      timeout: config.behavior.searchTimeoutMs,
      threshold: config.behavior.fuzzySearchThreshold
    });
    
    // Test 2: Update behavior settings
    console.log('\n2. Testing behavior settings update...');
    const originalMaxResults = config.behavior.maxResults;
    const newMaxResults = originalMaxResults === 20 ? 30 : 20;
    
    await searchConfigService.updateBehaviorSetting('maxResults', newMaxResults);
    const updatedConfig = await searchConfigService.getBehaviorConfig();
    
    if (updatedConfig.maxResults === newMaxResults) {
      console.log('✅ Behavior settings update successful');
    } else {
      console.log('❌ Behavior settings update failed');
    }
    
    // Test 3: Update engine configuration
    console.log('\n3. Testing engine configuration update...');
    const engineConfig = await searchConfigService.getEngineConfig();
    const originalWeight = engineConfig.traditional.weight;
    const newWeight = originalWeight === 0.7 ? 0.6 : 0.7;
    
    const newEngineConfig = {
      ...engineConfig,
      traditional: {
        ...engineConfig.traditional,
        weight: newWeight
      },
      fulltext: {
        ...engineConfig.fulltext,
        weight: 1.0 - newWeight
      }
    };
    
    await searchConfigService.updateEngineConfig(newEngineConfig);
    const updatedEngineConfig = await searchConfigService.getEngineConfig();
    
    if (Math.abs(updatedEngineConfig.traditional.weight - newWeight) < 0.01) {
      console.log('✅ Engine configuration update successful');
    } else {
      console.log('❌ Engine configuration update failed');
    }
    
    // Test 4: Real-time configuration listening
    console.log('\n4. Testing real-time configuration listening...');
    let configChangeReceived = false;
    
    const testListener = (event: any) => {
      console.log('📡 Configuration change event received:', event.type);
      configChangeReceived = true;
    };
    
    searchConfigListener.addListener('test-listener', testListener);
    
    // Trigger a config change
    await searchConfigService.updateBehaviorSetting('enableDebugMode', !config.behavior.enableDebugMode);
    
    // Wait a moment for the event
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (configChangeReceived) {
      console.log('✅ Real-time configuration listening works');
    } else {
      console.log('❌ Real-time configuration listening failed');
    }
    
    searchConfigListener.removeListener('test-listener');
    
    // Test 5: HybridSearchEngine configuration integration
    console.log('\n5. Testing HybridSearchEngine configuration integration...');
    const searchEngine = new HybridSearchEngine();
    
    // Access private config through reflection (for testing only)
    const engineConfig = (searchEngine as any).config;
    const unifiedConfig = (searchEngine as any).unifiedSearchConfig;
    
    if (unifiedConfig && engineConfig.maxResults > 0) {
      console.log('✅ HybridSearchEngine loads unified configuration');
      console.log(`   - maxResults: ${engineConfig.maxResults}`);
      console.log(`   - timeoutMs: ${engineConfig.timeoutMs}`);
      console.log(`   - threshold: ${engineConfig.stringSearchThreshold}`);
    } else {
      console.log('❌ HybridSearchEngine configuration integration failed');
    }
    
    // Cleanup
    searchEngine.dispose();
    
    // Restore original settings
    console.log('\n6. Restoring original settings...');
    await searchConfigService.updateBehaviorSetting('maxResults', originalMaxResults);
    await searchConfigService.updateEngineConfig({
      ...engineConfig,
      traditional: { ...engineConfig.traditional, weight: originalWeight },
      fulltext: { ...engineConfig.fulltext, weight: 1.0 - originalWeight }
    });
    
    console.log('\n=== Test Summary ===');
    console.log('✅ All tests completed successfully!');
    console.log('🎉 Search settings integration fix is working correctly.');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return false;
  }
}

// Mock Chrome API for testing if not available
if (typeof globalThis !== 'undefined' && !(globalThis as any).chrome) {
  (globalThis as any).chrome = {
    storage: {
      local: {
        get: async (keys: any) => {
          console.log('Mock chrome.storage.local.get called with:', keys);
          return {};
        },
        set: async (items: any) => {
          console.log('Mock chrome.storage.local.set called with:', items);
        },
        remove: async (keys: any) => {
          console.log('Mock chrome.storage.local.remove called with:', keys);
        }
      }
    }
  };
}

// Export for manual testing
export { testSearchSettingsIntegration };

// If run directly, execute the test
if (typeof require !== 'undefined' && require.main === module) {
  testSearchSettingsIntegration()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

console.log('Search Settings Integration Test Script Created');
console.log('To run manually: import and call testSearchSettingsIntegration()');