<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>兼容性测试仪表板 - Recall Extension</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            text-align: center;
        }
        .stat-value {
            font-size: 36px;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 14px;
        }
        .perfect { color: #27ae60; }
        .good { color: #3498db; }
        .acceptable { color: #f39c12; }
        .issues { color: #e67e22; }
        .failed { color: #e74c3c; }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .section-title {
            font-size: 20px;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .site-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }
        .site-card {
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        .site-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .site-card.tested {
            border-color: #27ae60;
            background-color: #f0f9f4;
        }
        .site-card.failed {
            border-color: #e74c3c;
            background-color: #fef5f5;
        }
        .site-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .site-url {
            font-size: 12px;
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        .site-type {
            display: inline-block;
            padding: 2px 8px;
            background: #ecf0f1;
            border-radius: 12px;
            font-size: 11px;
            color: #34495e;
        }
        .test-button {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 12px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-status {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #ecf0f1;
            font-size: 12px;
        }
        .results-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .result-item {
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        .result-item:last-child {
            border-bottom: none;
        }
        .test-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .test-details {
            font-size: 13px;
            color: #7f8c8d;
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background: #2980b9;
        }
        .btn-secondary {
            background: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .log-area {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Recall扩展兼容性测试仪表板</h1>
    </div>

    <div class="container">
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-label">总测试数</div>
                <div class="stat-value" id="totalTests">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">完美兼容</div>
                <div class="stat-value perfect" id="perfectCount">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">良好</div>
                <div class="stat-value good" id="goodCount">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">可接受</div>
                <div class="stat-value acceptable" id="acceptableCount">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">有问题</div>
                <div class="stat-value issues" id="issuesCount">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">失败</div>
                <div class="stat-value failed" id="failedCount">0</div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn btn-primary" onclick="testAllSites()">测试所有网站</button>
            <button class="btn btn-secondary" onclick="exportReport()">导出报告</button>
            <button class="btn btn-secondary" onclick="clearResults()">清除结果</button>
        </div>

        <!-- 中文网站 -->
        <div class="test-section">
            <h2 class="section-title">中文网站</h2>
            <div class="site-grid" id="chineseSites"></div>
        </div>

        <!-- 英文网站 -->
        <div class="test-section">
            <h2 class="section-title">英文网站</h2>
            <div class="site-grid" id="englishSites"></div>
        </div>

        <!-- 测试结果面板 -->
        <div class="results-panel" id="resultsPanel" style="display: none;">
            <h2 class="section-title">测试结果详情</h2>
            <div id="resultDetails"></div>
        </div>

        <!-- 日志区域 -->
        <div class="log-area" id="logArea">
            等待测试开始...
        </div>
    </div>

    <script src="../scripts/compatibility-test.js"></script>
    <script>
        // 测试结果存储
        let testResults = {};
        
        // 初始化页面
        function initializeDashboard() {
            renderSiteCards('chinese', CompatibilityTest.testSites.chinese);
            renderSiteCards('english', CompatibilityTest.testSites.english);
            loadSavedResults();
        }
        
        // 渲染网站卡片
        function renderSiteCards(category, sites) {
            const container = document.getElementById(category + 'Sites');
            container.innerHTML = '';
            
            sites.forEach((site, index) => {
                const card = document.createElement('div');
                card.className = 'site-card';
                card.id = `site-${category}-${index}`;
                
                const result = testResults[site.url];
                if (result) {
                    card.classList.add(result.overall === 'FAILED' ? 'failed' : 'tested');
                }
                
                card.innerHTML = `
                    <div class="site-name">${site.name}</div>
                    <div class="site-url">${site.url}</div>
                    <span class="site-type">${site.type}</span>
                    <button class="test-button" onclick="testSite('${category}', ${index})">
                        测试
                    </button>
                    ${result ? `
                        <div class="test-status">
                            状态: <span class="${result.overall.toLowerCase()}">${result.overall}</span>
                            | 耗时: ${result.duration}ms
                        </div>
                    ` : ''}
                `;
                
                card.onclick = (e) => {
                    if (!e.target.classList.contains('test-button')) {
                        if (result) {
                            showResultDetails(result);
                        } else {
                            window.open(site.url, '_blank');
                        }
                    }
                };
                
                container.appendChild(card);
            });
        }
        
        // 测试单个网站
        async function testSite(category, index) {
            const sites = CompatibilityTest.testSites[category];
            const site = sites[index];
            
            log(`开始测试 ${site.name}...`);
            
            // 更新UI
            const button = document.querySelector(`#site-${category}-${index} .test-button`);
            button.innerHTML = '<span class="loading"></span>';
            button.disabled = true;
            
            // 在新标签页打开网站并等待
            const testWindow = window.open(site.url, '_blank');
            
            log(`请在新打开的标签页中运行测试...`);
            log(`在Console中执行: CompatibilityTest.testCurrentSite()`);
            
            // 恢复按钮
            setTimeout(() => {
                button.innerHTML = '测试';
                button.disabled = false;
            }, 3000);
        }
        
        // 测试所有网站
        async function testAllSites() {
            log('批量测试功能需要手动在每个网站上运行测试脚本');
            log('请按照以下步骤操作:');
            log('1. 点击每个网站的"测试"按钮');
            log('2. 在打开的页面Console中运行: CompatibilityTest.testCurrentSite()');
            log('3. 将结果复制到这里进行汇总');
        }
        
        // 显示结果详情
        function showResultDetails(result) {
            const panel = document.getElementById('resultsPanel');
            const details = document.getElementById('resultDetails');
            
            panel.style.display = 'block';
            
            let html = `
                <h3>${result.site.name}</h3>
                <p>URL: ${result.site.url}</p>
                <p>类型: ${result.site.type}</p>
                <p>总体评估: <span class="${result.overall.toLowerCase()}">${result.overall}</span></p>
                <p>测试时间: ${new Date(result.timestamp).toLocaleString()}</p>
                <hr>
            `;
            
            // 显示各项测试结果
            Object.entries(result.tests).forEach(([testName, testResult]) => {
                html += `
                    <div class="result-item">
                        <div class="test-name">
                            ${testResult.passed ? '✅' : '❌'} ${testName}
                        </div>
                        <div class="test-details">
                            ${JSON.stringify(testResult, null, 2)}
                        </div>
                    </div>
                `;
            });
            
            details.innerHTML = html;
            
            // 滚动到结果面板
            panel.scrollIntoView({ behavior: 'smooth' });
        }
        
        // 更新统计
        function updateStats() {
            const results = Object.values(testResults);
            
            document.getElementById('totalTests').textContent = results.length;
            document.getElementById('perfectCount').textContent = 
                results.filter(r => r.overall === 'PERFECT').length;
            document.getElementById('goodCount').textContent = 
                results.filter(r => r.overall === 'GOOD').length;
            document.getElementById('acceptableCount').textContent = 
                results.filter(r => r.overall === 'ACCEPTABLE').length;
            document.getElementById('issuesCount').textContent = 
                results.filter(r => r.overall === 'ISSUES').length;
            document.getElementById('failedCount').textContent = 
                results.filter(r => r.overall === 'FAILED').length;
        }
        
        // 日志输出
        function log(message) {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `\n[${time}] ${message}`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // 导出报告
        function exportReport() {
            const report = {
                generatedAt: new Date().toISOString(),
                results: testResults,
                summary: {
                    total: Object.keys(testResults).length,
                    perfect: Object.values(testResults).filter(r => r.overall === 'PERFECT').length,
                    good: Object.values(testResults).filter(r => r.overall === 'GOOD').length,
                    acceptable: Object.values(testResults).filter(r => r.overall === 'ACCEPTABLE').length,
                    issues: Object.values(testResults).filter(r => r.overall === 'ISSUES').length,
                    failed: Object.values(testResults).filter(r => r.overall === 'FAILED').length
                }
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], 
                { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `recall-compatibility-report-${Date.now()}.json`;
            a.click();
            
            log('报告已导出');
        }
        
        // 清除结果
        function clearResults() {
            if (confirm('确定要清除所有测试结果吗？')) {
                testResults = {};
                localStorage.removeItem('recallCompatibilityResults');
                initializeDashboard();
                updateStats();
                log('测试结果已清除');
            }
        }
        
        // 保存结果到本地存储
        function saveResults() {
            localStorage.setItem('recallCompatibilityResults', 
                JSON.stringify(testResults));
        }
        
        // 加载保存的结果
        function loadSavedResults() {
            const saved = localStorage.getItem('recallCompatibilityResults');
            if (saved) {
                testResults = JSON.parse(saved);
                updateStats();
                log('已加载保存的测试结果');
            }
        }
        
        // 监听来自测试脚本的消息
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'compatibilityTestResult') {
                const result = event.data.result;
                testResults[result.site.url] = result;
                saveResults();
                initializeDashboard();
                updateStats();
                log(`收到测试结果: ${result.site.name} - ${result.overall}`);
            }
        });
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            initializeDashboard();
            log('兼容性测试仪表板已就绪');
            log('提示: 点击网站卡片的"测试"按钮开始测试');
        });
    </script>
</body>
</html>