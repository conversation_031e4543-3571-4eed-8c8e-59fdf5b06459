<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能基准测试 - Recall Extension</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f2f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .subtitle {
            margin-top: 10px;
            opacity: 0.9;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .control-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .control-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 5px;
            color: #495057;
        }
        .control-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .results-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .result-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }
        .result-card h3 {
            margin-top: 0;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .metric:last-child {
            border-bottom: none;
        }
        .metric-label {
            color: #6c757d;
        }
        .metric-value {
            font-weight: 600;
            color: #212529;
        }
        .status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status.pass {
            background: #d4edda;
            color: #155724;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .status.fail {
            background: #f8d7da;
            color: #721c24;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
        }
        .log-container {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.5;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .log-entry {
            margin: 4px 0;
        }
        .log-time {
            color: #858585;
            margin-right: 10px;
        }
        .log-info {
            color: #4fc1ff;
        }
        .log-success {
            color: #4ec9b0;
        }
        .log-warning {
            color: #ce9178;
        }
        .log-error {
            color: #f48771;
        }
        .chart-container {
            height: 300px;
            margin-top: 20px;
        }
        #performanceChart {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚡ AJAX性能基准测试</h1>
        <div class="subtitle">测量Recall扩展对AJAX请求的性能影响</div>
    </div>

    <div class="container">
        <h2>测试配置</h2>
        <div class="control-panel">
            <div class="control-group">
                <label>迭代次数</label>
                <input type="number" id="iterations" value="100" min="10" max="1000">
            </div>
            <div class="control-group">
                <label>预热运行次数</label>
                <input type="number" id="warmupRuns" value="10" min="5" max="50">
            </div>
            <div class="control-group">
                <label>批量请求数</label>
                <input type="number" id="requestsPerBatch" value="50" min="10" max="200">
            </div>
            <div class="control-group">
                <label>压力测试时长(ms)</label>
                <input type="number" id="testDuration" value="30000" min="5000" max="60000">
            </div>
        </div>

        <div class="button-group">
            <button class="btn-primary" onclick="runBenchmark()">
                🚀 运行完整基准测试
            </button>
            <button class="btn-secondary" onclick="runQuickTest()">
                ⚡ 快速测试
            </button>
            <button class="btn-danger" onclick="stopTest()">
                ⏹️ 停止测试
            </button>
        </div>

        <div class="progress-bar" id="progressBar" style="display: none;">
            <div class="progress-fill" id="progressFill"></div>
        </div>
    </div>

    <div class="container" id="resultsContainer" style="display: none;">
        <h2>测试结果</h2>
        
        <div class="results-container">
            <div class="result-card">
                <h3>⏱️ 请求延迟</h3>
                <div id="latencyResults">
                    <div class="metric">
                        <span class="metric-label">等待测试...</span>
                    </div>
                </div>
            </div>

            <div class="result-card">
                <h3>💾 内存使用</h3>
                <div id="memoryResults">
                    <div class="metric">
                        <span class="metric-label">等待测试...</span>
                    </div>
                </div>
            </div>

            <div class="result-card">
                <h3>🔥 CPU影响</h3>
                <div id="cpuResults">
                    <div class="metric">
                        <span class="metric-label">等待测试...</span>
                    </div>
                </div>
            </div>

            <div class="result-card">
                <h3>📈 吞吐量</h3>
                <div id="throughputResults">
                    <div class="metric">
                        <span class="metric-label">等待测试...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="chart-container">
            <canvas id="performanceChart"></canvas>
        </div>
    </div>

    <div class="container">
        <h2>测试日志</h2>
        <div class="log-container" id="logContainer">
            <div class="log-entry">
                <span class="log-time">[00:00:00]</span>
                <span class="log-info">等待开始测试...</span>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../scripts/performance-benchmark.js"></script>
    <script>
        let testInProgress = false;
        let performanceChart = null;

        function updateConfig() {
            PerformanceBenchmark.config.iterations = parseInt(document.getElementById('iterations').value);
            PerformanceBenchmark.config.warmupRuns = parseInt(document.getElementById('warmupRuns').value);
            PerformanceBenchmark.config.requestsPerBatch = parseInt(document.getElementById('requestsPerBatch').value);
            PerformanceBenchmark.config.testDuration = parseInt(document.getElementById('testDuration').value);
        }

        function log(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const time = new Date().toLocaleTimeString('zh-CN', { hour12: false });
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.innerHTML = `
                <span class="log-time">[${time}]</span>
                <span class="log-${type}">${message}</span>
            `;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        async function runBenchmark() {
            if (testInProgress) {
                log('测试已在进行中', 'warning');
                return;
            }

            // 检查环境
            if (!window.PerformanceBenchmark) {
                log('性能测试工具未加载', 'error');
                return;
            }

            if (!window.RecallDebug) {
                log('Recall扩展未检测到，请确保扩展已启用', 'error');
                return;
            }

            testInProgress = true;
            updateConfig();
            
            // 显示进度和结果区域
            document.getElementById('progressBar').style.display = 'block';
            document.getElementById('resultsContainer').style.display = 'block';
            
            // 清空之前的日志
            document.getElementById('logContainer').innerHTML = '';
            
            log('开始性能基准测试...', 'info');
            log(`配置: ${JSON.stringify(PerformanceBenchmark.config)}`, 'info');

            try {
                // 更新进度
                updateProgress(10);
                
                // 运行测试
                const results = await PerformanceBenchmark.runBenchmark();
                
                updateProgress(100);
                log('测试完成！', 'success');
                
                // 显示结果
                displayResults(results);
                
            } catch (error) {
                log(`测试错误: ${error.message}`, 'error');
            } finally {
                testInProgress = false;
            }
        }

        async function runQuickTest() {
            // 使用较小的配置进行快速测试
            PerformanceBenchmark.config = {
                iterations: 20,
                warmupRuns: 5,
                requestsPerBatch: 10,
                testDuration: 5000
            };
            
            await runBenchmark();
        }

        function stopTest() {
            if (testInProgress) {
                // 这里应该有一个停止机制
                log('停止功能暂未实现', 'warning');
            }
        }

        function updateProgress(percent) {
            document.getElementById('progressFill').style.width = percent + '%';
        }

        function displayResults(results) {
            // 显示延迟结果
            if (results.withInterception.requestLatency) {
                const latency = results.withInterception.requestLatency;
                document.getElementById('latencyResults').innerHTML = `
                    <div class="metric">
                        <span class="metric-label">XHR平均延迟</span>
                        <span class="metric-value">${latency.xhr.mean} ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Fetch平均延迟</span>
                        <span class="metric-value">${latency.fetch.mean} ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">P95延迟</span>
                        <span class="metric-value">${latency.overall.p95} ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">状态</span>
                        <span class="status ${latency.overall.mean < 5 ? 'pass' : 'warning'}">
                            ${latency.overall.mean < 5 ? 'PASS' : 'WARNING'}
                        </span>
                    </div>
                `;
            }

            // 显示内存结果
            if (results.withInterception.memoryUsage) {
                const memory = results.withInterception.memoryUsage;
                document.getElementById('memoryResults').innerHTML = `
                    <div class="metric">
                        <span class="metric-label">内存增长</span>
                        <span class="metric-value">${memory.growthMB} MB</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">每请求内存</span>
                        <span class="metric-value">${memory.averageGrowthPerRequest} bytes</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">状态</span>
                        <span class="status ${memory.growthMB < 20 ? 'pass' : 'warning'}">
                            ${memory.growthMB < 20 ? 'PASS' : 'WARNING'}
                        </span>
                    </div>
                `;
            }

            // 显示CPU结果
            if (results.withInterception.cpuImpact) {
                const cpu = results.withInterception.cpuImpact;
                document.getElementById('cpuResults').innerHTML = `
                    <div class="metric">
                        <span class="metric-label">CPU开销</span>
                        <span class="metric-value">${cpu.overhead}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">基准时间</span>
                        <span class="metric-value">${cpu.baselineAvg} ms</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">状态</span>
                        <span class="status ${parseFloat(cpu.overhead) < 5 ? 'pass' : 'warning'}">
                            ${parseFloat(cpu.overhead) < 5 ? 'PASS' : 'WARNING'}
                        </span>
                    </div>
                `;
            }

            // 显示吞吐量结果
            if (results.withInterception.throughput) {
                const throughput = results.withInterception.throughput;
                document.getElementById('throughputResults').innerHTML = `
                    <div class="metric">
                        <span class="metric-label">请求/秒</span>
                        <span class="metric-value">${throughput.requestsPerSecond}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">总请求数</span>
                        <span class="metric-value">${throughput.totalRequests}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">错误率</span>
                        <span class="metric-value">${throughput.errorRate}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">状态</span>
                        <span class="status ${parseFloat(throughput.errorRate) < 1 ? 'pass' : 'fail'}">
                            ${parseFloat(throughput.errorRate) < 1 ? 'PASS' : 'FAIL'}
                        </span>
                    </div>
                `;
            }

            // 更新图表
            updateChart(results);
        }

        function updateChart(results) {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            
            if (performanceChart) {
                performanceChart.destroy();
            }

            const baseline = results.baseline;
            const intercepted = results.withInterception;

            performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['XHR延迟', 'Fetch延迟', 'CPU开销', '内存增长'],
                    datasets: [{
                        label: '基准测试',
                        data: [
                            baseline.requestLatency?.xhr.mean || 0,
                            baseline.requestLatency?.fetch.mean || 0,
                            0,
                            baseline.memoryUsage?.growthMB || 0
                        ],
                        backgroundColor: 'rgba(102, 126, 234, 0.5)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1
                    }, {
                        label: '启用拦截',
                        data: [
                            intercepted.requestLatency?.xhr.mean || 0,
                            intercepted.requestLatency?.fetch.mean || 0,
                            parseFloat(intercepted.cpuImpact?.overhead || 0),
                            intercepted.memoryUsage?.growthMB || 0
                        ],
                        backgroundColor: 'rgba(118, 75, 162, 0.5)',
                        borderColor: 'rgba(118, 75, 162, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: '性能影响对比'
                        }
                    }
                }
            });
        }

        // 页面加载时的初始化
        window.addEventListener('load', () => {
            log('性能测试页面已加载', 'info');
            
            // 检查环境
            setTimeout(() => {
                if (window.RecallDebug) {
                    log('✅ Recall扩展已检测到', 'success');
                } else {
                    log('⚠️ Recall扩展未检测到，请确保扩展已启用', 'warning');
                }
                
                if (window.PerformanceBenchmark) {
                    log('✅ 性能测试工具已加载', 'success');
                } else {
                    log('❌ 性能测试工具加载失败', 'error');
                }
            }, 1000);
        });
    </script>
</body>
</html>