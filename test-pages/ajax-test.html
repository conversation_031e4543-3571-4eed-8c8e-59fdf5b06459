<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AJAX功能测试页面 - Recall Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background-color: #45a049;
        }
        .test-area {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #e8f5e9;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .error {
            background-color: #ffebee;
            color: #c62828;
        }
        .content-area {
            min-height: 200px;
            padding: 20px;
            background-color: #fff;
            border: 2px dashed #ccc;
            margin: 20px 0;
        }
        .comment {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-left: 3px solid #2196F3;
        }
        .comment-author {
            font-weight: bold;
            color: #1976D2;
        }
        .comment-time {
            color: #666;
            font-size: 12px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            display: inline-block;
        }
        .status.success {
            background-color: #4CAF50;
            color: white;
        }
        .status.error {
            background-color: #f44336;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🧪 AJAX功能测试页面</h1>
    <p>此页面用于测试Recall扩展的AJAX拦截和动态内容检测功能。</p>

    <div class="container">
        <h2>1. XMLHttpRequest 测试</h2>
        <div class="test-area">
            <button onclick="testXHR()">发送XHR请求</button>
            <button onclick="testXHRJson()">发送JSON XHR请求</button>
            <button onclick="testXHRLarge()">发送大数据XHR请求</button>
            <div id="xhr-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>2. Fetch API 测试</h2>
        <div class="test-area">
            <button onclick="testFetch()">发送Fetch请求</button>
            <button onclick="testFetchJson()">发送JSON Fetch请求</button>
            <button onclick="testFetchStream()">测试流式Fetch</button>
            <div id="fetch-result" class="result"></div>
        </div>
    </div>

    <div class="container">
        <h2>3. 动态内容加载测试</h2>
        <div class="test-area">
            <button onclick="loadDynamicContent()">加载动态内容</button>
            <button onclick="simulateInfiniteScroll()">模拟无限滚动</button>
            <button onclick="simulateForumComments()">模拟论坛评论加载</button>
            <div id="dynamic-content" class="content-area">
                <p>动态内容将显示在这里...</p>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>4. 调试助手测试</h2>
        <div class="test-area">
            <button onclick="checkAjaxMonitor()">检查AJAX监听器状态</button>
            <button onclick="checkContentWatcher()">检查内容监测器状态</button>
            <button onclick="checkDebouncer()">检查防抖器状态</button>
            <button onclick="checkForumDetection()">检查论坛检测状态</button>
            <div id="debug-result" class="result"></div>
        </div>
    </div>

    <script>
        // 辅助函数
        function log(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = isError ? 'result error' : 'result';
        }

        function generateLongText(length = 1000) {
            const words = ['测试', '内容', '动态', '加载', 'AJAX', '请求', '数据', '文本', '示例', '扩展'];
            let text = '';
            for (let i = 0; i < length; i++) {
                text += words[Math.floor(Math.random() * words.length)] + ' ';
            }
            return text;
        }

        // XHR测试函数
        function testXHR() {
            log('xhr-result', '发送XHR请求中...');
            
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/test/simple');
            xhr.onload = function() {
                if (xhr.status === 200) {
                    log('xhr-result', `✅ XHR请求成功\n状态: ${xhr.status}\n响应: ${xhr.responseText}`);
                } else {
                    log('xhr-result', `❌ XHR请求失败\n状态: ${xhr.status}`, true);
                }
            };
            xhr.onerror = function() {
                // 模拟成功响应（因为测试API可能不存在）
                log('xhr-result', '✅ XHR请求已发送（模拟响应）\n注意：真实API不存在，但拦截器应该已经捕获了请求');
            };
            xhr.send();
        }

        function testXHRJson() {
            log('xhr-result', '发送JSON XHR请求中...');
            
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/test/json');
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onload = function() {
                log('xhr-result', `✅ JSON XHR请求成功\n状态: ${xhr.status}\n响应: ${xhr.responseText}`);
            };
            xhr.onerror = function() {
                log('xhr-result', '✅ JSON XHR请求已发送（模拟响应）');
            };
            xhr.send(JSON.stringify({ test: 'data', timestamp: Date.now() }));
        }

        function testXHRLarge() {
            log('xhr-result', '发送大数据XHR请求中...');
            
            const largeData = generateLongText(5000);
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/test/large');
            xhr.onload = function() {
                log('xhr-result', `✅ 大数据XHR请求成功\n数据大小: ${largeData.length} 字符`);
            };
            xhr.onerror = function() {
                log('xhr-result', `✅ 大数据XHR请求已发送（模拟响应）\n数据大小: ${largeData.length} 字符`);
            };
            xhr.send(largeData);
        }

        // Fetch测试函数
        async function testFetch() {
            log('fetch-result', '发送Fetch请求中...');
            
            try {
                const response = await fetch('/api/test/simple');
                const text = await response.text();
                log('fetch-result', `✅ Fetch请求成功\n状态: ${response.status}\n响应: ${text}`);
            } catch (error) {
                log('fetch-result', '✅ Fetch请求已发送（模拟响应）\n注意：真实API不存在，但拦截器应该已经捕获了请求');
            }
        }

        async function testFetchJson() {
            log('fetch-result', '发送JSON Fetch请求中...');
            
            try {
                const response = await fetch('/api/test/json', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test: 'data', timestamp: Date.now() })
                });
                const data = await response.json();
                log('fetch-result', `✅ JSON Fetch请求成功\n数据: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                log('fetch-result', '✅ JSON Fetch请求已发送（模拟响应）');
            }
        }

        async function testFetchStream() {
            log('fetch-result', '测试流式Fetch中...');
            
            // 模拟流式响应
            const chunks = ['第一块数据... ', '第二块数据... ', '第三块数据... ', '流式传输完成！'];
            let result = '';
            
            for (const chunk of chunks) {
                result += chunk;
                log('fetch-result', `流式数据接收中...\n${result}`);
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('fetch-result', `✅ 流式Fetch测试完成\n总数据: ${result}`);
        }

        // 动态内容测试函数
        let contentCounter = 0;
        
        function loadDynamicContent() {
            const container = document.getElementById('dynamic-content');
            container.innerHTML = '<p>正在加载内容...</p>';
            
            setTimeout(() => {
                contentCounter++;
                const newContent = `
                    <div class="dynamic-item">
                        <h3>动态加载的内容 #${contentCounter}</h3>
                        <p>${generateLongText(200)}</p>
                        <p class="status success">加载时间: ${new Date().toLocaleTimeString()}</p>
                    </div>
                `;
                container.innerHTML = newContent;
            }, 1000);
        }

        let scrollCounter = 0;
        function simulateInfiniteScroll() {
            const container = document.getElementById('dynamic-content');
            
            const loadMore = () => {
                for (let i = 0; i < 3; i++) {
                    scrollCounter++;
                    const item = document.createElement('div');
                    item.className = 'comment';
                    item.innerHTML = `
                        <div class="comment-author">用户${scrollCounter}</div>
                        <div class="comment-time">${new Date().toLocaleTimeString()}</div>
                        <p>这是第 ${scrollCounter} 条动态加载的内容。${generateLongText(50)}</p>
                    `;
                    container.appendChild(item);
                }
            };
            
            // 初始加载
            if (scrollCounter === 0) {
                container.innerHTML = '<h4>无限滚动内容区域</h4>';
            }
            
            loadMore();
            
            // 模拟AJAX请求
            fetch('/api/comments/more?page=' + Math.ceil(scrollCounter / 3))
                .catch(() => console.log('模拟AJAX请求已发送'));
        }

        function simulateForumComments() {
            const container = document.getElementById('dynamic-content');
            container.innerHTML = '<h4>论坛评论区</h4>';
            
            const comments = [
                { author: '张三', content: '这是一个很好的测试页面！', time: '2分钟前' },
                { author: '李四', content: '同意楼上，AJAX拦截功能很实用。', time: '1分钟前' },
                { author: '王五', content: '期待看到更多动态内容检测的改进。', time: '刚刚' }
            ];
            
            comments.forEach((comment, index) => {
                setTimeout(() => {
                    const commentEl = document.createElement('div');
                    commentEl.className = 'comment';
                    commentEl.innerHTML = `
                        <div class="comment-author">${comment.author}</div>
                        <div class="comment-time">${comment.time}</div>
                        <p>${comment.content}</p>
                    `;
                    container.appendChild(commentEl);
                    
                    // 模拟论坛AJAX请求
                    fetch('/api/forum/comment/' + (index + 1))
                        .catch(() => console.log('模拟论坛AJAX请求已发送'));
                }, (index + 1) * 1000);
            });
        }

        // 调试助手测试函数
        function checkAjaxMonitor() {
            if (window.RecallDebug && window.RecallDebug.checkAjaxMonitor) {
                const status = window.RecallDebug.checkAjaxMonitor();
                log('debug-result', `AJAX监听器状态:\n${JSON.stringify(status, null, 2)}`);
            } else {
                log('debug-result', '❌ RecallDebug.checkAjaxMonitor 不可用', true);
            }
        }

        function checkContentWatcher() {
            if (window.RecallDebug && window.RecallDebug.checkEnhancedWatcher) {
                const status = window.RecallDebug.checkEnhancedWatcher();
                log('debug-result', `内容监测器状态:\n${JSON.stringify(status, null, 2)}`);
            } else {
                log('debug-result', '❌ RecallDebug.checkEnhancedWatcher 不可用', true);
            }
        }

        function checkDebouncer() {
            if (window.RecallDebug && window.RecallDebug.checkSmartDebouncer) {
                const status = window.RecallDebug.checkSmartDebouncer();
                log('debug-result', `智能防抖器状态:\n${JSON.stringify(status, null, 2)}`);
            } else {
                log('debug-result', '❌ RecallDebug.checkSmartDebouncer 不可用', true);
            }
        }

        function checkForumDetection() {
            if (window.RecallDebug && window.RecallDebug.checkForumDetection) {
                const status = window.RecallDebug.checkForumDetection();
                log('debug-result', `论坛检测状态:\n${JSON.stringify(status, null, 2)}`);
            } else {
                log('debug-result', '❌ RecallDebug.checkForumDetection 不可用', true);
            }
        }

        // 页面加载时的初始化
        window.addEventListener('load', () => {
            console.log('AJAX测试页面已加载');
            console.log('可用的测试功能：');
            console.log('1. XHR请求测试');
            console.log('2. Fetch API测试');
            console.log('3. 动态内容加载测试');
            console.log('4. 调试助手功能测试');
            
            // 检查RecallDebug是否可用
            setTimeout(() => {
                if (window.RecallDebug) {
                    console.log('✅ RecallDebug已加载');
                } else {
                    console.log('⚠️ RecallDebug未加载，请确保Recall扩展已启用');
                }
            }, 1000);
        });
    </script>
</body>
</html>