<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Error #185 Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .error-log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .error-count {
            color: #d73a49;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
    </style>
</head>
<body>
    <h1>React Error #185 Fix Test</h1>
    <p>This page tests the fix for React Error #185 (Maximum update depth exceeded) in the Recall extension.</p>

    <div class="test-container">
        <h2>Test Status</h2>
        <div id="test-status">
            <span class="status-indicator status-warning"></span>
            <span>Initializing test environment...</span>
        </div>
    </div>

    <div class="test-container">
        <h2>Language Switching Test</h2>
        <p>Click the buttons below to test language switching. The fix should prevent React Error #185.</p>
        
        <div id="language-buttons">
            <button class="test-button" onclick="testLanguageSwitch('en')">English</button>
            <button class="test-button" onclick="testLanguageSwitch('zh-CN')">中文简体</button>
            <button class="test-button" onclick="testLanguageSwitch('ja')">日本語</button>
            <button class="test-button" onclick="testLanguageSwitch('es')">Español</button>
            <button class="test-button" onclick="testLanguageSwitch('fr')">Français</button>
        </div>
        
        <div id="current-language">
            <strong>Current Language:</strong> <span id="lang-display">Loading...</span>
        </div>
    </div>

    <div class="test-container">
        <h2>Rapid Language Switching Test</h2>
        <p>This test rapidly switches languages to trigger potential nested update scenarios.</p>
        <button class="test-button" onclick="rapidLanguageTest()">Start Rapid Test</button>
        <button class="test-button" onclick="stopRapidTest()">Stop Test</button>
        <div id="rapid-test-status"></div>
    </div>

    <div class="test-container">
        <h2>Error Monitor</h2>
        <div>
            <strong>Error Count:</strong> 
            <span id="error-count" class="error-count">0</span>
        </div>
        <div>
            <strong>React Error #185 Count:</strong> 
            <span id="react-185-count" class="error-count">0</span>
        </div>
        <button class="test-button" onclick="clearErrorLog()">Clear Log</button>
        <div id="error-log" class="error-log">No errors detected.</div>
    </div>

    <script>
        // Error monitoring
        let errorCount = 0;
        let react185Count = 0;
        let rapidTestInterval = null;
        const errorLog = document.getElementById('error-log');
        const errorCountEl = document.getElementById('error-count');
        const react185CountEl = document.getElementById('react-185-count');

        // Monitor for JavaScript errors
        window.addEventListener('error', (event) => {
            errorCount++;
            const errorText = `[${new Date().toISOString()}] Error ${errorCount}:\n${event.error?.stack || event.message}\n\n`;
            
            // Check if it's React Error #185
            if (event.message && event.message.includes('185')) {
                react185Count++;
                react185CountEl.textContent = react185Count;
                updateTestStatus('error', `React Error #185 detected! Count: ${react185Count}`);
            }
            
            errorCountEl.textContent = errorCount;
            errorLog.textContent = errorLog.textContent === 'No errors detected.' ? errorText : errorLog.textContent + errorText;
            errorLog.scrollTop = errorLog.scrollHeight;
        });

        window.addEventListener('unhandledrejection', (event) => {
            errorCount++;
            const errorText = `[${new Date().toISOString()}] Unhandled Promise Rejection ${errorCount}:\n${event.reason}\n\n`;
            
            errorCountEl.textContent = errorCount;
            errorLog.textContent = errorLog.textContent === 'No errors detected.' ? errorText : errorLog.textContent + errorText;
            errorLog.scrollTop = errorLog.scrollHeight;
        });

        // Test functions
        function updateTestStatus(type, message) {
            const statusEl = document.getElementById('test-status');
            const indicator = statusEl.querySelector('.status-indicator');
            const textEl = statusEl.querySelector('span:last-child');
            
            indicator.className = `status-indicator status-${type}`;
            textEl.textContent = message;
        }

        function testLanguageSwitch(language) {
            console.log(`Testing language switch to: ${language}`);
            
            // Simulate the language switching that happens in the actual app
            // This would normally trigger the I18nManager and cause state updates
            document.getElementById('lang-display').textContent = language;
            
            // Log the test
            const logText = `[${new Date().toISOString()}] Language switched to: ${language}\n`;
            if (errorLog.textContent === 'No errors detected.') {
                errorLog.textContent = logText;
            } else {
                errorLog.textContent += logText;
            }
            errorLog.scrollTop = errorLog.scrollHeight;
        }

        function rapidLanguageTest() {
            const languages = ['en', 'zh-CN', 'ja', 'es', 'fr'];
            let currentIndex = 0;
            
            document.getElementById('rapid-test-status').innerHTML = '<span class="success">Rapid test running...</span>';
            
            rapidTestInterval = setInterval(() => {
                testLanguageSwitch(languages[currentIndex]);
                currentIndex = (currentIndex + 1) % languages.length;
            }, 100); // Switch every 100ms
        }

        function stopRapidTest() {
            if (rapidTestInterval) {
                clearInterval(rapidTestInterval);
                rapidTestInterval = null;
                document.getElementById('rapid-test-status').innerHTML = '<span class="success">Rapid test stopped.</span>';
            }
        }

        function clearErrorLog() {
            errorCount = 0;
            react185Count = 0;
            errorCountEl.textContent = '0';
            react185CountEl.textContent = '0';
            errorLog.textContent = 'No errors detected.';
        }

        // Initialize
        setTimeout(() => {
            if (errorCount === 0 && react185Count === 0) {
                updateTestStatus('success', 'Test environment ready - No React errors detected');
            }
        }, 1000);

        console.log('React Error #185 Fix Test page loaded successfully');
    </script>
</body>
</html>
