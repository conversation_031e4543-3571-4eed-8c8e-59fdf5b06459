# Recall - 智能历史搜索Chrome扩展

<div align="center">

![Recall Logo](public/icons/icon-128.png)

**一个强大的Chrome扩展，提供本地全文搜索浏览历史功能**

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/penwyp/Recall)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-18+-61dafb)](https://reactjs.org/)
[![License](https://img.shields.io/badge/license-MIT-green)](LICENSE)

[功能特性](#-功能特性) • [安装使用](#-安装使用) • [开发指南](#-开发指南) • [技术架构](#-技术架构) • [贡献指南](#-贡献指南)

</div>

## 🎯 项目简介

Recall 解决了用户"看过但找不到"的痛点，通过智能全文搜索让您的浏览历史变得真正有用。不同于浏览器自带的简单历史记录，Recall 能够搜索页面的完整内容，帮您快速找回曾经浏览过的任何信息。

### 🌟 核心优势

- 🔍 **全文搜索** - 搜索页面完整内容，不仅仅是标题和URL
- ⚡ **极速响应** - 搜索结果实时展示，平均响应时间 < 300ms
- 🛡️ **隐私保护** - 所有数据本地存储，绝不上传到服务器
- 🎯 **智能匹配** - 支持模糊搜索和拼写纠错
- 📱 **现代界面** - 简洁美观的用户界面，支持暗色模式
- 🔧 **高度可配置** - 灵活的过滤和排序选项

## 🚀 功能特性

### 核心功能
- **智能内容提取**: 使用 Mozilla Readability 算法提取页面主要内容
- **全文搜索引擎**: 基于 Fuse.js 的高性能模糊搜索
- **实时索引**: 自动为访问的页面建立搜索索引
- **SPA 支持**: 智能检测单页应用的路由变化
- **多维度过滤**: 按时间、域名、内容类型等维度筛选结果

### 高级功能
- **搜索建议**: 智能搜索建议和自动补全
- **结果高亮**: 搜索关键词在结果中高亮显示
- **访问统计**: 记录页面访问次数和最后访问时间
- **数据管理**: 支持数据导出、清理和备份
- **性能优化**: 针对大数据集优化，支持 10,000+ 页面

## 📦 安装使用

### 方式一：Chrome Web Store（推荐）
*即将上线，敬请期待*

### 方式二：开发者模式安装
1. 下载最新版本的 [Release](https://github.com/penwyp/Recall/releases)
2. 解压到本地目录
3. 打开 Chrome 浏览器，进入 `chrome://extensions/`
4. 开启"开发者模式"
5. 点击"加载已解压的扩展程序"，选择解压后的目录
6. 扩展安装完成，开始使用！

### 使用指南
1. **自动索引**: 安装后，扩展会自动为您访问的页面建立索引
2. **搜索内容**: 点击扩展图标，在搜索框中输入关键词
3. **筛选结果**: 使用时间和域名过滤器精确定位内容
4. **查看详情**: 点击搜索结果直接跳转到原页面

## 🛠️ 技术架构

### 核心技术栈
- **前端框架**: React 18 + TypeScript 5.0+
- **构建工具**: Vite + @crxjs/vite-plugin
- **扩展平台**: Chrome Manifest V3
- **数据存储**: IndexedDB (本地数据库)
- **搜索引擎**: Fuse.js (模糊搜索算法)
- **内容提取**: @mozilla/readability (Mozilla 算法)

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Content       │    │   Background    │    │   Popup UI      │
│   Scripts       │◄──►│   Service       │◄──►│   (React)       │
│                 │    │   Worker        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Readability   │    │   IndexedDB     │    │   Search        │
│   Content       │    │   Database      │    │   Service       │
│   Extraction    │    │   Service       │    │   (Fuse.js)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 开发指南

### 环境要求
- Node.js 18+
- npm 9+
- Chrome 88+

### 快速开始
```bash
# 克隆项目
git clone https://github.com/penwyp/Recall.git
cd Recall

# 安装依赖
npm install

# 开发模式
npm run dev

# 构建生产版本
npm run build

# 运行测试
npm run test

# 代码检查
npm run lint
```

### 开发模式使用
1. 运行 `npm run dev` 启动开发服务器
2. 在 Chrome 中加载 `dist` 目录作为未打包扩展
3. 修改代码后自动重新构建
4. 刷新扩展页面查看更改

### 项目结构
```
Recall/
├── src/
│   ├── background/         # 后台服务脚本
│   │   └── index.ts       # Service Worker 主文件
│   ├── content/           # 内容脚本
│   │   ├── index.ts       # 内容脚本主文件
│   │   └── scraper.ts     # 内容提取器
│   ├── popup/             # 弹窗界面
│   │   ├── components/    # React 组件
│   │   └── App.tsx        # 主应用组件
│   ├── services/          # 核心服务
│   │   ├── db.service.ts  # 数据库服务
│   │   └── search.service.ts # 搜索服务
│   ├── models/            # 数据模型
│   │   └── index.ts       # 类型定义
│   └── types/             # TypeScript 类型
├── public/                # 静态资源
│   ├── icons/            # 扩展图标
│   └── manifest.json     # 扩展清单
├── docs/                 # 项目文档
├── e2e/                  # E2E 测试
├── scripts/              # 构建脚本
└── test-results/         # 测试结果
```

### 开发工作流
1. **功能开发**: 基于 GitHub Issues 进行功能开发
2. **代码审查**: 所有 PR 需要代码审查
3. **自动化测试**: 单元测试 + E2E 测试
4. **性能监控**: 定期进行性能测试和优化

## 🧪 测试

### 运行测试
```bash
# 单元测试
npm run test

# E2E 测试
npm run test:e2e

# 性能测试
npm run test:performance

# 测试覆盖率
npm run test:coverage
```

### 测试策略
- **单元测试**: Jest + Testing Library
- **E2E 测试**: Playwright
- **性能测试**: 自定义性能测试脚本
- **手动测试**: 详细的测试指南

## 📊 性能指标

| 指标 | 目标值 | 当前值 | 状态 |
|------|--------|--------|------|
| 搜索响应时间 | < 500ms | ~300ms | ✅ |
| 内容提取时间 | < 3s | ~2s | ✅ |
| 内存使用 | < 50MB | ~25MB | ✅ |
| 支持页面数 | 10,000+ | 10,000+ | ✅ |
| 索引构建时间 | < 5s | ~3s | ✅ |

## 🔒 隐私与安全

### 隐私保护
- ✅ **本地存储**: 所有数据存储在用户本地，绝不上传
- ✅ **最小权限**: 仅请求必要的浏览器权限
- ✅ **透明度**: 开源代码，用户可审查所有功能
- ✅ **用户控制**: 用户完全控制数据的收集和删除

### 安全措施
- ✅ **内容安全策略**: 严格的 CSP 配置
- ✅ **权限最小化**: 遵循最小权限原则
- ✅ **安全编码**: 防止 XSS 和注入攻击
- ✅ **定期审计**: 定期进行安全代码审查

## 🤝 贡献指南

我们欢迎所有形式的贡献！

### 如何贡献
1. **Fork** 本仓库
2. **创建** 功能分支 (`git checkout -b feature/AmazingFeature`)
3. **提交** 更改 (`git commit -m 'Add some AmazingFeature'`)
4. **推送** 到分支 (`git push origin feature/AmazingFeature`)
5. **创建** Pull Request

### 贡献类型
- 🐛 **Bug 修复**: 修复已知问题
- ✨ **新功能**: 添加新的功能特性
- 📚 **文档**: 改进项目文档
- 🎨 **UI/UX**: 改进用户界面和体验
- ⚡ **性能**: 性能优化和改进
- 🧪 **测试**: 添加或改进测试

### 开发规范
- 遵循 TypeScript 和 ESLint 规范
- 编写清晰的提交信息
- 添加适当的测试用例
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Mozilla Readability](https://github.com/mozilla/readability) - 内容提取算法
- [Fuse.js](https://fusejs.io/) - 模糊搜索引擎
- [React](https://reactjs.org/) - 用户界面框架
- [Vite](https://vitejs.dev/) - 构建工具
- [TypeScript](https://www.typescriptlang.org/) - 类型安全

## 📞 联系我们

- **GitHub Issues**: [提交问题](https://github.com/penwyp/Recall/issues)
- **Email**: <EMAIL>
- **项目主页**: [Recall](https://github.com/penwyp/Recall)

---

<div align="center">

**如果这个项目对您有帮助，请给我们一个 ⭐️**

Made with ❤️ by [penwyp](https://github.com/penwyp)

</div>
