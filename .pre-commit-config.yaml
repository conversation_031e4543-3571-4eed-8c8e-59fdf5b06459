# Recall Pre-commit Hooks Configuration
# Ensures code quality and architecture compliance before commits

repos:
  # TypeScript and JavaScript formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        name: Prettier Code Formatter
        description: Format TypeScript, JavaScript, JSON, and CSS files
        files: \.(ts|tsx|js|jsx|json|css|html)$
        exclude: ^(dist/|node_modules/|coverage/)

  # ESLint for code quality
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.57.0
    hooks:
      - id: eslint
        name: ESLint Code Quality
        description: Check code quality and style issues
        files: \.(ts|tsx|js|jsx)$
        exclude: ^(dist/|node_modules/|coverage/)
        args: [--fix]
        additional_dependencies:
          - eslint@8.57.0
          - "@typescript-eslint/eslint-plugin@6.21.0"
          - "@typescript-eslint/parser@6.21.0"

  # TypeScript type checking
  - repo: local
    hooks:
      - id: typescript-check
        name: TypeScript Type Check
        description: Run TypeScript compiler to check types
        entry: npm run type-check
        language: system
        files: \.(ts|tsx)$
        exclude: ^(dist/|node_modules/|coverage/)
        pass_filenames: false

  # Chrome Extension Architecture Compliance
  - repo: local
    hooks:
      - id: architecture-compliance
        name: Chrome Extension Architecture Compliance
        description: Check architecture compliance for Chrome Extension contexts
        entry: npm run check:architecture
        language: system
        files: ^src/.*\.(ts|tsx|js|jsx)$
        pass_filenames: false

  # Test quality check
  - repo: local
    hooks:
      - id: test-quality
        name: Test Quality Check
        description: Ensure tests pass before commit
        entry: npm run test:unit
        language: system
        files: \.(test|spec)\.(ts|tsx|js|jsx)$
        pass_filenames: false

  # Package security audit
  - repo: local
    hooks:
      - id: security-audit
        name: Security Audit
        description: Check for security vulnerabilities in dependencies
        entry: bash -c 'npm audit --audit-level=moderate'
        language: system
        files: ^(package\.json|package-lock\.json)$
        pass_filenames: false

  # JSON validation
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-json
        name: JSON Syntax Check
        description: Validate JSON files syntax
        files: \.(json|jsonc)$

  # YAML validation
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-yaml
        name: YAML Syntax Check
        description: Validate YAML files syntax
        files: \.(yml|yaml)$

  # Prevent large files
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: check-added-large-files
        name: Check Large Files
        description: Prevent accidentally committing large files
        args: ['--maxkb=1000']

  # Remove trailing whitespace
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
        name: Remove Trailing Whitespace
        description: Remove trailing whitespace from files
        exclude: \.(md|txt)$

  # End of file fixer
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: end-of-file-fixer
        name: Fix End of Files
        description: Ensure files end with a newline
        exclude: \.(md|txt)$

  # AI Model and Extension specific checks
  - repo: local
    hooks:
      - id: extension-manifest-check
        name: Extension Manifest Validation
        description: Validate Chrome Extension manifest.json
        entry: bash -c 'if [ -f "public/manifest.json" ]; then node -e "JSON.parse(require(\"fs\").readFileSync(\"public/manifest.json\", \"utf8\"))"; echo "✅ Manifest is valid JSON"; else echo "⚠️ Manifest not found"; fi'
        language: system
        files: ^public/manifest\.json$
        pass_filenames: false

  # Build size check
  - repo: local
    hooks:
      - id: build-size-check
        name: Build Size Check
        description: Ensure build output doesn't exceed size limits
        entry: bash -c 'npm run build >/dev/null 2>&1 && BUILD_SIZE=$(du -sb dist 2>/dev/null | cut -f1 || echo 0) && MAX_SIZE=5242880 && if [ $BUILD_SIZE -gt $MAX_SIZE ]; then echo "❌ Build size $BUILD_SIZE bytes exceeds limit $MAX_SIZE bytes"; exit 1; else echo "✅ Build size: $BUILD_SIZE bytes (within limit)"; fi'
        language: system
        files: ^(src/|public/|package\.json|tsconfig\.json|vite\.config\.ts|webpack\..*\.cjs)
        pass_filenames: false

# Global configuration
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: '3.0.0'

# Custom hook configurations
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false