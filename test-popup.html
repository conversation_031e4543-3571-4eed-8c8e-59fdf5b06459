<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Popup - JSX Runtime Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #popup-frame {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-top: 20px;
        }
        .test-info {
            background-color: #e2e3e5;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 JSX Runtime Fix Test</h1>
        
        <div class="test-info">
            <h3>Test Purpose</h3>
            <p>This test verifies that the "Cannot read properties of undefined (reading 'jsx')" error has been resolved by:</p>
            <ul>
                <li>✅ Removing the <code>topLevelAwait()</code> plugin from Vite configuration</li>
                <li>✅ Changing build target from <code>esnext</code> to <code>es2020</code> for better Chrome extension compatibility</li>
                <li>✅ Disabling code splitting with <code>manualChunks: undefined</code></li>
                <li>✅ Rebuilding the extension with the updated configuration</li>
            </ul>
        </div>

        <div id="test-status" class="status loading">
            🔄 Loading popup to test JSX runtime...
        </div>

        <div id="console-output" class="console-output">
            <strong>Console Output:</strong><br>
            <div id="console-log"></div>
        </div>

        <iframe id="popup-frame" src="dist/index.html"></iframe>
    </div>

    <script>
        // Capture console messages
        const consoleLog = document.getElementById('console-log');
        const testStatus = document.getElementById('test-status');
        let hasJsxError = false;
        let loadTimeout;

        // Override console methods to capture output
        const originalConsoleError = console.error;
        const originalConsoleLog = console.log;
        const originalConsoleWarn = console.warn;

        function logToOutput(level, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${
                level === 'error' ? '#dc3545' : level === 'warn' ? '#ffc107' : '#28a745'
            };">[${level.toUpperCase()}]</span> ${message}`;
            consoleLog.appendChild(logEntry);
            consoleLog.scrollTop = consoleLog.scrollHeight;

            // Check for JSX runtime error
            if (level === 'error' && message.includes('Cannot read properties of undefined (reading \'jsx\')')) {
                hasJsxError = true;
                testStatus.className = 'status error';
                testStatus.innerHTML = '❌ JSX Runtime Error Detected! The fix did not work.';
                clearTimeout(loadTimeout);
            }
        }

        console.error = function(...args) {
            logToOutput('error', ...args);
            originalConsoleError.apply(console, args);
        };

        console.log = function(...args) {
            logToOutput('log', ...args);
            originalConsoleLog.apply(console, args);
        };

        console.warn = function(...args) {
            logToOutput('warn', ...args);
            originalConsoleWarn.apply(console, args);
        };

        // Set up iframe load monitoring
        const iframe = document.getElementById('popup-frame');
        
        iframe.onload = function() {
            logToOutput('log', 'Popup iframe loaded successfully');
            
            // Wait a bit for any async errors to surface
            setTimeout(() => {
                if (!hasJsxError) {
                    testStatus.className = 'status success';
                    testStatus.innerHTML = '✅ Success! No JSX runtime errors detected. The fix appears to be working!';
                }
            }, 2000);
        };

        iframe.onerror = function(error) {
            logToOutput('error', 'Iframe failed to load:', error);
            testStatus.className = 'status error';
            testStatus.innerHTML = '❌ Failed to load popup iframe';
        };

        // Set a timeout for the test
        loadTimeout = setTimeout(() => {
            if (!hasJsxError) {
                testStatus.className = 'status success';
                testStatus.innerHTML = '✅ Test completed! No JSX runtime errors detected after 5 seconds.';
            }
        }, 5000);

        // Log initial test start
        logToOutput('log', 'Starting JSX runtime fix test...');
        logToOutput('log', 'Loading popup from: dist/index.html');
    </script>
</body>
</html>
