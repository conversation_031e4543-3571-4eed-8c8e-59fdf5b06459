--- a/src/search/hybrid/HybridSearchEngine.ts
+++ b/src/search/hybrid/HybridSearchEngine.ts
@@ -994,16 +994,17 @@ export class HybridSearchEngine {
     stringResults: SearchResult[],
     fulltextResults: SearchResult[]
   ): SearchResult[] {
+    // Use page ID as unique key instead of URL to preserve all history entries
     const mergedMap = new Map<string, SearchResult>();
     
     // Weight for combining scores (can be adjusted)
     const stringWeight = 0.4;
-    const fulltextWeight = 0.6; // Give fulltext slightly more weight as it's full-text
+    const fulltextWeight = 0.6;
     
     // Add string results first
     stringResults.forEach(result => {
-      mergedMap.set(result.url, {
+      mergedMap.set(result.id, {
         ...result,
         combinedScore: result.stringScore * stringWeight
       });
     });
@@ -1011,9 +1012,9 @@ export class HybridSearchEngine {
     // Add fulltext results, combining if URL already exists
     fulltextResults.forEach(result => {
-      const existing = mergedMap.get(result.url);
+      const existing = mergedMap.get(result.id);
       if (existing) {
-        // Combine scores for duplicates
+        // Combine scores for duplicates (same page ID)
         existing.combinedScore = 
           existing.stringScore * stringWeight +
           result.fulltextScore * fulltextWeight;
@@ -1030,7 +1031,7 @@ export class HybridSearchEngine {
       } else {
         // New result from fulltext only
-        mergedMap.set(result.url, {
+        mergedMap.set(result.id, {
           ...result,
           combinedScore: result.fulltextScore * fulltextWeight
         });
@@ -1039,7 +1040,15 @@ export class HybridSearchEngine {
     
     // Convert to array and sort by combined score
     const mergedResults = Array.from(mergedMap.values());
-    mergedResults.sort((a, b) => b.combinedScore - a.combinedScore);
+    mergedResults.sort((a, b) => {
+      // Primary sort by combined score
+      const scoreDiff = b.combinedScore - a.combinedScore;
+      if (Math.abs(scoreDiff) > 0.001) {
+        return scoreDiff;
+      }
+      
+      // Secondary sort by visit time (newer first)
+      return b.lastVisitTime - a.lastVisitTime;
+    });
     
     return mergedResults;
   }