/**
 * Test script to verify the search fix
 * This script can be run in the browser console to test the search functionality
 */

async function testSearchFix() {
  console.log('🔍 Testing search fix...');
  
  try {
    // Import the hybrid search service
    const { hybridSearchService } = await import('./src/services/index.js');
    
    console.log('✅ Successfully imported hybridSearchService');
    
    // Initialize the service
    console.log('🔄 Initializing hybrid search service...');
    await hybridSearchService.init();
    console.log('✅ Hybrid search service initialized');
    
    // Get index stats
    const stats = hybridSearchService.getIndexStats();
    console.log('📊 Index stats:', stats);
    
    // Perform a test search
    console.log('🔍 Performing test search...');
    const results = await hybridSearchService.search('test', { limit: 5 });
    console.log(`✅ Search completed successfully, found ${results.length} results`);
    
    if (results.length > 0) {
      console.log('📄 First result:', {
        title: results[0].page.title,
        url: results[0].page.url,
        score: results[0].score
      });
    }
    
    console.log('🎉 All tests passed! The search fix appears to be working.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testSearchFix();
