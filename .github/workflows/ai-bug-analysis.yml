# .github/workflows/ai-bug-analysis.yml

name: AI Bug Analysis

on:
  push:
    # 只在 docs/bugs/ 目录下的文件发生变化时触发
    paths:
      - 'docs/bugs/**.md'

permissions:
  contents: read
  # 需要写权限来在commit上发表评论
  issues: write
  pull-requests: write # 也在PR评论中提示
  commits: write # 实际上GitHub Actions不允许直接写commit评论，我们将评论到issue或PR

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        # 需要获取完整的git历史来找到变更的文件
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      - name: Install Dependencies
        run: pip install google-generativeai requests PyYAML

      - name: AI Bug Analysis
        env:
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          REPO_FULL_NAME: ${{ github.repository }}
          COMMIT_SHA: ${{ github.sha }}
        run: |
          import os
          import re
          import requests
          import google.generativeai as genai
          import sys
          import yaml

          # --- 1. 配置AI模型 ---
          try:
              genai.configure(api_key=os.environ["GEMINI_API_KEY"])
              model = genai.GenerativeModel('gemini-1.5-flash')
          except Exception as e:
              print(f"AI模型配置失败: {e}")
              sys.exit(1)

          # --- 2. 找到变更的Bug报告文件 ---
          import subprocess
          # 获取上一次提交和当前提交之间变更的文件
          result = subprocess.run(
              ['git', 'diff', '--name-only', 'HEAD~1', 'HEAD'],
              capture_output=True, text=True
          )
          changed_files = result.stdout.strip().split('\n')
          
          bug_report_file = None
          for file_path in changed_files:
              if file_path.startswith('docs/bugs/') and file_path.endswith('.md'):
                  bug_report_file = file_path
                  break

          if not bug_report_file:
              print("未找到变更的Bug报告文件。")
              sys.exit(0)
          
          print(f"检测到Bug报告变更: {bug_report_file}")

          # --- 3. 读取和解析Bug报告 ---
          try:
              with open(bug_report_file, 'r') as f:
                  bug_report_content = f.read()

              # 尝试从frontmatter中解析相关文件
              affected_files = []
              match = re.search(r'---\s*\n(.*?)\n---', bug_report_content, re.DOTALL)
              if match:
                  frontmatter = yaml.safe_load(match.group(1))
                  if 'affected_files' in frontmatter and isinstance(frontmatter['affected_files'], list):
                      affected_files = frontmatter['affected_files']
              
              if not affected_files:
                  print("警告: Bug报告中未找到 'affected_files' 列表。")
                  # 也可以在这里添加更复杂的逻辑，用AI从正文中提取文件名

          except Exception as e:
              print(f"读取或解析Bug报告失败: {e}")
              sys.exit(1)

          # --- 4. 读取相关代码文件 ---
          related_code = {}
          for file_path in affected_files:
              if os.path.exists(file_path):
                  with open(file_path, 'r') as f:
                      related_code[file_path] = f.read()
              else:
                  print(f"警告: 相关文件 '{file_path}' 未找到。")

          # --- 5. 构建Prompt并调用AI ---
          code_context = "\n\n".join([f"--- 文件: {path} ---\n```\n{content}\n```" for path, content in related_code.items()])

          prompt = f"""
          你是一名顶级的软件调试专家。你的任务是分析一份Bug报告和相关的源代码，然后提供一份专业的分析报告。

          你的分析报告应该包括：
          1.  **问题摘要**: 简要总结Bug的核心问题。
          2.  **根本原因分析**: 根据错误日志和代码，推测最可能的根本原因。
          3.  **修复建议**: 提供具体的代码修复建议或调试思路。
          4.  **潜在影响**: 分析这个Bug可能对系统的其他部分产生什么影响。

          **Bug报告内容:**
          ```markdown
          {bug_report_content}
          ```

          **相关的源代码:**
          {code_context}

          请生成你的分析报告。
          """

          try:
              response = model.generate_content(prompt)
              analysis_text = response.text
          except Exception as e:
              print(f"调用AI API失败: {e}")
              sys.exit(1)

          # --- 6. 将分析报告发布到Commit评论 ---
          full_comment = f"## AI Bug Analysis for `{bug_report_file}`\n\n" + analysis_text
          
          repo_full_name = os.environ["REPO_FULL_NAME"]
          commit_sha = os.environ["COMMIT_SHA"]
          github_token = os.environ["GITHUB_TOKEN"]

          url = f"https://api.github.com/repos/{repo_full_name}/commits/{commit_sha}/comments"
          headers = {
              "Authorization": f"token {github_token}",
              "Accept": "application/vnd.github.v3+json"
          }
          body = {"body": full_comment}

          try:
              res = requests.post(url, json=body, headers=headers)
              res.raise_for_status()
              print("Bug分析报告已成功发布到Commit评论。")
          except requests.exceptions.RequestException as e:
              print(f"发布评论失败: {e}")
              print(f"响应内容: {e.response.text if e.response else 'N/A'}")
              sys.exit(1)
