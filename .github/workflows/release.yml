name: Release

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:

jobs:
  build-and-release:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test:unit
    
    - name: Build extension
      run: npm run build
    
    - name: Validate extension
      run: |
        # Check if manifest.json exists
        if [ ! -f "dist/manifest.json" ]; then
          echo "Error: manifest.json not found in dist/"
          exit 1
        fi
        
        # Check extension structure
        echo "Extension structure:"
        ls -la dist/
        
        # Validate manifest format
        if ! node -e "JSON.parse(require('fs').readFileSync('dist/manifest.json', 'utf8'))"; then
          echo "Error: Invalid manifest.json format"
          exit 1
        fi
        
        echo "Extension validation passed"
    
    - name: Package extension
      run: |
        cd dist
        zip -r ../recall-v3.0.zip .
        cd ..
        echo "Extension packaged successfully"
    
    - name: Get version from package.json
      id: version
      run: |
        VERSION=$(node -p "require('./package.json').version")
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        echo "tag=v$VERSION" >> $GITHUB_OUTPUT
    
    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/v')
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Recall ${{ steps.version.outputs.version }}
        body: |
          ## Recall V3.0 Release
          
          ### What's New
          - AI-powered semantic search
          - Local ONNX Runtime integration
          - BYOK (Bring Your Own Key) support
          - Enhanced knowledge management
          
          ### Installation
          1. Download `recall-v3.0.zip`
          2. Extract the archive
          3. Load the extension in Chrome Developer mode
          
          ### System Requirements
          - Chrome 88+ or Microsoft Edge 88+
          - 100MB+ available memory
          - Local storage permissions
          
          Full release notes and documentation available at [docs/](./docs/)
        draft: false
        prerelease: false
    
    - name: Upload Release Asset
      if: startsWith(github.ref, 'refs/tags/v')
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: ./recall-v3.0.zip
        asset_name: recall-v3.0.zip
        asset_content_type: application/zip
    
    - name: Upload build artifacts (main branch)
      if: github.ref == 'refs/heads/main'
      uses: actions/upload-artifact@v4
      with:
        name: extension-main-build
        path: |
          dist/
          recall-v3.0.zip
        retention-days: 30