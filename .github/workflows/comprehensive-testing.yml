name: Comprehensive Testing Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '20'
  CHROME_VERSION: 'stable'

jobs:
  # Architecture Compliance Check
  architecture-check:
    name: Architecture Compliance
    runs-on: ubuntu-latest
    timeout-minutes: 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Check Chrome Extension architecture compliance
        run: npm run check:architecture

      - name: Upload compliance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: architecture-compliance
          path: |
            test-results/
          retention-days: 7

  # Security and Migration Tests
  security-migration-tests:
    name: Security & Migration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [architecture-check]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Run IndexedDB migration security tests
        run: npm run test:migration:integrity

      - name: Run API key security penetration tests
        run: npm run test:security:pentest

      - name: Run comprehensive security & migration suite
        run: npm run test:security:migration
        env:
          SECURITY_VERBOSE: true
          SECURITY_COVERAGE: true

      - name: Upload security test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-migration-results
          path: |
            test-results/security-migration/
            test-results/security-coverage/
          retention-days: 30

      - name: Security test summary
        run: |
          echo "## 🔒 Security Test Results" >> $GITHUB_STEP_SUMMARY
          if [ -f test-results/security-migration/security-migration-report.json ]; then
            SECURITY_SCORE=$(cat test-results/security-migration/security-migration-report.json | jq -r '.metadata.securityScore // 0')
            SECURITY_ISSUES=$(cat test-results/security-migration/security-migration-report.json | jq -r '.securityIssues | length')
            MIGRATION_ISSUES=$(cat test-results/security-migration/security-migration-report.json | jq -r '.migrationIssues | length')
            
            echo "- **Security Score**: ${SECURITY_SCORE}%" >> $GITHUB_STEP_SUMMARY
            echo "- **Security Issues**: ${SECURITY_ISSUES}" >> $GITHUB_STEP_SUMMARY  
            echo "- **Migration Issues**: ${MIGRATION_ISSUES}" >> $GITHUB_STEP_SUMMARY
            
            if [ "$SECURITY_SCORE" -ge "90" ] && [ "$SECURITY_ISSUES" -eq "0" ]; then
              echo "- **Status**: ✅ Production Ready" >> $GITHUB_STEP_SUMMARY
            elif [ "$SECURITY_SCORE" -ge "75" ]; then
              echo "- **Status**: ⚠️ Minor Issues" >> $GITHUB_STEP_SUMMARY
            else
              echo "- **Status**: 🚨 Critical Issues" >> $GITHUB_STEP_SUMMARY
            fi
          fi

  # Unit and Integration Tests
  unit-integration-tests:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: architecture-check
    
    strategy:
      matrix:
        node-version: [18, 20, 22]
      fail-fast: false
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ matrix.node-version }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-${{ matrix.node-version }}-
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Type checking
        run: npm run type-check

      - name: Lint code
        run: npm run lint

      - name: Run unit tests
        run: npm run test:unit
        env:
          CI: true
          NODE_ENV: test

      - name: Run integration tests
        run: npm run test:integration
        env:
          CI: true
          NODE_ENV: test

      - name: Generate coverage report
        run: npm run test:coverage
        if: matrix.node-version == 18

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        if: matrix.node-version == 18
        with:
          file: ./coverage/lcov.info
          fail_ci_if_error: false

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-unit-${{ matrix.node-version }}
          path: |
            test-results/
            coverage/
          retention-days: 7

  # E2E Tests
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    strategy:
      matrix:
        browser: [chromium, chrome]
        shard: [1, 2, 3]
      fail-fast: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Install Playwright browsers
        run: npx playwright install --with-deps ${{ matrix.browser }}

      - name: Build extension
        run: npm run build
        env:
          NODE_ENV: production

      - name: Run E2E tests
        run: npx playwright test --project=${{ matrix.browser }} --shard=${{ matrix.shard }}/3
        env:
          CI: true
          PLAYWRIGHT_BROWSER: ${{ matrix.browser }}

      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-results-${{ matrix.browser }}-shard-${{ matrix.shard }}
          path: |
            test-results/
            playwright-report/
          retention-days: 7

      - name: Upload extension artifacts
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: extension-build-${{ matrix.browser }}
          path: dist/
          retention-days: 3

  # Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    timeout-minutes: 25
    needs: architecture-check
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Build extension for performance testing
        run: npm run build
        env:
          NODE_ENV: production

      - name: Run standard performance tests
        run: npm run test:performance
        env:
          CI: true
          NODE_ENV: test
          PERFORMANCE_THRESHOLD: strict

      - name: Run AI performance benchmarks
        run: |
          echo "Running AI performance benchmarks..."
          npm run test:performance:ai 2>/dev/null || echo "AI performance tests not yet implemented"
        continue-on-error: true

      - name: Check search response time baseline
        run: |
          echo "Checking search performance baseline..."
          node -e "
          const fs = require('fs');
          const resultsPath = 'test-results/performance-results.json';
          if (fs.existsSync(resultsPath)) {
            const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
            const searchTime = results.searchResponseTime || 0;
            const threshold = 250; // 250ms threshold
            if (searchTime > threshold) {
              console.error(\`Search response time \${searchTime}ms exceeds threshold \${threshold}ms\`);
              process.exit(1);
            }
            console.log(\`Search response time: \${searchTime}ms (within threshold)\`);
          } else {
            console.log('Performance results not found, skipping baseline check');
          }
          "

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-results
          path: |
            test-results/performance/
            benchmarks/
          retention-days: 30

  # Cross-Platform Tests
  cross-platform-tests:
    name: Cross-Platform Tests
    runs-on: ${{ matrix.os }}
    timeout-minutes: 15
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            display: ':99'
          - os: windows-latest
            display: ''
          - os: macos-latest
            display: ''
      fail-fast: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup virtual display (Linux)
        if: matrix.os == 'ubuntu-latest'
        run: |
          export DISPLAY=${{ matrix.display }}
          Xvfb :99 -screen 0 1024x768x24 > /dev/null 2>&1 &
          echo "DISPLAY=${{ matrix.display }}" >> $GITHUB_ENV

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Build extension
        run: npm run build

      - name: Run core tests
        run: npm run test:core
        env:
          CI: true
          DISPLAY: ${{ matrix.display }}

      - name: Upload cross-platform results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: cross-platform-${{ matrix.os }}
          path: test-results/
          retention-days: 3

  # Security and Code Quality
  security-quality:
    name: Security & Code Quality
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit

      - name: Run security audit
        run: npm audit --audit-level=moderate
        continue-on-error: true

      - name: Check for vulnerabilities
        run: |
          npm audit --json > audit-results.json
          if [ $(cat audit-results.json | jq '.vulnerabilities | length') -gt 0 ]; then
            echo "Security vulnerabilities found"
            cat audit-results.json | jq '.vulnerabilities'
          fi

      - name: Code quality check
        run: npm run lint:ci

      - name: Check bundle size
        run: |
          npm run build
          npx bundlesize

      - name: Upload security results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-audit
          path: |
            audit-results.json
            dist/
          retention-days: 7

  # Test Results Summary
  test-summary:
    name: Test Results Summary
    runs-on: ubuntu-latest
    needs: [architecture-check, unit-integration-tests, e2e-tests, performance-tests, cross-platform-tests, security-quality]
    if: always()

    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v3

      - name: Create test summary
        run: |
          echo "# Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "## Job Status" >> $GITHUB_STEP_SUMMARY
          echo "- Architecture Compliance: ${{ needs.architecture-check.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Unit & Integration Tests: ${{ needs.unit-integration-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- E2E Tests: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Performance Tests: ${{ needs.performance-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Cross-Platform Tests: ${{ needs.cross-platform-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Security & Quality: ${{ needs.security-quality.result }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          # Count test files
          if [ -d "test-results-unit-18" ]; then
            echo "## Test Metrics" >> $GITHUB_STEP_SUMMARY
            echo "- Test files found: $(find . -name "*.test.*" -o -name "*.spec.*" | wc -l)" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Check overall success
        run: |
          if [[ "${{ needs.architecture-check.result }}" == "success" && 
                "${{ needs.unit-integration-tests.result }}" == "success" && 
                "${{ needs.e2e-tests.result }}" == "success" && 
                "${{ needs.performance-tests.result }}" == "success" ]]; then
            echo "All critical tests passed ✅"
          else
            echo "Some critical tests failed ❌"
            echo "Architecture Check: ${{ needs.architecture-check.result }}"
            echo "Unit/Integration: ${{ needs.unit-integration-tests.result }}"
            echo "E2E Tests: ${{ needs.e2e-tests.result }}"
            echo "Performance: ${{ needs.performance-tests.result }}"
            exit 1
          fi

# Workflow dispatch for manual testing
  manual-test:
    name: Manual Test Run
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run all tests
        run: npm run test:all

      - name: Upload comprehensive results
        uses: actions/upload-artifact@v3
        with:
          name: manual-test-results
          path: |
            test-results/
            coverage/
            playwright-report/
          retention-days: 14