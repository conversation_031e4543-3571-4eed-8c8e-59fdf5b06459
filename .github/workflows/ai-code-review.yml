# .github/workflows/ai-code-review.yml

name: AI Code Review

on:
  pull_request:
    types: [opened, synchronize]

permissions:
  contents: read
  pull-requests: write

jobs:
  review:
    runs-on: ubuntu-latest
    steps:
      # 第一步：检出代码，以便Action可以访问
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          # 我们需要获取PR的完整历史，以便比较文件差异
          fetch-depth: 0

      # 第二步：设置Python环境
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'

      # 第三步：安装所需的Python库
      - name: Install Dependencies
        run: pip install google-generativeai requests

      # 第四步：执行AI审查脚本
      - name: AI Code Review
        env:
          # 将GitHub Secret传入脚本
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          # GitHub会自动提供一个令牌用于API交互
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # PR的编号
          PR_NUMBER: ${{ github.event.pull_request.number }}
          # 仓库所有者和名称
          REPO_OWNER: ${{ github.repository_owner }}
          REPO_NAME: ${{ github.event.repository.name }}
        run: |
          import os
          import requests
          import google.generativeai as genai
          import sys

          # --- 1. 配置AI模型 ---
          try:
              genai.configure(api_key=os.environ["GEMINI_API_KEY"])
              model = genai.GenerativeModel('gemini-1.5-flash')
          except Exception as e:
              print(f"AI模型配置失败: {e}")
              sys.exit(1)

          # --- 2. 获取PR中变更的文件 ---
          # GITHUB_BASE_REF是目标分支 (e.g., main)
          # GITHUB_HEAD_REF是源分支 (e.g., feature-branch)
          base_ref = os.environ.get("GITHUB_BASE_REF", "main")
          head_ref = os.environ.get("GITHUB_HEAD_REF")
          
          # 使用git diff获取变更的文件列表
          import subprocess
          result = subprocess.run(
              ['git', 'diff', '--name-only', f'origin/{base_ref}...origin/{head_ref}'],
              capture_output=True, text=True
          )
          changed_files = result.stdout.strip().split('\n')
          
          if not any(changed_files):
              print("未检测到文件变更。")
              sys.exit(0)

          print("检测到以下文件变更:", changed_files)

          # --- 3. 循环审查每个文件 ---
          review_comments = []
          for file_path in changed_files:
              if not os.path.exists(file_path) or file_path.startswith('docs/'):
                  print(f"跳过文件: {file_path} (不存在或为文档)")
                  continue

              try:
                  with open(file_path, 'r') as f:
                      file_content = f.read()

                  # 构建Prompt
                  prompt = f"""
                  你是一名资深的软件架构师和代码审查专家。请审查以下代码文件。
                  你的目标是：
                  1. 识别潜在的BUG或逻辑错误。
                  2. 发现不符合最佳实践的代码。
                  3. 提出可读性、性能或可维护性方面的改进建议。
                  4. 如果代码看起来很好，请给予肯定。

                  请以简洁、有建设性的方式提供您的反馈。

                  文件名: `{file_path}`

                  代码内容:
                  ```
                  {file_content}
                  ```
                  """

                  # 调用AI
                  response = model.generate_content(prompt)
                  
                  if response.text:
                      comment = f"### AI Code Review for `{file_path}`\n\n"
                      comment += response.text
                      review_comments.append(comment)
                      print(f"文件 {file_path} 的审查意见已生成。")

              except Exception as e:
                  print(f"处理文件 {file_path} 时出错: {e}")

          # --- 4. 将审查意见发布到PR评论 ---
          if review_comments:
              full_review = "# AI Code Review Summary\n\n" + "\n\n---\n\n".join(review_comments)
              
              pr_number = os.environ["PR_NUMBER"]
              repo_owner = os.environ["REPO_OWNER"]
              repo_name = os.environ["REPO_NAME"]
              github_token = os.environ["GITHUB_TOKEN"]

              url = f"https://api.github.com/repos/{repo_owner}/{repo_name}/issues/{pr_number}/comments"
              headers = {
                  "Authorization": f"token {github_token}",
                  "Accept": "application/vnd.github.v3+json"
              }
              body = {"body": full_review}
              
              try:
                  res = requests.post(url, json=body, headers=headers)
                  res.raise_for_status()
                  print("审查意见已成功发布到Pull Request。")
              except requests.exceptions.RequestException as e:
                  print(f"发布评论失败: {e}")
                  print(f"响应内容: {e.response.text if e.response else 'N/A'}")
                  sys.exit(1)
          else:
              print("没有生成任何审查意见。")
