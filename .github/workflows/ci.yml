name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run unit tests
      run: npm run test:unit
    
    - name: Run build
      run: npm run build
    
    - name: Check build size
      run: |
        BUILD_SIZE=$(du -sb dist | cut -f1)
        MAX_SIZE=5242880  # 5MB in bytes
        if [ $BUILD_SIZE -gt $MAX_SIZE ]; then
          echo "Build size $BUILD_SIZE bytes exceeds maximum $MAX_SIZE bytes"
          exit 1
        fi
        echo "Build size: $BUILD_SIZE bytes (within limit)"
    
    - name: Install Playwright Browsers
      run: npx playwright install --with-deps chromium
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.node-version }}
        path: |
          coverage/
          test-results/
          playwright-report/
        retention-days: 7
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: extension-build-${{ matrix.node-version }}
        path: dist/
        retention-days: 7

  performance-test:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run performance tests
      run: npm run test:performance
    
    - name: Upload performance results
      uses: actions/upload-artifact@v4
      with:
        name: performance-results
        path: test-results/
        retention-days: 7