{"permissions": {"allow": ["Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(rmdir:*)", "<PERSON><PERSON>(touch:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(head:*)", "<PERSON><PERSON>(tail:*)", "Bash(wc:*)", "Bash(sort:*)", "<PERSON><PERSON>(uniq:*)", "<PERSON><PERSON>(chmod:*)", "Bash(which:*)", "Bash(whereis:*)", "<PERSON><PERSON>(curl:*)", "Bash(ping:*)", "<PERSON><PERSON>(netstat:*)", "Bash(tar:*)", "Bash(zip:*)", "Ba<PERSON>(unzip:*)", "Bash(gzip:*)", "<PERSON><PERSON>(jq:*)", "Bash(less:*)", "Bash(more:*)", "Bash(vi:*)", "Bash(vim:*)", "Bash(nano:*)", "<PERSON><PERSON>(code:*)", "Bash(git:*)", "Bash(npm:*)", "Bash(yarn:*)", "Bash(pnpm:*)", "Bash(node:*)", "Bash(npx:*)", "Bash(vite:*)", "<PERSON><PERSON>(go:*)", "Bash(gofmt:*)", "<PERSON><PERSON>(goimports:*)", "Bash(java:*)", "<PERSON><PERSON>(javac:*)", "Bash(jar:*)", "<PERSON><PERSON>(javadoc:*)", "<PERSON><PERSON>(mvn:*)", "<PERSON><PERSON>(gradle:*)", "<PERSON><PERSON>(gradlew:*)", "<PERSON><PERSON>(make:*)", "Bash(cmake:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(ps:*)", "<PERSON><PERSON>(top:*)", "<PERSON><PERSON>(du:*)", "Bash(df:*)", "Bash(lsof:*)", "Bash(env:*)", "Bash(export:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(history:*)", "<PERSON><PERSON>(alias:*)", "Bash(tree:*)", "<PERSON><PERSON>(htop:*)", "<PERSON><PERSON>(wget:*)", "Bash(ss:*)", "HttpRequest", "Edit", "Diff", "<PERSON><PERSON>", "Python", "Search", "WebFetch"], "deny": []}}