/**
 * Quick test script to verify hybrid search functionality
 */

import { HybridSearchEngine } from '../src/search/hybrid/HybridSearchEngine';
import { LunrSearchEngine } from '../src/search/fulltext/LunrSearchEngine';
import { searchService } from '../src/services/search.service';
import type { Page } from '../src/models';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Mock Chrome API
(global as any).chrome = {
  storage: {
    local: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue(undefined),
    },
  },
  runtime: {
    sendMessage: jest.fn().mockResolvedValue({}),
    onMessage: {
      addListener: jest.fn(),
    },
  },
};

// Mock IndexedDB (simplified)
(global as any).indexedDB = {
  open: () => ({
    onsuccess: null,
    onerror: null,
    result: {
      transaction: () => ({
        objectStore: () => ({
          get: () => ({ onsuccess: null }),
          put: () => ({ onsuccess: null }),
          getAll: () => ({ onsuccess: null }),
        })
      })
    }
  })
};

// Add jest mock if not available
if (typeof jest === 'undefined') {
  (global as any).jest = {
    fn: (impl?: any) => {
      const fn = impl || (() => {});
      fn.mockImplementation = (newImpl: any) => newImpl;
      fn.mockReturnValue = (value: any) => () => value;
      fn.mockResolvedValue = (value: any) => () => Promise.resolve(value);
      fn.mockReturnThis = () => fn;
      return fn;
    }
  };
}

async function testHybridSearch() {
  console.log('=== Testing Hybrid Search Functionality ===\\n');

  try {
    // Load test data
    const backupPath = path.join(__dirname, '../tests/materials/recall-backup-2025-06-26.json');
    const backupContent = fs.readFileSync(backupPath, 'utf-8');
    const backup = JSON.parse(backupContent);
    const pages: Page[] = backup.pages;
    
    console.log(`Loaded ${pages.length} pages from backup`);

    // Manual verification
    console.log('\\n=== Manual Search Verification ===');
    const manualMatches = pages.filter(page => {
      const text = `${page.title || ''} ${page.content || ''}`.toLowerCase();
      return text.includes('claude code');
    });
    
    console.log(`Found ${manualMatches.length} pages containing "claude code":`);
    manualMatches.forEach((page, idx) => {
      console.log(`${idx + 1}. ${page.title}`);
      console.log(`   URL: ${page.url}`);
      console.log(`   Domain: ${page.domain}`);
    });

    // Test individual engines
    console.log('\\n=== Testing Individual Engines ===');
    
    // Test Lunr
    console.log('\\n1. LunrSearchEngine:');
    const lunrEngine = new LunrSearchEngine();
    await lunrEngine.createIndex(pages);
    const lunrResults = await lunrEngine.search('Claude Code');
    console.log(`   Found ${lunrResults.length} results`);
    
    // Test Fuse (through searchService)
    console.log('\\n2. Fuse.js (searchService):');
    await searchService.buildIndex();
    const fuseResults = await searchService.search('Claude Code');
    console.log(`   Found ${fuseResults.length} results`);

    // Test Hybrid Engine
    console.log('\\n=== Testing HybridSearchEngine ===');
    const hybridEngine = new HybridSearchEngine();
    await hybridEngine.initialize(pages);
    
    const hybridResults = await hybridEngine.search('Claude Code');
    
    console.log(`\\nHybrid Search Results:`);
    console.log(`- Total merged results: ${hybridResults.mergedResults.length}`);
    console.log(`- String search results: ${hybridResults.stringResults.length}`);
    console.log(`- Fulltext search results: ${hybridResults.fulltextResults.length}`);
    console.log(`- Response time: ${hybridResults.responseTime.toFixed(2)}ms`);
    
    console.log('\\nTop 10 Merged Results:');
    hybridResults.mergedResults.slice(0, 10).forEach((result, idx) => {
      console.log(`${idx + 1}. ${result.title}`);
      console.log(`   URL: ${result.url}`);
      console.log(`   Combined Score: ${result.combinedScore.toFixed(3)}`);
      console.log(`   String Score: ${result.stringScore.toFixed(3)}, Fulltext Score: ${result.fulltextScore.toFixed(3)}`);
    });

    // Check for duplicates
    console.log('\\n=== Duplicate Check ===');
    const urlCounts = new Map<string, number>();
    hybridResults.mergedResults.forEach(result => {
      const count = urlCounts.get(result.url) || 0;
      urlCounts.set(result.url, count + 1);
    });
    
    let duplicates = 0;
    urlCounts.forEach((count, url) => {
      if (count > 1) {
        duplicates++;
        console.log(`Duplicate found: ${url} (${count} times)`);
      }
    });
    
    if (duplicates === 0) {
      console.log('✓ No duplicate URLs found');
    }

    // Test partial query
    console.log('\\n=== Testing Partial Query "claude c" ===');
    const partialResults = await hybridEngine.search('claude c');
    console.log(`Found ${partialResults.mergedResults.length} results`);

    // Summary
    console.log('\\n=== SUMMARY ===');
    console.log(`✓ Manual search found ${manualMatches.length} exact matches`);
    console.log(`✓ Lunr returned ${lunrResults.length} results (OR logic)`)
    console.log(`✓ Fuse.js returned ${fuseResults.length} results`);
    console.log(`✓ Hybrid engine merged to ${hybridResults.mergedResults.length} unique results`);
    console.log(`✓ No duplicates in merged results`);
    
    if (hybridResults.mergedResults.length > 1) {
      console.log('\\n✅ SUCCESS: Hybrid search returns multiple results for "Claude Code"');
    } else {
      console.log('\\n❌ ISSUE: Still returning only one result');
    }

  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testHybridSearch().catch(console.error);