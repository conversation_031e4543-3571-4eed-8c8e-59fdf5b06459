/**
 * AJAX性能基准测试脚本
 * 
 * 测量AJAX拦截对页面性能的影响
 * 包括：延迟、CPU使用率、内存占用等指标
 */

const PerformanceBenchmark = {
  config: {
    iterations: 100,        // 每个测试的迭代次数
    warmupRuns: 10,        // 预热运行次数
    requestsPerBatch: 50,  // 每批请求数量
    testDuration: 30000,   // 压力测试持续时间（毫秒）
  },
  
  results: {
    baseline: {},
    withInterception: {},
    comparison: {}
  },
  
  /**
   * 运行完整的性能基准测试
   */
  async runBenchmark() {
    console.log('🚀 开始AJAX性能基准测试...\n');
    console.log('配置:', this.config);
    console.log('\n' + '='.repeat(60) + '\n');
    
    // 1. 基准测试（禁用拦截）
    console.log('📊 Phase 1: 基准测试（无拦截）');
    await this.disableInterception();
    this.results.baseline = await this.runTestSuite('baseline');
    
    // 2. 启用拦截测试
    console.log('\n📊 Phase 2: 启用拦截测试');
    await this.enableInterception();
    this.results.withInterception = await this.runTestSuite('interception');
    
    // 3. 生成对比报告
    this.generateComparisonReport();
    
    // 4. 恢复原始状态
    await this.enableInterception();
    
    return this.results;
  },
  
  /**
   * 运行测试套件
   */
  async runTestSuite(mode) {
    const suite = {
      requestLatency: await this.testRequestLatency(),
      memoryUsage: await this.testMemoryUsage(),
      cpuImpact: await this.testCPUImpact(),
      throughput: await this.testThroughput(),
      domOperations: await this.testDOMOperations()
    };
    
    console.log(`\n${mode} 测试结果:`, suite);
    return suite;
  },
  
  /**
   * 测试请求延迟
   */
  async testRequestLatency() {
    console.log('\n⏱️ 测试请求延迟...');
    
    const latencies = {
      xhr: [],
      fetch: [],
      overall: []
    };
    
    // 预热
    for (let i = 0; i < this.config.warmupRuns; i++) {
      await this.makeXHRRequest('/warmup');
      await this.makeFetchRequest('/warmup');
    }
    
    // XHR延迟测试
    for (let i = 0; i < this.config.iterations; i++) {
      const start = performance.now();
      await this.makeXHRRequest('/test/xhr/' + i);
      const end = performance.now();
      latencies.xhr.push(end - start);
    }
    
    // Fetch延迟测试
    for (let i = 0; i < this.config.iterations; i++) {
      const start = performance.now();
      await this.makeFetchRequest('/test/fetch/' + i);
      const end = performance.now();
      latencies.fetch.push(end - start);
    }
    
    // 计算统计数据
    const stats = {
      xhr: this.calculateStats(latencies.xhr),
      fetch: this.calculateStats(latencies.fetch),
      overall: this.calculateStats([...latencies.xhr, ...latencies.fetch])
    };
    
    console.log('延迟统计:', stats);
    return stats;
  },
  
  /**
   * 测试内存使用
   */
  async testMemoryUsage() {
    console.log('\n💾 测试内存使用...');
    
    if (!performance.memory) {
      console.log('⚠️ 内存API不可用');
      return null;
    }
    
    const measurements = [];
    const startMemory = performance.memory.usedJSHeapSize;
    
    // 执行大量请求并监测内存
    for (let i = 0; i < 10; i++) {
      // 批量请求
      const promises = [];
      for (let j = 0; j < this.config.requestsPerBatch; j++) {
        promises.push(this.makeXHRRequest('/memory/test/' + i + '/' + j));
        promises.push(this.makeFetchRequest('/memory/test/' + i + '/' + j));
      }
      
      await Promise.all(promises);
      
      // 记录内存使用
      measurements.push({
        iteration: i,
        heapSize: performance.memory.usedJSHeapSize,
        heapDelta: performance.memory.usedJSHeapSize - startMemory
      });
      
      // 短暂等待GC
      await this.sleep(100);
    }
    
    const endMemory = performance.memory.usedJSHeapSize;
    const memoryGrowth = endMemory - startMemory;
    
    const result = {
      startMemoryMB: (startMemory / 1024 / 1024).toFixed(2),
      endMemoryMB: (endMemory / 1024 / 1024).toFixed(2),
      growthMB: (memoryGrowth / 1024 / 1024).toFixed(2),
      measurements,
      averageGrowthPerRequest: (memoryGrowth / (this.config.requestsPerBatch * 10 * 2)).toFixed(0)
    };
    
    console.log('内存使用:', result);
    return result;
  },
  
  /**
   * 测试CPU影响
   */
  async testCPUImpact() {
    console.log('\n🔥 测试CPU影响...');
    
    // 使用计算密集型任务来间接测量CPU影响
    const cpuTest = () => {
      const start = performance.now();
      let sum = 0;
      for (let i = 0; i < 1000000; i++) {
        sum += Math.sqrt(i);
      }
      return performance.now() - start;
    };
    
    // 基准CPU测试
    const baselineTimes = [];
    for (let i = 0; i < 5; i++) {
      baselineTimes.push(cpuTest());
    }
    
    // 在执行AJAX请求时测试CPU
    const withAjaxTimes = [];
    const ajaxPromises = [];
    
    // 启动并发AJAX请求
    for (let i = 0; i < 20; i++) {
      ajaxPromises.push(this.makeXHRRequest('/cpu/test/' + i));
      ajaxPromises.push(this.makeFetchRequest('/cpu/test/' + i));
    }
    
    // 同时进行CPU测试
    for (let i = 0; i < 5; i++) {
      withAjaxTimes.push(cpuTest());
    }
    
    await Promise.all(ajaxPromises);
    
    const result = {
      baselineAvg: this.average(baselineTimes).toFixed(2),
      withAjaxAvg: this.average(withAjaxTimes).toFixed(2),
      overhead: ((this.average(withAjaxTimes) / this.average(baselineTimes) - 1) * 100).toFixed(2) + '%'
    };
    
    console.log('CPU影响:', result);
    return result;
  },
  
  /**
   * 测试吞吐量
   */
  async testThroughput() {
    console.log('\n📈 测试请求吞吐量...');
    
    const startTime = performance.now();
    let completedRequests = 0;
    const errors = [];
    
    // 持续发送请求直到时间结束
    const endTime = startTime + this.config.testDuration;
    const promises = [];
    
    while (performance.now() < endTime) {
      // 交替使用XHR和Fetch
      const useXHR = completedRequests % 2 === 0;
      const promise = useXHR
        ? this.makeXHRRequest('/throughput/' + completedRequests)
        : this.makeFetchRequest('/throughput/' + completedRequests);
      
      promise
        .then(() => completedRequests++)
        .catch(err => errors.push(err));
      
      promises.push(promise);
      
      // 控制并发数
      if (promises.length >= 10) {
        await Promise.race(promises);
        promises.splice(0, 1);
      }
    }
    
    // 等待所有请求完成
    await Promise.allSettled(promises);
    
    const duration = performance.now() - startTime;
    const result = {
      totalRequests: completedRequests,
      durationMs: duration.toFixed(0),
      requestsPerSecond: (completedRequests / (duration / 1000)).toFixed(2),
      errors: errors.length,
      errorRate: ((errors.length / completedRequests) * 100).toFixed(2) + '%'
    };
    
    console.log('吞吐量测试:', result);
    return result;
  },
  
  /**
   * 测试DOM操作性能
   */
  async testDOMOperations() {
    console.log('\n🏗️ 测试DOM操作性能...');
    
    const measurements = {
      withoutAjax: [],
      withAjax: []
    };
    
    // 测试DOM操作（无AJAX）
    for (let i = 0; i < 10; i++) {
      const start = performance.now();
      this.performDOMOperations(100);
      measurements.withoutAjax.push(performance.now() - start);
    }
    
    // 测试DOM操作（有并发AJAX）
    for (let i = 0; i < 10; i++) {
      // 启动AJAX请求
      const ajaxPromises = [];
      for (let j = 0; j < 10; j++) {
        ajaxPromises.push(this.makeXHRRequest('/dom/' + i + '/' + j));
      }
      
      const start = performance.now();
      this.performDOMOperations(100);
      measurements.withAjax.push(performance.now() - start);
      
      await Promise.all(ajaxPromises);
    }
    
    const result = {
      withoutAjaxAvg: this.average(measurements.withoutAjax).toFixed(2),
      withAjaxAvg: this.average(measurements.withAjax).toFixed(2),
      overhead: ((this.average(measurements.withAjax) / this.average(measurements.withoutAjax) - 1) * 100).toFixed(2) + '%'
    };
    
    console.log('DOM操作性能:', result);
    return result;
  },
  
  /**
   * 辅助函数：执行DOM操作
   */
  performDOMOperations(count) {
    const container = document.createElement('div');
    container.style.display = 'none';
    document.body.appendChild(container);
    
    for (let i = 0; i < count; i++) {
      const elem = document.createElement('div');
      elem.className = 'test-element-' + i;
      elem.textContent = 'Test content ' + i;
      container.appendChild(elem);
      
      // 触发重排
      elem.offsetHeight;
      
      // 修改样式
      elem.style.color = 'red';
      elem.style.fontSize = '14px';
    }
    
    document.body.removeChild(container);
  },
  
  /**
   * 辅助函数：发送XHR请求
   */
  makeXHRRequest(url) {
    return new Promise((resolve) => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', url);
      xhr.onloadend = () => resolve();
      xhr.onerror = () => resolve();
      xhr.send();
    });
  },
  
  /**
   * 辅助函数：发送Fetch请求
   */
  makeFetchRequest(url) {
    return fetch(url).catch(() => {});
  },
  
  /**
   * 辅助函数：计算统计数据
   */
  calculateStats(values) {
    const sorted = values.slice().sort((a, b) => a - b);
    const len = sorted.length;
    
    return {
      min: sorted[0].toFixed(2),
      max: sorted[len - 1].toFixed(2),
      mean: this.average(sorted).toFixed(2),
      median: sorted[Math.floor(len / 2)].toFixed(2),
      p95: sorted[Math.floor(len * 0.95)].toFixed(2),
      p99: sorted[Math.floor(len * 0.99)].toFixed(2)
    };
  },
  
  /**
   * 辅助函数：计算平均值
   */
  average(values) {
    return values.reduce((a, b) => a + b, 0) / values.length;
  },
  
  /**
   * 辅助函数：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  
  /**
   * 禁用AJAX拦截
   */
  async disableInterception() {
    if (window.RecallDebug && window.RecallDebug.disableVerboseLogging) {
      window.RecallDebug.disableVerboseLogging();
    }
    // 这里应该有一个方法来完全禁用拦截，但目前API中没有
    console.log('⚠️ 注意：无法完全禁用拦截，基准测试包含拦截开销');
  },
  
  /**
   * 启用AJAX拦截
   */
  async enableInterception() {
    if (window.RecallDebug && window.RecallDebug.enableVerboseLogging) {
      window.RecallDebug.enableVerboseLogging();
    }
  },
  
  /**
   * 生成对比报告
   */
  generateComparisonReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 性能对比报告\n');
    
    const baseline = this.results.baseline;
    const intercepted = this.results.withInterception;
    
    // 请求延迟对比
    if (baseline.requestLatency && intercepted.requestLatency) {
      console.log('📍 请求延迟影响:');
      console.log(`  XHR平均延迟: ${baseline.requestLatency.xhr.mean}ms → ${intercepted.requestLatency.xhr.mean}ms`);
      console.log(`  Fetch平均延迟: ${baseline.requestLatency.fetch.mean}ms → ${intercepted.requestLatency.fetch.mean}ms`);
      
      const xhrOverhead = ((intercepted.requestLatency.xhr.mean - baseline.requestLatency.xhr.mean) / baseline.requestLatency.xhr.mean * 100).toFixed(2);
      const fetchOverhead = ((intercepted.requestLatency.fetch.mean - baseline.requestLatency.fetch.mean) / baseline.requestLatency.fetch.mean * 100).toFixed(2);
      
      console.log(`  XHR开销: +${xhrOverhead}%`);
      console.log(`  Fetch开销: +${fetchOverhead}%`);
    }
    
    // 内存使用对比
    if (baseline.memoryUsage && intercepted.memoryUsage) {
      console.log('\n📍 内存使用影响:');
      console.log(`  内存增长: ${baseline.memoryUsage.growthMB}MB → ${intercepted.memoryUsage.growthMB}MB`);
      const memoryOverhead = (intercepted.memoryUsage.growthMB - baseline.memoryUsage.growthMB).toFixed(2);
      console.log(`  额外内存开销: +${memoryOverhead}MB`);
    }
    
    // CPU影响对比
    if (baseline.cpuImpact && intercepted.cpuImpact) {
      console.log('\n📍 CPU影响:');
      console.log(`  CPU开销: ${baseline.cpuImpact.overhead} → ${intercepted.cpuImpact.overhead}`);
    }
    
    // 吞吐量对比
    if (baseline.throughput && intercepted.throughput) {
      console.log('\n📍 吞吐量影响:');
      console.log(`  请求/秒: ${baseline.throughput.requestsPerSecond} → ${intercepted.throughput.requestsPerSecond}`);
      const throughputLoss = ((baseline.throughput.requestsPerSecond - intercepted.throughput.requestsPerSecond) / baseline.throughput.requestsPerSecond * 100).toFixed(2);
      console.log(`  吞吐量损失: ${throughputLoss}%`);
    }
    
    // DOM操作对比
    if (baseline.domOperations && intercepted.domOperations) {
      console.log('\n📍 DOM操作影响:');
      console.log(`  DOM操作时间: ${baseline.domOperations.withoutAjaxAvg}ms → ${intercepted.domOperations.withAjaxAvg}ms`);
      console.log(`  性能开销: ${intercepted.domOperations.overhead}`);
    }
    
    // 总结
    console.log('\n' + '='.repeat(60));
    console.log('📋 总结:');
    
    const impacts = [];
    
    // 评估各项指标
    if (intercepted.requestLatency) {
      const avgOverhead = (parseFloat(intercepted.requestLatency.overall.mean) - parseFloat(baseline.requestLatency.overall.mean)).toFixed(2);
      if (avgOverhead < 5) {
        impacts.push('✅ 请求延迟影响可接受 (<5ms)');
      } else {
        impacts.push(`⚠️ 请求延迟增加 ${avgOverhead}ms`);
      }
    }
    
    if (intercepted.memoryUsage && baseline.memoryUsage) {
      const memDiff = parseFloat(intercepted.memoryUsage.growthMB) - parseFloat(baseline.memoryUsage.growthMB);
      if (memDiff < 20) {
        impacts.push('✅ 内存使用影响可接受 (<20MB)');
      } else {
        impacts.push(`⚠️ 内存使用增加 ${memDiff.toFixed(2)}MB`);
      }
    }
    
    if (intercepted.cpuImpact) {
      const cpuOverhead = parseFloat(intercepted.cpuImpact.overhead);
      if (cpuOverhead < 5) {
        impacts.push('✅ CPU影响可接受 (<5%)');
      } else {
        impacts.push(`⚠️ CPU开销增加 ${cpuOverhead}%`);
      }
    }
    
    impacts.forEach(impact => console.log(impact));
    
    // 保存比较结果
    this.results.comparison = {
      requestLatencyOverhead: xhrOverhead || 0,
      memoryOverhead: memoryOverhead || 0,
      cpuOverhead: intercepted.cpuImpact?.overhead || 0,
      throughputLoss: throughputLoss || 0,
      overall: impacts.filter(i => i.includes('✅')).length >= impacts.length / 2 ? 'PASS' : 'NEEDS_OPTIMIZATION'
    };
    
    console.log(`\n整体评估: ${this.results.comparison.overall}`);
  }
};

// 导出到全局
if (typeof window !== 'undefined') {
  window.PerformanceBenchmark = PerformanceBenchmark;
  console.log('性能基准测试工具已加载');
  console.log('运行测试: PerformanceBenchmark.runBenchmark()');
}