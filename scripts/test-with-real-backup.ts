/**
 * Test search with real backup file
 */

import { dbService } from '../src/services/db.service';
import { searchService } from '../src/services/search.service';
import { lunrSearchEngine } from '../src/search/fulltext/LunrSearchEngine';
import { HybridSearchEngine } from '../src/search/hybrid/HybridSearchEngine';
import type { Page } from '../src/models';
import * as fs from 'fs';
import * as path from 'path';

// Initialize Chrome API mock
(global as any).chrome = {
  storage: {
    local: {
      get: jest.fn().mockResolvedValue({}),
      set: jest.fn().mockResolvedValue(undefined),
    },
  },
  runtime: {
    sendMessage: jest.fn().mockResolvedValue({}),
    onMessage: {
      addListener: jest.fn(),
    },
  },
  tabs: {
    query: jest.fn().mockResolvedValue([]),
  },
};

// Mock IndexedDB
const mockIndexedDB = {
  open: jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
    onupgradeneeded: jest.fn(),
    result: {
      close: jest.fn(),
      objectStoreNames: { contains: jest.fn().mockReturnValue(false) },
      createObjectStore: jest.fn().mockReturnValue({
        createIndex: jest.fn(),
      }),
    },
  }),
  deleteDatabase: jest.fn(),
};

(global as any).indexedDB = mockIndexedDB;

async function loadBackupFile(): Promise<{ pages: Page[] }> {
  const backupPath = path.join(__dirname, '../tests/materials/recall-backup-2025-06-26.json');
  const backupContent = fs.readFileSync(backupPath, 'utf-8');
  return JSON.parse(backupContent);
}

async function testSearchWithBackup() {
  console.log('=== Testing Search with Real Backup File ===\n');

  try {
    // Load backup data
    console.log('Loading backup file...');
    const backup = await loadBackupFile();
    console.log(`Loaded ${backup.pages.length} pages from backup`);

    // Count pages containing "claude code"
    const claudeCodePages = backup.pages.filter(page => {
      const text = `${page.title} ${page.content}`.toLowerCase();
      return text.includes('claude') && text.includes('code');
    });
    console.log(`\nPages containing both "claude" AND "code": ${claudeCodePages.length}`);

    // Count pages containing either term
    const claudeOrCodePages = backup.pages.filter(page => {
      const text = `${page.title} ${page.content}`.toLowerCase();
      return text.includes('claude') || text.includes('code');
    });
    console.log(`Pages containing "claude" OR "code": ${claudeOrCodePages.length}`);

    // Initialize Lunr
    console.log('\nInitializing Lunr search engine...');
    await lunrSearchEngine.createIndex(backup.pages);

    // Test 1: Search for "Claude Code"
    console.log('\n=== Test 1: Searching for "Claude Code" ===');
    const claudeCodeResults = await lunrSearchEngine.search('Claude Code');
    console.log(`Lunr returned ${claudeCodeResults.length} results`);
    
    if (claudeCodeResults.length > 0) {
      console.log('First 5 results:');
      claudeCodeResults.slice(0, 5).forEach((result, idx) => {
        console.log(`  ${idx + 1}. [${result.page.id}] ${result.page.title}`);
        console.log(`     Score: ${result.score.toFixed(3)}`);
        console.log(`     Matched terms: ${result.metadata?.matchedTerms?.join(', ') || 'N/A'}`);
      });
    }

    // Test 2: Search for "claude c"
    console.log('\n=== Test 2: Searching for "claude c" ===');
    const claudeCResults = await lunrSearchEngine.search('claude c');
    console.log(`Lunr returned ${claudeCResults.length} results`);

    // Test 3: Search for just "claude"
    console.log('\n=== Test 3: Searching for "claude" ===');
    const claudeResults = await lunrSearchEngine.search('claude');
    console.log(`Lunr returned ${claudeResults.length} results`);

    // Test 4: Search for just "code"
    console.log('\n=== Test 4: Searching for "code" ===');
    const codeResults = await lunrSearchEngine.search('code');
    console.log(`Lunr returned ${codeResults.length} results`);

    // Test 5: Manual text search to verify
    console.log('\n=== Test 5: Manual Text Search ===');
    const manualClaudeCode = backup.pages.filter(page => {
      const text = `${page.title} ${page.content}`.toLowerCase();
      // Look for "claude code" as a phrase
      return text.includes('claude code');
    });
    console.log(`Pages containing "claude code" as phrase: ${manualClaudeCode.length}`);

    // Show some examples
    if (manualClaudeCode.length > 0) {
      console.log('\nExample pages containing "claude code":');
      manualClaudeCode.slice(0, 3).forEach((page, idx) => {
        console.log(`\n${idx + 1}. [${page.id}] ${page.title}`);
        // Find and show the context
        const text = `${page.title} ${page.content}`.toLowerCase();
        const index = text.indexOf('claude code');
        if (index !== -1) {
          const start = Math.max(0, index - 50);
          const end = Math.min(text.length, index + 100);
          const snippet = text.substring(start, end);
          console.log(`   Context: "...${snippet}..."`);
        }
      });
    }

    // Test HybridSearchEngine
    console.log('\n=== Test 6: HybridSearchEngine ===');
    const hybridEngine = new HybridSearchEngine();
    const hybridResults = await hybridEngine.search('Claude Code');
    console.log(`Hybrid search returned ${hybridResults.mergedResults.length} results`);
    console.log(`String results: ${hybridResults.stringResults.length}`);
    console.log(`Fulltext results: ${hybridResults.fulltextResults.length}`);

    // Analysis
    console.log('\n=== Analysis ===');
    console.log(`Expected results (manual count): ${claudeCodePages.length}`);
    console.log(`Lunr "Claude Code" results: ${claudeCodeResults.length}`);
    console.log(`Discrepancy: ${claudeCodePages.length - claudeCodeResults.length} missing results`);

    // Check if Lunr is splitting the query
    console.log('\n=== Lunr Query Analysis ===');
    console.log('Lunr appears to be using OR logic for multi-word queries.');
    console.log(`"claude" alone: ${claudeResults.length} results`);
    console.log(`"code" alone: ${codeResults.length} results`);
    console.log(`"Claude Code" (should be intersection): ${claudeCodeResults.length} results`);

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the test
testSearchWithBackup().catch(console.error);