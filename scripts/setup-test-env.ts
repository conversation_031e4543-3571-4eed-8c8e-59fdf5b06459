/**
 * Setup test environment with necessary mocks
 */

// Mock Chrome API
(global as any).chrome = {
  storage: {
    local: {
      get: () => Promise.resolve({}),
      set: () => Promise.resolve(),
    },
  },
  runtime: {
    sendMessage: () => Promise.resolve({}),
    onMessage: {
      addListener: () => {},
    },
  },
};

// Mock IndexedDB with a simple in-memory implementation
class MockObjectStore {
  private data: Map<string, any> = new Map();

  add(value: any, key?: string) {
    const k = key || value.id || Math.random().toString();
    this.data.set(k, value);
    return { onsuccess: null, onerror: null };
  }

  put(value: any, key?: string) {
    const k = key || value.id || Math.random().toString();
    this.data.set(k, value);
    return { onsuccess: null, onerror: null };
  }

  get(key: string) {
    const value = this.data.get(key);
    const request = {
      onsuccess: null as any,
      onerror: null as any,
      result: value
    };
    setTimeout(() => {
      if (request.onsuccess) request.onsuccess({ target: { result: value } });
    }, 0);
    return request;
  }

  getAll() {
    const values = Array.from(this.data.values());
    const request = {
      onsuccess: null as any,
      onerror: null as any,
      result: values
    };
    setTimeout(() => {
      if (request.onsuccess) request.onsuccess({ target: { result: values } });
    }, 0);
    return request;
  }

  delete(key: string) {
    this.data.delete(key);
    return { onsuccess: null, onerror: null };
  }

  clear() {
    this.data.clear();
    return { onsuccess: null, onerror: null };
  }

  count() {
    const count = this.data.size;
    const request = {
      onsuccess: null as any,
      onerror: null as any,
      result: count
    };
    setTimeout(() => {
      if (request.onsuccess) request.onsuccess({ target: { result: count } });
    }, 0);
    return request;
  }

  openCursor() {
    const entries = Array.from(this.data.entries());
    let index = 0;
    const request = {
      onsuccess: null as any,
      onerror: null as any,
      result: null as any
    };
    
    const cursor = {
      key: null as any,
      value: null as any,
      continue: () => {
        index++;
        if (index < entries.length) {
          cursor.key = entries[index][0];
          cursor.value = entries[index][1];
          if (request.onsuccess) {
            request.onsuccess({ target: { result: cursor } });
          }
        } else {
          if (request.onsuccess) {
            request.onsuccess({ target: { result: null } });
          }
        }
      }
    };
    
    setTimeout(() => {
      if (entries.length > 0) {
        cursor.key = entries[0][0];
        cursor.value = entries[0][1];
        request.result = cursor;
        if (request.onsuccess) {
          request.onsuccess({ target: { result: cursor } });
        }
      } else {
        if (request.onsuccess) {
          request.onsuccess({ target: { result: null } });
        }
      }
    }, 0);
    
    return request;
  }
}

class MockTransaction {
  private stores: Map<string, MockObjectStore> = new Map();

  objectStore(name: string) {
    if (!this.stores.has(name)) {
      this.stores.set(name, new MockObjectStore());
    }
    return this.stores.get(name)!;
  }
}

class MockDatabase {
  version = 1;
  objectStoreNames = ['pages', 'settings', 'searchIndexes'];
  
  private stores: Map<string, MockObjectStore> = new Map();

  constructor() {
    this.objectStoreNames.forEach(name => {
      this.stores.set(name, new MockObjectStore());
    });
  }

  transaction(storeNames: string | string[], _mode?: string) {
    const transaction = new MockTransaction();
    const names = Array.isArray(storeNames) ? storeNames : [storeNames];
    names.forEach(name => {
      const store = this.stores.get(name) || new MockObjectStore();
      transaction.objectStore(name);
    });
    return transaction;
  }

  createObjectStore(name: string, _options?: any) {
    this.stores.set(name, new MockObjectStore());
    return this.stores.get(name)!;
  }

  close() {
    // Mock close
  }
}

(global as any).indexedDB = {
  open: (name: string, version?: number) => {
    const request = {
      onsuccess: null as any,
      onerror: null as any,
      onupgradeneeded: null as any,
      result: new MockDatabase()
    };
    
    setTimeout(() => {
      if (request.onsuccess) {
        request.onsuccess({ target: request });
      }
    }, 0);
    
    return request;
  },
  deleteDatabase: (_name: string) => {
    const request = {
      onsuccess: null as any,
      onerror: null as any
    };
    
    setTimeout(() => {
      if (request.onsuccess) {
        request.onsuccess({ target: request });
      }
    }, 0);
    
    return request;
  }
};

// Mock performance API if not available
if (typeof performance === 'undefined') {
  (global as any).performance = {
    now: () => Date.now()
  };
}

// Export for TypeScript
export {};