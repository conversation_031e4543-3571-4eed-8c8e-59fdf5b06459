/**
 * Test script to verify the popup search fix
 * Run this in the browser console to test the search functionality
 */

(async function testPopupSearchFix() {
  console.log('🔍 Testing popup search fix...');
  
  try {
    // Check if we're in the right context
    if (typeof chrome === 'undefined' || !chrome.runtime) {
      console.error('❌ This script must be run in a Chrome extension context');
      return;
    }
    
    console.log('✅ Chrome extension context detected');
    
    // Try to access the hybrid search service
    let hybridSearchService;
    try {
      // This will work if the script is run in the popup context
      if (typeof window !== 'undefined' && window.hybridSearchService) {
        hybridSearchService = window.hybridSearchService;
      } else {
        // Try to import it
        const services = await import(chrome.runtime.getURL('src/services/index.js'));
        hybridSearchService = services.hybridSearchService;
      }
    } catch (importError) {
      console.error('❌ Failed to access hybrid search service:', importError);
      return;
    }
    
    console.log('✅ Hybrid search service accessed');
    
    // Check if the service is initialized
    const stats = hybridSearchService.getIndexStats();
    console.log('📊 Index stats:', stats);
    
    if (stats.totalPages === 0) {
      console.log('⚠️ No pages in index, initializing...');
      await hybridSearchService.init();
      const newStats = hybridSearchService.getIndexStats();
      console.log('📊 New index stats:', newStats);
    }
    
    // Perform test searches
    const testQueries = ['test', 'javascript', 'github', 'google'];
    
    for (const query of testQueries) {
      try {
        console.log(`🔍 Testing search: "${query}"`);
        const startTime = Date.now();
        const results = await hybridSearchService.search(query, { limit: 5 });
        const searchTime = Date.now() - startTime;
        
        console.log(`✅ Search "${query}" completed in ${searchTime}ms, found ${results.length} results`);
        
        if (results.length > 0) {
          console.log(`   First result: ${results[0].page.title} (${results[0].page.url})`);
        }
        
        // Small delay between searches
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (searchError) {
        console.error(`❌ Search "${query}" failed:`, searchError);
        
        // Check if it's the specific error we're trying to fix
        if (searchError.message && searchError.message.includes('not found in documents map')) {
          console.error('🚨 Found the "Document not found in documents map" error!');
          console.error('   This indicates the fix may not be working properly.');
        }
      }
    }
    
    console.log('🎉 Popup search fix test completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    console.error('Stack trace:', error.stack);
  }
})();

// Also provide a manual test function
window.testPopupSearch = async function(query = 'test') {
  try {
    const { hybridSearchService } = await import(chrome.runtime.getURL('src/services/index.js'));
    const results = await hybridSearchService.search(query, { limit: 10 });
    console.log(`Search results for "${query}":`, results);
    return results;
  } catch (error) {
    console.error('Manual search test failed:', error);
    throw error;
  }
};

console.log('💡 You can also run: testPopupSearch("your query") to test manually');
