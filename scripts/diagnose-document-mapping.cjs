#!/usr/bin/env node

/**
 * Diagnostic script to analyze the "Document not found in documents map" issue
 * This script will help identify the root cause of the mapping inconsistency
 *
 * Note: This file should be renamed to .cjs extension to work with ES modules
 */

const path = require('path');
const fs = require('fs');

// Mock logger for the diagnostic
const logger = {
  info: (...args) => console.log('[INFO]', ...args),
  warn: (...args) => console.warn('[WARN]', ...args),
  error: (...args) => console.error('[ERROR]', ...args),
  debug: (...args) => console.log('[DEBUG]', ...args)
};

async function diagnoseDocumentMapping() {
  console.log('🔍 Starting Document Mapping Diagnostic...\n');

  try {
    // Check if we can access the built extension files
    const distPath = path.join(process.cwd(), 'dist');
    if (!fs.existsSync(distPath)) {
      console.error('❌ dist/ directory not found. Please run "npm run build" first.');
      return;
    }

    console.log('✅ Extension build found');

    // Look for the main search service files
    const searchFiles = fs.readdirSync(path.join(distPath, 'assets'))
      .filter(file => file.includes('blacklist.service') || file.includes('LanguageSelector'))
      .slice(0, 2); // Just check the first couple

    console.log('📁 Found search-related files:', searchFiles);

    // Analyze the issue patterns
    console.log('\n🔍 Analyzing potential causes:\n');

    console.log('1. 📋 Index-Document Synchronization Issues:');
    console.log('   - Stored index contains document IDs that no longer exist in current pages');
    console.log('   - Document mapping is cleared and rebuilt, but index still references old IDs');
    console.log('   - Race condition between index loading and document mapping construction');

    console.log('\n2. 🔄 Timing Issues:');
    console.log('   - Index is loaded from storage before all pages are available');
    console.log('   - Document mapping is built with incomplete page data');
    console.log('   - Async operations complete in unexpected order');

    console.log('\n3. 📝 Data Consistency Issues:');
    console.log('   - Page IDs in database don\'t match IDs in stored index');
    console.log('   - Pages were deleted but index wasn\'t updated');
    console.log('   - Index rebuild logic has bugs in ID comparison');

    console.log('\n4. 🏗️ Build/Runtime Issues:');
    console.log('   - Different ID generation between build time and runtime');
    console.log('   - Extension context affects how modules are loaded');
    console.log('   - Chrome extension security model interferes with IndexedDB access');

    console.log('\n📊 Recommended Diagnostic Steps:\n');

    console.log('1. Check Index Storage:');
    console.log('   - Open Chrome DevTools → Application → IndexedDB → RecallDB');
    console.log('   - Look for "settings" store and find the Lunr index entry');
    console.log('   - Compare documentIds in stored index with actual page IDs');

    console.log('\n2. Monitor Console Warnings:');
    console.log('   - Look for the exact document IDs that are "not found"');
    console.log('   - Check if these IDs follow a consistent pattern');
    console.log('   - Note the frequency and timing of these warnings');

    console.log('\n3. Verify Page Data:');
    console.log('   - Check if pages table in IndexedDB has the missing document IDs');
    console.log('   - Verify that page.id values are consistent and not null/undefined');
    console.log('   - Look for any ID format changes or corruption');

    console.log('\n🛠️ Potential Fixes:\n');

    console.log('1. Force Index Rebuild:');
    console.log('   - Clear stored index when document count or IDs don\'t match');
    console.log('   - Add more robust validation in needsRebuild() method');

    console.log('\n2. Improve Error Handling:');
    console.log('   - Add null checks and fallbacks when documents are missing');
    console.log('   - Log more detailed information about missing documents');

    console.log('\n3. Synchronization Fixes:');
    console.log('   - Ensure document mapping is fully built before using index');
    console.log('   - Add validation that all index document IDs exist in mapping');

    console.log('\n4. Debug Mode:');
    console.log('   - Add detailed logging to track index loading and document mapping');
    console.log('   - Create diagnostic endpoints to inspect internal state');

    console.log('\n🎯 Next Steps:');
    console.log('1. Load the extension and reproduce the warnings');
    console.log('2. Note the specific document IDs that are missing');
    console.log('3. Check IndexedDB to see if those IDs exist in the pages table');
    console.log('4. Apply the appropriate fix based on findings');

    console.log('\n✨ This diagnostic completed successfully!');

  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

// Run the diagnostic
diagnoseDocumentMapping().catch(console.error);
