/**
 * Simple test script for real backup file without jest dependencies
 */

import { LunrSearchEngine } from '../src/search/fulltext/LunrSearchEngine';
import type { Page } from '../src/models';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testRealBackup() {
  console.log('=== Testing Search with Real Backup File ===\n');

  try {
    // Load backup data
    const backupPath = path.join(__dirname, '../tests/materials/recall-backup-2025-06-26.json');
    const backupContent = fs.readFileSync(backupPath, 'utf-8');
    const backup = JSON.parse(backupContent);
    const pages: Page[] = backup.pages;
    
    console.log(`Loaded ${pages.length} pages from backup`);

    // Manual search for "claude code"
    console.log('\n=== Manual Search for "claude code" ===');
    const claudeCodePages = pages.filter(page => {
      const text = `${page.title || ''} ${page.content || ''}`.toLowerCase();
      return text.includes('claude code');
    });
    console.log(`Found ${claudeCodePages.length} pages containing "claude code" (exact phrase)`);

    // Show examples
    if (claudeCodePages.length > 0) {
      console.log('\nExamples:');
      claudeCodePages.slice(0, 5).forEach((page, idx) => {
        console.log(`\n${idx + 1}. ${page.title}`);
        console.log(`   URL: ${page.url}`);
        const text = `${page.title || ''} ${page.content || ''}`.toLowerCase();
        const index = text.indexOf('claude code');
        if (index !== -1) {
          const start = Math.max(0, index - 100);
          const end = Math.min(text.length, index + 150);
          const snippet = text.substring(start, end).replace(/\n/g, ' ');
          console.log(`   Context: "...${snippet}..."`);
        }
      });
    }

    // Search for "claude" AND "code" separately
    console.log('\n=== Manual Search for "claude" AND "code" (separate words) ===');
    const claudeAndCodePages = pages.filter(page => {
      const text = `${page.title || ''} ${page.content || ''}`.toLowerCase();
      return text.includes('claude') && text.includes('code');
    });
    console.log(`Found ${claudeAndCodePages.length} pages containing both "claude" AND "code"`);

    // Initialize Lunr
    console.log('\n=== Testing Lunr Search Engine ===');
    const lunrEngine = new LunrSearchEngine();
    
    console.log('Building Lunr index...');
    await lunrEngine.createIndex(pages);
    const stats = lunrEngine.getStats();
    console.log(`Index built with ${stats.documentCount} documents`);

    // Test searches
    console.log('\n--- Lunr Search: "Claude Code" ---');
    const claudeCodeLunr = await lunrEngine.search('Claude Code');
    console.log(`Results: ${claudeCodeLunr.length}`);
    if (claudeCodeLunr.length > 0) {
      claudeCodeLunr.slice(0, 5).forEach((result, idx) => {
        console.log(`  ${idx + 1}. ${result.page.title} (score: ${result.score.toFixed(3)})`);
      });
    }

    console.log('\n--- Lunr Search: "claude code" (lowercase) ---');
    const claudeCodeLowerLunr = await lunrEngine.search('claude code');
    console.log(`Results: ${claudeCodeLowerLunr.length}`);

    console.log('\n--- Lunr Search: "claude" ---');
    const claudeLunr = await lunrEngine.search('claude');
    console.log(`Results: ${claudeLunr.length}`);

    console.log('\n--- Lunr Search: "code" ---');
    const codeLunr = await lunrEngine.search('code');
    console.log(`Results: ${codeLunr.length}`);

    console.log('\n--- Lunr Search: "claude c" ---');
    const claudeCLunr = await lunrEngine.search('claude c');
    console.log(`Results: ${claudeCLunr.length}`);

    // Analysis
    console.log('\n=== ANALYSIS ===');
    console.log(`Manual search "claude code" (exact phrase): ${claudeCodePages.length} pages`);
    console.log(`Manual search "claude" AND "code": ${claudeAndCodePages.length} pages`);
    console.log(`Lunr search "Claude Code": ${claudeCodeLunr.length} results`);
    console.log(`Lunr search "claude c": ${claudeCLunr.length} results`);
    
    console.log('\nDISCREPANCY FOUND:');
    console.log(`Expected at least ${claudeCodePages.length} results for "Claude Code"`);
    console.log(`But Lunr returned only ${claudeCodeLunr.length} results`);

    // Check if it's an indexing issue
    console.log('\n=== Checking Specific Pages ===');
    if (claudeCodePages.length > 0) {
      const testPage = claudeCodePages[0];
      console.log(`\nChecking if page "${testPage.title}" is searchable...`);
      
      // Search by ID
      const idSearch = await lunrEngine.search(testPage.id);
      console.log(`Search by ID "${testPage.id}": ${idSearch.length} results`);
      
      // Search by title words
      const titleWords = testPage.title.split(/\s+/).slice(0, 3).join(' ');
      const titleSearch = await lunrEngine.search(titleWords);
      console.log(`Search by title words "${titleWords}": ${titleSearch.length} results`);
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run test
testRealBackup().catch(console.error);