/**
 * Debug Lunr search issue
 */

import lunr from 'lunr';
import type { Page } from '../src/models';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simple Lunr test without the wrapper
async function debugLunr() {
  console.log('=== Debug Lunr Search ===\n');

  try {
    // Load backup data
    const backupPath = path.join(__dirname, '../tests/materials/recall-backup-2025-06-26.json');
    const backupContent = fs.readFileSync(backupPath, 'utf-8');
    const backup = JSON.parse(backupContent);
    const pages: Page[] = backup.pages;
    
    console.log(`Loaded ${pages.length} pages`);

    // Find pages with "claude code"
    const claudeCodePages = pages.filter(page => {
      const text = `${page.title || ''} ${page.content || ''}`.toLowerCase();
      return text.includes('claude code');
    });
    console.log(`\nPages containing "claude code": ${claudeCodePages.length}`);

    // Create a simple Lunr index directly
    console.log('\n=== Building Lunr Index Directly ===');
    
    const documents = pages.map(page => ({
      id: page.id,
      title: page.title || '',
      content: page.content || '',
      url: page.url
    }));

    console.log(`Creating index with ${documents.length} documents`);

    // Build index
    const idx = lunr(function() {
      this.ref('id');
      this.field('title', { boost: 10 });
      this.field('content');
      this.field('url', { boost: 5 });

      documents.forEach(doc => {
        console.log(`Adding doc: ${doc.id} - ${doc.title.substring(0, 50)}...`);
        this.add(doc);
      });
    });

    console.log('\nIndex built successfully');

    // Test searches
    console.log('\n=== Testing Searches ===');

    // Test 1: Search for "Claude Code"
    console.log('\n1. Searching for "Claude Code"');
    try {
      const results1 = idx.search('Claude Code');
      console.log(`   Results: ${results1.length}`);
      results1.slice(0, 3).forEach(r => {
        const doc = documents.find(d => d.id === r.ref);
        console.log(`   - ${doc?.title} (score: ${r.score.toFixed(3)})`);
      });
    } catch (err) {
      console.error('   Error:', err);
    }

    // Test 2: Search for "claude"
    console.log('\n2. Searching for "claude"');
    try {
      const results2 = idx.search('claude');
      console.log(`   Results: ${results2.length}`);
    } catch (err) {
      console.error('   Error:', err);
    }

    // Test 3: Search for "code"
    console.log('\n3. Searching for "code"');
    try {
      const results3 = idx.search('code');
      console.log(`   Results: ${results3.length}`);
    } catch (err) {
      console.error('   Error:', err);
    }

    // Test 4: Advanced search with query builder
    console.log('\n4. Advanced search with query builder');
    try {
      const results4 = idx.query(function(q) {
        // Search for documents containing both "claude" and "code"
        q.term('claude', { boost: 10 });
        q.term('code', { boost: 10 });
      });
      console.log(`   Results: ${results4.length}`);
      results4.slice(0, 3).forEach(r => {
        const doc = documents.find(d => d.id === r.ref);
        console.log(`   - ${doc?.title} (score: ${r.score.toFixed(3)})`);
      });
    } catch (err) {
      console.error('   Error:', err);
    }

    // Test 5: Wildcard search
    console.log('\n5. Wildcard search for "claud*"');
    try {
      const results5 = idx.search('claud*');
      console.log(`   Results: ${results5.length}`);
    } catch (err) {
      console.error('   Error:', err);
    }

    // Check if specific document is indexed
    console.log('\n=== Checking Specific Document ===');
    if (claudeCodePages.length > 0) {
      const testPage = claudeCodePages[0];
      console.log(`\nChecking if "${testPage.title}" is in index...`);
      
      // Search by exact title
      try {
        const titleWords = testPage.title.toLowerCase().split(/\s+/);
        console.log(`Title words: ${titleWords.join(', ')}`);
        
        // Search for first word
        const firstWord = titleWords[0];
        console.log(`\nSearching for first word "${firstWord}":`);
        const wordResults = idx.search(firstWord);
        console.log(`Results: ${wordResults.length}`);
      } catch (err) {
        console.error('Error:', err);
      }
    }

    // Debug: Check what's actually in the index
    console.log('\n=== Index Debug Info ===');
    console.log('Index fields:', Object.keys(idx.fields));
    console.log('Pipeline functions:', idx.pipeline._stack.map(fn => fn.name || fn.toString().substring(0, 30)));

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run debug
debugLunr().catch(console.error);