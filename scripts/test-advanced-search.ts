/**
 * <PERSON><PERSON><PERSON> to test advanced search syntax functionality
 */

// Add mocks before imports
import './setup-test-env';

import { HybridSearchEngine } from '../src/search/hybrid/HybridSearchEngine';
import type { Page } from '../src/models';

// Test data
const testPages: Page[] = [
  {
    id: '1',
    url: 'https://example.com/react-hooks',
    title: 'React Hooks Best Practices',
    content: 'React Hooks are a powerful feature in React. They allow you to use state and other React features without writing a class. This guide covers best practices for using hooks effectively.',
    domain: 'example.com',
    visitTime: Date.now() - 86400000,
    lastUpdated: Date.now() - 86400000,
    accessCount: 5,
    language: 'en'
  },
  {
    id: '2',
    url: 'https://developer.mozilla.org/en-US/docs/Web/JavaScript',
    title: 'JavaScript Documentation',
    content: 'JavaScript is a programming language that enables interactive web pages. Learn about JavaScript features, syntax, and best practices.',
    domain: 'developer.mozilla.org',
    visitTime: Date.now() - *********,
    lastUpdated: Date.now() - *********,
    accessCount: 10,
    language: 'en'
  },
  {
    id: '3',
    url: 'https://stackoverflow.com/questions/react-hooks-vs-class',
    title: 'React Hooks vs Class Components',
    content: 'A comparison between React Hooks and class components. Hooks provide a more direct API to the React concepts you already know.',
    domain: 'stackoverflow.com',
    visitTime: Date.now() - *********,
    lastUpdated: Date.now() - *********,
    accessCount: 3,
    language: 'en'
  },
  {
    id: '4',
    url: 'https://example.com/vue-composition-api',
    title: 'Vue Composition API Guide',
    content: 'The Vue Composition API is a set of APIs that allows us to author Vue components using imported functions instead of declaring options.',
    domain: 'example.com',
    visitTime: Date.now() - *********,
    lastUpdated: Date.now() - *********,
    accessCount: 2,
    language: 'en'
  },
  {
    id: '5',
    url: 'https://github.com/facebook/react',
    title: 'React GitHub Repository',
    content: 'React is a JavaScript library for building user interfaces. This is the official React repository with source code and documentation.',
    domain: 'github.com',
    visitTime: Date.now() - *********,
    lastUpdated: Date.now() - *********,
    accessCount: 7,
    language: 'en'
  }
];

async function runTests() {
  console.log('🧪 Testing Advanced Search Syntax\n');
  
  const searchEngine = new HybridSearchEngine();
  
  // Initialize search engine
  console.log('Initializing search engine...');
  await searchEngine.initialize(testPages);
  console.log('✅ Search engine initialized\n');
  
  // Test 1: Exact phrase search
  console.log('1️⃣  Testing exact phrase search with quotes');
  console.log('Query: "React Hooks"');
  let results = await searchEngine.search('"React Hooks"');
  console.log(`Found ${results.mergedResults.length} results`);
  if (results.mergedResults.length > 0) {
    console.log('Top results:');
    results.mergedResults.slice(0, 3).forEach((r, i) => {
      console.log(`  ${i + 1}. ${r.title}`);
    });
  }
  console.log('');
  
  // Test 2: Exclude terms
  console.log('2️⃣  Testing exclude terms with minus sign');
  console.log('Query: React -Vue');
  results = await searchEngine.search('React -Vue');
  console.log(`Found ${results.mergedResults.length} results`);
  const hasVue = results.mergedResults.some(r => 
    r.content.toLowerCase().includes('vue') || r.title.toLowerCase().includes('vue')
  );
  console.log(`Contains Vue pages: ${hasVue ? '❌ Yes' : '✅ No'}`);
  console.log('');
  
  // Test 3: Site filter
  console.log('3️⃣  Testing site filter');
  console.log('Query: site:example.com');
  results = await searchEngine.search('site:example.com');
  console.log(`Found ${results.mergedResults.length} results`);
  const allFromExample = results.mergedResults.every(r => r.domain === 'example.com');
  console.log(`All from example.com: ${allFromExample ? '✅ Yes' : '❌ No'}`);
  console.log('');
  
  // Test 4: Combined syntax
  console.log('4️⃣  Testing combined syntax');
  console.log('Query: "best practices" -Vue');
  results = await searchEngine.search('"best practices" -Vue');
  console.log(`Found ${results.mergedResults.length} results`);
  if (results.mergedResults.length > 0) {
    const firstResult = results.mergedResults[0];
    const hasBestPractices = firstResult.content.toLowerCase().includes('best practices') || 
                           firstResult.title.toLowerCase().includes('best practices');
    const hasVue = firstResult.content.toLowerCase().includes('vue') || 
                  firstResult.title.toLowerCase().includes('vue');
    console.log(`First result has "best practices": ${hasBestPractices ? '✅ Yes' : '❌ No'}`);
    console.log(`First result has "vue": ${hasVue ? '❌ Yes' : '✅ No'}`);
  }
  console.log('');
  
  // Test 5: Check filters are extracted
  console.log('5️⃣  Testing query processing');
  console.log('Query: "React Hooks" -class site:example.com');
  results = await searchEngine.search('"React Hooks" -class site:example.com');
  console.log('Query analysis:');
  console.log(`  Original query: ${results.query}`);
  console.log(`  Cleaned query: ${results.cleanedQuery}`);
  console.log(`  Strategy: ${results.strategy}`);
  console.log('');
  
  console.log('✅ All tests completed!');
}

// Run tests
runTests().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
});