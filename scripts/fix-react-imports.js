#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 获取所有需要修改的文件
const files = [
  'src/options/main.tsx',
  'src/options/components/SearchSettings.tsx',
  'src/options/components/DataBackup.tsx',
  'src/options/components/BlacklistManagement.tsx',
  'src/options/components/SearchConfigPanel.tsx',
  'src/options/components/ConfigInputField.tsx',
  'src/options/components/DomainSelector.tsx',
  'src/options/components/LanguageSettings.tsx',
  'src/options/components/About.tsx',
  'src/options/components/HistoryManagement.tsx',
  'src/options/OptionsApp.tsx',
  'src/popup/components/SearchProgress.tsx',
  'src/popup/components/Filters.tsx',
  'src/popup/components/SkeletonLoader.tsx',
  'src/popup/components/ResultList.tsx',
  'src/popup/components/VirtualResultList.tsx',
  'src/popup/components/SearchBar.tsx',
  'src/popup/components/StatusBar.tsx',
  'src/popup/components/SyntaxHelp.tsx',
  'src/content/MarkdownRenderer.tsx',
  'src/content/ToastNotification.tsx',
  'src/components/Highlight.tsx',
  'src/components/LanguageSelector.tsx',
  'src/i18n/LanguageSelector.tsx',
  'src/App.tsx'
];

// 处理每个文件
files.forEach(filePath => {
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf8');
    
    // 替换 import React 语句
    let newContent = content;
    

    newContent = newContent.replace(/import\s+React\s+from\s+['"]react['"];?\s*/g, '');
    
    // 替换 "import React, { ... } from 'react';" 为 "import { ... } from 'react';"
    newContent = newContent.replace(/import\s+React,\s*{([^}]*)}\s+from\s+['"]react['"];?/g, 'import { $1 } from "react";');
    
    // 如果文件内容有变化，写入文件
    if (newContent !== content) {
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
    } else {
      console.log(`⏭️ No changes needed: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error);
  }
});

console.log('✨ All files processed!');