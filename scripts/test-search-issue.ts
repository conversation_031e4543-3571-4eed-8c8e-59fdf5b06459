/**
 * <PERSON><PERSON><PERSON> to test and analyze the search result merging issue
 * Run with: npx tsx scripts/test-search-issue.ts
 */

import { LunrSearchEngine } from '../src/search/fulltext/LunrSearchEngine';
import { HybridSearchEngine } from '../src/search/hybrid/HybridSearchEngine';
import type { Page } from '../src/models';

async function testSearchIssue() {
  console.log('=== Testing Search Result Merging Issue ===\n');

  const lunrEngine = new LunrSearchEngine();
  const hybridEngine = new HybridSearchEngine();

  // Create test data with multiple pages containing "Claude Code"
  const testPages: Page[] = [
    {
      id: '1',
      url: 'https://claude.ai/code/docs/page1',
      title: 'Getting Started with Claude Code',
      content: '<PERSON> is an AI-powered coding assistant that helps developers write better code faster.',
      domain: 'claude.ai',
      visitTime: Date.now() - 1000 * 60 * 60,
      accessCount: 1,
      createdAt: Date.now() - 1000 * 60 * 60,
      lastModified: Date.now() - 1000 * 60 * 60
    },
    {
      id: '2',
      url: 'https://claude.ai/code/docs/page2',
      title: 'Claude Code Advanced Features',
      content: 'Learn about the advanced features of Claude Code including AI suggestions and code refactoring.',
      domain: 'claude.ai',
      visitTime: Date.now() - 1000 * 60 * 30,
      accessCount: 1,
      createdAt: Date.now() - 1000 * 60 * 30,
      lastModified: Date.now() - 1000 * 60 * 30
    },
    {
      id: '3',
      url: 'https://docs.anthropic.com/claude-code',
      title: 'Claude Code Documentation',
      content: 'Complete documentation for Claude Code. This guide covers everything about using Claude Code effectively.',
      domain: 'docs.anthropic.com',
      visitTime: Date.now() - 1000 * 60 * 15,
      accessCount: 1,
      createdAt: Date.now() - 1000 * 60 * 15,
      lastModified: Date.now() - 1000 * 60 * 15
    },
    {
      id: '4',
      url: 'https://blog.example.com/claude-review',
      title: 'My Experience with Claude',
      content: 'I recently started using Claude for coding and it has been amazing. Claude Code helps me write clean code.',
      domain: 'blog.example.com',
      visitTime: Date.now() - 1000 * 60 * 5,
      accessCount: 1,
      createdAt: Date.now() - 1000 * 60 * 5,
      lastModified: Date.now() - 1000 * 60 * 5
    },
    // Add duplicate URLs to test merging behavior
    {
      id: '5',
      url: 'https://claude.ai/code/docs/page1', // Same URL as id '1'
      title: 'Getting Started with Claude Code (Updated)',
      content: 'Updated: Claude Code is the best AI coding assistant. Use Claude Code for faster development.',
      domain: 'claude.ai',
      visitTime: Date.now() - 1000 * 60 * 2,
      accessCount: 2,
      createdAt: Date.now() - 1000 * 60 * 2,
      lastModified: Date.now() - 1000 * 60 * 2
    }
  ];

  // Build Lunr index
  await lunrEngine.createIndex(testPages);

  // Test different search queries
  console.log('Test Data Summary:');
  console.log(`- Total pages: ${testPages.length}`);
  console.log(`- Unique URLs: ${new Set(testPages.map(p => p.url)).size}`);
  console.log(`- Pages containing "Claude Code": ${testPages.filter(p => 
    p.content.toLowerCase().includes('claude') && p.content.toLowerCase().includes('code')
  ).length}\n`);

  // Test 1: Search for "Claude Code"
  console.log('=== Test 1: Searching for "Claude Code" ===');
  const claudeCodeResults = await lunrEngine.search('Claude Code');
  console.log(`Found ${claudeCodeResults.length} results`);
  claudeCodeResults.forEach((result, idx) => {
    console.log(`  ${idx + 1}. [${result.page.id}] ${result.page.title}`);
    console.log(`     Score: ${result.score.toFixed(3)}, URL: ${result.page.url}`);
    console.log(`     Matched terms: ${result.metadata?.matchedTerms?.join(', ') || 'N/A'}`);
  });

  // Test 2: Search for "claude c"
  console.log('\n=== Test 2: Searching for "claude c" ===');
  const claudeCResults = await lunrEngine.search('claude c');
  console.log(`Found ${claudeCResults.length} results`);
  claudeCResults.forEach((result, idx) => {
    console.log(`  ${idx + 1}. [${result.page.id}] ${result.page.title}`);
    console.log(`     Score: ${result.score.toFixed(3)}, URL: ${result.page.url}`);
    console.log(`     Matched terms: ${result.metadata?.matchedTerms?.join(', ') || 'N/A'}`);
  });

  // Test 3: Test HybridSearchEngine merging
  console.log('\n=== Test 3: HybridSearchEngine Merging ===');
  
  // Simulate string and fulltext results
  const stringResults = testPages.map(page => ({
    id: page.id,
    url: page.url,
    title: page.title,
    content: page.content,
    lastVisitTime: page.visitTime,
    visitCount: page.accessCount || 1,
    stringScore: 0.8,
    fulltextScore: 0,
    combinedScore: 0.8,
    relevanceScore: 0.8,
    domain: page.domain
  }));

  const fulltextResults = claudeCodeResults.map(item => ({
    id: item.page.id,
    url: item.page.url,
    title: item.page.title,
    content: item.page.content || '',
    lastVisitTime: item.page.visitTime,
    visitCount: item.page.accessCount || 1,
    stringScore: 0,
    fulltextScore: item.score,
    combinedScore: item.score,
    relevanceScore: item.score,
    domain: item.page.domain
  }));

  console.log(`String search returned: ${stringResults.length} results`);
  console.log(`Fulltext search returned: ${fulltextResults.length} results`);

  // The mergeSearchResults method is private, so we'll analyze the behavior
  console.log('\n=== Analysis ===');
  console.log('The issue occurs because:');
  console.log('1. Lunr splits "Claude Code" into ["claude", "code"] and searches with OR logic');
  console.log('2. HybridSearchEngine.mergeSearchResults uses URL as the unique key');
  console.log('3. When merging, duplicate URLs are collapsed into a single result');
  console.log('\nThis explains why:');
  console.log('- "Claude Code" might return fewer results (after merging by URL)');
  console.log('- "claude c" might return more results (partial matching creates different scores)');

  // Test with actual hybrid search
  console.log('\n=== Test 4: Actual HybridSearchEngine Search ===');
  const hybridResults = await hybridEngine.search('Claude Code');
  console.log(`Hybrid search returned: ${hybridResults.mergedResults.length} merged results`);
  console.log(`String results: ${hybridResults.stringResults.length}`);
  console.log(`Fulltext results: ${hybridResults.fulltextResults.length}`);
  
  hybridResults.mergedResults.forEach((result, idx) => {
    console.log(`  ${idx + 1}. [${result.id}] ${result.title}`);
    console.log(`     Combined Score: ${result.combinedScore.toFixed(3)}, URL: ${result.url}`);
  });

  // Demonstrate the root cause
  console.log('\n=== Root Cause Demonstration ===');
  const urlMap = new Map<string, number>();
  testPages.forEach(page => {
    urlMap.set(page.url, (urlMap.get(page.url) || 0) + 1);
  });
  
  console.log('URL duplicates in test data:');
  urlMap.forEach((count, url) => {
    if (count > 1) {
      console.log(`  ${url}: ${count} occurrences`);
    }
  });

  console.log('\nConclusion:');
  console.log('The search result count discrepancy is caused by:');
  console.log('1. Lunr\'s query splitting behavior (OR logic for multi-word queries)');
  console.log('2. HybridSearchEngine merging results by URL, losing duplicate entries');
  console.log('3. This is especially problematic for browsing history where the same page');
  console.log('   might be visited multiple times with different content versions');
}

// Run the test
testSearchIssue().catch(console.error);