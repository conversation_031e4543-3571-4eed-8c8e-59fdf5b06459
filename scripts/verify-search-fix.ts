/**
 * Verify that the search result merging fix works correctly
 */

import type { SearchResult } from '../src/search/hybrid/HybridSearchEngine';

// Original mergeSearchResults (using URL as key)
function originalMergeSearchResults(
  stringResults: SearchResult[],
  fulltextResults: SearchResult[]
): SearchResult[] {
  const mergedMap = new Map<string, SearchResult>();
  const stringWeight = 0.4;
  const fulltextWeight = 0.6;
  
  stringResults.forEach(result => {
    mergedMap.set(result.url, {
      ...result,
      combinedScore: result.stringScore * stringWeight
    });
  });
  
  fulltextResults.forEach(result => {
    const existing = mergedMap.get(result.url);
    if (existing) {
      existing.combinedScore = 
        existing.stringScore * stringWeight +
        result.fulltextScore * fulltextWeight;
      existing.relevanceScore = Math.max(
        existing.relevanceScore,
        result.relevanceScore
      );
    } else {
      mergedMap.set(result.url, {
        ...result,
        combinedScore: result.fulltextScore * fulltextWeight
      });
    }
  });
  
  const mergedResults = Array.from(mergedMap.values());
  mergedResults.sort((a, b) => b.combinedScore - a.combinedScore);
  return mergedResults;
}

// Fixed mergeSearchResults (using ID as key)
function fixedMergeSearchResults(
  stringResults: SearchResult[],
  fulltextResults: SearchResult[]
): SearchResult[] {
  const mergedMap = new Map<string, SearchResult>();
  const stringWeight = 0.4;
  const fulltextWeight = 0.6;
  
  stringResults.forEach(result => {
    mergedMap.set(result.id, {
      ...result,
      combinedScore: result.stringScore * stringWeight
    });
  });
  
  fulltextResults.forEach(result => {
    const existing = mergedMap.get(result.id);
    if (existing) {
      existing.combinedScore = 
        existing.stringScore * stringWeight +
        result.fulltextScore * fulltextWeight;
      existing.relevanceScore = Math.max(
        existing.relevanceScore,
        result.relevanceScore
      );
    } else {
      mergedMap.set(result.id, {
        ...result,
        combinedScore: result.fulltextScore * fulltextWeight
      });
    }
  });
  
  const mergedResults = Array.from(mergedMap.values());
  mergedResults.sort((a, b) => {
    const scoreDiff = b.combinedScore - a.combinedScore;
    if (Math.abs(scoreDiff) > 0.001) {
      return scoreDiff;
    }
    return b.lastVisitTime - a.lastVisitTime;
  });
  return mergedResults;
}

// Test data with duplicate URLs
const testResults: SearchResult[] = [
  {
    id: '1',
    url: 'https://claude.ai/docs',
    title: 'Claude Code Documentation (Visit 1)',
    content: 'First visit to Claude Code docs',
    lastVisitTime: Date.now() - 1000 * 60 * 60,
    visitCount: 1,
    stringScore: 0.8,
    fulltextScore: 0.9,
    combinedScore: 0,
    relevanceScore: 0.9
  },
  {
    id: '2',
    url: 'https://claude.ai/docs', // Same URL as id '1'
    title: 'Claude Code Documentation (Visit 2)',
    content: 'Second visit to Claude Code docs with updated content',
    lastVisitTime: Date.now() - 1000 * 60 * 30,
    visitCount: 2,
    stringScore: 0.7,
    fulltextScore: 0.95,
    combinedScore: 0,
    relevanceScore: 0.95
  },
  {
    id: '3',
    url: 'https://anthropic.com/claude-code',
    title: 'Official Claude Code Page',
    content: 'Official information about Claude Code',
    lastVisitTime: Date.now() - 1000 * 60 * 15,
    visitCount: 1,
    stringScore: 0.6,
    fulltextScore: 0.85,
    combinedScore: 0,
    relevanceScore: 0.85
  },
  {
    id: '4',
    url: 'https://claude.ai/docs', // Same URL as id '1' and '2'
    title: 'Claude Code Documentation (Visit 3)',
    content: 'Third visit with even newer Claude Code information',
    lastVisitTime: Date.now() - 1000 * 60 * 5,
    visitCount: 3,
    stringScore: 0.9,
    fulltextScore: 0.98,
    combinedScore: 0,
    relevanceScore: 0.98
  }
];

console.log('=== Search Result Merging Fix Verification ===\n');

console.log('Test Data:');
console.log(`Total results: ${testResults.length}`);
console.log(`Unique URLs: ${new Set(testResults.map(r => r.url)).size}`);
console.log('Results:');
testResults.forEach(r => {
  console.log(`  ID: ${r.id}, URL: ${r.url}`);
  console.log(`  Title: ${r.title}`);
});

// Test original implementation
console.log('\n=== Original Implementation (URL-based merging) ===');
const originalMerged = originalMergeSearchResults(testResults, testResults);
console.log(`Merged results: ${originalMerged.length}`);
originalMerged.forEach((r, idx) => {
  console.log(`  ${idx + 1}. ID: ${r.id}, Title: ${r.title}`);
  console.log(`     Combined Score: ${r.combinedScore.toFixed(3)}`);
});

// Test fixed implementation
console.log('\n=== Fixed Implementation (ID-based merging) ===');
const fixedMerged = fixedMergeSearchResults(testResults, testResults);
console.log(`Merged results: ${fixedMerged.length}`);
fixedMerged.forEach((r, idx) => {
  console.log(`  ${idx + 1}. ID: ${r.id}, Title: ${r.title}`);
  console.log(`     Combined Score: ${r.combinedScore.toFixed(3)}`);
});

console.log('\n=== Analysis ===');
console.log(`Original implementation lost ${testResults.length - originalMerged.length} results due to URL deduplication`);
console.log(`Fixed implementation preserves all ${fixedMerged.length} results`);

console.log('\n=== Benefits of the Fix ===');
console.log('1. All history entries are preserved (no data loss)');
console.log('2. Users can see all visits to the same page');
console.log('3. Search results accurately reflect browsing history');
console.log('4. Each visit can have different relevance scores');
console.log('5. Better for understanding browsing patterns over time');