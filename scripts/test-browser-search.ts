/**
 * Test browser search with actual service flow
 */

import { dbService } from '../src/services/db.service';
import { searchService } from '../src/services/search.service';
import { settingsService } from '../src/services/settings.service';
import type { Page } from '../src/models';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Mock Chrome API
(global as any).chrome = {
  storage: {
    local: {
      get: jest.fn().mockImplementation((key, callback) => {
        callback?.({});
        return Promise.resolve({});
      }),
      set: jest.fn().mockImplementation((data, callback) => {
        callback?.();
        return Promise.resolve();
      }),
    },
  },
  runtime: {
    sendMessage: jest.fn().mockResolvedValue({}),
    onMessage: {
      addListener: jest.fn(),
    },
  },
  tabs: {
    query: jest.fn().mockResolvedValue([]),
  },
};

// Mock IndexedDB
class MockIDBObjectStore {
  constructor(public name: string) {}
  createIndex = jest.fn();
  index = jest.fn().mockReturnThis();
  get = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
  add = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
  put = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
  delete = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
  clear = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
  getAll = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
  getAllKeys = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
  count = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
  openCursor = jest.fn().mockReturnValue({
    onsuccess: jest.fn(),
    onerror: jest.fn(),
  });
}

class MockIDBTransaction {
  constructor(public storeNames: string[]) {}
  objectStore = jest.fn((name) => new MockIDBObjectStore(name));
  oncomplete: any = null;
  onerror: any = null;
  onabort: any = null;
}

class MockIDBDatabase {
  constructor() {
    this.objectStoreNames = ['pages', 'settings', 'searchIndexes'] as any;
  }
  objectStoreNames: any;
  transaction = jest.fn((storeNames, mode) => new MockIDBTransaction(storeNames));
  createObjectStore = jest.fn((name) => new MockIDBObjectStore(name));
  close = jest.fn();
}

const mockIndexedDB = {
  open: jest.fn().mockImplementation(() => {
    const request: any = {
      result: new MockIDBDatabase(),
      onsuccess: null,
      onerror: null,
      onupgradeneeded: null,
    };
    setTimeout(() => request.onsuccess?.({ target: request }), 0);
    return request;
  }),
  deleteDatabase: jest.fn(),
};

(global as any).indexedDB = mockIndexedDB;
(global as any).IDBKeyRange = {
  bound: jest.fn(),
  only: jest.fn(),
  lowerBound: jest.fn(),
  upperBound: jest.fn(),
};

async function testBrowserSearch() {
  console.log('=== Testing Browser Search Flow ===\n');

  try {
    // Load backup data
    const backupPath = path.join(__dirname, '../tests/materials/recall-backup-2025-06-26.json');
    const backupContent = fs.readFileSync(backupPath, 'utf-8');
    const backup = JSON.parse(backupContent);
    const pages: Page[] = backup.pages;
    
    console.log(`Loaded ${pages.length} pages from backup`);

    // Check what pages contain "claude code"
    console.log('\n=== Pages containing "claude code" ===');
    const claudeCodePages = pages.filter(page => {
      const text = `${page.title || ''} ${page.content || ''}`.toLowerCase();
      return text.includes('claude code');
    });
    
    claudeCodePages.forEach((page, idx) => {
      console.log(`\n${idx + 1}. ${page.title}`);
      console.log(`   URL: ${page.url}`);
      console.log(`   Domain: ${page.domain}`);
    });

    // Initialize search service
    console.log('\n=== Initializing Search Service ===');
    try {
      await searchService.initialize();
      console.log('Search service initialized');
    } catch (error) {
      console.log('Search service initialization failed (expected in test env):', error.message);
    }

    // Test search service directly
    console.log('\n=== Testing Search Service ===');
    try {
      const results = await searchService.search('Claude Code');
      console.log(`Search service returned ${results.length} results`);
      
      results.forEach((result, idx) => {
        console.log(`\n${idx + 1}. ${result.page.title}`);
        console.log(`   URL: ${result.page.url}`);
        console.log(`   Score: ${result.score?.toFixed(3)}`);
      });
    } catch (error) {
      console.log('Search service search failed:', error.message);
    }

    // Check if there are duplicate URLs that might be filtered
    console.log('\n=== Checking for Duplicate URLs ===');
    const urlMap = new Map<string, Page[]>();
    pages.forEach(page => {
      const url = page.url;
      if (!urlMap.has(url)) {
        urlMap.set(url, []);
      }
      urlMap.get(url)!.push(page);
    });

    let hasDuplicates = false;
    urlMap.forEach((pages, url) => {
      if (pages.length > 1) {
        hasDuplicates = true;
        console.log(`\nDuplicate URL: ${url}`);
        pages.forEach(page => {
          console.log(`  - ID: ${page.id}, Title: ${page.title}`);
        });
      }
    });

    if (!hasDuplicates) {
      console.log('No duplicate URLs found in the backup data');
    }

    // Check the specific zhihu page
    console.log('\n=== Checking Zhihu Page ===');
    const zhihuPage = pages.find(p => p.url.includes('zhihu.com/question'));
    if (zhihuPage) {
      console.log(`Found: ${zhihuPage.title}`);
      console.log(`URL: ${zhihuPage.url}`);
      const content = zhihuPage.content?.toLowerCase() || '';
      console.log(`Contains "claude code": ${content.includes('claude code')}`);
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Add jest functions if not available
if (typeof jest === 'undefined') {
  (global as any).jest = {
    fn: (impl?: any) => {
      const fn = impl || (() => {});
      fn.mockImplementation = (newImpl: any) => newImpl;
      fn.mockReturnValue = (value: any) => () => value;
      fn.mockResolvedValue = (value: any) => () => Promise.resolve(value);
      fn.mockReturnThis = () => fn;
      return fn;
    }
  };
}

// Run test
testBrowserSearch().catch(console.error);