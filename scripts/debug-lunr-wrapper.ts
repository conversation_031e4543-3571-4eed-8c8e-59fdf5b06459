/**
 * Debug LunrSearchEngine wrapper
 */

import { LunrSearchEngine } from '../src/search/fulltext/LunrSearchEngine';
import type { Page } from '../src/models';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Add debug logging to LunrSearchEngine
class DebugLunrSearchEngine extends LunrSearchEngine {
  async search(query: string, options: any = {}): Promise<any[]> {
    console.log(`\n[DEBUG] Search called with query: "${query}"`);
    console.log(`[DEBUG] Index exists: ${!!(this as any).index}`);
    console.log(`[DEBUG] Documents map size: ${(this as any).documents?.size || 0}`);
    
    if ((this as any).documents?.size > 0) {
      console.log('[DEBUG] First 3 document IDs in map:');
      let count = 0;
      for (const [id, page] of (this as any).documents.entries()) {
        console.log(`  - ${id}: ${page.title}`);
        if (++count >= 3) break;
      }
    }
    
    // Call parent search
    const results = await super.search(query, options);
    console.log(`[DEBUG] Search returned ${results.length} results`);
    
    return results;
  }
}

async function debugLunrWrapper() {
  console.log('=== Debug LunrSearchEngine Wrapper ===\n');

  try {
    // Load backup data
    const backupPath = path.join(__dirname, '../tests/materials/recall-backup-2025-06-26.json');
    const backupContent = fs.readFileSync(backupPath, 'utf-8');
    const backup = JSON.parse(backupContent);
    const pages: Page[] = backup.pages;
    
    console.log(`Loaded ${pages.length} pages`);

    // Create debug engine
    const engine = new DebugLunrSearchEngine();
    
    console.log('\nBuilding index...');
    await engine.createIndex(pages);
    
    const stats = engine.getStats();
    console.log(`\nEngine stats after createIndex:`);
    console.log(`  - documentCount: ${stats.documentCount}`);
    console.log(`  - hasIndex: ${stats.hasIndex}`);
    console.log(`  - isBuilding: ${stats.isBuilding}`);

    // Test searches
    console.log('\n=== Testing Searches ===');
    
    console.log('\n1. Search for "Claude Code"');
    const results1 = await engine.search('Claude Code');
    console.log(`Results: ${results1.length}`);
    if (results1.length > 0) {
      results1.slice(0, 3).forEach((r, idx) => {
        console.log(`  ${idx + 1}. ${r.page.title} (score: ${r.score.toFixed(3)})`);
      });
    }

    console.log('\n2. Search for "claude"');
    const results2 = await engine.search('claude');
    console.log(`Results: ${results2.length}`);

    console.log('\n3. Search for "code"');
    const results3 = await engine.search('code');
    console.log(`Results: ${results3.length}`);

    // Check internal state
    console.log('\n=== Internal State Check ===');
    const internalDocs = (engine as any).documents;
    console.log(`Documents map size: ${internalDocs?.size || 0}`);
    
    if (internalDocs && internalDocs.size > 0) {
      console.log('\nDocument IDs in map:');
      for (const [id, page] of internalDocs.entries()) {
        console.log(`  ${id}: ${page.title}`);
      }
    }

    // Test with a known page ID
    if (pages.length > 0) {
      const testPage = pages.find(p => p.content?.toLowerCase().includes('claude code'));
      if (testPage) {
        console.log(`\n=== Testing with known page ===`);
        console.log(`Page ID: ${testPage.id}`);
        console.log(`Page title: ${testPage.title}`);
        console.log(`Is in documents map: ${internalDocs?.has(testPage.id)}`);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run debug
debugLunrWrapper().catch(console.error);