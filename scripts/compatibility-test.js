/**
 * 兼容性测试脚本
 * 
 * 用于测试AJAX检测功能在主要网站上的兼容性
 */

const CompatibilityTest = {
  // 测试网站列表
  testSites: {
    chinese: [
      { name: '百度贴吧', url: 'https://tieba.baidu.com', type: 'forum' },
      { name: '知乎', url: 'https://www.zhihu.com', type: 'forum' },
      { name: '微博', url: 'https://weibo.com', type: 'social' },
      { name: 'B站', url: 'https://www.bilibili.com', type: 'video' },
      { name: '掘金', url: 'https://juejin.cn', type: 'tech' },
      { name: 'V2EX', url: 'https://www.v2ex.com', type: 'forum' },
      { name: '简书', url: 'https://www.jianshu.com', type: 'blog' },
      { name: 'CSDN', url: 'https://www.csdn.net', type: 'tech' },
      { name: '豆瓣', url: 'https://www.douban.com', type: 'social' },
      { name: '网易云音乐', url: 'https://music.163.com', type: 'media' }
    ],
    english: [
      { name: 'Reddit', url: 'https://www.reddit.com', type: 'forum' },
      { name: 'Twitter/X', url: 'https://twitter.com', type: 'social' },
      { name: 'Stack Overflow', url: 'https://stackoverflow.com', type: 'forum' },
      { name: 'GitHub', url: 'https://github.com', type: 'tech' },
      { name: 'Medium', url: 'https://medium.com', type: 'blog' },
      { name: 'Quora', url: 'https://www.quora.com', type: 'forum' },
      { name: 'LinkedIn', url: 'https://www.linkedin.com', type: 'social' },
      { name: 'YouTube', url: 'https://www.youtube.com', type: 'video' },
      { name: 'Wikipedia', url: 'https://en.wikipedia.org', type: 'wiki' },
      { name: 'Amazon', url: 'https://www.amazon.com', type: 'ecommerce' }
    ]
  },
  
  results: [],
  currentTest: null,
  
  /**
   * 运行当前页面的兼容性测试
   */
  async testCurrentSite() {
    console.log('🧪 开始当前网站兼容性测试...\n');
    
    const siteInfo = this.identifyCurrentSite();
    console.log(`当前网站: ${siteInfo.name || '未知'} (${window.location.hostname})`);
    
    const result = await this.runSiteTest(siteInfo);
    this.displayResult(result);
    
    return result;
  },
  
  /**
   * 识别当前网站
   */
  identifyCurrentSite() {
    const hostname = window.location.hostname;
    const allSites = [...this.testSites.chinese, ...this.testSites.english];
    
    const matched = allSites.find(site => {
      const siteHost = new URL(site.url).hostname;
      return hostname.includes(siteHost) || siteHost.includes(hostname);
    });
    
    return matched || {
      name: hostname,
      url: window.location.origin,
      type: 'unknown'
    };
  },
  
  /**
   * 运行单个网站测试
   */
  async runSiteTest(siteInfo) {
    const startTime = Date.now();
    const result = {
      site: siteInfo,
      timestamp: new Date().toISOString(),
      tests: {},
      issues: [],
      overall: 'PENDING'
    };
    
    // 1. 测试扩展加载
    result.tests.extensionLoaded = this.testExtensionLoaded();
    
    // 2. 测试AJAX监听器
    result.tests.ajaxMonitor = await this.testAjaxMonitor();
    
    // 3. 测试内容监测器
    result.tests.contentWatcher = await this.testContentWatcher();
    
    // 4. 测试论坛检测（如果适用）
    if (['forum', 'social'].includes(siteInfo.type)) {
      result.tests.forumDetection = await this.testForumDetection();
    }
    
    // 5. 测试动态内容检测
    result.tests.dynamicContent = await this.testDynamicContent();
    
    // 6. 测试性能影响
    result.tests.performance = await this.testPerformanceImpact();
    
    // 7. 测试错误和异常
    result.tests.errors = await this.testErrorHandling();
    
    // 计算总体结果
    result.duration = Date.now() - startTime;
    result.overall = this.calculateOverallResult(result.tests);
    
    // 保存结果
    this.results.push(result);
    
    return result;
  },
  
  /**
   * 测试扩展是否正确加载
   */
  testExtensionLoaded() {
    const checks = {
      recallDebug: !!window.RecallDebug,
      contentScript: window.RecallDebug?.getStatus?.().contentScriptLoaded || false,
      debugFunctions: !!(window.RecallDebug?.checkAjaxMonitor && 
                        window.RecallDebug?.checkEnhancedWatcher &&
                        window.RecallDebug?.checkSmartDebouncer)
    };
    
    return {
      passed: Object.values(checks).every(v => v === true),
      details: checks
    };
  },
  
  /**
   * 测试AJAX监听器
   */
  async testAjaxMonitor() {
    if (!window.RecallDebug?.checkAjaxMonitor) {
      return { passed: false, error: 'checkAjaxMonitor not available' };
    }
    
    try {
      const status = window.RecallDebug.checkAjaxMonitor();
      const startStats = status.stats || {};
      
      // 发送测试请求
      await fetch('/test-ajax-monitor-' + Date.now()).catch(() => {});
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const endStatus = window.RecallDebug.checkAjaxMonitor();
      const endStats = endStatus.stats || {};
      
      return {
        passed: status.isActive && !status.error,
        isActive: status.isActive,
        requestsCaptured: (endStats.totalRequests || 0) > (startStats.totalRequests || 0),
        details: endStatus
      };
    } catch (error) {
      return { passed: false, error: error.message };
    }
  },
  
  /**
   * 测试内容监测器
   */
  async testContentWatcher() {
    if (!window.RecallDebug?.checkEnhancedWatcher) {
      return { passed: false, error: 'checkEnhancedWatcher not available' };
    }
    
    try {
      const status = window.RecallDebug.checkEnhancedWatcher();
      const startStats = status.stats || {};
      
      // 创建测试DOM变化
      const testDiv = document.createElement('div');
      testDiv.className = 'recall-test-content';
      testDiv.innerHTML = '<p>兼容性测试内容 ' + Date.now() + '</p>';
      document.body.appendChild(testDiv);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const endStatus = window.RecallDebug.checkEnhancedWatcher();
      const endStats = endStatus.stats || {};
      
      // 清理
      document.body.removeChild(testDiv);
      
      return {
        passed: status.isActive && !status.error,
        isActive: status.isActive,
        changesDetected: (endStats.totalChanges || 0) > (startStats.totalChanges || 0),
        details: endStatus
      };
    } catch (error) {
      return { passed: false, error: error.message };
    }
  },
  
  /**
   * 测试论坛检测
   */
  async testForumDetection() {
    if (!window.RecallDebug?.checkForumDetection) {
      return { passed: false, error: 'checkForumDetection not available' };
    }
    
    try {
      const detection = window.RecallDebug.checkForumDetection();
      const genericAnalysis = window.RecallDebug.analyzeGenericForum?.() || {};
      
      return {
        passed: true,
        detected: detection.detected,
        platform: detection.platform,
        confidence: detection.confidence,
        isGenericForum: genericAnalysis.isForumPage,
        forumType: genericAnalysis.forumType,
        details: { detection, genericAnalysis }
      };
    } catch (error) {
      return { passed: false, error: error.message };
    }
  },
  
  /**
   * 测试动态内容检测
   */
  async testDynamicContent() {
    try {
      // 检查页面是否有动态内容特征
      const indicators = {
        hasInfiniteScroll: !!document.querySelector('[class*="infinite"], [class*="scroll"], [class*="load-more"]'),
        hasLazyLoad: !!document.querySelector('[loading="lazy"], [data-lazy]'),
        hasPagination: !!document.querySelector('[class*="pag"], [class*="page"], .pagination'),
        hasComments: !!document.querySelector('[class*="comment"], [class*="reply"], [class*="discussion"]'),
        hasAjaxContent: false
      };
      
      // 监测AJAX活动
      if (window.RecallDebug?.checkAjaxMonitor) {
        const status = window.RecallDebug.checkAjaxMonitor();
        indicators.hasAjaxContent = (status.stats?.totalRequests || 0) > 0;
      }
      
      return {
        passed: true,
        hasDynamicContent: Object.values(indicators).some(v => v === true),
        indicators
      };
    } catch (error) {
      return { passed: false, error: error.message };
    }
  },
  
  /**
   * 测试性能影响
   */
  async testPerformanceImpact() {
    try {
      const measurements = {
        memoryUsage: null,
        jsHeapSize: null,
        documentNodes: document.getElementsByTagName('*').length
      };
      
      if (performance.memory) {
        measurements.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
        measurements.jsHeapSize = Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024);
      }
      
      // 简单的响应性测试
      const start = performance.now();
      document.body.style.display = 'none';
      document.body.offsetHeight; // 强制重排
      document.body.style.display = '';
      const repaintTime = performance.now() - start;
      
      measurements.repaintTime = repaintTime.toFixed(2);
      
      return {
        passed: repaintTime < 100, // 重排应该在100ms内完成
        measurements
      };
    } catch (error) {
      return { passed: false, error: error.message };
    }
  },
  
  /**
   * 测试错误处理
   */
  async testErrorHandling() {
    const errors = [];
    
    // 设置错误监听器
    const errorHandler = (event) => {
      if (event.message && event.message.includes('Recall')) {
        errors.push({
          message: event.message,
          source: event.filename,
          line: event.lineno
        });
      }
    };
    
    window.addEventListener('error', errorHandler);
    
    try {
      // 尝试一些可能触发错误的操作
      if (window.RecallDebug) {
        // 测试各种调试功能
        await window.RecallDebug.simulateAjaxRequest?.().catch(() => {});
        await window.RecallDebug.testEnhancedContentDetection?.().catch(() => {});
      }
      
      // 等待可能的异步错误
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } finally {
      window.removeEventListener('error', errorHandler);
    }
    
    return {
      passed: errors.length === 0,
      errorCount: errors.length,
      errors: errors.slice(0, 5) // 最多显示5个错误
    };
  },
  
  /**
   * 计算总体结果
   */
  calculateOverallResult(tests) {
    const results = Object.values(tests).map(test => test.passed);
    const passedCount = results.filter(r => r === true).length;
    const totalCount = results.length;
    const passRate = passedCount / totalCount;
    
    if (passRate === 1) return 'PERFECT';
    if (passRate >= 0.8) return 'GOOD';
    if (passRate >= 0.6) return 'ACCEPTABLE';
    if (passRate >= 0.4) return 'ISSUES';
    return 'FAILED';
  },
  
  /**
   * 显示测试结果
   */
  displayResult(result) {
    console.log('\n' + '='.repeat(60));
    console.log(`📊 兼容性测试结果 - ${result.site.name}`);
    console.log('='.repeat(60));
    
    // 显示各项测试结果
    Object.entries(result.tests).forEach(([testName, testResult]) => {
      const status = testResult.passed ? '✅' : '❌';
      console.log(`${status} ${testName}:`, testResult);
    });
    
    // 显示问题
    if (result.issues.length > 0) {
      console.log('\n⚠️ 发现的问题:');
      result.issues.forEach(issue => console.log(`  - ${issue}`));
    }
    
    // 总结
    console.log('\n' + '-'.repeat(60));
    console.log(`总体评估: ${result.overall}`);
    console.log(`测试耗时: ${result.duration}ms`);
    console.log('='.repeat(60));
    
    return result;
  },
  
  /**
   * 生成兼容性报告
   */
  generateReport() {
    const report = {
      generatedAt: new Date().toISOString(),
      totalSites: this.results.length,
      results: this.results,
      summary: {
        perfect: this.results.filter(r => r.overall === 'PERFECT').length,
        good: this.results.filter(r => r.overall === 'GOOD').length,
        acceptable: this.results.filter(r => r.overall === 'ACCEPTABLE').length,
        issues: this.results.filter(r => r.overall === 'ISSUES').length,
        failed: this.results.filter(r => r.overall === 'FAILED').length
      },
      recommendations: []
    };
    
    // 生成建议
    if (report.summary.failed > 0) {
      report.recommendations.push('严重兼容性问题需要立即修复');
    }
    if (report.summary.issues > 2) {
      report.recommendations.push('多个网站存在兼容性问题，建议优化');
    }
    
    // 分析常见问题
    const commonIssues = {};
    this.results.forEach(result => {
      Object.entries(result.tests).forEach(([testName, testResult]) => {
        if (!testResult.passed) {
          commonIssues[testName] = (commonIssues[testName] || 0) + 1;
        }
      });
    });
    
    report.commonIssues = commonIssues;
    
    return report;
  },
  
  /**
   * 导出测试指南
   */
  exportTestGuide() {
    const guide = `
# Recall扩展兼容性测试指南

## 测试步骤

1. **安装并启用Recall扩展**
2. **打开目标网站**
3. **在Console中运行**: \`CompatibilityTest.testCurrentSite()\`
4. **记录测试结果**

## 测试网站清单

### 中文网站
${this.testSites.chinese.map(site => `- [ ] ${site.name} (${site.url})`).join('\n')}

### 英文网站
${this.testSites.english.map(site => `- [ ] ${site.name} (${site.url})`).join('\n')}

## 测试要点

1. **扩展加载**: 确认RecallDebug可用
2. **AJAX监听**: 验证请求拦截功能
3. **内容检测**: 确认DOM变化监测
4. **论坛适配**: 检查论坛平台识别
5. **性能影响**: 监控内存和响应性
6. **错误处理**: 检查控制台错误

## 问题记录模板

**网站**: [网站名称]
**测试结果**: [PERFECT/GOOD/ACCEPTABLE/ISSUES/FAILED]
**问题描述**: 
- 问题1: [描述]
- 问题2: [描述]
**截图**: [如有必要]
`;
    
    return guide;
  }
};

// 导出到全局
if (typeof window !== 'undefined') {
  window.CompatibilityTest = CompatibilityTest;
  console.log('兼容性测试工具已加载');
  console.log('使用方法:');
  console.log('  - CompatibilityTest.testCurrentSite() - 测试当前网站');
  console.log('  - CompatibilityTest.generateReport() - 生成测试报告');
  console.log('  - CompatibilityTest.exportTestGuide() - 导出测试指南');
}