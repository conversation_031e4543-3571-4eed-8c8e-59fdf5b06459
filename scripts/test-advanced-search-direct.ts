/**
 * Direct test of advanced search syntax functionality
 */

import { QueryProcessor } from '../src/search/QueryProcessor';
import { QueryAnalyzer } from '../src/search/hybrid/QueryAnalyzer';

console.log('🧪 Testing Advanced Search Syntax Processing\n');

// Test Query Processor
const processor = new QueryProcessor();
const analyzer = new QueryAnalyzer();

// Test cases
const testQueries = [
  '"React Hooks"',
  'React -Vue',
  'site:example.com',
  '"best practices" -Vue',
  '"React Hooks" -class site:example.com',
  'JavaScript "best practices" -jQuery site:developer.mozilla.org'
];

console.log('📋 Testing Query Processor\n');

testQueries.forEach((query, index) => {
  console.log(`Test ${index + 1}: "${query}"`);
  
  const processed = processor.process(query);
  
  console.log('  Processed:');
  console.log(`    Clean text: "${processed.cleanText}"`);
  console.log(`    Keywords: [${processed.keywords.join(', ')}]`);
  
  if (processed.filters) {
    console.log('  Filters:');
    if (processed.filters.exactPhrases) {
      console.log(`    Exact phrases: [${processed.filters.exactPhrases.map(p => `"${p}"`).join(', ')}]`);
    }
    if (processed.filters.exclude) {
      console.log(`    Exclude terms: [${processed.filters.exclude.join(', ')}]`);
    }
    if (processed.filters.site) {
      console.log(`    Site filter: ${processed.filters.site}`);
    }
  }
  
  console.log('');
});

console.log('📊 Testing Query Analyzer\n');

testQueries.forEach((query, index) => {
  console.log(`Test ${index + 1}: "${query}"`);
  
  const analysis = analyzer.analyzeQuery(query);
  
  console.log('  Analysis:');
  console.log(`    Cleaned query: "${analysis.cleanedQuery}"`);
  console.log(`    Query type: ${analysis.type}`);
  console.log(`    Strategy: ${analysis.strategy}`);
  console.log(`    Confidence: ${(analysis.confidence * 100).toFixed(0)}%`);
  
  if (analysis.filters) {
    console.log('  Extracted filters:');
    if (analysis.filters.exactPhrases) {
      console.log(`    Exact phrases: [${analysis.filters.exactPhrases.map(p => `"${p}"`).join(', ')}]`);
    }
    if (analysis.filters.exclude) {
      console.log(`    Exclude terms: [${analysis.filters.exclude.join(', ')}]`);
    }
    if (analysis.filters.site) {
      console.log(`    Site filter: ${analysis.filters.site}`);
    }
  }
  
  console.log('');
});

console.log('✅ Test Summary\n');

// Verify key functionality
const testResults = {
  exactPhrases: false,
  excludeTerms: false,
  siteFilter: false,
  combined: false
};

// Test exact phrases
const exactPhraseTest = processor.process('"React Hooks"');
if (exactPhraseTest.filters?.exactPhrases?.includes('React Hooks')) {
  testResults.exactPhrases = true;
  console.log('✅ Exact phrase extraction works');
} else {
  console.log('❌ Exact phrase extraction failed');
}

// Test exclude terms
const excludeTest = processor.process('React -Vue');
if (excludeTest.filters?.exclude?.includes('Vue')) {
  testResults.excludeTerms = true;
  console.log('✅ Exclude term extraction works');
} else {
  console.log('❌ Exclude term extraction failed');
}

// Test site filter
const siteTest = processor.process('site:example.com');
if (siteTest.filters?.site === 'example.com') {
  testResults.siteFilter = true;
  console.log('✅ Site filter extraction works');
} else {
  console.log('❌ Site filter extraction failed');
}

// Test combined syntax
const combinedTest = processor.process('"React Hooks" -class site:example.com');
if (combinedTest.filters?.exactPhrases?.includes('React Hooks') &&
    combinedTest.filters?.exclude?.includes('class') &&
    combinedTest.filters?.site === 'example.com') {
  testResults.combined = true;
  console.log('✅ Combined syntax extraction works');
} else {
  console.log('❌ Combined syntax extraction failed');
}

console.log('\n📈 Overall Results:');
const passed = Object.values(testResults).filter(r => r).length;
const total = Object.values(testResults).length;
console.log(`${passed}/${total} tests passed (${(passed/total * 100).toFixed(0)}%)`);

if (passed === total) {
  console.log('\n🎉 All advanced search syntax tests passed!');
} else {
  console.log('\n⚠️  Some tests failed. Check the output above for details.');
  process.exit(1);
}