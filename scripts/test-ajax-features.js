#!/usr/bin/env node

/**
 * AJAX功能自动化测试脚本
 * 
 * 用于验证AJAX拦截、内容检测和论坛适配功能
 * 
 * 使用方法:
 * 1. 确保扩展已加载
 * 2. 在浏览器中打开测试页面
 * 3. 在Console中运行此脚本的内容
 */

const AjaxFeatureTests = {
  results: [],
  
  // 测试运行器
  async runAllTests() {
    console.log('🚀 开始AJAX功能测试...\n');
    
    // 检查环境
    if (!this.checkEnvironment()) {
      return;
    }
    
    // 运行测试组
    await this.testAjaxInterception();
    await this.testContentDetection();
    await this.testForumAdaptation();
    await this.testPerformance();
    
    // 生成报告
    this.generateReport();
  },
  
  // 环境检查
  checkEnvironment() {
    console.log('📋 检查测试环境...');
    
    const checks = [
      {
        name: 'RecallDebug可用性',
        test: () => window.RecallDebug !== undefined,
        critical: true
      },
      {
        name: 'Content Script加载',
        test: () => window.RecallDebug && window.RecallDebug.getStatus().contentScriptLoaded,
        critical: true
      },
      {
        name: 'AJAX监听器',
        test: () => {
          const status = window.RecallDebug.checkAjaxMonitor();
          return !status.error && status.isActive;
        },
        critical: false
      }
    ];
    
    let allPassed = true;
    checks.forEach(check => {
      const passed = check.test();
      console.log(`${passed ? '✅' : '❌'} ${check.name}`);
      if (!passed && check.critical) {
        allPassed = false;
      }
      this.results.push({
        category: '环境检查',
        test: check.name,
        passed,
        critical: check.critical
      });
    });
    
    return allPassed;
  },
  
  // 测试AJAX拦截
  async testAjaxInterception() {
    console.log('\n📡 测试AJAX拦截功能...');
    
    // 测试XHR拦截
    await this.testCase('XHR GET请求拦截', async () => {
      const startStats = window.RecallDebug.checkAjaxMonitor().stats;
      
      // 发送XHR请求
      const xhr = new XMLHttpRequest();
      xhr.open('GET', '/test/xhr');
      xhr.send();
      
      // 等待处理
      await this.sleep(100);
      
      const endStats = window.RecallDebug.checkAjaxMonitor().stats;
      return endStats.xhrRequests > startStats.xhrRequests;
    });
    
    // 测试Fetch拦截
    await this.testCase('Fetch请求拦截', async () => {
      const startStats = window.RecallDebug.checkAjaxMonitor().stats;
      
      // 发送Fetch请求
      try {
        await fetch('/test/fetch');
      } catch (e) {
        // 忽略网络错误
      }
      
      // 等待处理
      await this.sleep(100);
      
      const endStats = window.RecallDebug.checkAjaxMonitor().stats;
      return endStats.fetchRequests > startStats.fetchRequests;
    });
    
    // 测试请求过滤
    await this.testCase('静态资源过滤', async () => {
      const startStats = window.RecallDebug.checkAjaxMonitor().stats;
      
      // 请求应该被忽略的资源
      try {
        await fetch('/test/script.js');
        await fetch('/test/style.css');
        await fetch('/test/image.png');
      } catch (e) {
        // 忽略错误
      }
      
      await this.sleep(100);
      
      const endStats = window.RecallDebug.checkAjaxMonitor().stats;
      // 这些请求应该被过滤掉
      return endStats.relevantRequests === startStats.relevantRequests;
    });
  },
  
  // 测试内容检测
  async testContentDetection() {
    console.log('\n📝 测试内容检测功能...');
    
    // 测试DOM变化检测
    await this.testCase('DOM内容变化检测', async () => {
      const testDiv = document.createElement('div');
      testDiv.id = 'test-content-' + Date.now();
      testDiv.className = 'content';
      testDiv.innerHTML = '<p>这是测试内容，包含足够的文字来触发检测。' + 
                          '内容检测应该能够识别这段新添加的文本。</p>';
      
      document.body.appendChild(testDiv);
      
      // 等待检测
      await this.sleep(500);
      
      // 检查是否触发了内容变化
      const watcherStatus = window.RecallDebug.checkEnhancedWatcher();
      const hasDetected = watcherStatus.stats && watcherStatus.stats.contentChanges > 0;
      
      // 清理
      document.body.removeChild(testDiv);
      
      return hasDetected;
    });
    
    // 测试智能防抖
    await this.testCase('智能防抖机制', async () => {
      const metrics = window.RecallDebug.getDebounceMetrics();
      const startTotal = metrics.totalOperations || 0;
      
      // 快速触发多个变化
      for (let i = 0; i < 5; i++) {
        const div = document.createElement('div');
        div.textContent = 'Quick change ' + i;
        document.body.appendChild(div);
        document.body.removeChild(div);
      }
      
      await this.sleep(100);
      
      const endMetrics = window.RecallDebug.getDebounceMetrics();
      const endTotal = endMetrics.totalOperations || 0;
      
      // 应该有防抖效果，不是每个变化都立即执行
      return endTotal > startTotal && endMetrics.pendingOperations >= 0;
    });
  },
  
  // 测试论坛适配
  async testForumAdaptation() {
    console.log('\n🏛️ 测试论坛适配功能...');
    
    // 测试论坛检测
    await this.testCase('论坛平台检测', () => {
      const detection = window.RecallDebug.checkForumDetection();
      // 在非论坛页面应该返回未检测到
      return detection.detected === false || detection.platform === 'none';
    });
    
    // 测试通用论坛分析
    await this.testCase('通用论坛结构分析', () => {
      const analysis = window.RecallDebug.analyzeGenericForum();
      // 应该能够分析页面结构
      return analysis.isForumPage !== undefined && 
             analysis.confidence >= 0 && 
             analysis.confidence <= 1;
    });
    
    // 模拟论坛内容
    await this.testCase('论坛内容识别', async () => {
      // 创建类似论坛的结构
      const forumContainer = document.createElement('div');
      forumContainer.className = 'comments-section';
      forumContainer.innerHTML = `
        <div class="comment">
          <div class="comment-author">测试用户</div>
          <div class="comment-time">2分钟前</div>
          <div class="comment-content">这是一条测试评论</div>
        </div>
      `;
      
      document.body.appendChild(forumContainer);
      await this.sleep(300);
      
      const analysis = window.RecallDebug.analyzeGenericForum();
      const hasCommentSections = analysis.structure.commentSections > 0;
      
      // 清理
      document.body.removeChild(forumContainer);
      
      return hasCommentSections;
    });
  },
  
  // 性能测试
  async testPerformance() {
    console.log('\n⚡ 测试性能影响...');
    
    // 测试内存使用
    await this.testCase('内存使用检查', () => {
      if (performance.memory) {
        const usedMemoryMB = performance.memory.usedJSHeapSize / 1024 / 1024;
        console.log(`  当前内存使用: ${usedMemoryMB.toFixed(2)} MB`);
        return usedMemoryMB < 150; // 假设合理的内存使用上限
      }
      return true; // 如果无法获取内存信息，跳过测试
    });
    
    // 测试响应时间
    await this.testCase('AJAX拦截延迟', async () => {
      const iterations = 10;
      let totalTime = 0;
      
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        const xhr = new XMLHttpRequest();
        xhr.open('GET', '/test/performance');
        xhr.send();
        
        const end = performance.now();
        totalTime += (end - start);
      }
      
      const avgTime = totalTime / iterations;
      console.log(`  平均拦截延迟: ${avgTime.toFixed(2)} ms`);
      
      return avgTime < 5; // 拦截应该在5ms内完成
    });
  },
  
  // 辅助函数
  async testCase(name, testFn) {
    try {
      const result = await testFn();
      console.log(`${result ? '✅' : '❌'} ${name}`);
      this.results.push({
        category: this.currentCategory || '其他',
        test: name,
        passed: result
      });
      return result;
    } catch (error) {
      console.log(`❌ ${name} - 错误: ${error.message}`);
      this.results.push({
        category: this.currentCategory || '其他',
        test: name,
        passed: false,
        error: error.message
      });
      return false;
    }
  },
  
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  
  // 生成测试报告
  generateReport() {
    console.log('\n📊 测试报告\n' + '='.repeat(50));
    
    const categories = {};
    let totalTests = 0;
    let passedTests = 0;
    
    // 按类别统计
    this.results.forEach(result => {
      if (!categories[result.category]) {
        categories[result.category] = {
          total: 0,
          passed: 0,
          tests: []
        };
      }
      
      categories[result.category].total++;
      categories[result.category].tests.push(result);
      totalTests++;
      
      if (result.passed) {
        categories[result.category].passed++;
        passedTests++;
      }
    });
    
    // 输出各类别结果
    Object.entries(categories).forEach(([category, data]) => {
      const percentage = ((data.passed / data.total) * 100).toFixed(0);
      console.log(`\n${category}: ${data.passed}/${data.total} (${percentage}%)`);
      
      data.tests.forEach(test => {
        const status = test.passed ? '✅' : '❌';
        const error = test.error ? ` - ${test.error}` : '';
        console.log(`  ${status} ${test.test}${error}`);
      });
    });
    
    // 总体结果
    const totalPercentage = ((passedTests / totalTests) * 100).toFixed(0);
    console.log('\n' + '='.repeat(50));
    console.log(`总计: ${passedTests}/${totalTests} 测试通过 (${totalPercentage}%)`);
    
    if (passedTests === totalTests) {
      console.log('\n🎉 所有测试通过！');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查上述错误。');
    }
    
    // 返回结果供进一步处理
    return {
      total: totalTests,
      passed: passedTests,
      percentage: totalPercentage,
      categories,
      results: this.results
    };
  }
};

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
  window.AjaxFeatureTests = AjaxFeatureTests;
  console.log('AJAX功能测试工具已加载');
  console.log('运行测试: AjaxFeatureTests.runAllTests()');
} else {
  // Node.js环境（用于文档）
  console.log('此脚本应在浏览器Console中运行');
}