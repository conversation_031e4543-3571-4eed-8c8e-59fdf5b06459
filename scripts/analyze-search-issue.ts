/**
 * Analysis of the search result merging issue
 * 
 * Problem: When searching for "Claude Code", only one result is returned,
 * but searching for "claude c" returns multiple results.
 */

import type { SearchResult } from '../src/search/hybrid/HybridSearchEngine';

// Simulate the mergeSearchResults method from HybridSearchEngine
function simulateMergeSearchResults(
  stringResults: SearchResult[],
  fulltextResults: SearchResult[]
): SearchResult[] {
  const mergedMap = new Map<string, SearchResult>();
  
  const stringWeight = 0.4;
  const fulltextWeight = 0.6;
  
  // Add string results first
  stringResults.forEach(result => {
    mergedMap.set(result.url, {
      ...result,
      combinedScore: result.stringScore * stringWeight
    });
  });
  
  // Add fulltext results, combining if URL already exists
  fulltextResults.forEach(result => {
    const existing = mergedMap.get(result.url);
    if (existing) {
      // Combine scores for duplicates
      existing.combinedScore = 
        existing.stringScore * stringWeight +
        result.fulltextScore * fulltextWeight;
      existing.relevanceScore = Math.max(
        existing.relevanceScore,
        result.relevanceScore
      );
    } else {
      // New result from fulltext only
      mergedMap.set(result.url, {
        ...result,
        combinedScore: result.fulltextScore * fulltextWeight
      });
    }
  });
  
  // Convert to array and sort by combined score
  const mergedResults = Array.from(mergedMap.values());
  mergedResults.sort((a, b) => b.combinedScore - a.combinedScore);
  
  return mergedResults;
}

// Simulate Lunr's query splitting behavior
function simulateLunrQuerySplitting(query: string): string[] {
  return query.toLowerCase().split(/\s+/).filter(t => t.length > 0);
}

// Test data representing pages from the backup
const testPages = [
  {
    id: '1',
    url: 'https://claude.ai/docs/intro',
    title: 'Introduction to Claude Code',
    content: 'Claude Code is an AI assistant for coding.'
  },
  {
    id: '2',
    url: 'https://claude.ai/docs/features',
    title: 'Claude Code Features',
    content: 'Explore the features of Claude Code including autocomplete.'
  },
  {
    id: '3',
    url: 'https://claude.ai/docs/intro', // Same URL as id '1' - simulating revisit
    title: 'Introduction to Claude Code (Updated)',
    content: 'Updated guide for Claude Code with new examples.'
  },
  {
    id: '4',
    url: 'https://blog.example.com/review',
    title: 'My Review of Claude',
    content: 'I use Claude for coding every day. Claude Code is amazing.'
  },
  {
    id: '5',
    url: 'https://forum.example.com/discussion',
    title: 'Discussion about AI Coding Tools',
    content: 'Comparing GitHub Copilot with Claude Code and other tools.'
  }
];

console.log('=== Search Issue Analysis ===\n');

// Analyze query splitting
console.log('1. Query Splitting Behavior:');
console.log(`   "Claude Code" → [${simulateLunrQuerySplitting('Claude Code').map(t => `"${t}"`).join(', ')}]`);
console.log(`   "claude c" → [${simulateLunrQuerySplitting('claude c').map(t => `"${t}"`).join(', ')}]`);
console.log('   Lunr uses OR logic, so it finds pages containing ANY of these terms.\n');

// Analyze pages that would match
console.log('2. Pages That Would Match:');
console.log('   For "claude" OR "code":');
testPages.forEach(page => {
  const hasClaudeInContent = page.content.toLowerCase().includes('claude');
  const hasCodeInContent = page.content.toLowerCase().includes('code');
  const hasClaudeInTitle = page.title.toLowerCase().includes('claude');
  const hasCodeInTitle = page.title.toLowerCase().includes('code');
  
  const hasClaude = hasClaudeInContent || hasClaudeInTitle;
  const hasCode = hasCodeInContent || hasCodeInTitle;
  
  if (hasClaude || hasCode) {
    console.log(`   - Page ${page.id}: "${page.title}"`);
    console.log(`     URL: ${page.url}`);
    console.log(`     Matches: ${hasClaude ? 'claude' : ''}${hasClaude && hasCode ? ', ' : ''}${hasCode ? 'code' : ''}`);
  }
});

// Simulate search results
const mockStringResults: SearchResult[] = testPages
  .filter(p => p.content.toLowerCase().includes('claude') || p.title.toLowerCase().includes('claude'))
  .map(p => ({
    id: p.id,
    url: p.url,
    title: p.title,
    content: p.content,
    lastVisitTime: Date.now(),
    visitCount: 1,
    stringScore: 0.8,
    fulltextScore: 0,
    combinedScore: 0.8,
    relevanceScore: 0.8
  }));

const mockFulltextResults: SearchResult[] = testPages
  .filter(p => 
    (p.content.toLowerCase().includes('claude') || p.title.toLowerCase().includes('claude')) &&
    (p.content.toLowerCase().includes('code') || p.title.toLowerCase().includes('code'))
  )
  .map(p => ({
    id: p.id,
    url: p.url,
    title: p.title,
    content: p.content,
    lastVisitTime: Date.now(),
    visitCount: 1,
    stringScore: 0,
    fulltextScore: 0.9,
    combinedScore: 0.9,
    relevanceScore: 0.9
  }));

console.log(`\n3. Search Results Before Merging:`);
console.log(`   String search found: ${mockStringResults.length} results`);
console.log(`   Fulltext search found: ${mockFulltextResults.length} results`);

// Show the merging issue
console.log('\n4. The Merging Problem:');
console.log('   Before merging:');
mockFulltextResults.forEach(r => {
  console.log(`   - ID: ${r.id}, URL: ${r.url}`);
});

const mergedResults = simulateMergeSearchResults(mockStringResults, mockFulltextResults);
console.log(`\n   After merging by URL: ${mergedResults.length} results`);
mergedResults.forEach(r => {
  console.log(`   - ID: ${r.id}, URL: ${r.url}, Combined Score: ${r.combinedScore.toFixed(3)}`);
});

// Show URL duplicates
const urlCounts = new Map<string, number>();
testPages.forEach(p => {
  urlCounts.set(p.url, (urlCounts.get(p.url) || 0) + 1);
});

console.log('\n5. URL Duplicates in Test Data:');
urlCounts.forEach((count, url) => {
  if (count > 1) {
    console.log(`   ${url}: ${count} occurrences`);
    const pages = testPages.filter(p => p.url === url);
    pages.forEach(p => {
      console.log(`     - ID ${p.id}: "${p.title}"`);
    });
  }
});

console.log('\n=== Root Cause Summary ===');
console.log('The issue occurs because:');
console.log('1. HybridSearchEngine.mergeSearchResults uses URL as the unique key');
console.log('2. When multiple pages have the same URL (e.g., revisits), only one is kept');
console.log('3. This reduces the total number of results returned');
console.log('\nFor the query "Claude Code" vs "claude c":');
console.log('- Both queries match similar pages initially');
console.log('- The difference might be in scoring or how partial matches are handled');
console.log('- But the main issue is the URL-based deduplication during merging');

console.log('\n=== Proposed Solutions ===');
console.log('1. Use page ID instead of URL for merging (preserves all history entries)');
console.log('2. Group results by URL but show all entries (like browser history)');
console.log('3. Add an option to disable deduplication for history searches');
console.log('4. Implement exact phrase matching for multi-word queries');