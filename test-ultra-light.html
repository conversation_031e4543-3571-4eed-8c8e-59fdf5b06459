<!DOCTYPE html>
<html>
<head>
    <title>Ultra-Light Mode Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🚀 Recall Ultra-Light Mode Test Page</h1>
    
    <div class="section">
        <h2>📊 Test Configuration</h2>
        <p>This page tests the ultra-light performance optimizations:</p>
        <ul>
            <li>✅ AJAX监控被禁用</li>
            <li>✅ 增强内容监听器被禁用</li>
            <li>✅ MutationObserver使用轻量配置（subtree: false）</li>
            <li>✅ 定期检查间隔增加到30秒</li>
            <li>✅ 滚动检查间隔增加到3秒</li>
        </ul>
    </div>

    <div class="section">
        <h2>🧪 Dynamic Content Test</h2>
        <p>Testing if content extraction still works with dynamic changes...</p>
        <div id="dynamic-content">
            <p>Original content</p>
        </div>
        <button onclick="addContent()">Add Dynamic Content</button>
        <button onclick="makeAjaxRequest()">Make AJAX Request</button>
    </div>

    <div class="section">
        <h2>📈 Performance Monitoring</h2>
        <div id="performance-results"></div>
    </div>

    <div class="section">
        <h2>🔍 Console Logs</h2>
        <p>Check browser console for Recall logs. You should see:</p>
        <ul>
            <li><code>🚀 Recall: Ultra-light mode enabled for performance optimization</code></li>
            <li>AJAX monitoring disabled: <code>ajaxMonitoring: false</code></li>
            <li>Enhanced watcher disabled: <code>enhancedWatcher: false</code></li>
        </ul>
    </div>

    <script>
        // Test dynamic content addition
        let contentCounter = 1;
        function addContent() {
            const container = document.getElementById('dynamic-content');
            const newElement = document.createElement('p');
            newElement.textContent = `Dynamic content ${++contentCounter} - ${new Date().toLocaleTimeString()}`;
            container.appendChild(newElement);
            
            console.log('🧪 Added dynamic content:', newElement.textContent);
        }

        // Test AJAX requests
        function makeAjaxRequest() {
            fetch('https://jsonplaceholder.typicode.com/posts/1')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('dynamic-content');
                    const newElement = document.createElement('div');
                    newElement.innerHTML = `
                        <strong>AJAX Response:</strong>
                        <p>Title: ${data.title}</p>
                        <p>Body: ${data.body.substring(0, 100)}...</p>
                    `;
                    container.appendChild(newElement);
                    console.log('🌐 AJAX request completed:', data.title);
                })
                .catch(error => {
                    console.error('❌ AJAX request failed:', error);
                });
        }

        // Performance monitoring
        function updatePerformance() {
            const performanceDiv = document.getElementById('performance-results');
            const memInfo = performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            } : null;
            
            performanceDiv.innerHTML = `
                <div class="result">
                    <strong>⏱️ Performance Metrics (${new Date().toLocaleTimeString()})</strong><br>
                    ${memInfo ? `Memory Used: ${memInfo.used}MB / ${memInfo.total}MB (Limit: ${memInfo.limit}MB)` : 'Memory info not available'}<br>
                    Page Load Time: ${Math.round(performance.now())}ms
                </div>
            `;
        }

        // Update performance metrics every 5 seconds
        setInterval(updatePerformance, 5000);
        updatePerformance();

        // Add some content periodically to test monitoring
        setInterval(() => {
            if (Math.random() > 0.7) {
                addContent();
            }
        }, 8000);

        console.log('🧪 Test page loaded. Ultra-light mode should be active.');
    </script>
</body>
</html>