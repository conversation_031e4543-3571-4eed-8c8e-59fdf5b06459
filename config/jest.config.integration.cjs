/**
 * Jest Integration Test Configuration
 * Optimized for integration testing with better isolation and reliability
 */

const baseConfig = require('./jest.config.cjs');

module.exports = {
  ...baseConfig,
  
  // Test environment configuration for integration tests
  testEnvironment: 'jsdom',
  
  // Integration test specific patterns
  testMatch: [
    '<rootDir>/src/**/*.integration.test.(ts|tsx|js)',
    '<rootDir>/tests/integration/**/*.test.(ts|tsx|js)',
    '<rootDir>/tests/performance/**/*.test.(ts|tsx|js)',
    '<rootDir>/tests/unit/error-handling-edge-cases.test.ts'
  ],
  
  // Longer timeout for integration tests
  testTimeout: 30000,
  
  // Coverage configuration for integration tests
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{ts,tsx}',
    '!src/**/*.spec.{ts,tsx}',
    '!tests/**/*',
    '!src/vite-env.d.ts',
    '!src/main.tsx'
  ],
  
  coverageThreshold: {
    global: {
      branches: 60,
      functions: 60,
      lines: 60,
      statements: 60
    },
    // Specific thresholds for critical modules
    'src/services/': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    'src/storage/': {
      branches: 75,
      functions: 75,
      lines: 75,
      statements: 75
    },
    'src/search/': {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Enhanced setup for integration tests
  setupFilesAfterEnv: [
    '<rootDir>/tests/setup/setup.ts'
  ],
  
  // Reporters for better CI integration
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: 'reports/test-results',
      outputName: 'integration-results.xml',
      suiteNameTemplate: '{filepath}',
      classNameTemplate: '{classname}',
      titleTemplate: '{title}'
    }],
    ['jest-html-reporter', {
      pageTitle: 'Integration Test Report',
      outputPath: 'reports/test-results/integration-report.html',
      includeFailureMsg: true,
      includeSuiteFailure: true
    }]
  ],
  
  // Retry configuration for flaky tests (Jest doesn't support retry directly)
  // retry: process.env.CI ? 2 : 0,
  
  // Parallel execution control
  maxWorkers: process.env.CI ? 2 : '50%',
  
  // Memory management
  logHeapUsage: true,
  detectOpenHandles: true,
  forceExit: true,
  
  // Test result formatting
  verbose: true,
  silent: false,
  
  // Global configuration (deprecated in favor of transform config)
  // globals: {
  //   'ts-jest': {
  //     tsconfig: 'tsconfig.test.json',
  //     isolatedModules: true
  //   }
  // },
  
  // Module resolution for integration tests
  moduleNameMapper: {
    ...baseConfig.moduleNameMapper,
    '^@test/(.*)$': '<rootDir>/tests/$1'
  },
  
  // Error handling
  errorOnDeprecated: true,
  bail: false, // Don't stop on first failure in integration tests
  
  // Cache configuration
  cache: true,
  cacheDirectory: '<rootDir>/node_modules/.cache/jest',
  
  // Transform configuration
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.test.json'
    }]
  },

  // Fake timers configuration to fix timer warnings
  fakeTimers: {
    enableGlobally: true
  }
};