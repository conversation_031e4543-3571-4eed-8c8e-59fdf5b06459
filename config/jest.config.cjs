/**
 * Jest 测试配置
 */

module.exports = {
  // 测试环境
  testEnvironment: 'jsdom',

  // 根目录
  rootDir: '..',

  // 测试文件匹配模式
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.(ts|tsx|js)',
    '<rootDir>/src/**/*.test.(ts|tsx|js)',
    '<rootDir>/tests/**/*.test.(ts|tsx|js)'
  ],

  // 模块文件扩展名
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],

  // TypeScript 转换 (configured below with ESM support)

  // 模块名映射
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@xenova/transformers$': '<rootDir>/tests/mocks/transformers-mock.ts'
  },

  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/tests/setup/setup.ts'],
  
  // TypeScript setup with Chrome types
  setupFiles: ['<rootDir>/src/types/chrome.d.ts'],
  
  // 覆盖率配置 (enabled for V4.0 TDD development)
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{ts,tsx}',
    '!src/**/*.spec.{ts,tsx}',
    '!tests/**/*',
    '!src/vite-env.d.ts',
    '!src/main.tsx'
  ],
  
  // 覆盖率报告
  coverageReporters: ['text', 'lcov', 'html'],
  
  // 覆盖率阈值 (V4.0 TDD requirements)
  coverageThreshold: {
    global: {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    },
    // 核心模块更高要求
    'src/security/**/*.ts': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    },
    'src/search/**/*.ts': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    },
    'src/ai/**/*.ts': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    }
  },
  
  // 忽略的路径
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/dist/',
    '<rootDir>/build/'
  ],
  
  // 模块路径忽略
  modulePathIgnorePatterns: [
    '<rootDir>/dist/',
    '<rootDir>/build/'
  ],
  
  // 清除模拟
  clearMocks: true,
  
  // 恢复模拟
  restoreMocks: true,
  
  // 超时设置
  testTimeout: 10000,
  
  // TypeScript Jest 配置 (updated for Jest 30 with ESM support)
  preset: 'ts-jest',
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.test.json',
      useESM: true,
      jsx: 'react-jsx'
    }]
  },
  
  // ESM support for modern dependencies
  transformIgnorePatterns: [
    'node_modules/(?!(@xenova/transformers)/)'
  ]
};
