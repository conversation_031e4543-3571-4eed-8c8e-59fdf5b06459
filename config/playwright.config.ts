/**
 * Playwright 配置文件
 * 
 * 配置端到端测试环境
 */

import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  // 测试目录
  testDir: './tests/e2e',
  
  // 全局设置
  fullyParallel: false, // Chrome 扩展测试通常不能并行运行
  forbidOnly: !!process.env.CI, // CI 环境中禁止 test.only
  retries: process.env.CI ? 2 : 0, // CI 环境中重试失败的测试
  workers: 1, // 使用单个 worker 避免扩展冲突
  
  // 报告器
  reporter: [
    ['html'],
    ['json', { outputFile: 'reports/test-results/results.json' }],
    ['junit', { outputFile: 'reports/test-results/results.xml' }]
  ],
  
  // 全局配置
  use: {
    // 基础 URL（如果需要的话）
    // baseURL: 'http://localhost:3000',
    
    // 追踪配置
    trace: 'on-first-retry',
    
    // 截图配置
    screenshot: 'only-on-failure',
    
    // 视频配置
    video: 'retain-on-failure',
    
    // 超时设置
    actionTimeout: 10000,
    navigationTimeout: 30000
  },

  // Visual regression testing configuration
  expect: {
    // Global screenshot comparison settings
    toHaveScreenshot: {
      threshold: 0.3,
      maxDiffPixels: 1000,
      mode: 'strict'
    },
    toMatchSnapshot: {
      threshold: 0.3,
      maxDiffPixels: 1000
    }
  },

  // 项目配置
  projects: [
    {
      name: 'chromium-extension',
      use: { 
        ...devices['Desktop Chrome'],
        // Chrome 扩展特定配置
        channel: 'chrome',
        launchOptions: {
          args: [
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-extensions-except=./dist',
            '--load-extension=./dist'
          ]
        }
      },
    }
  ],

  // 输出目录
  outputDir: 'reports/test-results/',
  
  // 超时设置
  timeout: 60000,
  expect: {
    timeout: 10000
  },

  // Web 服务器配置 - Chrome Extension 测试不需要 web 服务器
  // webServer: process.env.CI ? undefined : {
  //   command: 'npm run build && npm run preview',
  //   port: 4173,
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120000
  // }
});
