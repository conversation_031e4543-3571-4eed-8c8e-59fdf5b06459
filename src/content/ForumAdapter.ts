/**
 * Forum Platform Adapter
 * 
 * Provides specialized adaptation rules for major forum platforms
 * including Baidu Tieba, Zhihu, Reddit, Weibo, and other forums.
 * 
 * Features:
 * - Platform-specific content selectors
 * - Comment and pagination detection
 * - Infinite scroll handling
 * - Platform-specific AJAX monitoring
 */

export interface ForumPlatformConfig {
  /** Platform identifier */
  platform: string;
  /** Domain patterns to match */
  domainPatterns: RegExp[];
  /** Main content selectors */
  contentSelectors: string[];
  /** Comment container selectors */
  commentSelectors: string[];
  /** Pagination selectors */
  paginationSelectors: string[];
  /** Load more button selectors */
  loadMoreSelectors: string[];
  /** AJAX URL patterns to monitor */
  ajaxPatterns: RegExp[];
  /** Infinite scroll container */
  scrollContainer?: string;
  /** Special handling functions */
  handlers?: {
    preProcess?: (document: Document) => void;
    postProcess?: (content: string) => string;
    detectNewContent?: (mutations: MutationRecord[]) => boolean;
  };
}

export interface ForumDetectionResult {
  platform: string;
  config: ForumPlatformConfig;
  confidence: number;
}

export class ForumAdapter {
  private static platforms: ForumPlatformConfig[] = [
    // 百度贴吧 (Baidu Tieba)
    {
      platform: 'baidu-tieba',
      domainPatterns: [
        /^tieba\.baidu\.com$/,
        /^.*\.tieba\.baidu\.com$/
      ],
      contentSelectors: [
        '.d_post_content',
        '.core_reply_wrapper',
        '.p_content',
        '.d_post_content_main',
        'cc .d_post_content'
      ],
      commentSelectors: [
        '.l_reply_num',
        '.core_reply',
        '.lzl_single_post',
        '.lzl_cnt',
        '.j_lzl_container'
      ],
      paginationSelectors: [
        '.pb_list_pager',
        '.pagination',
        '#frs_list_pager'
      ],
      loadMoreSelectors: [
        '.core_reply_tail',
        '.lzl_link_unfold'
      ],
      ajaxPatterns: [
        /\/f\/commit\/thread\/list/,
        /\/mo\/q\/m\?/,
        /\/f\/frs\/generalTabContent/,
        /\/p\/totalComment/
      ],
      scrollContainer: '.core_reply_wrapper',
      handlers: {
        preProcess: (doc) => {
          // Remove ads and unnecessary elements
          const ads = doc.querySelectorAll('.ad_bottom, .ad_entry, .ad_thread_theme');
          ads.forEach(ad => ad.remove());
        },
        postProcess: (content) => {
          // Clean up Tieba-specific formatting
          return content
            .replace(/\s*回复\s*:\s*/g, ' ')
            .replace(/\s*楼主\s*/g, ' ')
            .replace(/\s*沙发\s*/g, ' ');
        },
        detectNewContent: (mutations) => {
          return mutations.some(mutation => 
            mutation.addedNodes.length > 0 &&
            Array.from(mutation.addedNodes).some(node => 
              node.nodeType === Node.ELEMENT_NODE &&
              (node as Element).classList.contains('l_reply')
            )
          );
        }
      }
    },

    // 知乎 (Zhihu)
    {
      platform: 'zhihu',
      domainPatterns: [
        /^www\.zhihu\.com$/,
        /^zhuanlan\.zhihu\.com$/
      ],
      contentSelectors: [
        '.RichContent-inner',
        '.Post-RichTextContainer',
        '.AnswerCard .RichContent',
        '.ArticleItem .RichContent',
        '.QuestionAnswer-content',
        '.Post-content .RichText'
      ],
      commentSelectors: [
        '.CommentItem',
        '.Comment-content',
        '.CommentEditor-content',
        '.Comments-container .CommentItem'
      ],
      paginationSelectors: [
        '.Pagination',
        '.LoadMore',
        '.QuestionAnswers-answers .List-more'
      ],
      loadMoreSelectors: [
        '.Button--blue.Button--withIcon',
        '.ViewAll',
        '.ContentItem-more'
      ],
      ajaxPatterns: [
        /\/api\/v4\/questions\/\d+\/answers/,
        /\/api\/v4\/articles\/\d+\/comments/,
        /\/api\/v4\/comment_v5\/answers\/\d+\/root_comments/,
        /\/api\/v3\/feed\/topstory/
      ],
      scrollContainer: '.Question-mainColumn',
      handlers: {
        preProcess: (doc) => {
          // Remove Zhihu ads and recommendations
          const ads = doc.querySelectorAll('.AdblockBanner, .Recommendation, .Card[data-za-module="AdItem"]');
          ads.forEach(ad => ad.remove());
        },
        postProcess: (content) => {
          return content
            .replace(/\s*知乎用户\s*/g, ' ')
            .replace(/\s*编辑于\s*\d{4}-\d{2}-\d{2}/, '');
        },
        detectNewContent: (mutations) => {
          return mutations.some(mutation => 
            mutation.addedNodes.length > 0 &&
            Array.from(mutation.addedNodes).some(node => 
              node.nodeType === Node.ELEMENT_NODE &&
              ((node as Element).classList.contains('AnswerCard') ||
               (node as Element).classList.contains('CommentItem'))
            )
          );
        }
      }
    },

    // Reddit
    {
      platform: 'reddit',
      domainPatterns: [
        /^www\.reddit\.com$/,
        /^old\.reddit\.com$/,
        /^new\.reddit\.com$/
      ],
      contentSelectors: [
        '[data-test-id="post-content"]',
        '.usertext-body',
        '.md p',
        '.Post .RichTextJSON-root',
        'div[data-click-id="text"]'
      ],
      commentSelectors: [
        '.Comment',
        '.commentarea .thing',
        '[data-test-id="comment"]',
        '.usertext .usertext-body'
      ],
      paginationSelectors: [
        '.nav-buttons',
        '.next-button',
        '[data-click-id="load_more_comments"]'
      ],
      loadMoreSelectors: [
        '.morecomments a',
        '.load-more-comments',
        '[data-click-id="load_more_comments"]'
      ],
      ajaxPatterns: [
        /\/api\/morechildren/,
        /\/comments\/[a-z0-9]+\//,
        /\/_\/api\/graphql/
      ],
      scrollContainer: '.scrollerItem',
      handlers: {
        detectNewContent: (mutations) => {
          return mutations.some(mutation => 
            mutation.addedNodes.length > 0 &&
            Array.from(mutation.addedNodes).some(node => 
              node.nodeType === Node.ELEMENT_NODE &&
              ((node as Element).hasAttribute('data-test-id') ||
               (node as Element).classList.contains('Comment'))
            )
          );
        }
      }
    },

    // 微博 (Weibo)
    {
      platform: 'weibo',
      domainPatterns: [
        /^weibo\.com$/,
        /^www\.weibo\.com$/,
        /^m\.weibo\.com$/
      ],
      contentSelectors: [
        '.WB_text',
        '.WB_detail .WB_text',
        '.card-comment .txt',
        '.weibo-text',
        '.content .txt'
      ],
      commentSelectors: [
        '.list_con .WB_text',
        '.comment_txt',
        '.list_ul .comment_txt',
        '.card-comment'
      ],
      paginationSelectors: [
        '.W_pages',
        '.page .next',
        '.loadmore'
      ],
      loadMoreSelectors: [
        '.WB_cardmore',
        '.more_txt',
        '.load_more'
      ],
      ajaxPatterns: [
        /\/ajax\/statuses\/buildComments/,
        /\/ajax\/profile\/info/,
        /\/ajax\/statuses\/show/
      ],
      handlers: {
        preProcess: (doc) => {
          // Remove Weibo ads
          const ads = doc.querySelectorAll('.ads, .WB_ad, .promote');
          ads.forEach(ad => ad.remove());
        },
        postProcess: (content) => {
          return content
            .replace(/\s*转发微博\s*/g, ' ')
            .replace(/\s*\d+分钟前\s*/g, ' ')
            .replace(/全文/g, '');
        }
      }
    },

    // V2EX
    {
      platform: 'v2ex',
      domainPatterns: [
        /^www\.v2ex\.com$/,
        /^v2ex\.com$/
      ],
      contentSelectors: [
        '.topic_content',
        '.reply_content',
        '.cell .topic_content'
      ],
      commentSelectors: [
        '.box .cell[id^="r_"]',
        '.reply_content'
      ],
      paginationSelectors: [
        '.page_normal',
        '.page_current',
        '.super.button'
      ],
      loadMoreSelectors: [],
      ajaxPatterns: [
        /\/api\/topics\/show\.json/,
        /\/api\/replies\/show\.json/
      ]
    },

    // StackOverflow
    {
      platform: 'stackoverflow',
      domainPatterns: [
        /^stackoverflow\.com$/,
        /^.*\.stackoverflow\.com$/,
        /^.*\.stackexchange\.com$/
      ],
      contentSelectors: [
        '.post-text',
        '.question .postcell .post-text',
        '.answer .postcell .post-text'
      ],
      commentSelectors: [
        '.comment-copy',
        '.comments .comment',
        '.js-comment-text-and-form'
      ],
      paginationSelectors: [
        '.pager',
        '.s-pagination'
      ],
      loadMoreSelectors: [
        '.js-show-link.comments-link',
        '.js-load-remaining-comments'
      ],
      ajaxPatterns: [
        /\/posts\/\d+\/comments/,
        /\/questions\/\d+/
      ]
    }
  ];

  /**
   * Detect forum platform based on current URL
   */
  public static detectPlatform(url: string = window.location.href): ForumDetectionResult | null {
    const hostname = new URL(url).hostname;
    
    for (const config of this.platforms) {
      for (const pattern of config.domainPatterns) {
        if (pattern.test(hostname)) {
          // Calculate confidence based on domain match and content presence
          let confidence = 0.8; // Base confidence for domain match
          
          // Check if platform-specific content exists
          const hasContent = config.contentSelectors.some(selector => 
            document.querySelector(selector) !== null
          );
          if (hasContent) confidence += 0.15;
          
          const hasComments = config.commentSelectors.some(selector => 
            document.querySelector(selector) !== null
          );
          if (hasComments) confidence += 0.05;
          
          return {
            platform: config.platform,
            config,
            confidence: Math.min(confidence, 1.0)
          };
        }
      }
    }
    
    return null;
  }

  /**
   * Get content selectors for detected platform
   */
  public static getContentSelectors(platform?: string): string[] {
    if (!platform) {
      const detected = this.detectPlatform();
      if (!detected) return [];
      platform = detected.platform;
    }
    
    const config = this.platforms.find(p => p.platform === platform);
    return config ? config.contentSelectors : [];
  }

  /**
   * Get comment selectors for detected platform
   */
  public static getCommentSelectors(platform?: string): string[] {
    if (!platform) {
      const detected = this.detectPlatform();
      if (!detected) return [];
      platform = detected.platform;
    }
    
    const config = this.platforms.find(p => p.platform === platform);
    return config ? config.commentSelectors : [];
  }

  /**
   * Check if URL matches forum AJAX patterns
   */
  public static isForumAjaxRequest(url: string, platform?: string): boolean {
    if (!platform) {
      const detected = this.detectPlatform();
      if (!detected) return false;
      platform = detected.platform;
    }
    
    const config = this.platforms.find(p => p.platform === platform);
    if (!config) return false;
    
    return config.ajaxPatterns.some(pattern => pattern.test(url));
  }

  /**
   * Apply platform-specific pre-processing
   */
  public static preProcessDocument(document: Document, platform?: string): void {
    if (!platform) {
      const detected = this.detectPlatform();
      if (!detected) return;
      platform = detected.platform;
    }
    
    const config = this.platforms.find(p => p.platform === platform);
    if (config?.handlers?.preProcess) {
      config.handlers.preProcess(document);
    }
  }

  /**
   * Apply platform-specific post-processing to content
   */
  public static postProcessContent(content: string, platform?: string): string {
    if (!platform) {
      const detected = this.detectPlatform();
      if (!detected) return content;
      platform = detected.platform;
    }
    
    const config = this.platforms.find(p => p.platform === platform);
    if (config?.handlers?.postProcess) {
      return config.handlers.postProcess(content);
    }
    
    return content;
  }

  /**
   * Detect if mutations indicate new forum content
   */
  public static detectNewForumContent(mutations: MutationRecord[], platform?: string): boolean {
    if (!platform) {
      const detected = this.detectPlatform();
      if (!detected) return false;
      platform = detected.platform;
    }
    
    const config = this.platforms.find(p => p.platform === platform);
    if (config?.handlers?.detectNewContent) {
      return config.handlers.detectNewContent(mutations);
    }
    
    // Default detection logic
    return mutations.some(mutation => 
      mutation.addedNodes.length > 0 &&
      Array.from(mutation.addedNodes).some(node => 
        node.nodeType === Node.ELEMENT_NODE &&
        this.isForumContentNode(node as Element, config)
      )
    );
  }

  /**
   * Check if an element is forum content
   */
  private static isForumContentNode(element: Element, config?: ForumPlatformConfig): boolean {
    if (!config) return false;
    
    const allSelectors = [
      ...config.contentSelectors,
      ...config.commentSelectors
    ];
    
    return allSelectors.some(selector => {
      try {
        return element.matches(selector) || element.querySelector(selector) !== null;
      } catch (e) {
        return false;
      }
    });
  }

  /**
   * Get all supported platforms
   */
  public static getSupportedPlatforms(): string[] {
    return this.platforms.map(p => p.platform);
  }

  /**
   * Get platform configuration
   */
  public static getPlatformConfig(platform: string): ForumPlatformConfig | null {
    return this.platforms.find(p => p.platform === platform) || null;
  }
}