/**
 * Enhanced Content Watcher for Dynamic Content Detection
 * 
 * Provides advanced DOM monitoring capabilities including:
 * - Enhanced MutationObserver configuration
 * - iframe and shadow DOM support
 * - Forum-specific content detection
 * - Smart filtering to reduce noise
 */

import { ErrorRecoverySystem } from './ErrorRecovery';

export interface ContentWatcherConfig {
  /** Enable enhanced mutation observation */
  enableMutationObserver: boolean;
  /** Enable iframe content monitoring */
  enableIframeMonitoring: boolean;
  /** Enable shadow DOM monitoring */
  enableShadowDOMMonitoring: boolean;
  /** Minimum content size to consider significant (characters) */
  minContentSize: number;
  /** Interval for periodic content checks (ms) */
  periodicCheckInterval: number;
  /** Enable attribute change monitoring */
  enableAttributeMonitoring: boolean;
  /** Enable character data monitoring */
  enableCharacterDataMonitoring: boolean;
  /** Debug logging */
  debug: boolean;
  /** Custom content selectors for specific sites */
  customContentSelectors: string[];
  /** Selectors to ignore */
  ignoreSelectors: string[];
}

export interface ContentChangeInfo {
  type: 'added' | 'removed' | 'modified' | 'attribute' | 'characterData';
  element: Element | Node;
  addedContent?: string;
  removedContent?: string;
  attributeName?: string;
  isSignificant: boolean;
  timestamp: number;
  source: 'mutation' | 'iframe' | 'shadow-dom' | 'periodic';
  mutations?: MutationRecord[]; // Add mutations for forum detection
}

export interface ContentWatcherEvents {
  onContentChanged: (changeInfo: ContentChangeInfo) => void;
  onSignificantContentAdded: (changeInfo: ContentChangeInfo) => void;
  onIframeContentChanged: (iframe: HTMLIFrameElement, changeInfo: ContentChangeInfo) => void;
}

export class EnhancedContentWatcher {
  private config: ContentWatcherConfig;
  private events: Partial<ContentWatcherEvents>;
  private isActive = false;
  
  // Observers
  private mainObserver: MutationObserver | null = null;
  private iframeObservers = new Map<HTMLIFrameElement, MutationObserver>();
  private shadowObservers = new Map<ShadowRoot, MutationObserver>();
  
  // Timers
  private periodicCheckTimer: number | null = null;
  private debounceTimer: number | null = null;
  
  // State tracking
  private lastContentHash = '';
  private observedElements = new Set<Element>();
  private processedIframes = new Set<HTMLIFrameElement>();
  
  // Error recovery
  private errorRecovery: ErrorRecoverySystem;

  private static readonly DEFAULT_CONFIG: ContentWatcherConfig = {
    enableMutationObserver: true,
    enableIframeMonitoring: true,
    enableShadowDOMMonitoring: true,
    minContentSize: 50,
    periodicCheckInterval: 10000, // 10 seconds
    enableAttributeMonitoring: true,
    enableCharacterDataMonitoring: true,
    debug: false,
    customContentSelectors: [
      // Common content containers
      'article', 'main', 'section',
      '.content', '.post', '.entry', '.story',
      '[role="main"]', '[role="article"]',
      // Forum-specific selectors
      '.comment', '.reply', '.discussion',
      '.thread', '.message', '.post-content',
      // Social media
      '.timeline', '.feed', '.stream',
      '.tweet', '.status', '.update'
    ],
    ignoreSelectors: [
      // Navigation and UI elements
      'nav', 'header', 'footer', 'aside',
      '.navigation', '.nav', '.menu',
      '.header', '.footer', '.sidebar',
      // Ads and tracking
      '.ad', '.ads', '.advertisement',
      '.tracking', '.analytics', '.metrics',
      // Scripts and styles
      'script', 'style', 'link', 'meta',
      // Common noise elements
      '.loading', '.spinner', '.placeholder'
    ]
  };

  constructor(
    config: Partial<ContentWatcherConfig> = {},
    events: Partial<ContentWatcherEvents> = {}
  ) {
    this.config = { ...EnhancedContentWatcher.DEFAULT_CONFIG, ...config };
    this.events = events;
    
    // Initialize error recovery
    this.errorRecovery = new ErrorRecoverySystem({
      debug: this.config.debug,
      autoRecover: true,
      maxRetries: 3
    });
    
    this.log('EnhancedContentWatcher initialized', this.config);
  }

  /**
   * Start content monitoring
   */
  public async start(): Promise<void> {
    if (this.isActive) {
      this.log('Content watcher already active');
      return;
    }

    try {
      if (this.config.enableMutationObserver) {
        await this.setupMainObserverSafely();
      }

      if (this.config.enableIframeMonitoring) {
        await this.setupIframeMonitoringSafely();
      }

      if (this.config.enableShadowDOMMonitoring) {
        await this.setupShadowDOMMonitoringSafely();
      }

      // Start periodic checking
      this.startPeriodicChecking();

      this.isActive = true;
      this.log('Enhanced content watcher started successfully');
    } catch (error) {
      // Try error recovery
      const recovery = await this.errorRecovery.handleError(
        error as Error,
        'EnhancedContentWatcher',
        { start: () => this.start() }
      );
      
      if (!recovery.success) {
        this.log('Failed to start enhanced content watcher even after recovery', error);
        throw error;
      }
    }
  }

  /**
   * Stop content monitoring
   */
  public stop(): void {
    if (!this.isActive) {
      return;
    }

    try {
      // Disconnect main observer
      if (this.mainObserver) {
        this.mainObserver.disconnect();
        this.mainObserver = null;
      }

      // Disconnect iframe observers
      this.iframeObservers.forEach(observer => observer.disconnect());
      this.iframeObservers.clear();

      // Disconnect shadow DOM observers
      this.shadowObservers.forEach(observer => observer.disconnect());
      this.shadowObservers.clear();

      // Clear timers
      if (this.periodicCheckTimer) {
        clearInterval(this.periodicCheckTimer);
        this.periodicCheckTimer = null;
      }

      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
        this.debounceTimer = null;
      }

      // Clear state
      this.observedElements.clear();
      this.processedIframes.clear();

      this.isActive = false;
      this.log('Enhanced content watcher stopped');
    } catch (error) {
      this.log('Error stopping enhanced content watcher', error);
      // Log error for recovery analysis
      this.errorRecovery.handleError(
        error as Error,
        'EnhancedContentWatcher.stop',
        {}
      );
    }
  }

  /**
   * Check if watcher is active
   */
  public isWatcherActive(): boolean {
    return this.isActive;
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<ContentWatcherConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.log('Configuration updated', this.config);

    // Restart if active to apply new configuration
    if (this.isActive) {
      this.stop();
      this.start();
    }
  }

  /**
   * Setup main mutation observer
   */
  private setupMainObserver(): void {
    this.mainObserver = new MutationObserver((mutations) => {
      this.processMutations(mutations, 'mutation');
    });

    // Enhanced observation options
    const observerOptions: MutationObserverInit = {
      childList: true,
      subtree: true,
      attributes: this.config.enableAttributeMonitoring,
      attributeOldValue: this.config.enableAttributeMonitoring,
      characterData: this.config.enableCharacterDataMonitoring,
      characterDataOldValue: this.config.enableCharacterDataMonitoring,
      // Monitor specific attributes that might indicate content changes
      attributeFilter: this.config.enableAttributeMonitoring ? [
        'class', 'style', 'hidden', 'aria-expanded', 'data-loaded'
      ] : undefined
    };

    // Observe multiple target elements
    const targets = this.getObservationTargets();
    targets.forEach(target => {
      if (target && !this.observedElements.has(target)) {
        this.mainObserver!.observe(target, observerOptions);
        this.observedElements.add(target);
      }
    });

    this.log(`Main observer setup complete, observing ${targets.length} targets`);
  }

  /**
   * Get elements to observe
   */
  private getObservationTargets(): Element[] {
    const targets: Element[] = [];

    // Always observe document.body
    if (document.body) {
      targets.push(document.body);
    }

    // Add standard content containers
    const standardSelectors = [
      'main', '[role="main"]', '[role="article"]',
      '.content', '#content', '.main-content'
    ];

    standardSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (!targets.includes(element)) {
          targets.push(element);
        }
      });
    });

    // Add custom selectors
    this.config.customContentSelectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (!targets.includes(element)) {
            targets.push(element);
          }
        });
      } catch (error) {
        this.log(`Invalid custom selector: ${selector}`, error);
      }
    });

    return targets;
  }

  /**
   * Setup iframe monitoring
   */
  private setupIframeMonitoring(): void {
    // Monitor existing iframes
    this.monitorExistingIframes();

    // Monitor for new iframes
    const iframeObserver = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            const iframes = element.tagName === 'IFRAME' 
              ? [element as HTMLIFrameElement]
              : Array.from(element.querySelectorAll('iframe'));
            
            iframes.forEach(iframe => this.monitorIframe(iframe));
          }
        });
      });
    });

    iframeObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.log('Iframe monitoring setup complete');
  }

  /**
   * Monitor existing iframes
   */
  private monitorExistingIframes(): void {
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach(iframe => this.monitorIframe(iframe as HTMLIFrameElement));
  }

  /**
   * Monitor a specific iframe
   */
  private monitorIframe(iframe: HTMLIFrameElement): void {
    if (this.processedIframes.has(iframe)) {
      return;
    }

    try {
      // Check if iframe is accessible (same-origin)
      const iframeDocument = iframe.contentDocument;
      if (!iframeDocument) {
        this.log('Cannot access iframe document (cross-origin)', iframe.src);
        return;
      }

      const iframeObserver = new MutationObserver((mutations) => {
        const changeInfo = this.processMutations(mutations, 'iframe')[0];
        if (changeInfo && this.events.onIframeContentChanged) {
          this.events.onIframeContentChanged(iframe, changeInfo);
        }
      });

      iframeObserver.observe(iframeDocument.body, {
        childList: true,
        subtree: true,
        attributes: this.config.enableAttributeMonitoring,
        characterData: this.config.enableCharacterDataMonitoring
      });

      this.iframeObservers.set(iframe, iframeObserver);
      this.processedIframes.add(iframe);
      
      this.log('Started monitoring iframe', iframe.src || 'about:blank');
    } catch (error) {
      this.log('Failed to monitor iframe', error);
    }
  }

  /**
   * Setup shadow DOM monitoring
   */
  private setupShadowDOMMonitoring(): void {
    // Monitor existing shadow roots
    this.monitorExistingShadowRoots();

    // Monitor for new shadow roots
    const shadowObserver = new MutationObserver((mutations) => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (element.shadowRoot) {
              this.monitorShadowRoot(element.shadowRoot);
            }
            
            // Check descendants for shadow roots
            const elementsWithShadow = element.querySelectorAll('*');
            elementsWithShadow.forEach(el => {
              if ((el as any).shadowRoot) {
                this.monitorShadowRoot((el as any).shadowRoot);
              }
            });
          }
        });
      });
    });

    shadowObserver.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.log('Shadow DOM monitoring setup complete');
  }

  /**
   * Monitor existing shadow roots
   */
  private monitorExistingShadowRoots(): void {
    // This is a bit tricky as there's no direct way to find all shadow roots
    // We'll use a common approach to find elements that might have shadow roots
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
      if ((element as any).shadowRoot) {
        this.monitorShadowRoot((element as any).shadowRoot);
      }
    });
  }

  /**
   * Monitor a specific shadow root
   */
  private monitorShadowRoot(shadowRoot: ShadowRoot): void {
    if (this.shadowObservers.has(shadowRoot)) {
      return;
    }

    try {
      const shadowObserver = new MutationObserver((mutations) => {
        this.processMutations(mutations, 'shadow-dom');
      });

      shadowObserver.observe(shadowRoot, {
        childList: true,
        subtree: true,
        attributes: this.config.enableAttributeMonitoring,
        characterData: this.config.enableCharacterDataMonitoring
      });

      this.shadowObservers.set(shadowRoot, shadowObserver);
      this.log('Started monitoring shadow root');
    } catch (error) {
      this.log('Failed to monitor shadow root', error);
    }
  }

  /**
   * Start periodic content checking
   */
  private startPeriodicChecking(): void {
    this.periodicCheckTimer = window.setInterval(() => {
      this.performPeriodicCheck();
    }, this.config.periodicCheckInterval);

    this.log(`Periodic checking started (${this.config.periodicCheckInterval}ms interval)`);
  }

  /**
   * Perform periodic content check
   */
  private performPeriodicCheck(): void {
    const currentHash = this.generateContentHash();
    
    if (currentHash !== this.lastContentHash && currentHash.length > 0) {
      this.log('Periodic check detected content change', {
        oldHash: this.lastContentHash,
        newHash: currentHash
      });
      
      this.lastContentHash = currentHash;
      
      const changeInfo: ContentChangeInfo = {
        type: 'modified',
        element: document.body,
        isSignificant: true,
        timestamp: Date.now(),
        source: 'periodic'
      };

      this.notifyContentChange(changeInfo);
    }
  }

  /**
   * Process mutation records
   */
  private processMutations(mutations: MutationRecord[], source: 'mutation' | 'iframe' | 'shadow-dom'): ContentChangeInfo[] {
    const changes: ContentChangeInfo[] = [];

    mutations.forEach(mutation => {
      let changeInfo: ContentChangeInfo | null = null;

      switch (mutation.type) {
        case 'childList':
          changeInfo = this.processChildListMutation(mutation, source);
          break;
        case 'attributes':
          changeInfo = this.processAttributeMutation(mutation, source);
          break;
        case 'characterData':
          changeInfo = this.processCharacterDataMutation(mutation, source);
          break;
      }

      if (changeInfo) {
        // Add mutations array for forum detection
        changeInfo.mutations = mutations;
        changes.push(changeInfo);
        this.notifyContentChange(changeInfo);
      }
    });

    return changes;
  }

  /**
   * Process child list mutations
   */
  private processChildListMutation(mutation: MutationRecord, source: string): ContentChangeInfo | null {
    let hasSignificantChange = false;
    let addedContent = '';
    let removedContent = '';

    // Check added nodes
    mutation.addedNodes.forEach(node => {
      if (this.isSignificantNode(node)) {
        hasSignificantChange = true;
        addedContent += this.extractTextContent(node);
      }
    });

    // Check removed nodes
    mutation.removedNodes.forEach(node => {
      if (this.isSignificantNode(node)) {
        hasSignificantChange = true;
        removedContent += this.extractTextContent(node);
      }
    });

    if (!hasSignificantChange) {
      return null;
    }

    return {
      type: 'added',
      element: mutation.target as Element,
      addedContent: addedContent || undefined,
      removedContent: removedContent || undefined,
      isSignificant: addedContent.length >= this.config.minContentSize,
      timestamp: Date.now(),
      source: source as any
    };
  }

  /**
   * Process attribute mutations
   */
  private processAttributeMutation(mutation: MutationRecord, source: string): ContentChangeInfo | null {
    if (!this.config.enableAttributeMonitoring) {
      return null;
    }

    // Only consider certain attributes as significant
    const significantAttributes = ['class', 'style', 'hidden', 'aria-expanded', 'data-loaded'];
    const attributeName = mutation.attributeName;

    if (!attributeName || !significantAttributes.includes(attributeName)) {
      return null;
    }

    return {
      type: 'attribute',
      element: mutation.target as Element,
      attributeName,
      isSignificant: false, // Attribute changes are usually not significant for content
      timestamp: Date.now(),
      source: source as any
    };
  }

  /**
   * Process character data mutations
   */
  private processCharacterDataMutation(mutation: MutationRecord, source: string): ContentChangeInfo | null {
    if (!this.config.enableCharacterDataMonitoring) {
      return null;
    }

    const newValue = mutation.target.textContent || '';
    const oldValue = mutation.oldValue || '';

    if (Math.abs(newValue.length - oldValue.length) < this.config.minContentSize) {
      return null;
    }

    return {
      type: 'characterData',
      element: mutation.target.parentElement || document.body,
      addedContent: newValue,
      removedContent: oldValue,
      isSignificant: Math.abs(newValue.length - oldValue.length) >= this.config.minContentSize,
      timestamp: Date.now(),
      source: source as any
    };
  }

  /**
   * Check if a node is significant for content monitoring
   */
  private isSignificantNode(node: Node): boolean {
    if (node.nodeType !== Node.ELEMENT_NODE && node.nodeType !== Node.TEXT_NODE) {
      return false;
    }

    // Check if element should be ignored
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      
      // Check ignore selectors
      for (const selector of this.config.ignoreSelectors) {
        try {
          if (element.matches && element.matches(selector)) {
            return false;
          }
        } catch (error) {
          // Invalid selector, skip
          continue;
        }
      }
    }

    // Check content size
    const textContent = this.extractTextContent(node);
    return textContent.length >= this.config.minContentSize;
  }

  /**
   * Extract text content from a node
   */
  private extractTextContent(node: Node): string {
    if (node.nodeType === Node.TEXT_NODE) {
      return node.textContent?.trim() || '';
    }
    
    if (node.nodeType === Node.ELEMENT_NODE) {
      return (node as Element).textContent?.trim() || '';
    }
    
    return '';
  }

  /**
   * Generate content hash for change detection
   */
  private generateContentHash(): string {
    const contentElements = document.querySelectorAll(
      this.config.customContentSelectors.join(', ')
    );
    
    const contentText = Array.from(contentElements)
      .map(el => el.textContent?.trim())
      .filter(text => text && text.length > 20)
      .join(' ')
      .substring(0, 2000); // Limit length

    return this.simpleHash(contentText);
  }

  /**
   * Simple hash function
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  /**
   * Notify about content changes
   */
  private notifyContentChange(changeInfo: ContentChangeInfo): void {
    // Use debouncing to avoid spam
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }

    this.debounceTimer = window.setTimeout(() => {
      if (this.events.onContentChanged) {
        this.events.onContentChanged(changeInfo);
      }

      if (changeInfo.isSignificant && this.events.onSignificantContentAdded) {
        this.events.onSignificantContentAdded(changeInfo);
      }
    }, 500); // 500ms debounce
  }

  /**
   * Get current statistics
   */
  public getStats(): {
    isActive: boolean;
    observedElements: number;
    monitoredIframes: number;
    monitoredShadowRoots: number;
    lastContentHash: string;
  } {
    return {
      isActive: this.isActive,
      observedElements: this.observedElements.size,
      monitoredIframes: this.iframeObservers.size,
      monitoredShadowRoots: this.shadowObservers.size,
      lastContentHash: this.lastContentHash
    };
  }

  /**
   * Safely setup main observer with error recovery
   */
  private async setupMainObserverSafely(): Promise<void> {
    try {
      this.setupMainObserver();
    } catch (error) {
      const recovery = await this.errorRecovery.handleError(
        error as Error,
        'EnhancedContentWatcher.setupMainObserver',
        { fallback: () => this.log('Main observer setup failed, continuing without mutation observation') }
      );
      
      if (!recovery.success) {
        throw error;
      }
    }
  }

  /**
   * Safely setup iframe monitoring with error recovery
   */
  private async setupIframeMonitoringSafely(): Promise<void> {
    try {
      this.setupIframeMonitoring();
    } catch (error) {
      const recovery = await this.errorRecovery.handleError(
        error as Error,
        'EnhancedContentWatcher.setupIframeMonitoring',
        { fallback: () => this.log('Iframe monitoring setup failed, continuing without iframe support') }
      );
      
      if (!recovery.success) {
        throw error;
      }
    }
  }

  /**
   * Safely setup shadow DOM monitoring with error recovery
   */
  private async setupShadowDOMMonitoringSafely(): Promise<void> {
    try {
      this.setupShadowDOMMonitoring();
    } catch (error) {
      const recovery = await this.errorRecovery.handleError(
        error as Error,
        'EnhancedContentWatcher.setupShadowDOMMonitoring',
        { fallback: () => this.log('Shadow DOM monitoring setup failed, continuing without shadow DOM support') }
      );
      
      if (!recovery.success) {
        throw error;
      }
    }
  }

  /**
   * Get error statistics and health status
   */
  public getErrorStats() {
    return this.errorRecovery.getErrorStats();
  }

  /**
   * Check system health
   */
  public checkHealth() {
    return this.errorRecovery.checkHealth();
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[EnhancedContentWatcher] ${message}`, data || '');
    }
  }
}