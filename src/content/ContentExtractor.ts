/**
 * Content Extraction Service for Recall V3.0
 * 
 * Main content extraction service that orchestrates different extraction strategies
 * for various content types and page structures.
 */

import { Readability } from '@mozilla/readability';
import { cleanContent, cleanTitle, shouldIndexUrl } from '../models';

/**
 * Content extraction result interface
 */
export interface ExtractedContent {
  /** Page title */
  title: string;
  /** Page main text content */
  content: string;
  /** Page URL */
  url: string;
  /** Whether extraction was successful */
  success: boolean;
  /** Error message if any */
  error?: string;
  /** Content length in characters */
  contentLength: number;
  /** Whether content is valid for indexing */
  isValidContent: boolean;
  /** Content type detected */
  contentType: ContentType;
  /** Additional metadata */
  metadata: ContentMetadata;
}

/**
 * Content type constants
 */
export const ContentType = {
  ARTICLE: 'article',
  BLOG_POST: 'blog_post',
  NEWS: 'news',
  DOCUMENTATION: 'documentation',
  FORUM: 'forum',
  SOCIAL_MEDIA: 'social_media',
  E_COMMERCE: 'e_commerce',
  PDF: 'pdf',
  VIDEO: 'video',
  SPA: 'spa',
  STATIC_PAGE: 'static_page',
  UNKNOWN: 'unknown'
} as const;

export type ContentType = typeof ContentType[keyof typeof ContentType];

/**
 * Content metadata interface
 */
export interface ContentMetadata {
  /** Language code */
  language?: string;
  /** Author information */
  author?: string;
  /** Publication date */
  publishDate?: Date;
  /** Keywords/tags */
  keywords?: string[];
  /** Description/excerpt */
  description?: string;
  /** Reading time estimate (minutes) */
  readingTime?: number;
  /** Whether this is a SPA page */
  isSPA: boolean;
  /** Extraction method used */
  extractionMethod: ExtractionMethod;
}

/**
 * Extraction method constants
 */
export const ExtractionMethod = {
  READABILITY: 'readability',
  DOM_PARSING: 'dom_parsing',
  SPECIAL_HANDLER: 'special_handler',
  FALLBACK: 'fallback'
} as const;

export type ExtractionMethod = typeof ExtractionMethod[keyof typeof ExtractionMethod];

/**
 * Extraction configuration options
 */
export interface ExtractionOptions {
  /** Minimum content length for valid extraction */
  minContentLength?: number;
  /** Maximum content length to prevent memory issues */
  maxContentLength?: number;
  /** Whether to include image alt text */
  includeImages?: boolean;
  /** Strict validation mode */
  strictMode?: boolean;
  /** Blacklisted domains */
  blacklistDomains?: string[];
  /** Debug mode for logging */
  debug?: boolean;
  /** Timeout for extraction in milliseconds */
  timeout?: number;
}

/**
 * Default extraction options
 */
const DEFAULT_OPTIONS: Required<ExtractionOptions> = {
  minContentLength: 100,
  maxContentLength: 100000, // Increased for V3.0 to handle larger content
  includeImages: false,
  strictMode: false,
  blacklistDomains: [],
  debug: false,
  timeout: 10000 // 10 second timeout
};

/**
 * Main content extraction service
 */
export class ContentExtractor {
  private readonly options: Required<ExtractionOptions>;

  constructor(options: ExtractionOptions = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
  }

  /**
   * Extract content from the current page
   */
  public async extractPageContent(): Promise<ExtractedContent> {
    const startTime = Date.now();
    const url = window.location.href;

    // Initialize result object
    const result: ExtractedContent = {
      title: '',
      content: '',
      url,
      success: false,
      contentLength: 0,
      isValidContent: false,
      contentType: ContentType.UNKNOWN,
      metadata: {
        isSPA: false,
        extractionMethod: ExtractionMethod.FALLBACK
      }
    };

    try {
      // Check URL validity and blacklist
      if (!this.shouldExtractFromUrl(url)) {
        result.error = 'URL is blacklisted or not indexable';
        return result;
      }

      // Check timeout
      const checkTimeout = () => {
        if (Date.now() - startTime > this.options.timeout) {
          throw new Error('Extraction timeout exceeded');
        }
      };

      // Detect content type and SPA status
      checkTimeout();
      result.contentType = this.detectContentType();
      result.metadata.isSPA = this.isSinglePageApp();

      // Extract title
      checkTimeout();
      result.title = this.extractTitle();

      // Check if page has valid content before proceeding
      checkTimeout();
      if (!this.hasValidContent()) {
        result.error = 'Page does not contain sufficient content';
        return result;
      }

      // Apply extraction strategy based on content type
      checkTimeout();
      const extractionResult = await this.applyExtractionStrategy(result.contentType);
      
      result.content = extractionResult.content;
      result.metadata.extractionMethod = extractionResult.method;
      result.success = extractionResult.success;
      result.error = extractionResult.error;

      // Extract additional metadata
      checkTimeout();
      result.metadata = { ...result.metadata, ...this.extractMetadata(result.content) };

      // Validate and clean content
      checkTimeout();
      if (result.success && result.content) {
        result.content = cleanContent(result.content);
        result.title = cleanTitle(result.title);
        result.contentLength = result.content.length;
        result.isValidContent = this.validateContent(result.content);
      }

    } catch (error) {
      result.error = `Content extraction failed: ${(error as Error).message}`;
      this.log('Extraction error:', error);
    }

    this.log('Extraction completed', {
      success: result.success,
      contentLength: result.contentLength,
      contentType: result.contentType,
      method: result.metadata.extractionMethod,
      duration: Date.now() - startTime
    });

    return result;
  }

  /**
   * Check if content should be extracted from the given URL
   */
  private shouldExtractFromUrl(url: string): boolean {
    // Allow local files and localhost in debug mode
    const isLocalFile = url.startsWith('file://');
    const isLocalhost = url.includes('localhost') || url.includes('127.0.0.1');
    if ((isLocalFile || isLocalhost) && this.options.debug) {
      return true;
    }

    return shouldIndexUrl(url, this.options.blacklistDomains);
  }

  /**
   * Detect the type of content on the page
   */
  private detectContentType(): ContentType {
    const url = window.location.href.toLowerCase();
    const domain = window.location.hostname.toLowerCase();

    // Check for specific content types based on URL patterns and DOM structure
    if (url.includes('.pdf') || document.querySelector('embed[type="application/pdf"]')) {
      return ContentType.PDF;
    }

    if (this.isVideoPage()) {
      return ContentType.VIDEO;
    }

    // Check domain patterns
    if (domain.includes('github.com') || domain.includes('docs.') || url.includes('/docs/')) {
      return ContentType.DOCUMENTATION;
    }

    if (domain.includes('reddit.com') || domain.includes('stackoverflow.com') || 
        domain.includes('forum') || document.querySelector('.forum, .discussion')) {
      return ContentType.FORUM;
    }

    if (domain.includes('twitter.com') || domain.includes('facebook.com') || 
        domain.includes('linkedin.com') || domain.includes('instagram.com')) {
      return ContentType.SOCIAL_MEDIA;
    }

    if (domain.includes('amazon.com') || domain.includes('shop') || 
        document.querySelector('.product, .item, .cart')) {
      return ContentType.E_COMMERCE;
    }

    // Check for news sites
    if (domain.includes('news') || document.querySelector('article[class*="news"]') ||
        document.querySelector('meta[property="article:published_time"]')) {
      return ContentType.NEWS;
    }

    // Check for blog posts
    if (url.includes('/blog/') || document.querySelector('.blog, .post') ||
        document.querySelector('meta[property="article:author"]')) {
      return ContentType.BLOG_POST;
    }

    // Check for articles
    if (document.querySelector('article') || 
        document.querySelector('meta[property="og:type"][content="article"]')) {
      return ContentType.ARTICLE;
    }

    // Default to static page or SPA
    return this.isSinglePageApp() ? ContentType.SPA : ContentType.STATIC_PAGE;
  }

  /**
   * Extract page title with fallback strategies
   */
  private extractTitle(): string {
    // Priority: Open Graph title > Twitter title > meta title > h1 > document.title
    const selectors = [
      'meta[property="og:title"]',
      'meta[name="twitter:title"]',
      'meta[name="title"]'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      const content = element?.getAttribute('content');
      if (content && content.trim()) {
        return content.trim();
      }
    }

    // Try h1 element
    const h1Element = document.querySelector('h1');
    if (h1Element && h1Element.textContent && h1Element.textContent.trim()) {
      return h1Element.textContent.trim();
    }

    // Fallback to document title
    return document.title || 'Untitled Page';
  }

  /**
   * Check if page has valid content for extraction
   */
  private hasValidContent(): boolean {
    const bodyText = document.body?.textContent || '';
    const textLength = bodyText.replace(/\s+/g, ' ').trim().length;
    
    // Check for minimum text length
    if (textLength < 50) {
      return false;
    }

    // Check for main content elements
    const contentSelectors = [
      'article', 'main', '[role="main"]', '.content', '.post', '.entry',
      '.article', '.story', '.text', '.body', '.content-body', '.markdown-body'
    ];
    
    const hasContentElements = contentSelectors.some(selector => 
      document.querySelector(selector)
    );

    return textLength > this.options.minContentLength || hasContentElements;
  }

  /**
   * Apply appropriate extraction strategy based on content type
   */
  private async applyExtractionStrategy(contentType: ContentType): Promise<{
    success: boolean;
    content: string;
    method: ExtractionMethod;
    error?: string;
  }> {
    // Handle special content types first
    if (contentType === ContentType.PDF || contentType === ContentType.VIDEO) {
      const result = await this.handleSpecialContent(contentType);
      return {
        success: result.success,
        content: result.content,
        method: ExtractionMethod.SPECIAL_HANDLER,
        error: result.error
      };
    }

    // Try Readability first for most content types
    const readabilityResult = this.extractWithReadability();
    if (readabilityResult.success && readabilityResult.content.length > this.options.minContentLength) {
      return {
        success: true,
        content: readabilityResult.content,
        method: ExtractionMethod.READABILITY,
        error: readabilityResult.error
      };
    }

    // Fallback to DOM parsing for SPA and complex pages
    if (contentType === ContentType.SPA || contentType === ContentType.FORUM) {
      const domResult = this.extractContentWithDOMParsing();
      if (domResult.success && domResult.content.length > this.options.minContentLength) {
        return {
          success: true,
          content: domResult.content,
          method: ExtractionMethod.DOM_PARSING,
          error: domResult.error
        };
      }
    }

    // Final fallback to basic text extraction
    const fallbackResult = this.extractBasicText();
    return {
      success: fallbackResult.success,
      content: fallbackResult.content,
      method: ExtractionMethod.FALLBACK,
      error: fallbackResult.error || readabilityResult.error
    };
  }

  /**
   * Extract content using Mozilla Readability
   */
  private extractWithReadability(): { success: boolean; content: string; error?: string } {
    try {
      const documentClone = document.cloneNode(true) as Document;
      
      const reader = new Readability(documentClone, {
        debug: this.options.debug,
        maxElemsToParse: 0,
        nbTopCandidates: 5,
        charThreshold: 500,
        classesToPreserve: ['highlight', 'code', 'pre', 'math', 'equation']
      });

      const article = reader.parse();
      
      if (article && article.textContent) {
        return {
          success: true,
          content: article.textContent
        };
      } else {
        return {
          success: false,
          content: '',
          error: 'Readability failed to extract content'
        };
      }

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `Readability error: ${(error as Error).message}`
      };
    }
  }

  /**
   * Basic text extraction as final fallback
   */
  private extractBasicText(): { success: boolean; content: string; error?: string } {
    try {
      const elementsToRemove = [
        'script', 'style', 'nav', 'header', 'footer', 'aside',
        '.navigation', '.menu', '.sidebar', '.ads', '.advertisement',
        '[role="navigation"]', '[role="banner"]', '[role="contentinfo"]',
        '.cookie-notice', '.popup', '.modal'
      ];

      const clone = document.body.cloneNode(true) as HTMLElement;
      
      elementsToRemove.forEach(selector => {
        const elements = clone.querySelectorAll(selector);
        elements.forEach(el => el.remove());
      });

      const textContent = clone.textContent || '';
      
      return {
        success: textContent.length > 0,
        content: textContent,
        error: textContent.length === 0 ? 'No text content found' : undefined
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `Basic extraction failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Extract additional metadata from the page
   */
  private extractMetadata(content: string): Partial<ContentMetadata> {
    const metadata: Partial<ContentMetadata> = {};

    // Language detection
    metadata.language = document.documentElement.lang || 
                       document.querySelector('meta[http-equiv="Content-Language"]')?.getAttribute('content') ||
                       'en';

    // Author extraction
    const authorSelectors = [
      'meta[name="author"]',
      'meta[property="article:author"]',
      'meta[name="twitter:creator"]',
      '.author',
      '.byline'
    ];

    for (const selector of authorSelectors) {
      const element = document.querySelector(selector);
      const author = element?.getAttribute('content') || element?.textContent;
      if (author && author.trim()) {
        metadata.author = author.trim();
        break;
      }
    }

    // Publication date
    const dateSelectors = [
      'meta[property="article:published_time"]',
      'meta[name="publication_date"]',
      'time[datetime]',
      '.date',
      '.published'
    ];

    for (const selector of dateSelectors) {
      const element = document.querySelector(selector);
      const dateStr = element?.getAttribute('content') || 
                     element?.getAttribute('datetime') || 
                     element?.textContent;
      if (dateStr) {
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          metadata.publishDate = date;
          break;
        }
      }
    }

    // Keywords extraction
    const keywordsElement = document.querySelector('meta[name="keywords"]');
    if (keywordsElement) {
      const keywordsStr = keywordsElement.getAttribute('content');
      if (keywordsStr) {
        metadata.keywords = keywordsStr.split(',').map(k => k.trim()).filter(k => k.length > 0);
      }
    }

    // Description
    const descriptionSelectors = [
      'meta[property="og:description"]',
      'meta[name="description"]',
      'meta[name="twitter:description"]'
    ];

    for (const selector of descriptionSelectors) {
      const element = document.querySelector(selector);
      const description = element?.getAttribute('content');
      if (description && description.trim()) {
        metadata.description = description.trim();
        break;
      }
    }

    // Reading time estimation (rough calculation: 200 words per minute)
    if (content) {
      const wordCount = content.split(/\s+/).length;
      metadata.readingTime = Math.max(1, Math.round(wordCount / 200));
    }

    return metadata;
  }

  /**
   * Validate extracted content quality
   */
  private validateContent(content: string): boolean {
    if (!content || typeof content !== 'string') {
      return false;
    }

    const trimmedContent = content.trim();
    
    // Length checks
    if (trimmedContent.length < this.options.minContentLength ||
        trimmedContent.length > this.options.maxContentLength) {
      return false;
    }

    // Quality checks in strict mode
    if (this.options.strictMode) {
      // Check character diversity
      const uniqueChars = new Set(trimmedContent.toLowerCase()).size;
      if (uniqueChars < 10) {
        return false;
      }

      // Check word count
      const words = trimmedContent.split(/\s+/).filter(word => word.length > 2);
      if (words.length < 20) {
        return false;
      }

      // Check for spam patterns
      const spamPatterns = [
        /^(loading|please wait|error|404|not found)/i,
        /^(cookie|privacy policy|terms of service)/i,
        /^(advertisement|sponsored|promoted)/i,
        /^(access denied|unauthorized|forbidden)/i
      ];

      if (spamPatterns.some(pattern => pattern.test(trimmedContent))) {
        return false;
      }
    }

    return true;
  }

  /**
   * Check if current page is a Single Page Application
   */
  private isSinglePageApp(): boolean {
    const spaIndicators = [
      () => !!(window as any).React,
      () => !!(window as any).Vue,
      () => !!(window as any).angular,
      () => !!(window as any).Angular,
      () => !!document.querySelector('[ng-app], [data-ng-app], [ng-controller]'),
      () => !!document.querySelector('[data-reactroot], #root, #app'),
      () => !!document.querySelector('script[src*="react"], script[src*="vue"], script[src*="angular"]'),
      () => document.body.innerHTML.includes('ng-') || document.body.innerHTML.includes('v-'),
      () => window.history && typeof window.history.pushState === 'function'
    ];

    return spaIndicators.some(check => {
      try {
        return check();
      } catch {
        return false;
      }
    });
  }

  /**
   * Check if current page is a video page
   */
  private isVideoPage(): boolean {
    const hostname = window.location.hostname.toLowerCase();
    
    // Check for known video platforms
    const videoDomains = ['youtube.com', 'youtu.be', 'vimeo.com', 'twitch.tv'];
    const isKnownVideoPlatform = videoDomains.some(domain => hostname.includes(domain));

    if (isKnownVideoPlatform) {
      return true;
    }

    // Check for video elements and players
    const hasVideoElements = !!(
      document.querySelector('video') ||
      document.querySelector('iframe[src*="youtube"]') ||
      document.querySelector('iframe[src*="vimeo"]') ||
      document.querySelector('.video-player, .player, [data-video]') ||
      document.querySelector('embed[type*="video"], object[type*="video"]')
    );

    return hasVideoElements;
  }

  /**
   * Handle special content types (PDF, Video)
   */
  private async handleSpecialContent(contentType: ContentType): Promise<{
    success: boolean;
    content: string;
    error?: string;
  }> {
    switch (contentType) {
      case ContentType.PDF:
        return this.handlePDFContent();
      case ContentType.VIDEO:
        return this.handleVideoContent();
      default:
        return {
          success: false,
          content: '',
          error: `Unsupported content type: ${contentType}`
        };
    }
  }

  /**
   * Handle PDF content extraction
   */
  private handlePDFContent(): { success: boolean; content: string; error?: string } {
    try {
      let content = '';

      // Check for embedded PDF
      const pdfEmbed = document.querySelector('embed[type="application/pdf"]') as HTMLEmbedElement;
      if (pdfEmbed) {
        const pdfUrl = pdfEmbed.src;
        content = `PDF Document: ${document.title}\nURL: ${pdfUrl}`;
      }

      // Check for PDF viewer interfaces
      const pdfViewer = document.querySelector('#viewer, .pdf-viewer, .document-viewer');
      if (pdfViewer) {
        const viewerText = pdfViewer.textContent || '';
        if (viewerText.trim()) {
          content += `\n\nDocument content: ${viewerText.trim()}`;
        }
      }

      // Fallback to basic page content
      if (!content.trim()) {
        content = `PDF Document: ${document.title || 'Untitled PDF'}`;
        const pageText = document.body.textContent || '';
        if (pageText.length > 50) {
          content += `\n\nPage text: ${pageText.substring(0, 500)}...`;
        }
      }

      return {
        success: content.length > 0,
        content: content.trim()
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `PDF extraction failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Handle video content extraction
   */
  private handleVideoContent(): { success: boolean; content: string; error?: string } {
    try {
      let content = `Video: ${document.title}\n`;

      // Extract title from common video elements
      const titleSelectors = [
        '#title h1', '.title', '.video-title', 'h1[data-name="title"]',
        'h2[data-a-target="stream-title"]', '.watch-title'
      ];

      for (const selector of titleSelectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent) {
          content = `Title: ${element.textContent.trim()}\n`;
          break;
        }
      }

      // Extract description
      const descriptionSelectors = [
        '#description', '.description', '.video-description',
        '[data-name="description"]', '.about-section'
      ];

      for (const selector of descriptionSelectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent) {
          content += `Description: ${element.textContent.trim()}\n`;
          break;
        }
      }

      // Extract any visible text as fallback
      if (content === `Video: ${document.title}\n`) {
        const visibleText = document.body.textContent || '';
        const cleanText = visibleText.replace(/\s+/g, ' ').trim();
        if (cleanText.length > 100) {
          content += `Content: ${cleanText.substring(0, 500)}...`;
        }
      }

      return {
        success: content.length > 0,
        content: content.trim()
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `Video extraction failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Extract content using DOM parsing approach
   */
  private extractContentWithDOMParsing(): {
    success: boolean;
    content: string;
    error?: string;
  } {
    try {
      // Find main content areas
      const contentSelectors = [
        'main',
        '[role="main"]',
        '.main-content',
        '.content',
        '.app-content',
        '.page-content',
        'article',
        '.post',
        '.entry'
      ];

      for (const selector of contentSelectors) {
        const element = document.querySelector(selector);
        if (element) {
          const text = this.extractCleanText(element as HTMLElement);
          if (text.length > this.options.minContentLength) {
            return {
              success: true,
              content: text
            };
          }
        }
      }

      // Fallback to body content with noise removal
      return this.extractBasicText();

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `DOM parsing failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Extract clean text from an element
   */
  private extractCleanText(element: HTMLElement): string {
    const clone = element.cloneNode(true) as HTMLElement;

    // Remove unwanted elements
    const unwantedSelectors = [
      'script', 'style', 'nav', 'header', 'footer', 'aside',
      '.nav', '.navigation', '.menu', '.sidebar', '.ads', '.ad',
      '.banner', '.popup', '.modal', '.cookie', '.gdpr'
    ];

    unwantedSelectors.forEach(selector => {
      const elements = clone.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    const text = clone.textContent || '';
    return text.replace(/\s+/g, ' ').trim();
  }

  /**
   * Log debug information
   */
  private log(message: string, data?: any): void {
    if (this.options.debug) {
      console.log(`[ContentExtractor] ${message}`, data || '');
    }
  }
}

// Export default instance for backward compatibility
export const contentExtractor = new ContentExtractor();