/**
 * Minimal type definitions for @mozilla/readability
 * To avoid compilation errors when the package is not installed
 */

declare module '@mozilla/readability' {
  export interface ReadabilityOptions {
    debug?: boolean;
    maxElemsToParse?: number;
    nbTopCandidates?: number;
    charThreshold?: number;
    classesToPreserve?: string[];
    keepClasses?: boolean;
    serializer?: (node: Node) => string;
  }

  export interface ReadabilityResult {
    title: string;
    content: string;
    textContent: string;
    length: number;
    excerpt: string;
    byline: string;
    dir: string;
    siteName: string;
    lang: string;
  }

  // Only declare if not already declared by the package
  export interface ReadabilityConstructor {
    new (document: Document, options?: ReadabilityOptions): ReadabilityInstance;
    isProbablyReaderable(document: Document, options?: { 
      minContentLength?: number;
      minScore?: number;
      visibilityChecker?: (node: Element) => boolean;
    }): boolean;
  }

  export interface ReadabilityInstance {
    parse(): ReadabilityResult | null;
  }
}