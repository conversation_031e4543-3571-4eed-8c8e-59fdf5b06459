/**
 * Toast Notification Styles for Recall V3.0
 * 
 * Non-intrusive notification styling with smooth animations
 */

/* Toast Container */
.recall-toast {
  --toast-bg: #ffffff;
  --toast-border: #e0e0e0;
  --toast-shadow: rgba(0, 0, 0, 0.15);
  --toast-text: #333333;
  --toast-text-secondary: #666666;
  --toast-accent: #2196f3;
  --toast-success: #4caf50;
  --toast-warning: #ff9800;
  --toast-error: #f44336;
  --toast-radius: 8px;
  --toast-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  position: fixed;
  z-index: 2147483646;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: var(--toast-text);
  background: var(--toast-bg);
  border: 1px solid var(--toast-border);
  border-radius: var(--toast-radius);
  box-shadow: 0 4px 16px var(--toast-shadow);
  backdrop-filter: blur(10px);
  min-width: 280px;
  max-width: 400px;
  overflow: hidden;
  transition: var(--toast-transition);
  transform: translateY(100px);
  opacity: 0;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .recall-toast {
    --toast-bg: #2d2d2d;
    --toast-border: #444444;
    --toast-shadow: rgba(0, 0, 0, 0.3);
    --toast-text: #ffffff;
    --toast-text-secondary: #cccccc;
    --toast-accent: #64b5f6;
    --toast-success: #66bb6a;
    --toast-warning: #ffb74d;
    --toast-error: #ef5350;
  }
}

/* Toast Visibility States */
.recall-toast.visible {
  transform: translateY(0);
  opacity: 1;
}

.recall-toast.hidden {
  transform: translateY(100px);
  opacity: 0;
}

/* Toast Type Variants */
.recall-toast.info {
  border-left: 4px solid var(--toast-accent);
}

.recall-toast.success {
  border-left: 4px solid var(--toast-success);
}

.recall-toast.warning {
  border-left: 4px solid var(--toast-warning);
}

.recall-toast.error {
  border-left: 4px solid var(--toast-error);
}

/* Toast Content */
.toast-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
}

.toast-icon {
  font-size: 18px;
  flex-shrink: 0;
  margin-top: 1px;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  color: var(--toast-text);
  word-wrap: break-word;
}

.toast-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* Action Buttons */
.toast-action-button,
.toast-close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: var(--toast-text-secondary);
  cursor: pointer;
  transition: var(--toast-transition);
  font-size: 12px;
  padding: 4px 8px;
}

.toast-action-button {
  background: var(--toast-accent);
  color: white;
  font-weight: 500;
  min-width: 60px;
}

.toast-action-button:hover {
  background: var(--toast-accent);
  transform: translateY(-1px);
  opacity: 0.9;
}

.toast-close-button {
  width: 24px;
  height: 24px;
  padding: 0;
}

.toast-close-button:hover {
  background: var(--toast-border);
  color: var(--toast-text);
}

.toast-close-button:active,
.toast-action-button:active {
  transform: scale(0.95);
}

/* Progress Bar */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--toast-border);
  overflow: hidden;
}

.toast-progress-bar {
  width: 100%;
  height: 100%;
  background: var(--toast-accent);
  transform: translateX(-100%);
  animation: toastProgress linear forwards;
}

@keyframes toastProgress {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0%);
  }
}

/* Type-specific progress colors */
.recall-toast.success .toast-progress-bar {
  background: var(--toast-success);
}

.recall-toast.warning .toast-progress-bar {
  background: var(--toast-warning);
}

.recall-toast.error .toast-progress-bar {
  background: var(--toast-error);
}

/* Responsive Design */
@media (max-width: 768px) {
  .recall-toast {
    min-width: 260px;
    max-width: calc(100vw - 32px);
    margin: 0 16px;
  }
  
  .toast-content {
    padding: 12px;
    gap: 8px;
  }
  
  .toast-message {
    font-size: 13px;
  }
  
  .toast-icon {
    font-size: 16px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .recall-toast {
    border-width: 2px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
  }
  
  .toast-action-button,
  .toast-close-button {
    border: 1px solid var(--toast-border);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .recall-toast,
  .toast-action-button,
  .toast-close-button {
    transition: none;
  }
  
  .recall-toast.visible {
    transform: none;
  }
  
  .recall-toast.hidden {
    transform: none;
  }
  
  .toast-progress-bar {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .recall-toast {
    display: none !important;
  }
}

/* Focus Management */
.recall-toast *:focus {
  outline: 2px solid var(--toast-accent);
  outline-offset: 2px;
}

.toast-action-button:focus-visible,
.toast-close-button:focus-visible {
  outline: 2px solid var(--toast-accent);
  outline-offset: 2px;
}

/* Ensure toast doesn't interfere with page interactions */
.recall-toast * {
  box-sizing: border-box;
  user-select: text;
}

.recall-toast button,
.recall-toast input {
  user-select: none;
}

/* Override any conflicting website styles */
.recall-toast,
.recall-toast * {
  all: unset;
}

.recall-toast {
  display: block !important;
  position: fixed !important;
  z-index: 2147483646 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: var(--toast-text) !important;
  background: var(--toast-bg) !important;
  border: 1px solid var(--toast-border) !important;
  border-radius: var(--toast-radius) !important;
  box-shadow: 0 4px 16px var(--toast-shadow) !important;
}

/* Restore necessary styling after reset */
.recall-toast button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
}

/* Animation for stacked toasts */
.recall-toast:nth-child(n+2) {
  transform: translateY(calc(-100% - 12px)) scale(0.95);
  opacity: 0.8;
}

.recall-toast:nth-child(n+3) {
  transform: translateY(calc(-200% - 24px)) scale(0.9);
  opacity: 0.6;
}

.recall-toast:nth-child(n+4) {
  display: none;
}

/* Hover effects */
.recall-toast:hover {
  box-shadow: 0 6px 20px var(--toast-shadow);
  transform: translateY(-2px);
}

.recall-toast:hover:nth-child(n+2) {
  transform: translateY(calc(-100% - 10px)) scale(0.95);
}

/* Custom scrollbar for long messages */
.toast-message {
  max-height: 80px;
  overflow-y: auto;
}

.toast-message::-webkit-scrollbar {
  width: 4px;
}

.toast-message::-webkit-scrollbar-track {
  background: transparent;
}

.toast-message::-webkit-scrollbar-thumb {
  background-color: var(--toast-border);
  border-radius: 2px;
}

.toast-message::-webkit-scrollbar-thumb:hover {
  background-color: var(--toast-text-secondary);
}