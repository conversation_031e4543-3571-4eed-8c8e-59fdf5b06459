/**
 * Reading Time Detection Service for Recall V3.0
 * 
 * Monitors user reading behavior and triggers notifications for AI summaries
 * Integrates with toast notifications and summary panel
 */

interface ReadingDetectorConfig {
  /** Minimum reading time before showing notification (ms) */
  minReadingTime: number;
  /** Page visibility threshold (percentage) */
  visibilityThreshold: number;
  /** Scroll activity threshold */
  scrollThreshold: number;
  /** Mouse activity timeout (ms) */
  mouseActivityTimeout: number;
  /** Enable debug logging */
  debug: boolean;
  /** Minimum content length to consider for reading detection */
  minContentLength: number;
}

export interface ReadingSession {
  /** Session start time */
  startTime: number;
  /** Last activity time */
  lastActivity: number;
  /** Total active reading time */
  activeTime: number;
  /** Whether user is currently active */
  isActive: boolean;
  /** Scroll position when session started */
  initialScrollY: number;
  /** Maximum scroll depth reached */
  maxScrollDepth: number;
  /** Page URL */
  url: string;
  /** Page title */
  title: string;
  /** Content length */
  contentLength: number;
}

interface ReadingDetectorEvents {
  onReadingTimeThresholdReached: (session: ReadingSession) => void;
  onReadingSessionStart: (session: ReadingSession) => void;
  onReadingSessionEnd: (session: ReadingSession) => void;
  onScrollProgress: (progress: number, session: ReadingSession) => void;
}

export class ReadingDetector {
  private config: ReadingDetectorConfig;
  private events: Partial<ReadingDetectorEvents>;
  private currentSession: ReadingSession | null = null;
  private activityTimer: number | null = null;
  private isPageVisible: boolean = true;
  private lastScrollY: number = 0;
  private hasTriggeredNotification: boolean = false;

  // Event listeners
  private boundHandlers: {
    scroll: (event: Event) => void;
    mouseMove: (event: MouseEvent) => void;
    click: (event: MouseEvent) => void;
    keydown: (event: KeyboardEvent) => void;
    visibilityChange: () => void;
    focus: () => void;
    blur: () => void;
    beforeUnload: () => void;
  };

  constructor(
    config: Partial<ReadingDetectorConfig> = {},
    events: Partial<ReadingDetectorEvents> = {}
  ) {
    this.config = {
      minReadingTime: 3 * 60 * 1000, // 3 minutes
      visibilityThreshold: 0.5, // 50% of page must be visible
      scrollThreshold: 100, // 100px scroll to count as activity
      mouseActivityTimeout: 30 * 1000, // 30 seconds
      debug: false,
      minContentLength: 1000, // 1000 characters minimum
      ...config
    };

    this.events = events;

    // Bind event handlers
    this.boundHandlers = {
      scroll: this.handleScroll.bind(this),
      mouseMove: this.handleMouseMove.bind(this),
      click: this.handleClick.bind(this),
      keydown: this.handleKeydown.bind(this),
      visibilityChange: this.handleVisibilityChange.bind(this),
      focus: this.handleFocus.bind(this),
      blur: this.handleBlur.bind(this),
      beforeUnload: this.handleBeforeUnload.bind(this)
    };

    this.log('ReadingDetector initialized', this.config);
  }

  /**
   * Start reading detection
   */
  public start(): void {
    this.log('Starting reading detection');

    // Check if page has enough content to warrant reading detection
    const contentLength = this.getPageContentLength();
    if (contentLength < this.config.minContentLength) {
      this.log('Page content too short, skipping reading detection', { contentLength });
      return;
    }

    // Initialize session
    this.currentSession = {
      startTime: Date.now(),
      lastActivity: Date.now(),
      activeTime: 0,
      isActive: true,
      initialScrollY: window.scrollY,
      maxScrollDepth: window.scrollY,
      url: window.location.href,
      title: document.title,
      contentLength
    };

    // Set up event listeners
    this.addEventListeners();

    // Start activity monitoring
    this.startActivityMonitoring();

    // Trigger session start event
    if (this.events.onReadingSessionStart) {
      this.events.onReadingSessionStart(this.currentSession);
    }

    this.log('Reading session started', this.currentSession);
  }

  /**
   * Stop reading detection
   */
  public stop(): void {
    this.log('Stopping reading detection');

    // Remove event listeners
    this.removeEventListeners();

    // Stop activity monitoring
    this.stopActivityMonitoring();

    // End current session
    if (this.currentSession) {
      this.endCurrentSession();
    }
  }

  /**
   * Get current reading session data
   */
  public getCurrentSession(): ReadingSession | null {
    return this.currentSession;
  }

  /**
   * Check if user has been reading for minimum time
   */
  public hasReachedReadingThreshold(): boolean {
    if (!this.currentSession) return false;
    
    const totalReadingTime = this.currentSession.activeTime + 
      (this.currentSession.isActive ? Date.now() - this.currentSession.lastActivity : 0);
    
    return totalReadingTime >= this.config.minReadingTime;
  }

  /**
   * Get reading progress (0-1)
   */
  public getReadingProgress(): number {
    const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
    if (scrollHeight <= 0) return 1;
    
    const scrollProgress = Math.min(window.scrollY / scrollHeight, 1);
    return Math.max(scrollProgress, 0);
  }

  /**
   * Set up event listeners
   */
  private addEventListeners(): void {
    // Scroll events
    window.addEventListener('scroll', this.boundHandlers.scroll, { passive: true });
    
    // Mouse events
    document.addEventListener('mousemove', this.boundHandlers.mouseMove, { passive: true });
    document.addEventListener('click', this.boundHandlers.click, { passive: true });
    
    // Keyboard events
    document.addEventListener('keydown', this.boundHandlers.keydown, { passive: true });
    
    // Page visibility events
    document.addEventListener('visibilitychange', this.boundHandlers.visibilityChange);
    window.addEventListener('focus', this.boundHandlers.focus);
    window.addEventListener('blur', this.boundHandlers.blur);
    
    // Page unload
    window.addEventListener('beforeunload', this.boundHandlers.beforeUnload);
  }

  /**
   * Remove event listeners
   */
  private removeEventListeners(): void {
    window.removeEventListener('scroll', this.boundHandlers.scroll);
    document.removeEventListener('mousemove', this.boundHandlers.mouseMove);
    document.removeEventListener('click', this.boundHandlers.click);
    document.removeEventListener('keydown', this.boundHandlers.keydown);
    document.removeEventListener('visibilitychange', this.boundHandlers.visibilityChange);
    window.removeEventListener('focus', this.boundHandlers.focus);
    window.removeEventListener('blur', this.boundHandlers.blur);
    window.removeEventListener('beforeunload', this.boundHandlers.beforeUnload);
  }

  /**
   * Handle scroll events
   */
  private handleScroll(_event: Event): void {
    if (!this.currentSession) return;

    const currentScrollY = window.scrollY;
    const scrollDiff = Math.abs(currentScrollY - this.lastScrollY);

    // Update max scroll depth
    this.currentSession.maxScrollDepth = Math.max(
      this.currentSession.maxScrollDepth,
      currentScrollY
    );

    // Check if scroll is significant enough to count as activity
    if (scrollDiff >= this.config.scrollThreshold) {
      this.recordActivity();
      this.lastScrollY = currentScrollY;

      // Trigger scroll progress event
      if (this.events.onScrollProgress) {
        const progress = this.getReadingProgress();
        this.events.onScrollProgress(progress, this.currentSession);
      }
    }
  }

  /**
   * Handle mouse movement
   */
  private handleMouseMove(_event: MouseEvent): void {
    this.recordActivity();
  }

  /**
   * Handle click events
   */
  private handleClick(_event: MouseEvent): void {
    this.recordActivity();
  }

  /**
   * Handle keyboard events
   */
  private handleKeydown(_event: KeyboardEvent): void {
    this.recordActivity();
  }

  /**
   * Handle page visibility changes
   */
  private handleVisibilityChange(): void {
    this.isPageVisible = !document.hidden;
    
    if (this.isPageVisible) {
      this.log('Page became visible');
      this.recordActivity();
    } else {
      this.log('Page became hidden');
      this.pauseActivity();
    }
  }

  /**
   * Handle window focus
   */
  private handleFocus(): void {
    this.log('Window focused');
    this.recordActivity();
  }

  /**
   * Handle window blur
   */
  private handleBlur(): void {
    this.log('Window blurred');
    this.pauseActivity();
  }

  /**
   * Handle page unload
   */
  private handleBeforeUnload(): void {
    this.log('Page unloading');
    this.stop();
  }

  /**
   * Record user activity
   */
  private recordActivity(): void {
    if (!this.currentSession || !this.isPageVisible) return;

    const now = Date.now();
    
    // If user was inactive, add gap to active time
    if (!this.currentSession.isActive) {
      this.currentSession.activeTime += now - this.currentSession.lastActivity;
    }
    
    this.currentSession.lastActivity = now;
    this.currentSession.isActive = true;

    // Check if we've reached the reading threshold
    this.checkReadingThreshold();

    // Reset activity timer
    this.resetActivityTimer();
  }

  /**
   * Pause activity tracking
   */
  private pauseActivity(): void {
    if (!this.currentSession) return;

    const now = Date.now();
    
    if (this.currentSession.isActive) {
      this.currentSession.activeTime += now - this.currentSession.lastActivity;
      this.currentSession.isActive = false;
    }

    this.stopActivityMonitoring();
  }

  /**
   * Start activity monitoring timer
   */
  private startActivityMonitoring(): void {
    this.resetActivityTimer();
  }

  /**
   * Stop activity monitoring timer
   */
  private stopActivityMonitoring(): void {
    if (this.activityTimer) {
      clearTimeout(this.activityTimer);
      this.activityTimer = null;
    }
  }

  /**
   * Reset activity timer
   */
  private resetActivityTimer(): void {
    this.stopActivityMonitoring();
    
    this.activityTimer = window.setTimeout(() => {
      this.log('User activity timeout');
      this.pauseActivity();
    }, this.config.mouseActivityTimeout);
  }

  /**
   * Check if reading threshold has been reached
   */
  private checkReadingThreshold(): void {
    if (!this.currentSession || this.hasTriggeredNotification) return;

    const totalActiveTime = this.currentSession.activeTime + 
      (this.currentSession.isActive ? Date.now() - this.currentSession.lastActivity : 0);

    if (totalActiveTime >= this.config.minReadingTime) {
      this.hasTriggeredNotification = true;
      
      this.log('Reading time threshold reached', { 
        totalActiveTime, 
        threshold: this.config.minReadingTime 
      });

      if (this.events.onReadingTimeThresholdReached) {
        this.events.onReadingTimeThresholdReached(this.currentSession);
      }
    }
  }

  /**
   * End current session
   */
  private endCurrentSession(): void {
    if (!this.currentSession) return;

    // Update final active time
    if (this.currentSession.isActive) {
      this.currentSession.activeTime += Date.now() - this.currentSession.lastActivity;
      this.currentSession.isActive = false;
    }

    this.log('Reading session ended', {
      duration: Date.now() - this.currentSession.startTime,
      activeTime: this.currentSession.activeTime,
      scrollProgress: this.getReadingProgress()
    });

    if (this.events.onReadingSessionEnd) {
      this.events.onReadingSessionEnd(this.currentSession);
    }

    this.currentSession = null;
    this.hasTriggeredNotification = false;
  }

  /**
   * Get page content length
   */
  private getPageContentLength(): number {
    const textContent = document.body?.textContent || '';
    return textContent.replace(/\s+/g, ' ').trim().length;
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[ReadingDetector] ${message}`, data || '');
    }
  }
}

export default ReadingDetector;