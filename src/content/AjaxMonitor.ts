/**
 * AJAX Monitor for Dynamic Content Detection
 * 
 * Intercepts XMLHttpRequest and fetch API calls to detect when new content
 * is loaded dynamically, particularly useful for forum pages, infinite scroll,
 * and SPA applications.
 */

import { ErrorRecoverySystem } from './ErrorRecovery';
import { SafeInterceptor, createSafeXHRInterceptor, createSafeFetchInterceptor } from './SafeInterceptor';

export interface AjaxRequestInfo {
  url: string;
  method: string;
  timestamp: number;
  responseSize?: number;
  contentType?: string;
  isRelevant: boolean;
}

export interface AjaxMonitorConfig {
  /** Enable XMLHttpRequest monitoring */
  enableXHR: boolean;
  /** Enable fetch API monitoring */
  enableFetch: boolean;
  /** Delay after AJAX completion before triggering content check (ms) */
  contentCheckDelay: number;
  /** Minimum response size to consider relevant (bytes) */
  minResponseSize: number;
  /** URL patterns to monitor (empty array = monitor all) */
  urlPatterns: RegExp[];
  /** URL patterns to ignore */
  ignorePatterns: RegExp[];
  /** Enable debug logging */
  debug: boolean;
}

export interface AjaxMonitorEvents {
  onAjaxCompleted: (requestInfo: AjaxRequestInfo) => void;
  onRelevantContentLoaded: (requestInfo: AjaxRequestInfo) => void;
}

export class AjaxMonitor {
  private config: AjaxMonitorConfig;
  private events: Partial<AjaxMonitorEvents>;
  private isInitialized = false;
  
  // Store original methods for restoration
  private originalXHROpen: typeof XMLHttpRequest.prototype.open | null = null;
  private originalXHRSend: typeof XMLHttpRequest.prototype.send | null = null;
  private originalFetch: typeof window.fetch | null = null;
  
  // Track active requests
  private activeRequests = new Set<string>();
  private requestCounter = 0;
  
  // Error recovery and safe interception
  private errorRecovery: ErrorRecoverySystem;
  private safeInterceptor: SafeInterceptor;
  private xhrInterceptorRestore?: () => void;
  private fetchInterceptorRestore?: () => void;

  private static readonly DEFAULT_CONFIG: AjaxMonitorConfig = {
    enableXHR: true,
    enableFetch: true,
    contentCheckDelay: 1500, // 1.5 seconds
    minResponseSize: 100, // 100 bytes
    urlPatterns: [], // Monitor all by default
    ignorePatterns: [
      /\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/i,
      /\/api\/analytics/i,
      /\/api\/tracking/i,
      /\/api\/metrics/i,
      /google-analytics/i,
      /googletagmanager/i,
      /doubleclick/i,
      /facebook\.com\/tr/i,
      /twitter\.com\/i\/jot/i
    ],
    debug: false
  };

  constructor(
    config: Partial<AjaxMonitorConfig> = {},
    events: Partial<AjaxMonitorEvents> = {}
  ) {
    this.config = { ...AjaxMonitor.DEFAULT_CONFIG, ...config };
    this.events = events;
    
    // Initialize error recovery and safe interceptor
    this.errorRecovery = new ErrorRecoverySystem({
      debug: this.config.debug,
      autoRecover: true,
      maxRetries: 3
    });
    this.safeInterceptor = new SafeInterceptor();
    
    this.log('AjaxMonitor initialized', this.config);
  }

  /**
   * Start monitoring AJAX requests
   */
  public async start(): Promise<void> {
    if (this.isInitialized) {
      this.log('AjaxMonitor already started');
      return;
    }

    try {
      if (this.config.enableXHR) {
        await this.interceptXHRSafely();
      }

      if (this.config.enableFetch) {
        await this.interceptFetchSafely();
      }

      this.isInitialized = true;
      this.log('AjaxMonitor started successfully');
    } catch (error) {
      // Try error recovery
      const recovery = await this.errorRecovery.handleError(
        error as Error,
        'AjaxMonitor',
        { start: () => this.start() }
      );
      
      if (!recovery.success) {
        this.log('Failed to start AjaxMonitor even after recovery', error);
        throw error;
      }
    }
  }

  /**
   * Stop monitoring and restore original methods
   */
  public stop(): void {
    if (!this.isInitialized) {
      return;
    }

    try {
      // Restore using safe interceptor if available
      if (this.xhrInterceptorRestore) {
        this.xhrInterceptorRestore();
        this.xhrInterceptorRestore = undefined;
      }
      
      if (this.fetchInterceptorRestore) {
        this.fetchInterceptorRestore();
        this.fetchInterceptorRestore = undefined;
      }
      
      // Fallback to manual restoration
      this.restoreOriginalMethods();
      this.activeRequests.clear();
      this.isInitialized = false;
      this.log('AjaxMonitor stopped');
    } catch (error) {
      this.log('Error stopping AjaxMonitor', error);
    }
  }

  /**
   * Check if monitoring is active
   */
  public isActive(): boolean {
    return this.isInitialized;
  }

  /**
   * Get current configuration
   */
  public getConfig(): AjaxMonitorConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<AjaxMonitorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.log('Configuration updated', this.config);
  }

  /**
   * Safely intercept XMLHttpRequest with error recovery
   */
  private async interceptXHRSafely(): Promise<void> {
    const result = createSafeXHRInterceptor((xhr, method, url) => {
      const requestInfo = {
        method: method.toUpperCase(),
        url: url,
        timestamp: Date.now(),
        id: `xhr_${++this.requestCounter}`
      };
      
      this.activeRequests.add(requestInfo.id);
      
      // Set up completion handler
      const originalOnReadyStateChange = xhr.onreadystatechange;
      
      xhr.onreadystatechange = (event) => {
        if (originalOnReadyStateChange) {
          originalOnReadyStateChange.call(xhr, event);
        }
        
        if (xhr.readyState === XMLHttpRequest.DONE) {
          this.handleXHRCompletion(xhr, requestInfo);
        }
      };
    });
    
    if (result.success) {
      this.xhrInterceptorRestore = result.restore;
      this.log('XMLHttpRequest interception setup complete (safe mode)');
    } else {
      // Fallback to original method
      this.log('Safe XHR interception failed, using fallback');
      this.interceptXHR();
    }
  }
  
  /**
   * Original XMLHttpRequest interception (fallback)
   */
  private interceptXHR(): void {
    if (typeof XMLHttpRequest === 'undefined') {
      this.log('XMLHttpRequest not available in this environment');
      return;
    }

    // Store original methods
    this.originalXHROpen = XMLHttpRequest.prototype.open;
    this.originalXHRSend = XMLHttpRequest.prototype.send;

    const monitor = this;

    // Override XMLHttpRequest.open
    XMLHttpRequest.prototype.open = function(
      method: string,
      url: string | URL,
      async?: boolean,
      user?: string | null,
      password?: string | null
    ) {
      const urlString = typeof url === 'string' ? url : url.toString();
      
      // Store request info on XHR instance
      (this as any)._recallRequestInfo = {
        method: method.toUpperCase(),
        url: urlString,
        timestamp: Date.now(),
        id: `xhr_${++monitor.requestCounter}`
      };

      // Call original method
      return monitor.originalXHROpen!.call(this, method, url, async ?? true, user, password);
    };

    // Override XMLHttpRequest.send
    XMLHttpRequest.prototype.send = function(body?: XMLHttpRequestBodyInit | null) {
      const requestInfo = (this as any)._recallRequestInfo;
      
      if (requestInfo) {
        monitor.activeRequests.add(requestInfo.id);
        
        // Set up completion handler
        const originalOnReadyStateChange = this.onreadystatechange;
        
        this.onreadystatechange = function(event) {
          // Call original handler first
          if (originalOnReadyStateChange) {
            originalOnReadyStateChange.call(this, event);
          }
          
          // Handle completion
          if (this.readyState === XMLHttpRequest.DONE) {
            monitor.handleXHRCompletion(this, requestInfo);
          }
        };
      }

      // Call original method
      return monitor.originalXHRSend!.call(this, body);
    };

    this.log('XMLHttpRequest interception setup complete');
  }

  /**
   * Safely intercept fetch API with error recovery
   */
  private async interceptFetchSafely(): Promise<void> {
    const result = createSafeFetchInterceptor(
      (url, options) => {
        const requestInfo = {
          method: options?.method?.toUpperCase() || 'GET',
          url: url,
          timestamp: Date.now(),
          id: `fetch_${++this.requestCounter}`
        };
        
        this.activeRequests.add(requestInfo.id);
      },
      (response, requestInfo) => {
        // Handle fetch completion
        this.handleFetchCompletion(response, requestInfo);
      }
    );
    
    if (result.success) {
      this.fetchInterceptorRestore = result.restore;
      this.log('Fetch API interception setup complete (safe mode)');
    } else {
      // Fallback to original method
      this.log('Safe Fetch interception failed, using fallback');
      this.interceptFetch();
    }
  }
  
  /**
   * Original fetch API interception (fallback)
   */
  private interceptFetch(): void {
    if (typeof window.fetch === 'undefined') {
      this.log('Fetch API not available in this environment');
      return;
    }

    // Store original fetch
    this.originalFetch = window.fetch;
    const monitor = this;

    // Override fetch
    window.fetch = function(
      input: RequestInfo | URL,
      init?: RequestInit
    ): Promise<Response> {
      const requestInfo = {
        method: init?.method?.toUpperCase() || 'GET',
        url: typeof input === 'string' ? input : input.toString(),
        timestamp: Date.now(),
        id: `fetch_${++monitor.requestCounter}`
      };

      monitor.activeRequests.add(requestInfo.id);

      // Call original fetch and handle response
      return monitor.originalFetch!(input, init)
        .then(response => {
          monitor.handleFetchCompletion(response.clone(), requestInfo);
          return response;
        })
        .catch(error => {
          monitor.activeRequests.delete(requestInfo.id);
          monitor.log('Fetch request failed', { requestInfo, error });
          throw error;
        });
    };

    this.log('Fetch API interception setup complete');
  }

  /**
   * Handle XHR request completion
   */
  private handleXHRCompletion(xhr: XMLHttpRequest, requestInfo: any): void {
    this.activeRequests.delete(requestInfo.id);

    try {
      const ajaxInfo: AjaxRequestInfo = {
        url: requestInfo.url,
        method: requestInfo.method,
        timestamp: requestInfo.timestamp,
        responseSize: xhr.responseText?.length || 0,
        contentType: xhr.getResponseHeader('content-type') || undefined,
        isRelevant: false
      };

      // Determine if this request is relevant
      ajaxInfo.isRelevant = this.isRequestRelevant(ajaxInfo);

      this.log('XHR completed', ajaxInfo);

      // Notify listeners
      if (this.events.onAjaxCompleted) {
        this.events.onAjaxCompleted(ajaxInfo);
      }

      if (ajaxInfo.isRelevant && this.events.onRelevantContentLoaded) {
        // Delay the content check to allow DOM updates
        setTimeout(() => {
          if (this.events.onRelevantContentLoaded) {
            this.events.onRelevantContentLoaded(ajaxInfo);
          }
        }, this.config.contentCheckDelay);
      }

    } catch (error) {
      this.log('Error handling XHR completion', error);
    }
  }

  /**
   * Handle fetch request completion
   */
  private handleFetchCompletion(response: Response, requestInfo: any): void {
    this.activeRequests.delete(requestInfo.id);

    try {
      // Try to get response size (approximate)
      const contentLength = response.headers.get('content-length');
      
      const ajaxInfo: AjaxRequestInfo = {
        url: requestInfo.url,
        method: requestInfo.method,
        timestamp: requestInfo.timestamp,
        responseSize: contentLength ? parseInt(contentLength, 10) : undefined,
        contentType: response.headers.get('content-type') || undefined,
        isRelevant: false
      };

      // Determine if this request is relevant
      ajaxInfo.isRelevant = this.isRequestRelevant(ajaxInfo);

      this.log('Fetch completed', ajaxInfo);

      // Notify listeners
      if (this.events.onAjaxCompleted) {
        this.events.onAjaxCompleted(ajaxInfo);
      }

      if (ajaxInfo.isRelevant && this.events.onRelevantContentLoaded) {
        // Delay the content check to allow DOM updates
        setTimeout(() => {
          if (this.events.onRelevantContentLoaded) {
            this.events.onRelevantContentLoaded(ajaxInfo);
          }
        }, this.config.contentCheckDelay);
      }

    } catch (error) {
      this.log('Error handling fetch completion', error);
    }
  }

  /**
   * Determine if a request is relevant for content monitoring
   */
  private isRequestRelevant(requestInfo: AjaxRequestInfo): boolean {
    // Check ignore patterns first
    for (const pattern of this.config.ignorePatterns) {
      if (pattern.test(requestInfo.url)) {
        return false;
      }
    }

    // Check if response size meets minimum threshold
    if (requestInfo.responseSize !== undefined && 
        requestInfo.responseSize < this.config.minResponseSize) {
      return false;
    }

    // Check content type (prefer HTML/JSON/XML)
    if (requestInfo.contentType) {
      const contentType = requestInfo.contentType.toLowerCase();
      const relevantTypes = ['text/html', 'application/json', 'application/xml', 'text/xml'];
      const isRelevantType = relevantTypes.some(type => contentType.includes(type));
      
      if (!isRelevantType) {
        return false;
      }
    }

    // Check URL patterns (if specified)
    if (this.config.urlPatterns.length > 0) {
      const matchesPattern = this.config.urlPatterns.some(pattern => 
        pattern.test(requestInfo.url)
      );
      if (!matchesPattern) {
        return false;
      }
    }

    return true;
  }

  /**
   * Restore original methods
   */
  private restoreOriginalMethods(): void {
    if (this.originalXHROpen && this.originalXHRSend) {
      XMLHttpRequest.prototype.open = this.originalXHROpen;
      XMLHttpRequest.prototype.send = this.originalXHRSend;
      this.originalXHROpen = null;
      this.originalXHRSend = null;
    }

    if (this.originalFetch) {
      window.fetch = this.originalFetch;
      this.originalFetch = null;
    }

    this.log('Original methods restored');
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[AjaxMonitor] ${message}`, data || '');
    }
  }

  /**
   * Get monitoring statistics
   */
  public getStats(): {
    isActive: boolean;
    activeRequests: number;
    totalRequests: number;
    errorStats?: any;
    interceptorStats?: any;
  } {
    return {
      isActive: this.isInitialized,
      activeRequests: this.activeRequests.size,
      totalRequests: this.requestCounter,
      errorStats: this.errorRecovery.getErrorStats(),
      interceptorStats: this.safeInterceptor.getStats()
    };
  }
  
  /**
   * Get system health status
   */
  public getHealthStatus(): {
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  } {
    return this.errorRecovery.checkHealth();
  }
  
  /**
   * Export error report for debugging
   */
  public exportErrorReport(): string {
    return this.errorRecovery.exportErrorReport();
  }
}