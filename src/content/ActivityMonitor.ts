/**
 * Activity Monitor for Recall V3.0
 * 
 * Provides low-level activity detection utilities
 * Used by ReadingDetector for comprehensive user behavior monitoring
 */

export interface ActivityMetrics {
  /** Last recorded activity timestamp */
  lastActivityTime: number;
  /** Mouse movement count in current window */
  mouseMovements: number;
  /** Keyboard events count in current window */
  keyboardEvents: number;
  /** Scroll events count in current window */
  scrollEvents: number;
  /** Click events count in current window */
  clickEvents: number;
  /** Total activity score */
  activityScore: number;
}

export interface ActivityWindow {
  /** Window start time */
  startTime: number;
  /** Window duration (ms) */
  duration: number;
  /** Activity metrics for this window */
  metrics: ActivityMetrics;
}

export interface ActivityMonitorConfig {
  /** Activity window duration (ms) */
  windowDuration: number;
  /** Mouse movement threshold for counting as activity */
  mouseMoveThreshold: number;
  /** Debounce time for similar events (ms) */
  eventDebounceTime: number;
  /** Enable detailed activity logging */
  enableLogging: boolean;
}

export class ActivityMonitor {
  private config: ActivityMonitorConfig;
  private currentWindow: ActivityWindow | null = null;
  private activityHistory: ActivityWindow[] = [];
  private lastEventTime: Map<string, number> = new Map();
  private lastMouseCoords: { x: number; y: number } | null = null;
  private isMonitoring: boolean = false;
  private windowTimer: number | null = null;

  // Bound event handlers to enable proper cleanup
  private boundHandleMouseMove: ((event: MouseEvent) => void) | null = null;
  private boundHandleClick: ((event: MouseEvent) => void) | null = null;
  private boundHandleKeydown: ((event: KeyboardEvent) => void) | null = null;
  private boundHandleScroll: ((event: Event) => void) | null = null;

  constructor(config: Partial<ActivityMonitorConfig> = {}) {
    this.config = {
      windowDuration: 30 * 1000, // 30 seconds
      mouseMoveThreshold: 5, // 5px minimum movement
      eventDebounceTime: 100, // 100ms debounce
      enableLogging: false,
      ...config
    };

    // Bind event handlers once in constructor
    this.boundHandleMouseMove = this.handleMouseMove.bind(this);
    this.boundHandleClick = this.handleClick.bind(this);
    this.boundHandleKeydown = this.handleKeydown.bind(this);
    this.boundHandleScroll = this.handleScroll.bind(this);

    this.log('ActivityMonitor initialized', this.config);
  }

  /**
   * Start monitoring user activity
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      this.log('Already monitoring activity');
      return;
    }

    this.isMonitoring = true;
    this.initializeNewWindow();
    this.setupEventListeners();
    this.startWindowTimer();

    this.log('Activity monitoring started');
  }

  /**
   * Stop monitoring user activity
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    this.removeEventListeners();
    this.stopWindowTimer();
    this.finalizeCurrentWindow();

    this.log('Activity monitoring stopped');
  }

  /**
   * Get current activity metrics
   */
  public getCurrentMetrics(): ActivityMetrics | null {
    return this.currentWindow?.metrics || null;
  }

  /**
   * Get activity history
   */
  public getActivityHistory(): ActivityWindow[] {
    return [...this.activityHistory];
  }

  /**
   * Calculate activity score for a time period
   */
  public calculateActivityScore(timeWindow: number): number {
    const now = Date.now();
    const cutoffTime = now - timeWindow;

    const relevantWindows = this.activityHistory.filter(
      window => window.startTime >= cutoffTime
    );

    if (this.currentWindow && this.currentWindow.startTime >= cutoffTime) {
      relevantWindows.push(this.currentWindow);
    }

    if (relevantWindows.length === 0) return 0;

    const totalScore = relevantWindows.reduce(
      (sum, window) => sum + window.metrics.activityScore, 
      0
    );

    return totalScore / relevantWindows.length;
  }

  /**
   * Check if user is currently active (based on recent activity)
   */
  public isUserActive(threshold: number = 1000): boolean {
    const currentMetrics = this.getCurrentMetrics();
    if (!currentMetrics) return false;

    const timeSinceLastActivity = Date.now() - currentMetrics.lastActivityTime;
    return timeSinceLastActivity < threshold;
  }

  /**
   * Reset activity tracking
   */
  public reset(): void {
    this.stopMonitoring();
    this.activityHistory = [];
    this.lastEventTime.clear();
    this.lastMouseCoords = null;
    this.currentWindow = null;

    // Clear bound handlers to prevent memory leaks
    this.boundHandleMouseMove = null;
    this.boundHandleClick = null;
    this.boundHandleKeydown = null;
    this.boundHandleScroll = null;

    this.log('Activity monitor reset');
  }

  /**
   * Initialize a new activity window
   */
  private initializeNewWindow(): void {
    this.finalizeCurrentWindow();

    this.currentWindow = {
      startTime: Date.now(),
      duration: this.config.windowDuration,
      metrics: {
        lastActivityTime: Date.now(),
        mouseMovements: 0,
        keyboardEvents: 0,
        scrollEvents: 0,
        clickEvents: 0,
        activityScore: 0
      }
    };

    this.log('New activity window initialized', this.currentWindow);
  }

  /**
   * Finalize current window and add to history
   */
  private finalizeCurrentWindow(): void {
    if (!this.currentWindow) return;

    // Calculate final activity score
    this.updateActivityScore();

    // Add to history
    this.activityHistory.push({ ...this.currentWindow });

    // Keep only last 100 windows to prevent memory growth
    if (this.activityHistory.length > 100) {
      this.activityHistory = this.activityHistory.slice(-100);
    }

    this.log('Activity window finalized', {
      duration: Date.now() - this.currentWindow.startTime,
      score: this.currentWindow.metrics.activityScore
    });
  }

  /**
   * Set up event listeners for activity detection
   */
  private setupEventListeners(): void {
    if (this.boundHandleMouseMove) {
      document.addEventListener('mousemove', this.boundHandleMouseMove, { passive: true });
    }
    if (this.boundHandleClick) {
      document.addEventListener('click', this.boundHandleClick, { passive: true });
    }
    if (this.boundHandleKeydown) {
      document.addEventListener('keydown', this.boundHandleKeydown, { passive: true });
    }
    if (this.boundHandleScroll) {
      window.addEventListener('scroll', this.boundHandleScroll, { passive: true });
    }
  }

  /**
   * Remove event listeners
   */
  private removeEventListeners(): void {
    if (this.boundHandleMouseMove) {
      document.removeEventListener('mousemove', this.boundHandleMouseMove);
    }
    if (this.boundHandleClick) {
      document.removeEventListener('click', this.boundHandleClick);
    }
    if (this.boundHandleKeydown) {
      document.removeEventListener('keydown', this.boundHandleKeydown);
    }
    if (this.boundHandleScroll) {
      window.removeEventListener('scroll', this.boundHandleScroll);
    }
  }

  /**
   * Start window rotation timer
   */
  private startWindowTimer(): void {
    this.stopWindowTimer();
    
    this.windowTimer = window.setTimeout(() => {
      this.initializeNewWindow();
      this.startWindowTimer(); // Set up next window
    }, this.config.windowDuration);
  }

  /**
   * Stop window rotation timer
   */
  private stopWindowTimer(): void {
    if (this.windowTimer) {
      clearTimeout(this.windowTimer);
      this.windowTimer = null;
    }
  }

  /**
   * Handle mouse movement events
   */
  private handleMouseMove(event: MouseEvent): void {
    if (!this.isValidEvent('mousemove')) return;

    // Check if movement is significant enough
    if (this.lastMouseCoords) {
      const distance = Math.sqrt(
        Math.pow(event.clientX - this.lastMouseCoords.x, 2) + 
        Math.pow(event.clientY - this.lastMouseCoords.y, 2)
      );

      if (distance < this.config.mouseMoveThreshold) return;
    }

    this.recordActivity('mousemove');
    this.lastMouseCoords = { x: event.clientX, y: event.clientY };
    
    if (this.currentWindow) {
      this.currentWindow.metrics.mouseMovements++;
    }
  }

  /**
   * Handle click events
   */
  private handleClick(_event: MouseEvent): void {
    if (!this.isValidEvent('click')) return;

    this.recordActivity('click');
    
    if (this.currentWindow) {
      this.currentWindow.metrics.clickEvents++;
    }
  }

  /**
   * Handle keyboard events
   */
  private handleKeydown(_event: KeyboardEvent): void {
    if (!this.isValidEvent('keydown')) return;

    this.recordActivity('keydown');
    
    if (this.currentWindow) {
      this.currentWindow.metrics.keyboardEvents++;
    }
  }

  /**
   * Handle scroll events
   */
  private handleScroll(_event: Event): void {
    if (!this.isValidEvent('scroll')) return;

    this.recordActivity('scroll');
    
    if (this.currentWindow) {
      this.currentWindow.metrics.scrollEvents++;
    }
  }

  /**
   * Check if event should be processed (debouncing)
   */
  private isValidEvent(eventType: string): boolean {
    const now = Date.now();
    const lastTime = this.lastEventTime.get(eventType) || 0;
    
    if (now - lastTime < this.config.eventDebounceTime) {
      return false;
    }

    this.lastEventTime.set(eventType, now);
    return true;
  }

  /**
   * Record activity and update metrics
   */
  private recordActivity(eventType: string): void {
    if (!this.currentWindow) return;

    const now = Date.now();
    this.currentWindow.metrics.lastActivityTime = now;
    
    this.updateActivityScore();
    
    this.log(`Activity recorded: ${eventType}`, {
      score: this.currentWindow.metrics.activityScore,
      time: now
    });
  }

  /**
   * Update activity score based on current metrics
   */
  private updateActivityScore(): void {
    if (!this.currentWindow) return;

    const metrics = this.currentWindow.metrics;
    
    // Calculate weighted activity score
    const mouseWeight = 0.3;
    const clickWeight = 0.4;
    const keyboardWeight = 0.4;
    const scrollWeight = 0.2;

    const score = (
      metrics.mouseMovements * mouseWeight +
      metrics.clickEvents * clickWeight +
      metrics.keyboardEvents * keyboardWeight +
      metrics.scrollEvents * scrollWeight
    );

    // Apply time decay (more recent activity has higher weight)
    const timeSinceStart = Date.now() - this.currentWindow.startTime;
    const decayFactor = Math.max(0.1, 1 - (timeSinceStart / this.config.windowDuration));

    metrics.activityScore = score * decayFactor;
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      console.log(`[ActivityMonitor] ${message}`, data || '');
    }
  }
}

export default ActivityMonitor;
