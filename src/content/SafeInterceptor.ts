/**
 * Safe Interceptor for AJAX Methods
 * 
 * Provides safe method interception with automatic fallback
 * and error boundary protection.
 */

export interface InterceptionConfig {
  /** Target object (e.g., XMLHttpRequest.prototype) */
  target: any;
  /** Method name to intercept */
  methodName: string;
  /** Interceptor function */
  interceptor: Function;
  /** Fallback function if interception fails */
  fallback?: Function;
  /** Whether to preserve original method */
  preserveOriginal?: boolean;
  /** Error handler */
  onError?: (error: Error) => void;
}

export interface InterceptionResult {
  success: boolean;
  method: 'intercepted' | 'fallback' | 'failed';
  error?: Error;
  restore?: () => void;
}

export class SafeInterceptor {
  private interceptedMethods: Map<string, {
    original: Function;
    target: any;
    methodName: string;
  }> = new Map();
  
  private errorCount = 0;
  private readonly maxErrors = 10;

  /**
   * Safely intercept a method with error boundaries
   */
  public intercept(config: InterceptionConfig): InterceptionResult {
    const key = this.getMethodKey(config.target, config.methodName);
    
    try {
      // Check if already intercepted
      if (this.interceptedMethods.has(key)) {
        console.warn(`Method ${config.methodName} already intercepted`);
        return { success: false, method: 'failed', error: new Error('Already intercepted') };
      }

      // Validate target and method
      if (!config.target || typeof config.target[config.methodName] !== 'function') {
        throw new Error(`Invalid target method: ${config.methodName}`);
      }

      // Store original method
      const original = config.target[config.methodName];
      this.interceptedMethods.set(key, {
        original,
        target: config.target,
        methodName: config.methodName
      });

      // Create safe wrapper
      const safeWrapper = this.createSafeWrapper(
        original,
        config.interceptor,
        config.fallback,
        config.onError
      );

      // Apply interception
      config.target[config.methodName] = safeWrapper;

      // Create restore function
      const restore = () => this.restore(key);

      return {
        success: true,
        method: 'intercepted',
        restore
      };
    } catch (error) {
      // Try fallback if available
      if (config.fallback) {
        try {
          config.fallback();
          return { success: true, method: 'fallback' };
        } catch (fallbackError) {
          return { 
            success: false, 
            method: 'failed', 
            error: fallbackError as Error 
          };
        }
      }

      return { 
        success: false, 
        method: 'failed', 
        error: error as Error 
      };
    }
  }

  /**
   * Create a safe wrapper with error boundaries
   */
  private createSafeWrapper(
    original: Function,
    interceptor: Function,
    fallback?: Function,
    onError?: (error: Error) => void
  ): Function {
    const self = this;
    
    return function(this: any, ...args: any[]) {
      try {
        // Reset error count on successful calls
        if (self.errorCount > 0) {
          self.errorCount = Math.max(0, self.errorCount - 1);
        }

        // Call interceptor
        return interceptor.call(this, original, ...args);
      } catch (error) {
        self.errorCount++;
        
        // Log error
        console.error('Interceptor error:', error);
        
        // Call error handler if provided
        if (onError) {
          try {
            onError(error as Error);
          } catch (handlerError) {
            console.error('Error handler failed:', handlerError);
          }
        }

        // Check if we should disable interception
        if (self.errorCount >= self.maxErrors) {
          console.error('Too many interceptor errors, falling back to original');
          self.restoreAll();
        }

        // Try fallback
        if (fallback) {
          try {
            return fallback.call(this, ...args);
          } catch (fallbackError) {
            console.error('Fallback failed:', fallbackError);
          }
        }

        // Call original method as last resort
        return original.apply(this, args);
      }
    };
  }

  /**
   * Restore a specific intercepted method
   */
  public restore(key: string): boolean {
    const intercepted = this.interceptedMethods.get(key);
    
    if (!intercepted) {
      return false;
    }

    try {
      intercepted.target[intercepted.methodName] = intercepted.original;
      this.interceptedMethods.delete(key);
      return true;
    } catch (error) {
      console.error('Failed to restore method:', error);
      return false;
    }
  }

  /**
   * Restore all intercepted methods
   */
  public restoreAll(): void {
    const keys = Array.from(this.interceptedMethods.keys());
    
    keys.forEach(key => {
      this.restore(key);
    });
    
    this.errorCount = 0;
  }

  /**
   * Get unique key for method
   */
  private getMethodKey(target: any, methodName: string): string {
    const targetName = target.constructor?.name || 'Unknown';
    return `${targetName}.${methodName}`;
  }

  /**
   * Check if a method is currently intercepted
   */
  public isIntercepted(target: any, methodName: string): boolean {
    const key = this.getMethodKey(target, methodName);
    return this.interceptedMethods.has(key);
  }

  /**
   * Get interception statistics
   */
  public getStats(): {
    interceptedCount: number;
    errorCount: number;
    methods: string[];
  } {
    return {
      interceptedCount: this.interceptedMethods.size,
      errorCount: this.errorCount,
      methods: Array.from(this.interceptedMethods.keys())
    };
  }
}

/**
 * Helper function to create XHR interceptor with error handling
 */
export function createSafeXHRInterceptor(
  onRequest: (xhr: XMLHttpRequest, method: string, url: string) => void
): InterceptionResult {
  const interceptor = new SafeInterceptor();
  
  // Intercept open method
  const openResult = interceptor.intercept({
    target: XMLHttpRequest.prototype,
    methodName: 'open',
    interceptor: function(original: Function, ...args: any[]) {
      const [method, url] = args;
      
      // Store request info
      (this as any)._requestInfo = { method, url, timestamp: Date.now() };
      
      // Call original
      return original.apply(this, args);
    },
    onError: (error) => {
      console.error('XHR open interception error:', error);
    }
  });

  // Intercept send method
  const sendResult = interceptor.intercept({
    target: XMLHttpRequest.prototype,
    methodName: 'send',
    interceptor: function(original: Function, ...args: any[]) {
      // 'this' context is the XMLHttpRequest instance when called
      const xhr = this as any;
      const requestInfo = xhr._requestInfo;
      
      if (requestInfo) {
        onRequest(xhr, requestInfo.method, requestInfo.url);
      }
      
      // Set up response handlers
      const originalOnload = xhr.onload;
      xhr.onload = function(event: ProgressEvent) {
        // Process response
        if (originalOnload) {
          originalOnload.call(xhr, event);
        }
      };
      
      // Call original
      return original.apply(this, args);
    },
    onError: (error) => {
      console.error('XHR send interception error:', error);
    }
  });

  return {
    success: openResult.success && sendResult.success,
    method: openResult.success && sendResult.success ? 'intercepted' : 'failed',
    restore: () => {
      openResult.restore?.();
      sendResult.restore?.();
    }
  };
}

/**
 * Helper function to create Fetch interceptor with error handling
 */
export function createSafeFetchInterceptor(
  onRequest: (url: string, options?: RequestInit) => void,
  onCompletion?: (response: Response, requestInfo: any) => void
): InterceptionResult {
  const interceptor = new SafeInterceptor();
  
  return interceptor.intercept({
    target: window,
    methodName: 'fetch',
    interceptor: function(original: typeof fetch, ...args: any[]) {
      const [url, options] = args;
      const requestInfo = {
        url: url.toString(),
        method: options?.method || 'GET',
        timestamp: Date.now()
      };
      
      // Notify about request
      onRequest(url.toString(), options);
      
      // Call original and process response - ensure args are properly spread
      const fetchArgs = args as Parameters<typeof fetch>;
      return original.apply(window, fetchArgs).then(response => {
        // Call completion handler if provided
        if (onCompletion) {
          try {
            onCompletion(response.clone(), requestInfo);
          } catch (callbackError) {
            console.warn('Fetch completion callback failed:', callbackError);
          }
        }
        
        return response;
      }).catch(error => {
        console.error('Fetch request failed:', error);
        throw error;
      });
    },
    fallback: () => {
      console.warn('Fetch interception failed, using fallback');
      // Fallback logic here
    },
    onError: (error) => {
      console.error('Fetch interception error:', error);
    }
  });
}