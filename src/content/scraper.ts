/**
 * 页面内容抓取模块
 * 
 * 使用 Mozilla Readability.js 从页面 DOM 中提取主要文本内容
 */

import { Readability } from '@mozilla/readability';
import { cleanContent, cleanTitle, shouldIndexUrl } from '../models';

/**
 * 提取结果接口
 */
export interface ExtractedContent {
  /** 页面标题 */
  title: string;
  /** 页面正文内容 */
  content: string;
  /** 页面URL */
  url: string;
  /** 提取是否成功 */
  success: boolean;
  /** 错误信息（如果有） */
  error?: string;
  /** 内容长度 */
  contentLength: number;
  /** 是否为有效内容 */
  isValidContent: boolean;
}

/**
 * 抓取配置选项
 */
export interface ScrapingOptions {
  /** 最小内容长度 */
  minContentLength?: number;
  /** 最大内容长度 */
  maxContentLength?: number;
  /** 是否包含图片信息 */
  includeImages?: boolean;
  /** 是否严格模式（更严格的内容验证） */
  strictMode?: boolean;
  /** 黑名单域名 */
  blacklistDomains?: string[];
  /** 调试模式 */
  debug?: boolean;
  /** 论坛平台类型（用于特定优化） */
  forumPlatform?: string;
}

/**
 * 默认配置
 */
const DEFAULT_OPTIONS: Required<ScrapingOptions> = {
  minContentLength: 100,
  maxContentLength: 50000,
  includeImages: false,
  strictMode: false,
  blacklistDomains: [],
  debug: false,
  forumPlatform: ''
};

/**
 * 从当前页面提取内容
 * 
 * @param options 抓取配置选项
 * @returns 提取的内容
 */
export function extractPageContent(options: ScrapingOptions = {}): ExtractedContent {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const url = window.location.href;

  // 基本信息
  const result: ExtractedContent = {
    title: '',
    content: '',
    url,
    success: false,
    contentLength: 0,
    isValidContent: false
  };

  try {
    // 检查是否应该索引此URL（调试模式下允许本地文件和localhost）
    const isLocalFile = url.startsWith('file://');
    const isLocalhost = url.includes('localhost') || url.includes('127.0.0.1');
    const allowLocalInDebug = (isLocalFile || isLocalhost) && (config as any).debug;

    if (!shouldIndexUrl(url, config.blacklistDomains) && !allowLocalInDebug) {
      result.error = 'URL is in blacklist or not indexable';
      return result;
    }

    // 获取页面标题
    result.title = extractTitle();

    // 检查页面是否有足够的内容
    if (!hasValidContent()) {
      result.error = 'Page does not contain sufficient content';
      return result;
    }

    // 使用 Readability 提取内容
    const readabilityResult = extractWithReadability();
    
    if (readabilityResult.success) {
      result.content = readabilityResult.content;
      result.contentLength = result.content.length;
      result.success = true;
    } else {
      // 降级到基本文本提取
      const fallbackResult = extractBasicText();
      result.content = fallbackResult.content;
      result.contentLength = result.content.length;
      result.success = fallbackResult.success;
      result.error = readabilityResult.error;
    }

    // 验证内容质量
    result.isValidContent = validateContent(result.content, config);

    // 清理内容
    if (result.success && result.content) {
      result.title = cleanTitle(result.title);
      result.content = cleanContent(result.content);
      result.contentLength = result.content.length;
    }

  } catch (error) {
    result.error = `Content extraction failed: ${(error as Error).message}`;
    console.error('Content extraction error:', error);
  }

  return result;
}

/**
 * 提取页面标题
 */
function extractTitle(): string {
  // 优先级：meta title > h1 > document.title
  const metaTitle = document.querySelector('meta[property="og:title"]')?.getAttribute('content') ||
                   document.querySelector('meta[name="title"]')?.getAttribute('content');
  
  if (metaTitle && metaTitle.trim()) {
    return metaTitle.trim();
  }

  const h1Element = document.querySelector('h1');
  if (h1Element && h1Element.textContent && h1Element.textContent.trim()) {
    return h1Element.textContent.trim();
  }

  return document.title || 'Untitled Page';
}

/**
 * 检查页面是否有有效内容
 */
function hasValidContent(): boolean {
  // 检查页面是否有足够的文本内容
  const bodyText = document.body?.textContent || '';
  const textLength = bodyText.replace(/\s+/g, ' ').trim().length;
  
  // 检查是否有主要内容元素
  const contentSelectors = [
    'article', 'main', '[role="main"]', '.content', '.post', '.entry',
    '.article', '.story', '.text', '.body', '.content-body'
  ];
  
  const hasContentElements = contentSelectors.some(selector => 
    document.querySelector(selector)
  );

  return textLength > 50 || hasContentElements;
}

/**
 * 使用 Readability 提取内容
 */
function extractWithReadability(): { success: boolean; content: string; error?: string } {
  try {
    // 克隆文档以避免修改原始DOM
    const documentClone = document.cloneNode(true) as Document;
    
    // 创建 Readability 实例
    const reader = new Readability(documentClone, {
      debug: false,
      maxElemsToParse: 0, // 不限制解析元素数量
      nbTopCandidates: 5,
      charThreshold: 500,
      classesToPreserve: ['highlight', 'code', 'pre']
    });

    // 解析内容
    const article = reader.parse();
    
    if (article && article.textContent) {
      return {
        success: true,
        content: article.textContent
      };
    } else {
      return {
        success: false,
        content: '',
        error: 'Readability failed to extract content'
      };
    }

  } catch (error) {
    return {
      success: false,
      content: '',
      error: `Readability error: ${(error as Error).message}`
    };
  }
}

/**
 * 基本文本提取（降级方案）
 */
function extractBasicText(): { success: boolean; content: string } {
  try {
    // 移除不需要的元素
    const elementsToRemove = [
      'script', 'style', 'nav', 'header', 'footer', 'aside',
      '.navigation', '.menu', '.sidebar', '.ads', '.advertisement',
      '[role="navigation"]', '[role="banner"]', '[role="contentinfo"]'
    ];

    const clone = document.body.cloneNode(true) as HTMLElement;
    
    elementsToRemove.forEach(selector => {
      const elements = clone.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    // 提取文本内容
    const textContent = clone.textContent || '';
    
    return {
      success: textContent.length > 0,
      content: textContent
    };

  } catch (error) {
    console.error('Basic text extraction failed:', error);
    return {
      success: false,
      content: ''
    };
  }
}

/**
 * 验证内容质量
 */
function validateContent(content: string, config: Required<ScrapingOptions>): boolean {
  if (!content || typeof content !== 'string') {
    return false;
  }

  const trimmedContent = content.trim();
  
  // 检查长度
  if (trimmedContent.length < config.minContentLength) {
    return false;
  }

  if (trimmedContent.length > config.maxContentLength) {
    return false;
  }

  // 严格模式下的额外检查
  if (config.strictMode) {
    // 检查是否主要是重复字符
    const uniqueChars = new Set(trimmedContent.toLowerCase()).size;
    if (uniqueChars < 10) {
      return false;
    }

    // 检查是否有足够的单词
    const words = trimmedContent.split(/\s+/).filter(word => word.length > 2);
    if (words.length < 20) {
      return false;
    }

    // 检查是否包含常见的无意义内容
    const spamPatterns = [
      /^(loading|please wait|error|404|not found)/i,
      /^(cookie|privacy policy|terms of service)/i,
      /^(advertisement|sponsored|promoted)/i
    ];

    if (spamPatterns.some(pattern => pattern.test(trimmedContent))) {
      return false;
    }
  }

  return true;
}

/**
 * 检查页面是否为单页应用
 */
export function isSinglePageApp(): boolean {
  // 检查常见的SPA框架标识
  const spaIndicators = [
    () => !!(window as any).React,
    () => !!(window as any).Vue,
    () => !!(window as any).angular,
    () => !!(window as any).Angular,
    () => document.querySelector('[ng-app], [data-ng-app], [ng-controller]'),
    () => document.querySelector('[data-reactroot], #root, #app'),
    () => document.querySelector('script[src*="react"], script[src*="vue"], script[src*="angular"]'),
    () => document.body.innerHTML.includes('ng-') || document.body.innerHTML.includes('v-'),
    () => window.history && typeof window.history.pushState === 'function'
  ];

  return spaIndicators.some(check => {
    try {
      return check();
    } catch {
      return false;
    }
  });
}

/**
 * 等待页面内容加载完成
 */
export function waitForContentLoad(timeout: number = 5000): Promise<void> {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    function checkContent() {
      const hasContent = hasValidContent();
      const timeElapsed = Date.now() - startTime;
      
      if (hasContent || timeElapsed >= timeout) {
        resolve();
      } else {
        setTimeout(checkContent, 100);
      }
    }
    
    // 如果页面已经加载完成，立即检查
    if (document.readyState === 'complete') {
      setTimeout(checkContent, 100);
    } else {
      // 等待页面加载完成
      window.addEventListener('load', () => {
        setTimeout(checkContent, 100);
      });
    }
  });
}
