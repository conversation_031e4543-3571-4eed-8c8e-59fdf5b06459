/**
 * Error Recovery System for AJAX Detection
 * 
 * Provides fallback mechanisms and error handling for when
 * AJAX interception fails or encounters issues.
 */

export interface ErrorRecoveryConfig {
  /** Maximum retry attempts */
  maxRetries: number;
  /** Retry delay in milliseconds */
  retryDelay: number;
  /** Enable automatic recovery */
  autoRecover: boolean;
  /** Enable error reporting */
  enableErrorReporting: boolean;
  /** Fallback detection methods */
  enableFallbackDetection: boolean;
  /** Debug mode */
  debug: boolean;
}

export interface RecoveryResult {
  success: boolean;
  method: 'retry' | 'fallback' | 'degraded' | 'failed';
  error?: Error;
  details?: any;
}

export interface ErrorReport {
  timestamp: number;
  errorType: string;
  message: string;
  stack?: string;
  context: {
    url: string;
    userAgent: string;
    extensionVersion?: string;
    failedComponent: string;
  };
  recoveryAttempted: boolean;
  recoveryResult?: RecoveryResult;
}

// 定义降级模式的接口
interface DegradedMode {
  enabled: boolean;
  message: string;
  [key: string]: any; // 允许其他属性
}

interface DegradedModes {
  AjaxMonitor: DegradedMode;
  ContentWatcher: DegradedMode;
  SmartDebouncer: DegradedMode;
}

export class ErrorRecoverySystem {
  private config: ErrorRecoveryConfig;
  private errorHistory: ErrorReport[] = [];
  private retryCounters: Map<string, number> = new Map();
  private fallbackMethods: Map<string, Function> = new Map();
  private isRecovering = false;
  
  private static readonly DEFAULT_CONFIG: ErrorRecoveryConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    autoRecover: true,
    enableErrorReporting: true,
    enableFallbackDetection: true,
    debug: false
  };

  constructor(config: Partial<ErrorRecoveryConfig> = {}) {
    this.config = { ...ErrorRecoverySystem.DEFAULT_CONFIG, ...config };
    this.initializeFallbackMethods();
  }

  /**
   * Initialize fallback detection methods
   */
  private initializeFallbackMethods(): void {
    // Fallback for XHR interception
    this.fallbackMethods.set('xhr', () => {
      this.log('Using XHR fallback method');
      return this.monitorXHRWithProxy();
    });

    // Fallback for Fetch interception
    this.fallbackMethods.set('fetch', () => {
      this.log('Using Fetch fallback method');
      return this.monitorFetchWithProxy();
    });

    // Fallback for content detection
    this.fallbackMethods.set('content', () => {
      this.log('Using content detection fallback');
      return this.usePerformanceObserver();
    });
  }

  /**
   * Handle error with recovery attempts
   */
  public async handleError(
    error: Error,
    component: string,
    context?: any
  ): Promise<RecoveryResult> {
    this.log(`Error in ${component}:`, error);

    // Create error report
    const report = this.createErrorReport(error, component);
    this.errorHistory.push(report);

    // Check if auto-recovery is enabled
    if (!this.config.autoRecover) {
      return { success: false, method: 'failed', error };
    }

    // Prevent recursive recovery
    if (this.isRecovering) {
      this.log('Already recovering, skipping nested recovery');
      return { success: false, method: 'failed', error };
    }

    this.isRecovering = true;
    
    try {
      // Try recovery strategies
      const result = await this.attemptRecovery(component, error, context);
      report.recoveryAttempted = true;
      report.recoveryResult = result;
      
      return result;
    } finally {
      this.isRecovering = false;
    }
  }

  /**
   * Attempt recovery using various strategies
   */
  private async attemptRecovery(
    component: string,
    error: Error,
    context?: any
  ): Promise<RecoveryResult> {
    const retryKey = `${component}-${error.message}`;
    const retryCount = this.retryCounters.get(retryKey) || 0;

    // Strategy 1: Retry
    if (retryCount < this.config.maxRetries) {
      this.retryCounters.set(retryKey, retryCount + 1);
      
      await this.delay(this.config.retryDelay * (retryCount + 1));
      
      try {
        const retryResult = await this.retryOperation(component, context);
        if (retryResult) {
          this.retryCounters.delete(retryKey);
          return { success: true, method: 'retry', details: retryResult };
        }
      } catch (retryError) {
        this.log('Retry failed:', retryError);
      }
    }

    // Strategy 2: Fallback method
    if (this.config.enableFallbackDetection && this.fallbackMethods.has(component)) {
      try {
        const fallbackMethod = this.fallbackMethods.get(component)!;
        const fallbackResult = await fallbackMethod();
        
        if (fallbackResult) {
          return { success: true, method: 'fallback', details: fallbackResult };
        }
      } catch (fallbackError) {
        this.log('Fallback failed:', fallbackError);
      }
    }

    // Strategy 3: Degraded mode
    const degradedResult = await this.enterDegradedMode(component);
    if (degradedResult) {
      return { success: true, method: 'degraded', details: degradedResult };
    }

    // All strategies failed
    return { success: false, method: 'failed', error };
  }

  /**
   * Retry the failed operation
   */
  private async retryOperation(component: string, context?: any): Promise<boolean> {
    this.log(`Retrying ${component} operation`);
    
    switch (component) {
      case 'AjaxMonitor':
        // Re-attempt to start AJAX monitoring
        if (context && context.start) {
          try {
            await context.start();
            return true;
          } catch (e) {
            return false;
          }
        }
        break;
      
      case 'ContentWatcher':
        // Re-attempt to start content watching
        if (context && context.start) {
          try {
            await context.start();
            return true;
          } catch (e) {
            return false;
          }
        }
        break;
      
      default:
        return false;
    }
    
    return false;
  }

  /**
   * Fallback: Monitor XHR using Proxy
   */
  private monitorXHRWithProxy(): boolean {
    try {
      const XHRProxy = new Proxy(XMLHttpRequest.prototype.send, {
        apply: (target, thisArg, argumentsList) => {
          this.log('XHR detected via Proxy');
          // Capture request info here
          return Reflect.apply(target, thisArg, argumentsList);
        }
      });
      
      XMLHttpRequest.prototype.send = XHRProxy;
      return true;
    } catch (error) {
      this.log('XHR Proxy fallback failed:', error);
      return false;
    }
  }

  /**
   * Fallback: Monitor Fetch using Proxy
   */
  private monitorFetchWithProxy(): boolean {
    try {
      const originalFetch = window.fetch;
      
      window.fetch = new Proxy(originalFetch, {
        apply: async (target, thisArg, argumentsList) => {
          this.log('Fetch detected via Proxy');
          // Capture request info here
          return Reflect.apply(target, thisArg, argumentsList);
        }
      });
      
      return true;
    } catch (error) {
      this.log('Fetch Proxy fallback failed:', error);
      return false;
    }
  }

  /**
   * Fallback: Use Performance Observer for resource detection
   */
  private usePerformanceObserver(): boolean {
    try {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'resource' && 
                (entry.name.includes('xhr') || entry.name.includes('fetch'))) {
              this.log('Resource detected via PerformanceObserver:', entry.name);
            }
          }
        });
        
        observer.observe({ entryTypes: ['resource'] });
        return true;
      }
      return false;
    } catch (error) {
      this.log('PerformanceObserver fallback failed:', error);
      return false;
    }
  }

  /**
   * Enter degraded mode with limited functionality
   */
  private async enterDegradedMode(component: string): Promise<any> {
    this.log(`Entering degraded mode for ${component}`);

    const degradedModes: DegradedModes = {
      AjaxMonitor: {
        enabled: true,
        message: 'AJAX monitoring is running in a limited state.',
        fallback: 'PerformanceObserver'
      },
      ContentWatcher: {
        enabled: true,
        reduced: true,
        message: 'Content watcher is running with reduced frequency.',
        checkInterval: 5000
      },
      SmartDebouncer: {
        enabled: true,
        fixed: true,
        message: 'Debouncer is using a fixed delay.',
        delay: 2000
      }
    };
    
    const mode = degradedModes[component as keyof DegradedModes] || null;

    if (mode && mode.enabled) {
      this.log(mode.message);
      // 在这里应用实际的降级逻辑
      // 例如: eventBus.emit('degradedModeChange', { component, mode });
      return mode;
    }

    return null;
  }

  /**
   * Create error report
   */
  private createErrorReport(error: Error, component: string): ErrorReport {
    return {
      timestamp: Date.now(),
      errorType: error.name || 'UnknownError',
      message: error.message,
      stack: error.stack,
      context: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        failedComponent: component,
        extensionVersion: chrome?.runtime?.getManifest?.()?.version
      },
      recoveryAttempted: false
    };
  }

  /**
   * Get error statistics
   */
  public getErrorStats(): {
    totalErrors: number;
    errorsByComponent: Record<string, number>;
    recoverySuccessRate: number;
    recentErrors: ErrorReport[];
  } {
    const errorsByComponent: Record<string, number> = {};
    let recoveryAttempts = 0;
    let successfulRecoveries = 0;
    
    this.errorHistory.forEach(report => {
      const component = report.context.failedComponent;
      errorsByComponent[component] = (errorsByComponent[component] || 0) + 1;
      
      if (report.recoveryAttempted) {
        recoveryAttempts++;
        if (report.recoveryResult?.success) {
          successfulRecoveries++;
        }
      }
    });
    
    return {
      totalErrors: this.errorHistory.length,
      errorsByComponent,
      recoverySuccessRate: recoveryAttempts > 0 
        ? (successfulRecoveries / recoveryAttempts) * 100 
        : 0,
      recentErrors: this.errorHistory.slice(-10)
    };
  }

  /**
   * Clear error history
   */
  public clearErrorHistory(): void {
    this.errorHistory = [];
    this.retryCounters.clear();
  }

  /**
   * Export error report for debugging
   */
  public exportErrorReport(): string {
    const stats = this.getErrorStats();
    const report = {
      generated: new Date().toISOString(),
      stats,
      errors: this.errorHistory
    };
    
    return JSON.stringify(report, null, 2);
  }

  /**
   * Check system health
   */
  public checkHealth(): {
    healthy: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    const stats = this.getErrorStats();
    
    // Check error rate
    if (this.errorHistory.length > 0) {
      const recentErrors = this.errorHistory.filter(
        e => e.timestamp > Date.now() - 300000 // Last 5 minutes
      );
      
      if (recentErrors.length > 10) {
        issues.push('High error rate detected');
        recommendations.push('Consider disabling problematic features');
      }
    }
    
    // Check recovery success rate
    if (stats.recoverySuccessRate < 50 && stats.totalErrors > 5) {
      issues.push('Low recovery success rate');
      recommendations.push('Manual intervention may be required');
    }
    
    // Check specific components
    Object.entries(stats.errorsByComponent).forEach(([component, count]) => {
      if (count > 5) {
        issues.push(`${component} experiencing frequent errors`);
        recommendations.push(`Consider disabling ${component}`);
      }
    });
    
    return {
      healthy: issues.length === 0,
      issues,
      recommendations
    };
  }

  /**
   * Utility: Delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Log helper
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[ErrorRecovery] ${message}`, data || '');
    }
  }
}

// Export singleton instance
export const errorRecovery = new ErrorRecoverySystem();