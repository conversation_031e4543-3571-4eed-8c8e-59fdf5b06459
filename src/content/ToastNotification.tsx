/**
 * Toast Notification Component for Recall V3.0
 * 
 * Non-intrusive notification system for reading time alerts and other messages
 * Integrates with the reading detection system
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import './toast.css';

interface ToastNotificationProps {
  /** Whether the toast is visible */
  isVisible: boolean;
  /** Toast message */
  message: string;
  /** Toast type */
  type?: 'info' | 'success' | 'warning' | 'error';
  /** Duration in milliseconds (0 = no auto-hide) */
  duration?: number;
  /** Callback when toast is dismissed */
  onDismiss: () => void;
  /** Callback when action button is clicked */
  onAction?: () => void;
  /** Action button text */
  actionText?: string;
  /** Position configuration */
  position?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  /** Custom CSS classes */
  className?: string;
  /** Auto-hide on scroll */
  hideOnScroll?: boolean;
}

/**
 * Toast Notification Component
 */
export const ToastNotification: React.FC<ToastNotificationProps> = ({
  isVisible,
  message,
  type = 'info',
  duration = 5000,
  onDismiss,
  onAction,
  actionText,
  position = { bottom: 20, right: 20 },
  className = '',
  hideOnScroll = true
}) => {
  const [isShowing, setIsShowing] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const timeoutRef = useRef<number | null>(null);
  const toastRef = useRef<HTMLDivElement>(null);

  // Handle visibility changes
  useEffect(() => {
    if (isVisible) {
      setIsShowing(true);
      setIsAnimating(true);
      
      // Set auto-hide timer
      if (duration > 0) {
        timeoutRef.current = window.setTimeout(() => {
          handleDismiss();
        }, duration);
      }
    } else {
      handleDismiss();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [isVisible, duration]);

  // Handle scroll hiding
  useEffect(() => {
    if (!hideOnScroll || !isVisible) return;

    let scrollTimeout: number | null = null;
    
    const handleScroll = () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
      
      // Hide toast temporarily during scroll
      setIsAnimating(false);
      
      scrollTimeout = window.setTimeout(() => {
        setIsAnimating(true);
      }, 150);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  }, [hideOnScroll, isVisible]);

  /**
   * Handle toast dismissal
   */
  const handleDismiss = useCallback(() => {
    setIsAnimating(false);
    
    // Wait for animation to complete
    setTimeout(() => {
      setIsShowing(false);
      onDismiss();
    }, 300);
  }, [onDismiss]);

  /**
   * Handle action button click
   */
  const handleAction = useCallback(() => {
    if (onAction) {
      onAction();
    }
    handleDismiss();
  }, [onAction, handleDismiss]);

  /**
   * Handle keyboard events
   */
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        handleDismiss();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isVisible, handleDismiss]);

  // Don't render if not showing
  if (!isShowing) {
    return null;
  }

  // Calculate position styles
  const positionStyles: React.CSSProperties = {
    position: 'fixed',
    zIndex: 2147483646, // High z-index but lower than summary panel
    ...position
  };

  // Get icon for toast type
  const getTypeIcon = () => {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return 'ℹ️';
    }
  };

  return (
    <div
      ref={toastRef}
      className={`recall-toast ${className} ${type} ${isAnimating ? 'visible' : 'hidden'}`}
      style={positionStyles}
      role="alert"
      aria-live="polite"
      aria-atomic="true"
    >
      <div className="toast-content">
        <div className="toast-icon">
          {getTypeIcon()}
        </div>
        
        <div className="toast-message">
          {message}
        </div>
        
        <div className="toast-actions">
          {onAction && actionText && (
            <button
              className="toast-action-button"
              onClick={handleAction}
              type="button"
            >
              {actionText}
            </button>
          )}
          
          <button
            className="toast-close-button"
            onClick={handleDismiss}
            type="button"
            aria-label="Close notification"
          >
            ✕
          </button>
        </div>
      </div>
      
      {/* Progress bar for auto-hide */}
      {duration > 0 && (
        <div className="toast-progress">
          <div 
            className="toast-progress-bar"
            style={{
              animationDuration: `${duration}ms`,
              animationPlayState: isAnimating ? 'running' : 'paused'
            }}
          />
        </div>
      )}
    </div>
  );
};

export default ToastNotification;