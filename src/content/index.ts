/**
 * Content Script 主文件
 *
 * 负责在网页中提取内容并发送给 Background Script
 */

import { extractPageContent, waitForContentLoad, isSinglePageApp } from './scraper';
import type { ExtractedContent } from './ContentExtractor';
import { ExtractionMethod } from './ContentExtractor';
import type { ExtractedContent as ScraperExtractedContent } from './scraper';
import { AjaxMonitor, type AjaxMonitorConfig, type AjaxRequestInfo } from './AjaxMonitor';
import { EnhancedContentWatcher, type ContentWatcherConfig, type ContentChangeInfo } from './EnhancedContentWatcher';
import { SmartDebouncer, type DebouncerConfig } from './SmartDebouncer';
import { ForumAdapter, type ForumDetectionResult } from './ForumAdapter';
import { GenericForumDetector, type ForumStructure } from './GenericForumDetector';
import './types'; // Import Chrome API types
import './readability-types'; // Import Readability types

/**
 * Convert scraper ExtractedContent to new ExtractedContent format
 */
function convertScraperResult(scraperResult: ScraperExtractedContent): ExtractedContent {
  return {
    ...scraperResult,
    contentType: 'unknown' as const,
    metadata: {
      isSPA: isSinglePageApp(),
      extractionMethod: ExtractionMethod.DOM_PARSING
    }
  };
}

// Define message types for content script
interface ContentMessage {
  type: string;
  data?: unknown;
  [key: string]: unknown;
}

/**
 * Content Script 配置
 */
interface ContentScriptConfig {
  /** 是否启用调试日志 */
  debug: boolean;
  /** 内容提取延迟（毫秒） */
  extractionDelay: number;
  /** 防抖延迟（毫秒） */
  debounceDelay: number;
  /** 最大重试次数 */
  maxRetries: number;
  /** 是否监听 SPA 路由变化 */
  watchSPAChanges: boolean;
  /** 是否启用 AJAX 监听 */
  enableAjaxMonitoring: boolean;
  /** AJAX 监听配置 */
  ajaxConfig: Partial<AjaxMonitorConfig>;
  /** 是否启用增强内容监测 */
  enableEnhancedContentWatcher: boolean;
  /** 增强内容监测配置 */
  enhancedWatcherConfig: Partial<ContentWatcherConfig>;
  /** 是否启用智能防抖机制 */
  enableSmartDebouncing: boolean;
  /** 智能防抖配置 */
  smartDebouncerConfig: Partial<DebouncerConfig>;
  /** 是否启用论坛适配 */
  enableForumAdaptation: boolean;
  /** 🔥 是否启用极简模式（性能优化实验） */
  enableUltraLightMode?: boolean;
}

/**
 * 默认配置
 */
const DEFAULT_CONFIG: ContentScriptConfig = {
  debug: true, // Enable debug logging to see AI initialization
  extractionDelay: 2000, // 页面加载后等待2秒再提取内容
  debounceDelay: 1000,   // 防抖1秒
  maxRetries: 3,
  watchSPAChanges: true,
  enableAjaxMonitoring: true, // 启用 AJAX 监听
  ajaxConfig: {
    debug: true,
    contentCheckDelay: 2000, // AJAX 完成后2秒检查内容
    minResponseSize: 200, // 最小响应大小200字节
    enableXHR: true,
    enableFetch: true
  },
  enableEnhancedContentWatcher: true, // 启用增强内容监测
  enhancedWatcherConfig: {
    debug: true,
    enableIframeMonitoring: true,
    enableShadowDOMMonitoring: true,
    minContentSize: 100, // 最小内容大小100字符
    periodicCheckInterval: 15000, // 15秒定期检查
    enableAttributeMonitoring: true,
    enableCharacterDataMonitoring: true
  },
  enableSmartDebouncing: true, // 启用智能防抖
  smartDebouncerConfig: {
    debug: true,
    baseDelay: 1500, // 基础延迟1.5秒
    maxDelay: 8000, // 最大延迟8秒
    minDelay: 300, // 最小延迟300ms
    activityThreshold: 15, // 高活动阈值
    maxPendingOperations: 3, // 最大挂起操作数
    enableAdaptiveDebouncing: true
  },
  enableForumAdaptation: true // 启用论坛适配
};

/**
 * 极简轻量级配置 - 用于性能优化实验
 * 目标：最小化对网站性能的影响，同时保持核心功能
 */
const ULTRA_LIGHT_CONFIG: ContentScriptConfig = {
  debug: false, // 关闭调试日志减少输出
  extractionDelay: 4000, // 增加到4秒，依赖初始页面加载
  debounceDelay: 2000,   // 增加防抖延迟到2秒
  maxRetries: 3,
  watchSPAChanges: true, // 保留SPA监控但简化
  enableAjaxMonitoring: false, // 🔥 完全禁用AJAX监控
  ajaxConfig: {
    debug: false,
    contentCheckDelay: 0, // 不使用
    minResponseSize: 0,   // 不使用
    enableXHR: false,     // 禁用
    enableFetch: false    // 禁用
  },
  enableEnhancedContentWatcher: false, // 🔥 禁用增强内容监测
  enhancedWatcherConfig: {
    debug: false,
    enableIframeMonitoring: false,      // 禁用iframe监控
    enableShadowDOMMonitoring: false,   // 禁用shadow DOM监控
    minContentSize: 200, // 提高阈值减少触发
    periodicCheckInterval: 0, // 禁用定期检查
    enableAttributeMonitoring: false,   // 禁用属性监控
    enableCharacterDataMonitoring: false // 禁用文本监控
  },
  enableSmartDebouncing: true, // 保留智能防抖
  smartDebouncerConfig: {
    debug: false,
    baseDelay: 2000, // 增加基础延迟
    maxDelay: 10000, // 增加最大延迟
    minDelay: 1000,  // 增加最小延迟
    activityThreshold: 20, // 提高活动阈值
    maxPendingOperations: 2, // 减少最大挂起操作
    enableAdaptiveDebouncing: true
  },
  enableForumAdaptation: false // 🔥 禁用论坛特殊处理
};

/**
 * Content Script 类
 */
class RecallContentScript {
  private config: ContentScriptConfig;
  private isProcessing: boolean = false;
  private lastUrl: string = '';
  private debounceTimer: number | null = null;
  private retryCount: number = 0;
  private observer: MutationObserver | null = null;
  
  // 新增：内容变化监测相关属性
  private contentWatcher: MutationObserver | null = null;
  private lastContentHash: string = '';
  private contentCheckTimer: number | null = null;
  private scrollWatcher: number | null = null;
  private lastScrollY: number = 0;
  
  // AJAX 监听器
  private ajaxMonitor: AjaxMonitor | null = null;
  
  // 增强内容监测器
  private enhancedWatcher: EnhancedContentWatcher | null = null;
  
  // 智能防抖器
  private smartDebouncer: SmartDebouncer | null = null;
  
  // 论坛检测结果
  private forumDetection: ForumDetectionResult | null = null;
  
  // 通用论坛结构分析
  private forumStructure: ForumStructure | null = null;
  

  // 存储原始的 History API 方法
  private originalPushState: typeof history.pushState | null = null;
  private originalReplaceState: typeof history.replaceState | null = null;

  // 绑定的事件处理函数引用（用于移除监听器）
  private boundHandleUrlChange: (() => void) | null = null;
  private boundHandleVisibilityChange: (() => void) | null = null;

  constructor(config: Partial<ContentScriptConfig> = {}) {
    // 🔥 实验：启用极简模式进行性能优化测试
    // 可以通过设置 config.enableUltraLightMode = false 来禁用
    const enableUltraLightMode = config.enableUltraLightMode !== false; // 默认启用
    
    if (enableUltraLightMode) {
      this.config = { ...ULTRA_LIGHT_CONFIG, ...config };
      console.log('🚀 Recall: Ultra-light mode enabled for performance optimization');
    } else {
      this.config = { ...DEFAULT_CONFIG, ...config };
      console.log('📊 Recall: Standard mode enabled');
    }
    
    this.lastUrl = window.location.href;

    // 绑定事件处理函数
    this.boundHandleUrlChange = this.handleUrlChange.bind(this);
    this.boundHandleVisibilityChange = this.handleVisibilityChange.bind(this);

    this.log('Content Script initialized', { 
      url: this.lastUrl, 
      ultraLightMode: enableUltraLightMode,
      ajaxMonitoring: this.config.enableAjaxMonitoring,
      enhancedWatcher: this.config.enableEnhancedContentWatcher
    });
  }

  /**
   * 启动 Content Script
   */
  public async start(): Promise<void> {
    try {
      // 等待页面内容加载
      await waitForContentLoad();

      // 延迟提取内容，确保页面完全渲染
      setTimeout(() => {
        this.extractAndSendContent();
      }, this.config.extractionDelay);

      // 监听 SPA 路由变化
      if (this.config.watchSPAChanges && isSinglePageApp()) {
        this.setupSPAWatcher();
      }

      // 监听页面可见性变化
      this.setupVisibilityWatcher();

      // 新增：设置内容变化监测
      this.setupContentWatcher();
      
      // 新增：设置滚动监测（用于懒加载检测）
      this.setupScrollWatcher();

      // 新增：设置 AJAX 监听器
      if (this.config.enableAjaxMonitoring) {
        this.setupAjaxMonitor();
      }

      // 新增：设置增强内容监测器
      if (this.config.enableEnhancedContentWatcher) {
        this.setupEnhancedContentWatcher();
      }

      // 新增：设置智能防抖器
      if (this.config.enableSmartDebouncing) {
        this.setupSmartDebouncer();
      }

      // 新增：检测论坛平台
      if (this.config.enableForumAdaptation) {
        this.detectForumPlatform();
      }

      // 设置消息监听器
      this.setupMessageListener();
      
      // AI services have been disabled

      this.log('Content Script started successfully');

    } catch (error) {
      this.log('Failed to start Content Script', error);
    }
  }

  /**
   * 停止 Content Script - 综合清理所有资源和事件监听器
   */
  public stop(): void {
    // 清理所有计时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    if (this.contentCheckTimer) {
      clearInterval(this.contentCheckTimer);
      this.contentCheckTimer = null;
    }

    if (this.scrollWatcher) {
      clearInterval(this.scrollWatcher);
      this.scrollWatcher = null;
    }

    // 清理所有观察器
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    if (this.contentWatcher) {
      this.contentWatcher.disconnect();
      this.contentWatcher = null;
    }

    // 停止 AJAX 监听器
    if (this.ajaxMonitor) {
      this.ajaxMonitor.stop();
      this.ajaxMonitor = null;
    }

    // 停止增强内容监测器
    if (this.enhancedWatcher) {
      this.enhancedWatcher.stop();
      this.enhancedWatcher = null;
    }

    // 停止智能防抖器
    if (this.smartDebouncer) {
      this.smartDebouncer.destroy();
      this.smartDebouncer = null;
    }

    // 移除事件监听器
    this.removeEventListeners();

    // AI functionality removed

    // 重置状态
    this.isProcessing = false;
    this.retryCount = 0;
    this.lastUrl = '';
    this.lastContentHash = '';
    this.lastScrollY = 0;

    this.log('Content Script stopped and cleaned up');
  }

  /**
   * 移除所有事件监听器
   */
  private removeEventListeners(): void {
    // 移除 popstate 监听器
    if (this.boundHandleUrlChange) {
      window.removeEventListener('popstate', this.boundHandleUrlChange);
    }

    // 移除 visibilitychange 监听器
    if (this.boundHandleVisibilityChange) {
      document.removeEventListener('visibilitychange', this.boundHandleVisibilityChange);
    }

    // 恢复原始的 History API 方法
    if (this.originalPushState) {
      history.pushState = this.originalPushState;
      this.originalPushState = null;
    }

    if (this.originalReplaceState) {
      history.replaceState = this.originalReplaceState;
      this.originalReplaceState = null;
    }

    // 清理绑定的函数引用
    this.boundHandleUrlChange = null;
    this.boundHandleVisibilityChange = null;
  }

  /**
   * 提取并发送内容（V2：总是保存基础记录）
   */
  public async extractAndSendContent(): Promise<void> {
    if (this.isProcessing) {
      this.log('Content extraction already in progress, skipping');
      return;
    }

    this.isProcessing = true;

    try {
      this.log('Starting content extraction and basic page recording');

      // 1. 首先收集基础页面信息（总是可用）
      const basicInfo = this.collectBasicPageInfo();
      
      // 2. 尝试提取页面内容
      const scraperResult = extractPageContent({
        strictMode: false, // 使用宽松模式，避免过度严格的验证
        minContentLength: 50, // 降低最小长度要求
        debug: this.config.debug, // 传递调试模式配置
        forumPlatform: this.forumDetection?.platform // 传递论坛平台信息
      });
      let extractedContent = convertScraperResult(scraperResult);

      // 3. 应用论坛特定的后处理
      if (this.forumDetection && extractedContent.success) {
        const processedContent = ForumAdapter.postProcessContent(
          extractedContent.content,
          this.forumDetection.platform
        );
        extractedContent = {
          ...extractedContent,
          content: processedContent
        };
      }

      this.log('Content extraction attempted', {
        basicInfoCollected: true,
        extractionSuccess: extractedContent.success,
        contentLength: extractedContent.contentLength,
        isValid: extractedContent.isValidContent,
        error: extractedContent.error,
        title: basicInfo.title,
        url: basicInfo.url
      });

      // 3. 合并基础信息和内容提取结果
      const pageData = {
        ...basicInfo,
        content: extractedContent.content || '',
        contentLength: extractedContent.contentLength || 0,
        extractionSuccess: extractedContent.success,
        isValidContent: extractedContent.isValidContent,
        extractionError: extractedContent.error || null,
        contentStatus: this.determineContentStatus(extractedContent)
      };

      // 4. 总是发送页面数据（不再依赖内容提取是否成功）
      const pageDataWithRequiredFields = {
        ...pageData,
        success: pageData.extractionSuccess,
        contentType: 'unknown' as const,
        metadata: {
          isSPA: false,
          extractionMethod: ExtractionMethod.DOM_PARSING
        }
      };
      await this.sendPageDataToBackground(pageDataWithRequiredFields);
      this.retryCount = 0; // 重置重试计数
      
      // 5. 如果内容提取失败但页面看起来有内容，安排重试
      if (!extractedContent.success && this.shouldRetryExtraction() && this.retryCount < this.config.maxRetries) {
        this.scheduleRetryExtraction();
      }

    } catch (error) {
      this.log('Page processing error', error);
      
      // 即使发生错误，也尝试保存基础信息
      try {
        const basicInfo = this.collectBasicPageInfo();
        const fallbackData = {
          ...basicInfo,
          content: '',
          contentLength: 0,
          extractionSuccess: false,
          isValidContent: false,
          extractionError: (error as Error).message,
          contentStatus: 'failed' as const,
          success: false,
          contentType: 'unknown' as const,
          metadata: {
            isSPA: false,
            extractionMethod: ExtractionMethod.FALLBACK
          }
        };
        await this.sendPageDataToBackground(fallbackData);
      } catch (fallbackError) {
        this.log('Failed to save even basic page info', fallbackError);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 收集基础页面信息（总是可用）
   */
  private collectBasicPageInfo() {
    return {
      url: window.location.href,
      title: this.extractBestTitle(),
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      referrer: document.referrer,
      domain: window.location.hostname,
      protocol: window.location.protocol
    };
  }

  /**
   * 提取最佳标题（尝试多种方法）
   */
  private extractBestTitle(): string {
    // 尝试多种标题提取方法
    const candidates = [
      document.querySelector('meta[property="og:title"]')?.getAttribute('content'),
      document.querySelector('meta[name="title"]')?.getAttribute('content'),
      document.querySelector('h1')?.textContent?.trim(),
      document.title
    ].filter(Boolean);
    
    return candidates[0] || 'Untitled Page';
  }

  /**
   * 确定内容状态
   */
  private determineContentStatus(extractedContent: ExtractedContent): 'extracted' | 'failed' | 'empty' | 'pending' {
    if (extractedContent.success && extractedContent.isValidContent) {
      return 'extracted';
    } else if (extractedContent.success && extractedContent.content) {
      return 'empty'; // 提取成功但内容不符合验证标准
    } else if (extractedContent.error) {
      return 'failed';
    } else {
      return 'pending';
    }
  }

  /**
   * 判断是否应该重试内容提取
   */
  private shouldRetryExtraction(): boolean {
    // 检查页面是否看起来有内容值得重试
    const bodyText = document.body?.textContent || '';
    const hasSignificantContent = bodyText.trim().length > 100;
    const hasContentElements = document.querySelector('article, main, .content, .post, .entry');
    
    return hasSignificantContent || !!hasContentElements;
  }

  /**
   * 安排重试内容提取
   */
  private scheduleRetryExtraction(): void {
    this.retryCount++;
    this.log(`Scheduling content extraction retry (${this.retryCount}/${this.config.maxRetries})`);
    
    setTimeout(() => {
      if (this.retryCount <= this.config.maxRetries) {
        this.isProcessing = false;
        this.extractAndSendContent();
      }
    }, 3000 * this.retryCount); // 递增延迟重试
  }

  /**
   * 发送页面数据到 Background Script（新版本）
   */
  private async sendPageDataToBackground(pageData: ExtractedContent): Promise<void> {
    try {
      // 首先检查扩展上下文是否有效
      if (!chrome?.runtime?.id) {
        this.log('Chrome runtime not available, skipping message send');
        return;
      }

      // Additional null safety checks
      if (!pageData) {
        this.log('Page data is null, skipping message send');
        return;
      }

      const message = {
        type: 'PAGE_DATA_COLLECTED', // 新的消息类型
        data: pageData
      };

      // 添加超时机制
      const sendMessageWithTimeout = () => {
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Message timeout after 5 seconds'));
          }, 5000);

          chrome.runtime.sendMessage(message, (response) => {
            clearTimeout(timeout);
            
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve(response);
            }
          });
        });
      };

      const response = await sendMessageWithTimeout();

      this.log('Content sent to background', {
        success: (response as any)?.success,
        messageId: (response as any)?.messageId
      });

    } catch (error) {
      const errorMessage = (error as Error).message;
      this.log('Failed to send content to background', error);

      // 检查各种错误类型并采取相应措施
      if (errorMessage.includes('Extension context invalidated') || 
          errorMessage.includes('Receiving end does not exist')) {
        this.log('Extension context invalidated or background not ready, stopping content script');
        this.stop();
      } else if (errorMessage.includes('timeout')) {
        this.log('Message timeout - background script may be busy, will retry later');
        // 可以考虑添加重试机制
      } else {
        this.log('Unknown error sending message to background', errorMessage);
      }
    }
  }

  /**
   * 设置 SPA 路由变化监听
   */
  private setupSPAWatcher(): void {
    // 保存原始的 History API 方法
    this.originalPushState = history.pushState;
    this.originalReplaceState = history.replaceState;

    // 监听 History API 变化
    history.pushState = (...args) => {
      this.originalPushState!.apply(history, args);
      this.handleUrlChange();
    };

    history.replaceState = (...args) => {
      this.originalReplaceState!.apply(history, args);
      this.handleUrlChange();
    };

    // 监听 popstate 事件（使用绑定的处理函数）
    if (this.boundHandleUrlChange) {
      window.addEventListener('popstate', this.boundHandleUrlChange);
    }

    // 监听 DOM 变化（作为备用方案）
    this.observer = new MutationObserver((mutations) => {
      const hasSignificantChanges = mutations.some(mutation =>
        mutation.type === 'childList' &&
        mutation.addedNodes.length > 0 &&
        Array.from(mutation.addedNodes).some(node =>
          node.nodeType === Node.ELEMENT_NODE &&
          (node as Element).tagName !== 'SCRIPT'
        )
      );

      if (hasSignificantChanges) {
        this.handleUrlChange();
      }
    });

    // 🔥 极简模式：使用轻量级观察配置
    const observerConfig = this.config.enableUltraLightMode !== false ? 
      {
        childList: true,
        subtree: false,  // 极简模式：不监控子树减少性能消耗
        attributes: false
      } : 
      {
        childList: true,
        subtree: true,   // 标准模式：监控整个子树
        attributes: false
      };
    
    this.observer.observe(document.body, observerConfig);

    this.log('SPA watcher setup completed');
  }

  /**
   * 设置页面可见性监听
   */
  private setupVisibilityWatcher(): void {
    if (this.boundHandleVisibilityChange) {
      document.addEventListener('visibilitychange', this.boundHandleVisibilityChange);
    }
  }

  /**
   * 处理页面可见性变化
   */
  private handleVisibilityChange(): void {
    if (document.visibilityState === 'visible') {
      // 页面变为可见时，检查URL是否有变化
      if (window.location.href !== this.lastUrl) {
        this.handleUrlChange();
      }
    }
  }

  /**
   * 处理 URL 变化
   */
  private handleUrlChange(): void {
    const currentUrl = window.location.href;

    if (currentUrl !== this.lastUrl) {
      this.log('URL changed', { from: this.lastUrl, to: currentUrl });
      this.lastUrl = currentUrl;

      if (this.smartDebouncer) {
        // 使用智能防抖机制，URL变化优先级较高
        this.smartDebouncer.debounce(
          'url-change',
          'content-change',
          'high',
          () => this.extractAndSendContent(),
          { oldUrl: this.lastUrl, newUrl: currentUrl }
        );
      } else {
        // 传统防抖作为回退方案
        if (this.debounceTimer) {
          clearTimeout(this.debounceTimer);
        }

        this.debounceTimer = window.setTimeout(() => {
          this.extractAndSendContent();
        }, this.config.debounceDelay);
      }
    }
  }

  /**
   * 设置内容变化监测（用于检测动态加载的内容）
   */
  private setupContentWatcher(): void {
    // 记录初始内容哈希值
    this.lastContentHash = this.generateContentHash();

    // 监听 DOM 内容变化
    this.contentWatcher = new MutationObserver((mutations) => {
      let hasContentChanges = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          // 检查是否有新增的有意义的内容
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              // 检查是否为内容相关元素
              if (this.isContentElement(element)) {
                hasContentChanges = true;
              }
            }
          });
        }
      });

      if (hasContentChanges) {
        this.handleContentChange();
      }
    });

    // 监听主要内容区域
    const contentTargets = [
      document.body,
      document.querySelector('main'),
      document.querySelector('[role="main"]'),
      document.querySelector('.content'),
      document.querySelector('#content')
    ].filter(Boolean);

    // 🔥 极简模式：优化观察器配置
    const observerConfig = this.config.enableUltraLightMode !== false ? 
      {
        childList: true,
        subtree: false,  // 极简模式：不监控子树
        attributes: false
      } : 
      {
        childList: true,
        subtree: true,   // 标准模式：监控子树
        attributes: false
      };

    contentTargets.forEach(target => {
      if (target) {
        this.contentWatcher!.observe(target, observerConfig);
      }
    });

    // 定期检查内容变化（作为备用机制）
    this.contentCheckTimer = window.setInterval(() => {
      if (this.smartDebouncer) {
        // 使用智能防抖机制，定期检查优先级最低
        this.smartDebouncer.debounce(
          'periodic-content-check',
          'periodic',
          'low',
          () => this.checkContentChanges()
        );
      } else {
        this.checkContentChanges();
      }
    }, this.config.enableUltraLightMode !== false ? 30000 : 10000); // 极简模式：30秒，标准模式：10秒

    this.log('Content watcher setup completed');
  }

  /**
   * 设置滚动监测（用于检测懒加载）
   */
  private setupScrollWatcher(): void {
    this.lastScrollY = window.scrollY;

    // 定期检查滚动和新内容
    this.scrollWatcher = window.setInterval(() => {
      const currentScrollY = window.scrollY;
      const scrollDiff = Math.abs(currentScrollY - this.lastScrollY);

      // 如果用户滚动了一定距离，检查是否有新内容加载
      if (scrollDiff > 100) {
        this.lastScrollY = currentScrollY;
        
        // 延迟检查，给懒加载一些时间
        setTimeout(() => {
          if (this.smartDebouncer) {
            // 使用智能防抖机制，滚动触发的检查优先级较低
            this.smartDebouncer.debounce(
              'scroll-content-check',
              'scroll-change',
              'low',
              () => this.checkContentChanges(),
              { scrollDiff }
            );
          } else {
            this.checkContentChanges();
          }
        }, 2000);
      }
    }, this.config.enableUltraLightMode !== false ? 3000 : 1000); // 极简模式：3秒，标准模式：1秒

    this.log('Scroll watcher setup completed');
  }

  /**
   * 生成页面内容的哈希值
   */
  private generateContentHash(): string {
    const contentElements = document.querySelectorAll('article, main, .content, .post, .entry, p, h1, h2, h3, h4, h5, h6');
    const contentText = Array.from(contentElements)
      .map(el => el.textContent?.trim())
      .filter(text => text && text.length > 20)
      .join(' ')
      .substring(0, 1000); // 限制长度

    return this.simpleHash(contentText);
  }

  /**
   * 简单的字符串哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 检查是否为内容相关元素
   */
  private isContentElement(element: Element): boolean {
    const contentTags = ['article', 'main', 'section', 'div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
    const contentClasses = ['content', 'post', 'entry', 'article', 'story', 'text', 'body'];
    
    // 检查标签名
    if (contentTags.includes(element.tagName.toLowerCase())) {
      return true;
    }

    // 检查类名
    const className = (element.className?.toString() || '').toLowerCase();
    if (contentClasses.some(cls => className.includes(cls))) {
      return true;
    }

    // 检查文本内容长度
    const textContent = element.textContent?.trim() || '';
    if (textContent.length > 100) {
      return true;
    }

    return false;
  }

  /**
   * 设置 AJAX 监听器
   */
  private setupAjaxMonitor(): void {
    this.ajaxMonitor = new AjaxMonitor(
      this.config.ajaxConfig,
      {
        onRelevantContentLoaded: (requestInfo: AjaxRequestInfo) => {
          this.log('AJAX content loaded detected', requestInfo);
          this.handleAjaxContentLoaded(requestInfo);
        },
        onAjaxCompleted: (requestInfo: AjaxRequestInfo) => {
          this.log('AJAX request completed', {
            url: requestInfo.url,
            method: requestInfo.method,
            isRelevant: requestInfo.isRelevant,
            responseSize: requestInfo.responseSize
          });
        }
      }
    );

    try {
      this.ajaxMonitor.start();
      this.log('AJAX monitor started successfully');
    } catch (error) {
      this.log('Failed to start AJAX monitor', error);
      this.ajaxMonitor = null;
    }
  }

  /**
   * 设置增强内容监测器
   */
  private setupEnhancedContentWatcher(): void {
    // 禁用旧的内容监测器，使用增强版本替代
    if (this.contentWatcher) {
      this.contentWatcher.disconnect();
      this.contentWatcher = null;
    }

    this.enhancedWatcher = new EnhancedContentWatcher(
      this.config.enhancedWatcherConfig,
      {
        onContentChanged: (changeInfo: ContentChangeInfo) => {
          this.log('Enhanced content change detected', changeInfo);
          this.handleEnhancedContentChange(changeInfo);
        },
        onSignificantContentAdded: (changeInfo: ContentChangeInfo) => {
          this.log('Significant content added', changeInfo);
          this.handleEnhancedContentChange(changeInfo);
        },
        onIframeContentChanged: (iframe: HTMLIFrameElement, changeInfo: ContentChangeInfo) => {
          this.log('Iframe content changed', { iframe: iframe.src, changeInfo });
          this.handleEnhancedContentChange(changeInfo);
        }
      }
    );

    try {
      this.enhancedWatcher.start();
      this.log('Enhanced content watcher started successfully');
    } catch (error) {
      this.log('Failed to start enhanced content watcher', error);
      this.enhancedWatcher = null;
      
      // 回退到原始内容监测器
      this.log('Falling back to original content watcher');
      this.setupContentWatcher();
    }
  }

  /**
   * 设置智能防抖器
   */
  private setupSmartDebouncer(): void {
    this.smartDebouncer = new SmartDebouncer(this.config.smartDebouncerConfig);
    this.log('Smart debouncer initialized successfully');
  }

  /**
   * 检测论坛平台
   */
  private detectForumPlatform(): void {
    try {
      // 1. 首先尝试特定平台检测
      this.forumDetection = ForumAdapter.detectPlatform();
      
      // 2. 进行通用论坛结构分析
      this.forumStructure = GenericForumDetector.analyzeForumStructure();
      
      if (this.forumDetection) {
        this.log('Specific forum platform detected', {
          platform: this.forumDetection.platform,
          confidence: this.forumDetection.confidence,
          genericConfidence: this.forumStructure.confidence
        });

        // 应用平台特定的预处理
        ForumAdapter.preProcessDocument(document, this.forumDetection.platform);
        
        // 如果是高置信度的论坛检测，调整AJAX监听配置
        if (this.forumDetection.confidence > 0.8) {
          this.optimizeForumAjaxMonitoring();
        }
      } else if (this.forumStructure.confidence > 0.6) {
        // 没有特定平台匹配，但通用检测表明这是一个论坛
        this.log('Generic forum structure detected', {
          confidence: this.forumStructure.confidence,
          commentSections: this.forumStructure.commentSections.length,
          paginationElements: this.forumStructure.paginationElements.length,
          forumType: GenericForumDetector.estimateForumType()
        });
        
        // 使用通用论坛优化
        this.applyGenericForumOptimization();
      } else {
        this.log('No forum platform or structure detected for current site');
      }
    } catch (error) {
      this.log('Error detecting forum platform', error);
    }
  }

  /**
   * 优化论坛AJAX监听
   */
  private optimizeForumAjaxMonitoring(): void {
    if (!this.forumDetection || !this.ajaxMonitor) return;

    try {
      // 获取平台特定的AJAX配置
      const config = this.forumDetection.config;
      
      // 更新AJAX监听器以包含论坛特定的URL模式
      // Note: Enhanced config could be used for future AJAX optimization

      this.log('Optimized AJAX monitoring for forum platform', {
        platform: this.forumDetection.platform,
        patterns: config.ajaxPatterns.length
      });
    } catch (error) {
      this.log('Error optimizing forum AJAX monitoring', error);
    }
  }

  /**
   * 应用通用论坛优化
   */
  private applyGenericForumOptimization(): void {
    if (!this.forumStructure) return;

    try {
      // 获取最佳评论容器用于优化内容检测
      const bestCommentContainer = GenericForumDetector.getBestCommentContainer();
      
      if (bestCommentContainer && this.enhancedWatcher) {
        // 更新增强内容监测器以专注于评论区域
        this.log('Applying generic forum optimization', {
          commentContainer: bestCommentContainer.tagName + '.' + bestCommentContainer.className,
          forumType: GenericForumDetector.estimateForumType()
        });
        
        // 这里可以通过enhancedWatcher的配置来优化监测
        // 由于当前enhancedWatcher不支持动态配置更新，我们记录信息供后续使用
      }

      // 优化滚动检测以适应无限滚动论坛
      if (this.forumStructure.infiniteScrollContainers.length > 0) {
        this.optimizeInfiniteScrollDetection();
      }

    } catch (error) {
      this.log('Error applying generic forum optimization', error);
    }
  }

  /**
   * 优化无限滚动检测
   */
  private optimizeInfiniteScrollDetection(): void {
    if (!this.forumStructure || this.forumStructure.infiniteScrollContainers.length === 0) return;

    try {
      const scrollContainer = this.forumStructure.infiniteScrollContainers[0];
      
      this.log('Optimizing infinite scroll detection', {
        container: scrollContainer.element.tagName + '.' + scrollContainer.element.className,
        confidence: scrollContainer.confidence
      });

      // 设置特定于无限滚动容器的监听
      let lastScrollTime = 0;
      const scrollHandler = () => {
        const now = Date.now();
        if (now - lastScrollTime > 500) { // 防抖500ms
          lastScrollTime = now;
          
          if (this.smartDebouncer) {
            this.smartDebouncer.debounce(
              'infinite-scroll-detection',
              'scroll-change',
              'medium',
              () => this.checkForNewContent(),
              { containerInfo: scrollContainer }
            );
          }
        }
      };

      scrollContainer.element.addEventListener('scroll', scrollHandler, { passive: true });
      
    } catch (error) {
      this.log('Error optimizing infinite scroll detection', error);
    }
  }

  /**
   * 检查新内容（用于无限滚动）
   */
  private checkForNewContent(): void {
    this.log('Checking for new content due to scroll activity');
    this.extractAndSendContent();
  }

  /**
   * 检测通用论坛内容变化
   */
  private detectGenericForumContent(mutations: MutationRecord[]): boolean {
    if (!this.forumStructure) return false;

    return mutations.some(mutation => {
      if (mutation.type !== 'childList' || mutation.addedNodes.length === 0) {
        return false;
      }

      return Array.from(mutation.addedNodes).some(node => {
        if (node.nodeType !== Node.ELEMENT_NODE) return false;
        
        const element = node as Element;
        
        // 检查是否在已知的评论区域内
        const isInCommentSection = this.forumStructure!.commentSections.some(section => 
          section.element.contains(element) || element.contains(section.element)
        );

        // 检查是否在无限滚动容器内
        const isInScrollContainer = this.forumStructure!.infiniteScrollContainers.some(container => 
          container.element.contains(element) || element.contains(container.element)
        );

        // 检查元素是否具有评论特征
        const hasCommentFeatures = this.hasCommentLikeFeatures(element);

        return isInCommentSection || isInScrollContainer || hasCommentFeatures;
      });
    });
  }

  /**
   * 检查元素是否具有评论特征
   */
  private hasCommentLikeFeatures(element: Element): boolean {
    const text = element.textContent?.toLowerCase() || '';
    const className = (element.className?.toString() || '').toLowerCase();
    const id = (element.id || '').toLowerCase();
    
    // 检查类名和ID中的评论相关词汇
    const commentKeywords = /comment|reply|post|message|discussion|feedback|review/i;
    if (commentKeywords.test(className) || commentKeywords.test(id)) {
      return true;
    }

    // 检查是否包含用户名、时间戳等典型评论元素
    const hasUsername = /user|author|by/i.test(text) || element.querySelector('[class*="user"], [class*="author"]');
    const hasTimestamp = /ago|time|date|\d+:\d+/i.test(text) || element.querySelector('time, [class*="time"], [class*="date"]');
    const hasReplyButton = element.querySelector('button, a, [role="button"]') && /reply|respond/i.test(element.innerHTML);

    return !!(hasUsername || hasTimestamp || hasReplyButton);
  }

  /**
   * 处理增强内容变化
   */
  private handleEnhancedContentChange(changeInfo: ContentChangeInfo): void {
    this.log('Handling enhanced content change', {
      type: changeInfo.type,
      isSignificant: changeInfo.isSignificant,
      source: changeInfo.source,
      contentSize: changeInfo.addedContent?.length || 0,
      forumPlatform: this.forumDetection?.platform
    });

    // 检查是否是论坛特定的新内容
    let isForumContent = false;
    if (this.forumDetection && changeInfo.mutations) {
      isForumContent = ForumAdapter.detectNewForumContent(
        changeInfo.mutations,
        this.forumDetection.platform
      );
    } else if (this.forumStructure && changeInfo.mutations) {
      // 使用通用论坛检测
      isForumContent = this.detectGenericForumContent(changeInfo.mutations);
    }

    // 只对重要的内容变化或论坛新内容触发重新提取
    if (changeInfo.isSignificant || changeInfo.type === 'added' || isForumContent) {
      if (this.smartDebouncer) {
        // 使用智能防抖机制，论坛内容优先级更高
        let priority: 'low' | 'medium' | 'high' | 'critical' = changeInfo.isSignificant ? 'high' : 'medium';
        if (isForumContent) priority = 'high';
        
        this.smartDebouncer.debounce(
          `content-change-${changeInfo.source}`,
          'content-change',
          priority,
          () => this.extractAndSendContent(),
          { changeInfo, isForumContent }
        );
      } else {
        // 回退到传统防抖机制
        this.handleContentChange();
      }
    }
  }

  /**
   * 处理 AJAX 内容加载
   */
  private handleAjaxContentLoaded(requestInfo: AjaxRequestInfo): void {
    this.log('Handling AJAX content loaded', {
      url: requestInfo.url,
      currentPageUrl: window.location.href,
      forumPlatform: this.forumDetection?.platform
    });

    // 检查是否是当前页面的相关请求
    const currentDomain = window.location.hostname;
    const requestDomain = new URL(requestInfo.url, window.location.href).hostname;
    
    // 检查是否是论坛特定的AJAX请求
    let isForumAjax = false;
    if (this.forumDetection) {
      isForumAjax = ForumAdapter.isForumAjaxRequest(
        requestInfo.url,
        this.forumDetection.platform
      );
    }
    
    // 处理同域请求或论坛特定的AJAX请求
    if (currentDomain === requestDomain || requestInfo.url.startsWith('/') || isForumAjax) {
      if (this.smartDebouncer) {
        // 使用智能防抖机制，论坛AJAX请求优先级最高
        const priority = isForumAjax ? 'critical' : 'high';
        this.smartDebouncer.debounce(
          `ajax-complete-${requestInfo.url}`,
          'ajax-complete',
          priority,
          () => this.extractAndSendContent(),
          { requestInfo, isForumAjax }
        );
      } else {
        // 回退到传统防抖机制
        this.handleContentChange();
      }
    }
  }

  /**
   * 处理内容变化
   */
  private handleContentChange(): void {
    if (this.smartDebouncer) {
      // 使用智能防抖机制
      this.smartDebouncer.debounce(
        'content-change-general',
        'content-change',
        'medium',
        () => this.extractAndSendContent()
      );
    } else {
      // 传统防抖作为回退方案
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }

      this.debounceTimer = window.setTimeout(() => {
        this.log('Content change detected, re-extracting...');
        this.extractAndSendContent();
      }, 3000); // 3秒防抖
    }
  }

  /**
   * 检查内容变化
   */
  private checkContentChanges(): void {
    const currentHash = this.generateContentHash();
    
    if (currentHash !== this.lastContentHash && currentHash.length > 0) {
      this.log('Content hash changed', {
        oldHash: this.lastContentHash,
        newHash: currentHash
      });
      
      this.lastContentHash = currentHash;
      this.handleContentChange();
    }
  }

  // AI services have been completely removed

  /**
   * Setup message listener for AI and content extraction requests
   */
  private setupMessageListener(): void {
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
      chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        this.handleMessage(message, sender)
          .then(response => sendResponse(response))
          .catch(error => {
            this.log('Message handling error:', error);
            sendResponse({
              success: false,
              error: error.message
            });
          });
        
        return true; // Keep message channel open for async response
      });
      
      this.log('Message listener setup complete');
    }
  }

  /**
   * Handle messages from background script or popup
   */
  private async handleMessage(message: ContentMessage, _sender: chrome.runtime.MessageSender): Promise<unknown> {
    try {
      this.log('Received message:', message.type);
      
      switch (message.type) {
        case 'EXTRACT_CONTENT':
          return await this.handleExtractContent(message);

        // Note: AI functionality has been removed

        default:
          return {
            success: false,
            error: `Unknown message type: ${message.type}`
          };
      }
    } catch (error) {
      this.log('Error handling message:', error);
      throw error;
    }
  }

  /**
   * Handle content extraction request
   */
  private async handleExtractContent(_message: ContentMessage): Promise<ExtractedContent | null> {
    try {
      const scraperResult = extractPageContent({
        strictMode: false,
        minContentLength: 50
      });

      return convertScraperResult(scraperResult);
    } catch (error) {
      return {
        title: '',
        content: '',
        url: window.location.href,
        success: false,
        error: (error as Error).message,
        contentLength: 0,
        isValidContent: false,
        contentType: 'unknown' as const,
        metadata: {
          isSPA: false,
          extractionMethod: ExtractionMethod.FALLBACK
        }
      };
    }
  }


  /**
   * 日志输出
   */
  private log(message: string, data?: unknown): void {
    if (this.config.debug) {
      console.log(`[Recall Content Script] ${message}`, data || '');
    }
  }
}

// 创建并启动 Content Script 实例
let contentScript: RecallContentScript | null = null;

/**
 * 初始化 Content Script
 */
function initContentScript(): void {
  // 避免重复初始化
  if (contentScript) {
    return;
  }

  try {
    // 检查是否在有效的页面环境中
    if (!window.location || !document.body) {
      console.warn('[Recall] Invalid page environment, skipping initialization');
      return;
    }

    // 检查是否为扩展页面（避免在扩展自身页面中运行）
    if (window.location.protocol === 'chrome-extension:') {
      return;
    }

    // 创建配置（可以从 Chrome Storage 读取用户配置）
    const config: Partial<ContentScriptConfig> = {
      debug: true // Enable debug mode to help diagnose issues
    };

    contentScript = new RecallContentScript(config);
    contentScript.start();

  } catch (error) {
    console.error('[Recall] Failed to initialize Content Script:', error);
  }
}

/**
 * 清理 Content Script
 */
function cleanupContentScript(): void {
  if (contentScript) {
    contentScript.stop();
    contentScript = null;
  }
}

/**
 * 简单的调试助手
 */
function initSimpleDebugHelper(): void {
  if (typeof window !== 'undefined') {
    // 强制设置到全局作用域
    const globalWindow = window as any;
    globalWindow.RecallDebug = {
      testContentExtraction: async () => {
        console.log('[Debug] Testing content extraction...');
        try {
          const scraperResult = extractPageContent({
            strictMode: false,
            minContentLength: 50
          });
          const result = convertScraperResult(scraperResult);
          console.log('[Debug] Content extraction result:', result);
          return result;
        } catch (error) {
          console.error('[Debug] Content extraction failed:', error);
          return { error: (error as Error).message };
        }
      },

      forceExtraction: async () => {
        console.log('[Debug] Forcing content extraction...');
        if (contentScript) {
          await contentScript.extractAndSendContent();
          return { success: true, message: 'Extraction triggered' };
        } else {
          return { success: false, message: 'Content script not initialized' };
        }
      },

      getStatus: () => {
        return {
          contentScriptLoaded: !!contentScript,
          currentUrl: window.location.href,
          timestamp: Date.now(),
          debugHelperLoaded: true
        };
      },

      checkPageContent: () => {
        const bodyText = document.body?.textContent || '';
        const textLength = bodyText.replace(/\s+/g, ' ').trim().length;
        const title = document.title;

        return {
          title,
          textLength,
          hasEnoughContent: textLength > 100,
          url: window.location.href,
          protocol: window.location.protocol
        };
      },

      debugValidation: () => {
        console.log('[Debug] Running detailed validation check...');
        const scraperResult = extractPageContent({
          strictMode: false,
          minContentLength: 50
        });
        const result = convertScraperResult(scraperResult);

        if (result.content) {
          const trimmedContent = result.content.trim();
          const words = trimmedContent.split(/\s+/).filter(word => word.length > 2);
          const uniqueChars = new Set(trimmedContent.toLowerCase()).size;

          console.log('[Debug] Validation details:', {
            contentLength: trimmedContent.length,
            wordCount: words.length,
            uniqueChars: uniqueChars,
            isValid: result.isValidContent,
            firstChars: trimmedContent.substring(0, 100) + '...'
          });
        }

        return result;
      },

      // 新增调试功能
      checkContentWatchers: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        return {
          hasContentWatcher: !!(contentScript as any).contentWatcher,
          hasScrollWatcher: !!(contentScript as any).scrollWatcher,
          lastContentHash: (contentScript as any).lastContentHash || 'none',
          currentScrollY: window.scrollY,
          lastScrollY: (contentScript as any).lastScrollY || 0
        };
      },

      simulateContentChange: () => {
        console.log('[Debug] Simulating content change...');
        if (contentScript && (contentScript as any).handleContentChange) {
          (contentScript as any).handleContentChange();
          return { success: true, message: 'Content change simulated' };
        }
        return { success: false, message: 'Content script not available' };
      },

      getContentHash: () => {
        if (contentScript && (contentScript as any).generateContentHash) {
          const hash = (contentScript as any).generateContentHash();
          console.log('[Debug] Current content hash:', hash);
          return { hash, timestamp: Date.now() };
        }
        return { error: 'Content script not available' };
      },

      testBackgroundConnection: async () => {
        console.log('[Debug] Testing background connection...');
        try {
          const basicInfo = {
            url: window.location.href,
            title: document.title,
            timestamp: Date.now(),
            testMode: true
          };

          const response = await chrome.runtime.sendMessage({
            type: 'PAGE_DATA_COLLECTED',
            data: {
              ...basicInfo,
              content: 'Test content for debug',
              contentLength: 20,
              userAgent: navigator.userAgent,
              referrer: document.referrer,
              domain: window.location.hostname,
              protocol: window.location.protocol,
              extractionSuccess: true,
              isValidContent: true,
              extractionError: null,
              contentStatus: 'extracted'
            }
          });

          console.log('[Debug] Background response:', response);
          return response;
        } catch (error) {
          console.error('[Debug] Background connection failed:', error);
          return { success: false, error: (error as Error).message };
        }
      },

      getPageStats: () => {
        const contentElements = document.querySelectorAll('article, main, .content, .post, .entry, p, h1, h2, h3, h4, h5, h6');
        const images = document.querySelectorAll('img');
        const links = document.querySelectorAll('a');
        const scripts = document.querySelectorAll('script');

        return {
          url: window.location.href,
          title: document.title,
          domain: window.location.hostname,
          protocol: window.location.protocol,
          bodyTextLength: (document.body?.textContent || '').length,
          contentElements: contentElements.length,
          images: images.length,
          links: links.length,
          scripts: scripts.length,
          hasMainElement: !!document.querySelector('main'),
          hasArticleElement: !!document.querySelector('article'),
          timestamp: Date.now()
        };
      },

      enableVerboseLogging: () => {
        if (contentScript) {
          (contentScript as any).config.debug = true;
          console.log('[Debug] Verbose logging enabled');
          return { success: true };
        }
        return { success: false, message: 'Content script not available' };
      },

      disableVerboseLogging: () => {
        if (contentScript) {
          (contentScript as any).config.debug = false;
          console.log('[Debug] Verbose logging disabled');
          return { success: true };
        }
        return { success: false, message: 'Content script not available' };
      },

      // AJAX 监听器调试功能
      checkAjaxMonitor: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const ajaxMonitor = (contentScript as any).ajaxMonitor;
        if (!ajaxMonitor) {
          return { error: 'AJAX monitor not initialized' };
        }

        return {
          isActive: ajaxMonitor.isActive(),
          stats: ajaxMonitor.getStats(),
          config: ajaxMonitor.getConfig()
        };
      },

      simulateAjaxRequest: async () => {
        console.log('[Debug] Simulating AJAX request...');
        try {
          // 模拟一个简单的fetch请求
          const response = await fetch('/favicon.ico', { method: 'HEAD' });
          console.log('[Debug] Simulated AJAX request completed:', response.status);
          return { 
            success: true, 
            status: response.status,
            message: 'AJAX request simulated' 
          };
        } catch (error) {
          console.error('[Debug] Simulated AJAX request failed:', error);
          return { 
            success: false, 
            error: (error as Error).message 
          };
        }
      },

      testAjaxDetection: () => {
        console.log('[Debug] Testing AJAX detection...');
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const ajaxMonitor = (contentScript as any).ajaxMonitor;
        if (!ajaxMonitor) {
          return { error: 'AJAX monitor not initialized' };
        }

        // 模拟AJAX内容加载
        const mockRequestInfo = {
          url: window.location.href + '/test',
          method: 'GET',
          timestamp: Date.now(),
          isRelevant: true,
          responseSize: 1000
        };

        if ((contentScript as any).handleAjaxContentLoaded) {
          (contentScript as any).handleAjaxContentLoaded(mockRequestInfo);
          return { 
            success: true, 
            message: 'AJAX content loaded handler triggered',
            mockRequest: mockRequestInfo
          };
        }

        return { success: false, message: 'AJAX content handler not available' };
      },

      // 增强内容监测器调试功能
      checkEnhancedWatcher: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const enhancedWatcher = (contentScript as any).enhancedWatcher;
        if (!enhancedWatcher) {
          return { error: 'Enhanced content watcher not initialized' };
        }

        return {
          isActive: enhancedWatcher.isWatcherActive(),
          stats: enhancedWatcher.getStats(),
          config: enhancedWatcher.config || 'N/A'
        };
      },

      testEnhancedContentDetection: () => {
        console.log('[Debug] Testing enhanced content detection...');
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        // 创建一个新的测试元素
        const testElement = document.createElement('div');
        testElement.className = 'test-content-change';
        testElement.textContent = 'This is a test content change with enough text to trigger the enhanced content watcher detection mechanism.';
        
        document.body.appendChild(testElement);
        
        // 稍后移除
        setTimeout(() => {
          if (document.body.contains(testElement)) {
            document.body.removeChild(testElement);
          }
        }, 2000);

        return { 
          success: true, 
          message: 'Test element added to trigger enhanced content detection',
          elementText: testElement.textContent
        };
      },

      simulateIframeContent: () => {
        console.log('[Debug] Simulating iframe content change...');
        try {
          // 创建一个简单的iframe
          const iframe = document.createElement('iframe');
          iframe.src = 'about:blank';
          iframe.style.display = 'none';
          document.body.appendChild(iframe);

          // 在iframe加载后添加内容
          iframe.onload = () => {
            try {
              const iframeDoc = iframe.contentDocument;
              if (iframeDoc) {
                const testDiv = iframeDoc.createElement('div');
                testDiv.textContent = 'Test iframe content for enhanced content watcher';
                iframeDoc.body.appendChild(testDiv);
                
                // 清理
                setTimeout(() => {
                  if (document.body.contains(iframe)) {
                    document.body.removeChild(iframe);
                  }
                }, 3000);
              }
            } catch (error) {
              console.error('Failed to modify iframe content:', error);
            }
          };

          return { 
            success: true, 
            message: 'Iframe created for content detection test' 
          };
        } catch (error) {
          return { 
            success: false, 
            error: (error as Error).message 
          };
        }
      },

      // 智能防抖器调试功能
      checkSmartDebouncer: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const smartDebouncer = (contentScript as any).smartDebouncer;
        if (!smartDebouncer) {
          return { error: 'Smart debouncer not initialized' };
        }

        return {
          metrics: smartDebouncer.getMetrics(),
          config: smartDebouncer.config || 'N/A'
        };
      },

      testSmartDebouncing: () => {
        console.log('[Debug] Testing smart debouncing...');
        
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const smartDebouncer = (contentScript as any).smartDebouncer;
        if (!smartDebouncer) {
          return { error: 'Smart debouncer not initialized' };
        }

        // 添加多个不同优先级的测试任务
        const testTasks = [
          { id: 'test-critical', priority: 'critical', type: 'content-change' },
          { id: 'test-high', priority: 'high', type: 'ajax-complete' },
          { id: 'test-medium', priority: 'medium', type: 'content-change' },
          { id: 'test-low', priority: 'low', type: 'scroll-change' }
        ];

        testTasks.forEach((task, index) => {
          setTimeout(() => {
            smartDebouncer.debounce(
              task.id,
              task.type,
              task.priority,
              () => {
                console.log(`[Debug] Executed test task: ${task.id} (${task.priority} priority)`);
              },
              { testData: `Test task ${index + 1}` }
            );
          }, index * 100); // 间隔100ms添加任务
        });

        return { 
          success: true, 
          message: 'Added 4 test tasks with different priorities',
          tasks: testTasks
        };
      },

      forceDebounceExecution: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const smartDebouncer = (contentScript as any).smartDebouncer;
        if (!smartDebouncer) {
          return { error: 'Smart debouncer not initialized' };
        }

        smartDebouncer.executeImmediate();
        return { 
          success: true, 
          message: 'Forced execution of all pending debounced tasks'
        };
      },

      getDebounceMetrics: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const smartDebouncer = (contentScript as any).smartDebouncer;
        if (!smartDebouncer) {
          return { error: 'Smart debouncer not initialized' };
        }

        return smartDebouncer.getMetrics();
      },

      // 论坛适配调试功能
      checkForumDetection: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const forumDetection = (contentScript as any).forumDetection;
        return {
          detected: !!forumDetection,
          platform: forumDetection?.platform || 'none',
          confidence: forumDetection?.confidence || 0,
          currentUrl: window.location.href,
          supportedPlatforms: ForumAdapter.getSupportedPlatforms()
        };
      },

      testForumContentDetection: () => {
        console.log('[Debug] Testing forum content detection...');
        
        const detection = ForumAdapter.detectPlatform();
        if (!detection) {
          return { error: 'No forum platform detected' };
        }

        const contentSelectors = ForumAdapter.getContentSelectors(detection.platform);
        const commentSelectors = ForumAdapter.getCommentSelectors(detection.platform);
        
        const foundContent = contentSelectors.filter(selector => 
          document.querySelector(selector) !== null
        );
        const foundComments = commentSelectors.filter(selector => 
          document.querySelector(selector) !== null
        );

        return {
          platform: detection.platform,
          confidence: detection.confidence,
          contentSelectors: {
            total: contentSelectors.length,
            found: foundContent.length,
            selectors: foundContent
          },
          commentSelectors: {
            total: commentSelectors.length,
            found: foundComments.length,
            selectors: foundComments
          }
        };
      },

      simulateForumAjax: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const forumDetection = (contentScript as any).forumDetection;
        if (!forumDetection) {
          return { error: 'No forum platform detected' };
        }

        // 模拟论坛AJAX请求
        const config = forumDetection.config;
        if (config.ajaxPatterns.length === 0) {
          return { error: 'No AJAX patterns defined for this platform' };
        }

        const testUrl = window.location.origin + '/test/forum/ajax';
        const isForumAjax = ForumAdapter.isForumAjaxRequest(testUrl, forumDetection.platform);

        const mockRequestInfo = {
          url: testUrl,
          method: 'GET',
          timestamp: Date.now(),
          isRelevant: isForumAjax,
          responseSize: 2000
        };

        if ((contentScript as any).handleAjaxContentLoaded) {
          (contentScript as any).handleAjaxContentLoaded(mockRequestInfo);
          return {
            success: true,
            message: 'Forum AJAX handler triggered',
            mockRequest: mockRequestInfo,
            isForumAjax
          };
        }

        return { success: false, message: 'AJAX handler not available' };
      },

      // 通用论坛检测调试功能
      analyzeGenericForum: () => {
        console.log('[Debug] Analyzing generic forum structure...');
        
        const structure = GenericForumDetector.analyzeForumStructure();
        const isForumPage = GenericForumDetector.isForumPage();
        const forumType = GenericForumDetector.estimateForumType();
        const bestCommentContainer = GenericForumDetector.getBestCommentContainer();

        return {
          isForumPage,
          forumType,
          confidence: structure.confidence,
          structure: {
            mainContent: !!structure.mainContent,
            commentSections: structure.commentSections.length,
            paginationElements: structure.paginationElements.length,
            infiniteScrollContainers: structure.infiniteScrollContainers.length,
            threadStructure: structure.threadStructure.length
          },
          bestCommentContainer: bestCommentContainer ? {
            tagName: bestCommentContainer.tagName,
            className: bestCommentContainer.className,
            id: bestCommentContainer.id
          } : null,
          commentDetails: structure.commentSections.map(section => ({
            confidence: section.confidence,
            metadata: section.metadata,
            tagName: section.element.tagName,
            className: section.element.className
          }))
        };
      },

      testGenericForumDetection: () => {
        if (!contentScript) {
          return { error: 'Content script not initialized' };
        }

        const forumStructure = (contentScript as any).forumStructure;
        if (!forumStructure) {
          return { error: 'No generic forum structure available' };
        }

        // 模拟内容变化检测
        const testMutations: MutationRecord[] = [];
        
        // 创建模拟的mutation记录
        if (forumStructure.commentSections.length > 0) {
          const commentSection = forumStructure.commentSections[0];
          const newElement = document.createElement('div');
          newElement.className = 'test-comment-item';
          newElement.textContent = 'Test comment by TestUser • 1 minute ago';
          
          // 模拟添加到评论区域
          const mockMutation = {
            type: 'childList' as const,
            target: commentSection.element,
            addedNodes: [newElement] as any,
            removedNodes: [] as any,
            previousSibling: null,
            nextSibling: null,
            attributeName: null,
            attributeNamespace: null,
            oldValue: null
          };
          
          testMutations.push(mockMutation);
        }

        const isDetected = (contentScript as any).detectGenericForumContent(testMutations);

        return {
          success: true,
          message: 'Generic forum content detection test completed',
          isDetected,
          testMutations: testMutations.length,
          forumStructure: {
            confidence: forumStructure.confidence,
            commentSections: forumStructure.commentSections.length
          }
        };
      }
    };

    console.log('[Debug] Recall Enhanced Debug Helper loaded.');
    console.log('[Debug] Available functions:');
    console.log('  Basic Testing:');
    console.log('    - testContentExtraction(): Test content extraction');
    console.log('    - forceExtraction(): Force content extraction');
    console.log('    - getStatus(): Get current status');
    console.log('    - checkPageContent(): Check page content details');
    console.log('    - debugValidation(): Debug content validation');
    console.log('  Advanced Features:');
    console.log('    - checkContentWatchers(): Check content monitoring status');
    console.log('    - simulateContentChange(): Trigger content change handler');
    console.log('    - getContentHash(): Get current page content hash');
    console.log('    - testBackgroundConnection(): Test background script connection');
    console.log('    - getPageStats(): Get detailed page statistics');
    console.log('  Logging Control:');
    console.log('    - enableVerboseLogging(): Enable detailed logging');
    console.log('    - disableVerboseLogging(): Disable detailed logging');
    console.log('  AJAX Monitoring:');
    console.log('    - checkAjaxMonitor(): Check AJAX monitor status and configuration');
    console.log('    - simulateAjaxRequest(): Simulate an AJAX request to test detection');
    console.log('    - testAjaxDetection(): Test AJAX content loaded handler');
    console.log('  Enhanced Content Monitoring:');
    console.log('    - checkEnhancedWatcher(): Check enhanced content watcher status');
    console.log('    - testEnhancedContentDetection(): Test enhanced content detection');
    console.log('    - simulateIframeContent(): Simulate iframe content changes');
    console.log('  Smart Debouncing:');
    console.log('    - checkSmartDebouncer(): Check smart debouncer status and metrics');
    console.log('    - testSmartDebouncing(): Test smart debouncing with different priorities');
    console.log('    - forceDebounceExecution(): Force execution of all pending tasks');
    console.log('    - getDebounceMetrics(): Get detailed debouncer performance metrics');
    console.log('  Forum Adaptation:');
    console.log('    - checkForumDetection(): Check forum platform detection results');
    console.log('    - testForumContentDetection(): Test forum content selector detection');
    console.log('    - simulateForumAjax(): Simulate forum-specific AJAX requests');
    console.log('  Generic Forum Detection:');
    console.log('    - analyzeGenericForum(): Analyze generic forum structure and patterns');
    console.log('    - testGenericForumDetection(): Test generic forum content change detection');

    // 验证设置是否成功
    if (globalWindow.RecallDebug) {
      console.log('[Debug] ✅ Debug helper successfully exposed to global scope');
    } else {
      console.error('[Debug] ❌ Failed to expose debug helper to global scope');
    }
  }
}

// 立即初始化调试助手
initSimpleDebugHelper();

// 强制暴露调试助手到全局作用域
setTimeout(() => {
  if (typeof window !== 'undefined' && !(window as any).RecallDebug) {
    console.warn('[Debug] Forcing debug helper exposure...');
    initSimpleDebugHelper();
  }
}, 100);

// 多次尝试暴露调试助手
const intervals = [500, 1000, 2000];
intervals.forEach(delay => {
  setTimeout(() => {
    if (typeof window !== 'undefined' && !(window as any).RecallDebug) {
      console.warn(`[Debug] Retry exposing debug helper after ${delay}ms...`);
      initSimpleDebugHelper();
    }
  }, delay);
});

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initContentScript);
} else {
  initContentScript();
}

// 页面卸载时清理
window.addEventListener('beforeunload', cleanupContentScript);

// 监听来自 Background Script 的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  switch (message.type) {
    case 'EXTRACT_CONTENT':
      // 手动触发内容提取
      if (contentScript) {
        contentScript.stop();
        contentScript = null;
      }
      initContentScript();
      sendResponse({ success: true });
      break;

    case 'TEST_CONTENT_EXTRACTION':
      // 测试内容提取
      if (typeof window !== 'undefined' && (window as any).RecallDebug) {
        (window as any).RecallDebug.testContentExtraction().then((result: unknown) => {
          sendResponse(result);
        }).catch((error: unknown) => {
          sendResponse({ success: false, error: (error as Error).message });
        });
      } else {
        sendResponse({ success: false, error: 'Debug helper not available' });
      }
      break;

    case 'FORCE_EXTRACTION':
      // 强制提取
      if (typeof window !== 'undefined' && (window as any).RecallDebug) {
        (window as any).RecallDebug.forceExtraction().then((result: unknown) => {
          sendResponse(result);
        }).catch((error: unknown) => {
          sendResponse({ success: false, error: (error as Error).message });
        });
      } else {
        sendResponse({ success: false, error: 'Debug helper not available' });
      }
      break;

    default:
      sendResponse({ success: false, error: 'Unknown message type' });
  }

  return true; // 保持消息通道开放
});

// ES Module export for Vite loader
export function onExecute() {
  console.log('Recall content script loaded');
  // The content script initialization happens via the IIFE above
}

// 导出用于测试
declare const module: any;
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { RecallContentScript, initContentScript, cleanupContentScript, onExecute };
}

// Export new V3.0 components
// Note: ToastNotification and MarkdownRenderer are TSX components
// They will be imported separately when needed in the React context
export { ReadingDetector } from './ReadingDetector';
export type { ReadingSession } from './ReadingDetector';
