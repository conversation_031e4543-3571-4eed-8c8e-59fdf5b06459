/**
 * Special Content Handler for Recall V3.0
 * 
 * Handles extraction from special content types including:
 * - PDF documents
 * - Video pages (YouTube, Vimeo, etc.)
 * - Single Page Applications (SPAs)
 * - Media-rich content
 */

import { ContentType } from './ContentExtractor';

/**
 * Special content handling result
 */
export interface SpecialContentResult {
  /** Whether handling was successful */
  success: boolean;
  /** Extracted content */
  content: string;
  /** Error message if any */
  error?: string;
  /** Additional metadata */
  metadata?: SpecialContentMetadata;
}

/**
 * Special content metadata
 */
export interface SpecialContentMetadata {
  /** Content duration (for videos) */
  duration?: string;
  /** Video/audio quality */
  quality?: string;
  /** Creator/channel name */
  creator?: string;
  /** View count */
  viewCount?: string;
  /** Upload/publish date */
  publishDate?: string;
  /** Upload date (alternative field) */
  uploadDate?: string;
  /** Content categories/tags */
  categories?: string[];
  /** Language */
  language?: string;
}

/**
 * SPA detection indicators
 */
const SPA_INDICATORS = [
  // Framework globals
  () => !!(window as any).React,
  () => !!(window as any).Vue,
  () => !!(window as any).angular,
  () => !!(window as any).Angular,
  () => !!(window as any).Ember,
  () => !!(window as any).Backbone,
  () => !!(window as any).jQuery && (window as any).jQuery.fn && (window as any).jQuery.fn.version,
  
  // DOM indicators
  () => !!document.querySelector('[ng-app], [data-ng-app], [ng-controller]'),
  () => !!document.querySelector('[data-reactroot], #root, #app, [data-react-class]'),
  () => !!document.querySelector('[data-vue-app], [data-v-app]'),
  () => !!document.querySelector('.ember-application, #ember-app'),
  
  // Script indicators
  () => !!document.querySelector('script[src*="react"], script[src*="vue"], script[src*="angular"]'),
  () => !!document.querySelector('script[src*="ember"], script[src*="backbone"]'),
  
  // HTML indicators
  () => document.body.innerHTML.includes('ng-') || document.body.innerHTML.includes('v-'),
  () => document.body.innerHTML.includes('data-react') || document.body.innerHTML.includes('class="react'),
  
  // History API usage
  () => window.history && typeof window.history.pushState === 'function' && 
        document.querySelectorAll('a[href]').length > 5,
  
  // Route patterns
  () => window.location.hash.includes('#/') || window.location.pathname.includes('/app/'),
  
  // Build artifacts
  () => !!document.querySelector('script[src*="chunk"], script[src*="bundle"], script[src*="vendor"]'),
  () => !!document.querySelector('link[href*="chunk"], link[href*="bundle"]')
];

/**
 * Video platform patterns
 */
const VIDEO_PLATFORMS = {
  youtube: {
    domains: ['youtube.com', 'youtu.be', 'm.youtube.com'],
    selectors: {
      title: '#title h1, .title, .watch-title, ytd-video-primary-info-renderer h1',
      description: '#description, .description, .watch-description, #meta-contents #description',
      creator: '#channel-name, .channel-name, #owner-name, ytd-channel-name',
      duration: '.duration, .ytp-time-duration',
      viewCount: '#count, .view-count, #info-text',
      uploadDate: '#date, .date, #info-text'
    }
  },
  vimeo: {
    domains: ['vimeo.com', 'player.vimeo.com'],
    selectors: {
      title: '.title, h1[data-name="title"]',
      description: '.description, [data-name="description"]',
      creator: '.byline, [data-name="owner"]',
      duration: '.duration',
      viewCount: '.plays, [data-name="plays"]',
      uploadDate: '.date'
    }
  },
  twitch: {
    domains: ['twitch.tv', 'www.twitch.tv'],
    selectors: {
      title: 'h2[data-a-target="stream-title"], .stream-title',
      description: '.about-section, .channel-info-content',
      creator: '[data-a-target="channel-name"], .channel-name',
      viewCount: '.live-indicator-container',
      category: '[data-a-target="stream-game-link"]',
      duration: '.duration', // Add missing duration selector
      uploadDate: '.date' // Add missing uploadDate selector
    }
  },
  generic: {
    domains: [],
    selectors: {
      title: 'video title, .video-title, .player-title',
      description: 'video description, .video-description, .player-description',
      creator: '.video-author, .creator, .channel',
      duration: '.video-duration, .duration',
      viewCount: '.views, .view-count',
      uploadDate: '.date, .upload-date'
    }
  }
};

/**
 * Special Content Handler class
 */
export class SpecialContentHandler {

  /**
   * Handle special content based on type
   */
  public async handleSpecialContent(contentType: ContentType): Promise<SpecialContentResult> {
    switch (contentType) {
      case ContentType.PDF:
        return this.handlePDFContent();
      
      case ContentType.VIDEO:
        return this.handleVideoContent();
      
      case ContentType.SPA:
        return this.handleSPAContent();
      
      default:
        return {
          success: false,
          content: '',
          error: `Unsupported content type: ${contentType}`
        };
    }
  }

  /**
   * Check if current page is a Single Page Application
   */
  public isSinglePageApp(): boolean {
    return SPA_INDICATORS.some(check => {
      try {
        return check();
      } catch {
        return false;
      }
    });
  }

  /**
   * Check if current page is a video page
   */
  public isVideoPage(): boolean {
    const hostname = window.location.hostname.toLowerCase();
    
    // Check for known video platforms
    const isKnownVideoPlatform = Object.values(VIDEO_PLATFORMS).some(platform =>
      platform.domains.some(domain => hostname.includes(domain))
    );

    if (isKnownVideoPlatform) {
      return true;
    }

    // Check for video elements and players
    const hasVideoElements = !!(
      document.querySelector('video') ||
      document.querySelector('iframe[src*="youtube"]') ||
      document.querySelector('iframe[src*="vimeo"]') ||
      document.querySelector('iframe[src*="twitch"]') ||
      document.querySelector('.video-player, .player, [data-video]') ||
      document.querySelector('embed[type*="video"], object[type*="video"]')
    );

    return hasVideoElements;
  }

  /**
   * Handle PDF content extraction
   */
  private handlePDFContent(): SpecialContentResult {
    try {
      let content = '';
      const metadata: SpecialContentMetadata = {};

      // Check for embedded PDF
      const pdfEmbed = document.querySelector('embed[type="application/pdf"]') as HTMLEmbedElement;
      if (pdfEmbed) {
        // Extract PDF URL and basic info
        const pdfUrl = pdfEmbed.src;
        content = `PDF Document: ${document.title}\nURL: ${pdfUrl}`;
        
        // Try to extract any visible text on the page
        const visibleText = this.extractVisibleText();
        if (visibleText) {
          content += `\n\nVisible content: ${visibleText}`;
        }
      }

      // Check for PDF viewer interfaces
      const pdfViewer = document.querySelector('#viewer, .pdf-viewer, .document-viewer');
      if (pdfViewer) {
        const viewerText = pdfViewer.textContent || '';
        if (viewerText.trim()) {
          content += `\n\nDocument content: ${viewerText.trim()}`;
        }
      }

      // Extract metadata from page
      metadata.language = document.documentElement.lang || 'en';
      
      // Look for document info
      const metaAuthor = document.querySelector('meta[name="author"]')?.getAttribute('content');
      
      if (metaAuthor) {
        metadata.creator = metaAuthor;
      }

      // If no content found, use fallback
      if (!content.trim()) {
        content = `PDF Document: ${document.title || 'Untitled PDF'}`;
        const pageText = document.body.textContent || '';
        if (pageText.length > 50) {
          content += `\n\nPage text: ${pageText.substring(0, 500)}...`;
        }
      }

      return {
        success: content.length > 0,
        content: content.trim(),
        metadata
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `PDF extraction failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Handle video content extraction
   */
  private handleVideoContent(): SpecialContentResult {
    try {
      const hostname = window.location.hostname.toLowerCase();
      const platform = this.detectVideoPlatform(hostname);
      let content = '';
      let metadata: SpecialContentMetadata = {};

      // Extract video information based on platform
      if (platform && platform !== 'generic') {
        const result = this.extractPlatformSpecificData(platform);
        content = result.content;
        metadata = result.metadata;
      } else {
        // Generic video extraction
        const result = this.extractGenericVideoData();
        content = result.content;
        metadata = result.metadata;
      }

      // Fallback: extract any visible text
      if (!content.trim()) {
        const visibleText = this.extractVisibleText();
        content = `Video content: ${document.title}\n\n${visibleText}`;
      }

      return {
        success: content.length > 0,
        content: content.trim(),
        metadata
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `Video extraction failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Handle SPA content extraction
   */
  private handleSPAContent(): SpecialContentResult {
    try {
      let content = '';
      
      // Wait for content to load (SPAs might need time)
      const maxWaitTime = 2000; // 2 seconds
      const startTime = Date.now();
      
      const extractContent = (): string => {
        // Try to find main content areas
        const contentSelectors = [
          'main',
          '[role="main"]',
          '.main-content',
          '.content',
          '.app-content',
          '.page-content',
          '.view',
          '.component',
          '#app > div',
          '#root > div',
          '.container:not(.navbar):not(.header):not(.footer)'
        ];

        for (const selector of contentSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            const text = this.extractCleanText(element as HTMLElement);
            if (text.length > 100) {
              return text;
            }
          }
        }

        // Fallback: extract from body but filter out navigation
        return this.extractSPABodyContent();
      };

      // Extract content immediately
      content = extractContent();

      // If content is insufficient, wait a bit for SPA to load
      if (content.length < 200 && Date.now() - startTime < maxWaitTime) {
        setTimeout(() => {
          const newContent = extractContent();
          if (newContent.length > content.length) {
            content = newContent;
          }
        }, 500);
      }

      return {
        success: content.length > 0,
        content: content.trim(),
        metadata: {
          language: document.documentElement.lang || 'en'
        }
      };

    } catch (error) {
      return {
        success: false,
        content: '',
        error: `SPA extraction failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Detect video platform
   */
  private detectVideoPlatform(hostname: string): string | null {
    for (const [platform, config] of Object.entries(VIDEO_PLATFORMS)) {
      if (platform === 'generic') continue;
      
      if (config.domains.some(domain => hostname.includes(domain))) {
        return platform;
      }
    }
    return 'generic';
  }

  /**
   * Extract platform-specific video data
   */
  private extractPlatformSpecificData(platform: string): { content: string; metadata: SpecialContentMetadata } {
    const config = VIDEO_PLATFORMS[platform as keyof typeof VIDEO_PLATFORMS];
    let content = '';
    const metadata: SpecialContentMetadata = {};

    if (!config) {
      return { content: '', metadata: {} };
    }

    // Extract title
    const title = this.getTextFromSelectors(config.selectors.title);
    if (title) {
      content += `Title: ${title}\n`;
    }

    // Extract description
    const description = this.getTextFromSelectors(config.selectors.description);
    if (description) {
      content += `Description: ${description}\n`;
    }

    // Extract creator/channel
    const creator = this.getTextFromSelectors(config.selectors.creator);
    if (creator) {
      content += `Creator: ${creator}\n`;
      metadata.creator = creator;
    }

    // Extract duration (if selector exists)
    if ('duration' in config.selectors && config.selectors.duration) {
      const duration = this.getTextFromSelectors(config.selectors.duration);
      if (duration) {
        metadata.duration = duration;
      }
    }

    // Extract view count (if selector exists)
    if ('viewCount' in config.selectors && config.selectors.viewCount) {
      const viewCount = this.getTextFromSelectors(config.selectors.viewCount);
      if (viewCount) {
        metadata.viewCount = viewCount;
      }
    }

    // Extract upload date (if selector exists)
    if ('uploadDate' in config.selectors && config.selectors.uploadDate) {
      const uploadDate = this.getTextFromSelectors(config.selectors.uploadDate);
      if (uploadDate) {
        metadata.uploadDate = uploadDate;
      }
    }

    // Platform-specific extractions
    if (platform === 'youtube') {
      // Extract comments or transcript if available
      const transcript = document.querySelector('#transcript, .transcript')?.textContent;
      if (transcript) {
        content += `\nTranscript: ${transcript.substring(0, 1000)}...`;
      }
    }

    return { content: content.trim(), metadata };
  }

  /**
   * Extract generic video data
   */
  private extractGenericVideoData(): { content: string; metadata: SpecialContentMetadata } {
    let content = `Video: ${document.title}\n`;
    const metadata: SpecialContentMetadata = {};

    // Try to find video elements
    const videoElement = document.querySelector('video');
    if (videoElement) {
      const src = videoElement.src || videoElement.currentSrc;
      if (src) {
        content += `Source: ${src}\n`;
      }
      
      if (videoElement.duration) {
        metadata.duration = this.formatDuration(videoElement.duration);
      }
    }

    // Extract any visible text that might be related to the video
    const visibleText = this.extractVisibleText();
    if (visibleText) {
      content += `\nContent: ${visibleText}`;
    }

    return { content: content.trim(), metadata };
  }

  /**
   * Get text content from CSS selectors
   */
  private getTextFromSelectors(selectors: string): string {
    if (!selectors) return '';
    
    const selectorList = selectors.split(', ');
    
    for (const selector of selectorList) {
      const element = document.querySelector(selector.trim());
      if (element && element.textContent) {
        const text = element.textContent.trim();
        if (text) {
          return text;
        }
      }
    }
    
    return '';
  }

  /**
   * Extract visible text from the page (excluding navigation and ads)
   */
  private extractVisibleText(): string {
    const textElements = document.querySelectorAll('p, div, span, h1, h2, h3, h4, h5, h6');
    const visibleTexts: string[] = [];

    textElements.forEach(element => {
      // Skip if element is not visible
      const style = window.getComputedStyle(element);
      if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
        return;
      }

      // Skip navigation and ad elements
      const classList = (element.className?.toString() || '').toLowerCase();
      const id = (element.id || '').toLowerCase();
      const skipPatterns = ['nav', 'menu', 'ad', 'banner', 'header', 'footer', 'sidebar'];
      
      if (skipPatterns.some(pattern => classList.includes(pattern) || id.includes(pattern))) {
        return;
      }

      const text = element.textContent?.trim();
      if (text && text.length > 20 && text.length < 500) {
        visibleTexts.push(text);
      }
    });

    // Remove duplicates and join
    const uniqueTexts = [...new Set(visibleTexts)];
    return uniqueTexts.slice(0, 10).join('\n\n'); // Limit to 10 text blocks
  }

  /**
   * Extract clean text from an element
   */
  private extractCleanText(element: HTMLElement): string {
    // Clone to avoid modifying original
    const clone = element.cloneNode(true) as HTMLElement;

    // Remove unwanted elements
    const unwantedSelectors = [
      'script', 'style', 'nav', 'header', 'footer', 'aside',
      '.nav', '.navigation', '.menu', '.sidebar', '.ads', '.ad',
      '.banner', '.popup', '.modal', '.cookie', '.gdpr'
    ];

    unwantedSelectors.forEach(selector => {
      const elements = clone.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    const text = clone.textContent || '';
    return text.replace(/\s+/g, ' ').trim();
  }

  /**
   * Extract content from SPA body while filtering navigation
   */
  private extractSPABodyContent(): string {
    const body = document.body.cloneNode(true) as HTMLElement;

    // Remove common SPA navigation and UI elements
    const uiSelectors = [
      'nav', 'header', 'footer', 'aside',
      '.navbar', '.nav', '.navigation', '.menu',
      '.sidebar', '.drawer', '.toolbar', '.header',
      '.footer', '.bottom-nav', '.tab-bar',
      '.breadcrumb', '.pagination', '.filters',
      '.ads', '.advertisement', '.banner',
      '.popup', '.modal', '.overlay', '.toast',
      '.loading', '.spinner', '.loader'
    ];

    uiSelectors.forEach(selector => {
      const elements = body.querySelectorAll(selector);
      elements.forEach(el => {
        // Only remove if it doesn't contain substantial content
        const text = el.textContent || '';
        if (text.length < 200) {
          el.remove();
        }
      });
    });

    const text = body.textContent || '';
    return text.replace(/\s+/g, ' ').trim();
  }

  /**
   * Format duration from seconds to readable format
   */
  private formatDuration(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
  }
}