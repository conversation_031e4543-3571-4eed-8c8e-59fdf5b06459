/**
 * AI Summary Panel Styles for Recall V3.0
 * 
 * Provides clean, modern styling for the summary panel with animations
 * and responsive design that works across different website layouts
 */

/* Panel Container */
.recall-summary-panel {
  --panel-bg: #ffffff;
  --panel-border: #e0e0e0;
  --panel-shadow: rgba(0, 0, 0, 0.15);
  --panel-text: #333333;
  --panel-text-secondary: #666666;
  --panel-accent: #2196f3;
  --panel-accent-hover: #1976d2;
  --panel-danger: #f44336;
  --panel-danger-hover: #d32f2f;
  --panel-success: #4caf50;
  --panel-warning: #ff9800;
  --panel-radius: 12px;
  --panel-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  position: fixed;
  z-index: 2147483647;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: var(--panel-text);
  background: var(--panel-bg);
  border: 1px solid var(--panel-border);
  border-radius: var(--panel-radius);
  box-shadow: 0 8px 32px var(--panel-shadow);
  backdrop-filter: blur(10px);
  transition: var(--panel-transition);
  max-width: 400px;
  min-width: 280px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .recall-summary-panel {
    --panel-bg: #1e1e1e;
    --panel-border: #333333;
    --panel-shadow: rgba(0, 0, 0, 0.3);
    --panel-text: #ffffff;
    --panel-text-secondary: #cccccc;
    --panel-accent: #64b5f6;
    --panel-accent-hover: #42a5f5;
  }
}

/* Panel States */
.recall-summary-panel.collapsed {
  width: auto;
  height: auto;
}

.recall-summary-panel.expanded {
  max-height: 80vh;
  overflow-y: auto;
}

/* Scrollbar styling for the panel */
.recall-summary-panel::-webkit-scrollbar {
  width: 6px;
}

.recall-summary-panel::-webkit-scrollbar-track {
  background: transparent;
}

.recall-summary-panel::-webkit-scrollbar-thumb {
  background-color: var(--panel-border);
  border-radius: 3px;
}

.recall-summary-panel::-webkit-scrollbar-thumb:hover {
  background-color: var(--panel-text-secondary);
}

/* Panel Header */
.summary-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--panel-border);
  background: var(--panel-bg);
  border-radius: var(--panel-radius) var(--panel-radius) 0 0;
}

.summary-panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 15px;
}

.summary-icon {
  font-size: 16px;
}

.summary-text {
  color: var(--panel-text);
}

.summary-panel-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Control Buttons */
.summary-panel-controls button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: var(--panel-text-secondary);
  cursor: pointer;
  transition: var(--panel-transition);
  font-size: 14px;
}

.summary-panel-controls button:hover {
  background: var(--panel-border);
  color: var(--panel-text);
}

.summary-panel-controls button:active {
  transform: scale(0.95);
}

.summary-panel-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.summary-generate-button:hover {
  background: var(--panel-accent);
  color: white;
}

.summary-copy-button:hover {
  background: var(--panel-success);
  color: white;
}

.summary-share-button:hover {
  background: var(--panel-accent);
  color: white;
}

.summary-close-button:hover {
  background: var(--panel-danger);
  color: white;
}

/* Panel Content */
.summary-panel-content {
  padding: 16px;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading State */
.summary-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
}

.summary-loading-text {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--panel-text-secondary);
  font-size: 14px;
}

.summary-loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--panel-border);
  border-top: 2px solid var(--panel-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.summary-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px 16px;
  text-align: center;
  color: var(--panel-danger);
}

.summary-error-icon {
  font-size: 24px;
}

.summary-error-text {
  font-size: 14px;
  line-height: 1.4;
}

.summary-retry-button {
  padding: 8px 16px;
  border: 1px solid var(--panel-danger);
  border-radius: 6px;
  background: transparent;
  color: var(--panel-danger);
  cursor: pointer;
  transition: var(--panel-transition);
  font-size: 13px;
}

.summary-retry-button:hover {
  background: var(--panel-danger);
  color: white;
}

/* Summary Content */
.summary-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.summary-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.summary-section-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--panel-text);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.summary-text {
  font-size: 14px;
  line-height: 1.5;
  color: var(--panel-text);
}

/* Key Points */
.summary-key-points {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.summary-key-point {
  position: relative;
  padding-left: 16px;
  font-size: 14px;
  line-height: 1.4;
  color: var(--panel-text);
}

.summary-key-point::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--panel-accent);
  font-weight: bold;
}

/* Tags */
.summary-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.summary-tag {
  padding: 4px 8px;
  background: var(--panel-accent);
  color: white;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Metadata */
.summary-metadata {
  padding-top: 8px;
  border-top: 1px solid var(--panel-border);
  font-size: 12px;
  color: var(--panel-text-secondary);
}

.summary-reading-time {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Initial State */
.summary-initial {
  padding: 32px 16px;
  text-align: center;
}

.summary-initial-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.summary-initial-icon {
  font-size: 32px;
  opacity: 0.7;
}

.summary-initial-text {
  font-size: 14px;
  color: var(--panel-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.summary-generate-button-large {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background: var(--panel-accent);
  color: white;
  cursor: pointer;
  transition: var(--panel-transition);
  font-size: 14px;
  font-weight: 500;
}

.summary-generate-button-large:hover {
  background: var(--panel-accent-hover);
  transform: translateY(-1px);
}

.summary-generate-button-large:active {
  transform: translateY(0);
}

.summary-generate-button-large:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .recall-summary-panel {
    max-width: calc(100vw - 32px);
    min-width: 280px;
  }
  
  .summary-panel-content {
    padding: 12px;
  }
  
  .summary-initial {
    padding: 24px 12px;
  }
}

@media (max-height: 600px) {
  .recall-summary-panel.expanded {
    max-height: 60vh;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .recall-summary-panel {
    --panel-border: #000000;
    border-width: 2px;
  }
  
  .summary-panel-controls button {
    border: 1px solid var(--panel-border);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .recall-summary-panel,
  .summary-panel-controls button,
  .summary-loading-spinner,
  .summary-generate-button-large {
    transition: none;
    animation: none;
  }
  
  .summary-panel-content {
    animation: none;
  }
}

/* Print Styles */
@media print {
  .recall-summary-panel {
    display: none !important;
  }
}

/* Focus Management */
.recall-summary-panel *:focus {
  outline: 2px solid var(--panel-accent);
  outline-offset: 2px;
}

.recall-summary-panel button:focus-visible {
  outline: 2px solid var(--panel-accent);
  outline-offset: 2px;
}

/* Interaction States */
.recall-summary-panel:hover {
  box-shadow: 0 12px 40px var(--panel-shadow);
}

/* Ensure panel doesn't interfere with page interactions */
.recall-summary-panel * {
  box-sizing: border-box;
  user-select: text;
}

.recall-summary-panel button,
.recall-summary-panel input {
  user-select: none;
}

/* Override any conflicting website styles */
.recall-summary-panel,
.recall-summary-panel * {
  all: unset;
}

.recall-summary-panel {
  display: block !important;
  position: fixed !important;
  z-index: 2147483647 !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  color: var(--panel-text) !important;
  background: var(--panel-bg) !important;
  border: 1px solid var(--panel-border) !important;
  border-radius: var(--panel-radius) !important;
  box-shadow: 0 8px 32px var(--panel-shadow) !important;
}

/* Restore necessary styling after reset */
.recall-summary-panel button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
}

.recall-summary-panel ul {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.recall-summary-panel h3 {
  font-weight: 600 !important;
  margin: 0 !important;
}

.recall-summary-panel p {
  margin: 0 !important;
}

/* Markdown Content Styles */
.markdown-content {
  font-size: 14px;
  line-height: 1.5;
  color: var(--panel-text);
  word-wrap: break-word;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin: 0 0 8px 0;
  font-weight: 600;
  color: var(--panel-text);
}

.markdown-content h1 { font-size: 18px; }
.markdown-content h2 { font-size: 16px; }
.markdown-content h3 { font-size: 15px; }
.markdown-content h4 { font-size: 14px; }
.markdown-content h5 { font-size: 13px; }
.markdown-content h6 { font-size: 12px; }

.markdown-content p {
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.markdown-content p:last-child {
  margin-bottom: 0;
}

.markdown-content strong {
  font-weight: 600;
  color: var(--panel-text);
}

.markdown-content em {
  font-style: italic;
}

.markdown-content del {
  text-decoration: line-through;
  opacity: 0.7;
}

.markdown-content code {
  padding: 2px 4px;
  background: var(--panel-border);
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.markdown-content pre {
  margin: 8px 0;
  padding: 12px;
  background: var(--panel-border);
  border-radius: 6px;
  overflow-x: auto;
  font-size: 13px;
}

.markdown-content pre code {
  padding: 0;
  background: transparent;
  border-radius: 0;
}

.markdown-content a {
  color: var(--panel-accent);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.markdown-content a:hover {
  border-bottom-color: var(--panel-accent);
}

.markdown-content blockquote {
  margin: 8px 0;
  padding: 8px 12px;
  border-left: 3px solid var(--panel-accent);
  background: var(--panel-border);
  border-radius: 0 4px 4px 0;
  font-style: italic;
}

.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-content li {
  margin: 4px 0;
  line-height: 1.4;
}

.markdown-content hr {
  margin: 16px 0;
  border: none;
  height: 1px;
  background: var(--panel-border);
}

.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 8px 0;
}

/* Markdown Style Variants */
.markdown-compact {
  font-size: 13px;
  line-height: 1.4;
}

.markdown-compact h1,
.markdown-compact h2,
.markdown-compact h3,
.markdown-compact h4,
.markdown-compact h5,
.markdown-compact h6 {
  margin: 0 0 4px 0;
}

.markdown-compact p {
  margin: 0 0 4px 0;
}

.markdown-compact ul,
.markdown-compact ol {
  margin: 4px 0;
  padding-left: 16px;
}

.markdown-compact li {
  margin: 2px 0;
}

.markdown-expanded {
  font-size: 15px;
  line-height: 1.6;
}

.markdown-expanded h1,
.markdown-expanded h2,
.markdown-expanded h3,
.markdown-expanded h4,
.markdown-expanded h5,
.markdown-expanded h6 {
  margin: 0 0 12px 0;
}

.markdown-expanded p {
  margin: 0 0 12px 0;
}

.markdown-expanded ul,
.markdown-expanded ol {
  margin: 12px 0;
  padding-left: 24px;
}

.markdown-expanded li {
  margin: 6px 0;
}

/* Dark mode adjustments for markdown */
@media (prefers-color-scheme: dark) {
  .markdown-content code {
    background: rgba(255, 255, 255, 0.1);
  }
  
  .markdown-content pre {
    background: rgba(255, 255, 255, 0.05);
  }
  
  .markdown-content blockquote {
    background: rgba(255, 255, 255, 0.05);
  }
}