/**
 * Markdown Renderer for Recall V3.0
 * 
 * Lightweight markdown renderer for displaying AI-generated content
 * with proper sanitization and formatting
 */

import React, { useMemo } from 'react';

interface MarkdownRendererProps {
  /** Markdown content to render */
  content: string;
  /** Custom CSS class */
  className?: string;
  /** Whether to allow HTML tags (sanitized) */
  allowHtml?: boolean;
  /** Maximum content length before truncation */
  maxLength?: number;
}

/**
 * Simple and secure markdown renderer
 */
export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = '',
  allowHtml = false,
  maxLength
}) => {
  const renderedContent = useMemo(() => {
    if (!content) return '';

    let processedContent = content;

    // Truncate if maxLength is specified
    if (maxLength && processedContent.length > maxLength) {
      processedContent = processedContent.slice(0, maxLength) + '...';
    }

    // Sanitize content to prevent XSS
    processedContent = sanitizeContent(processedContent);

    // Convert markdown to HTML
    const htmlContent = parseMarkdown(processedContent, allowHtml);

    return htmlContent;
  }, [content, allowHtml, maxLength]);

  return (
    <div 
      className={`markdown-content ${className}`}
      dangerouslySetInnerHTML={{ __html: renderedContent }}
    />
  );
};

/**
 * Sanitize content to prevent XSS attacks
 */
function sanitizeContent(content: string): string {
  // Remove potentially dangerous HTML tags and attributes
  const dangerousTags = /<(script|iframe|object|embed|form|input|meta|link|style)[^>]*>/gi;
  const dangerousAttributes = /(on\w+|javascript:|data:)[^=]*=["'][^"']*["']/gi;
  
  return content
    .replace(dangerousTags, '')
    .replace(dangerousAttributes, '');
}

/**
 * Parse markdown to HTML
 */
function parseMarkdown(markdown: string, allowHtml: boolean): string {
  let html = markdown;

  // If HTML is not allowed, escape HTML tags
  if (!allowHtml) {
    html = html
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }

  // Headers (# ## ### #### ##### ######)
  html = html.replace(/^#{6}\s+(.+)$/gm, '<h6>$1</h6>');
  html = html.replace(/^#{5}\s+(.+)$/gm, '<h5>$1</h5>');
  html = html.replace(/^#{4}\s+(.+)$/gm, '<h4>$1</h4>');
  html = html.replace(/^#{3}\s+(.+)$/gm, '<h3>$1</h3>');
  html = html.replace(/^#{2}\s+(.+)$/gm, '<h2>$1</h2>');
  html = html.replace(/^#{1}\s+(.+)$/gm, '<h1>$1</h1>');

  // Bold text (**text** or __text__)
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  html = html.replace(/__(.*?)__/g, '<strong>$1</strong>');

  // Italic text (*text* or _text_)
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  html = html.replace(/_(.*?)_/g, '<em>$1</em>');

  // Strikethrough (~~text~~)
  html = html.replace(/~~(.*?)~~/g, '<del>$1</del>');

  // Inline code (`code`)
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

  // Code blocks (```language\ncode\n```)
  html = html.replace(/```(\w+)?\n([\s\S]*?)\n```/g, (_match, language, code) => {
    const lang = language ? ` class="language-${language}"` : '';
    return `<pre><code${lang}>${code}</code></pre>`;
  });

  // Links [text](url)
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

  // Images ![alt](url)
  html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<img src="$2" alt="$1" />');

  // Horizontal rules (--- or ***)
  html = html.replace(/^---$/gm, '<hr>');
  html = html.replace(/^\*\*\*$/gm, '<hr>');

  // Blockquotes (> text)
  html = html.replace(/^>\s+(.+)$/gm, '<blockquote>$1</blockquote>');

  // Unordered lists (- item or * item)
  html = html.replace(/^[-*]\s+(.+)$/gm, '<li>$1</li>');
  html = html.replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>');

  // Ordered lists (1. item)
  html = html.replace(/^\d+\.\s+(.+)$/gm, '<li>$1</li>');
  // Note: This is a simple implementation. More complex ordered list handling would be needed for nested lists.

  // Line breaks (two spaces at end of line or double newline)
  html = html.replace(/ {2}$/gm, '<br>');
  html = html.replace(/\n\n/g, '</p><p>');

  // Wrap in paragraphs if not already structured
  if (!html.includes('<p>') && !html.includes('<h') && !html.includes('<ul>') && !html.includes('<ol>')) {
    html = `<p>${html}</p>`;
  }

  // Clean up any empty paragraphs
  html = html.replace(/<p><\/p>/g, '');

  // Clean up nested lists (basic cleanup)
  html = html.replace(/<\/ul>\s*<ul>/g, '');
  html = html.replace(/<\/ol>\s*<ol>/g, '');

  return html;
}

/**
 * Markdown renderer with additional styling
 */
export const StyledMarkdownRenderer: React.FC<MarkdownRendererProps & { 
  style?: 'default' | 'compact' | 'expanded' 
}> = ({ style = 'default', ...props }) => {
  const styleClass = {
    default: 'markdown-default',
    compact: 'markdown-compact',
    expanded: 'markdown-expanded'
  }[style];

  return (
    <MarkdownRenderer 
      {...props} 
      className={`${props.className || ''} ${styleClass}`}
    />
  );
};

export default MarkdownRenderer;