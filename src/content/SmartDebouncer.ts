/**
 * Smart Debouncer for Dynamic Content Detection
 * 
 * Provides intelligent delay and debouncing mechanisms to optimize performance
 * and avoid frequent triggering of content extraction processes.
 * 
 * Features:
 * - Adaptive debouncing based on activity levels
 * - Priority-based task scheduling
 * - Intelligent batching of multiple changes
 * - Performance monitoring and optimization
 */

export interface DebouncerConfig {
  /** Base debounce delay (ms) */
  baseDelay: number;
  /** Maximum debounce delay (ms) */
  maxDelay: number;
  /** Minimum debounce delay (ms) */
  minDelay: number;
  /** Activity threshold for adaptive timing */
  activityThreshold: number;
  /** Maximum pending operations before forcing execution */
  maxPendingOperations: number;
  /** Enable adaptive debouncing based on system performance */
  enableAdaptiveDebouncing: boolean;
  /** Enable debug logging */
  debug: boolean;
}

export interface DebounceTask {
  id: string;
  type: 'content-change' | 'ajax-complete' | 'scroll-change' | 'mutation' | 'periodic';
  priority: 'low' | 'medium' | 'high' | 'critical';
  callback: () => void | Promise<void>;
  timestamp: number;
  metadata?: any;
}

export interface PerformanceMetrics {
  totalExecutions: number;
  averageExecutionTime: number;
  currentActivityLevel: number;
  pendingTasks: number;
  lastExecutionTime: number;
  adaptiveDelay: number;
}

export class SmartDebouncer {
  private config: DebouncerConfig;
  private pendingTasks = new Map<string, DebounceTask>();
  private debounceTimers = new Map<string, number>();
  private activityWindow: number[] = [];
  private executionTimes: number[] = [];
  private lastExecutionTime = 0;
  private isExecuting = false;

  private static readonly DEFAULT_CONFIG: DebouncerConfig = {
    baseDelay: 1000, // 1 second base delay
    maxDelay: 5000,  // 5 seconds maximum
    minDelay: 200,   // 200ms minimum
    activityThreshold: 10, // 10 events per minute threshold
    maxPendingOperations: 5,
    enableAdaptiveDebouncing: true,
    debug: false
  };

  constructor(config: Partial<DebouncerConfig> = {}) {
    this.config = { ...SmartDebouncer.DEFAULT_CONFIG, ...config };
    this.log('SmartDebouncer initialized', this.config);
  }

  /**
   * Add a debounced task
   */
  public debounce(
    taskId: string,
    type: DebounceTask['type'],
    priority: DebounceTask['priority'],
    callback: () => void | Promise<void>,
    metadata?: any
  ): void {
    const task: DebounceTask = {
      id: taskId,
      type,
      priority,
      callback,
      timestamp: Date.now(),
      metadata
    };

    this.log('Adding debounced task', { taskId, type, priority });

    // Cancel existing timer for this task if it exists
    if (this.debounceTimers.has(taskId)) {
      clearTimeout(this.debounceTimers.get(taskId)!);
      this.debounceTimers.delete(taskId);
    }

    // Update or add the task
    this.pendingTasks.set(taskId, task);

    // Record activity
    this.recordActivity();

    // Calculate delay based on priority and current conditions
    const delay = this.calculateDebounceDelay(priority, type);

    // Check if we should force execution due to pending operations
    if (this.shouldForceExecution()) {
      this.log('Forcing execution due to pending operations threshold');
      this.executeImmediate();
      return;
    }

    // Set debounce timer
    const timer = window.setTimeout(() => {
      this.executeTask(taskId);
    }, delay);

    this.debounceTimers.set(taskId, timer);
    this.log(`Task ${taskId} scheduled for execution in ${delay}ms`);
  }

  /**
   * Execute a specific task immediately
   */
  public executeImmediate(taskId?: string): void {
    if (taskId) {
      this.executeTask(taskId);
    } else {
      this.executeAllPendingTasks();
    }
  }

  /**
   * Cancel a pending task
   */
  public cancel(taskId: string): boolean {
    if (this.debounceTimers.has(taskId)) {
      clearTimeout(this.debounceTimers.get(taskId)!);
      this.debounceTimers.delete(taskId);
    }

    if (this.pendingTasks.has(taskId)) {
      this.pendingTasks.delete(taskId);
      this.log(`Task ${taskId} cancelled`);
      return true;
    }

    return false;
  }

  /**
   * Cancel all pending tasks
   */
  public cancelAll(): void {
    this.debounceTimers.forEach(timer => clearTimeout(timer));
    this.debounceTimers.clear();
    this.pendingTasks.clear();
    this.log('All pending tasks cancelled');
  }

  /**
   * Get current performance metrics
   */
  public getMetrics(): PerformanceMetrics {
    return {
      totalExecutions: this.executionTimes.length,
      averageExecutionTime: this.getAverageExecutionTime(),
      currentActivityLevel: this.getCurrentActivityLevel(),
      pendingTasks: this.pendingTasks.size,
      lastExecutionTime: this.lastExecutionTime,
      adaptiveDelay: this.calculateAdaptiveDelay()
    };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<DebouncerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.log('Configuration updated', this.config);
  }

  /**
   * Calculate debounce delay based on priority and conditions
   */
  private calculateDebounceDelay(
    priority: DebounceTask['priority'], 
    type: DebounceTask['type']
  ): number {
    let baseDelay = this.config.baseDelay;

    // Priority-based adjustments
    switch (priority) {
      case 'critical':
        baseDelay = this.config.minDelay;
        break;
      case 'high':
        baseDelay = Math.max(baseDelay * 0.5, this.config.minDelay);
        break;
      case 'medium':
        baseDelay = baseDelay;
        break;
      case 'low':
        baseDelay = Math.min(baseDelay * 1.5, this.config.maxDelay);
        break;
    }

    // Type-based adjustments
    switch (type) {
      case 'ajax-complete':
        baseDelay = Math.max(baseDelay * 0.8, this.config.minDelay);
        break;
      case 'content-change':
        baseDelay = baseDelay;
        break;
      case 'scroll-change':
        baseDelay = Math.min(baseDelay * 1.2, this.config.maxDelay);
        break;
      case 'mutation':
        baseDelay = Math.max(baseDelay * 0.9, this.config.minDelay);
        break;
      case 'periodic':
        baseDelay = Math.min(baseDelay * 2, this.config.maxDelay);
        break;
    }

    // Apply adaptive debouncing if enabled
    if (this.config.enableAdaptiveDebouncing) {
      const adaptiveDelay = this.calculateAdaptiveDelay();
      baseDelay = Math.max(baseDelay * adaptiveDelay, this.config.minDelay);
    }

    return Math.min(Math.max(baseDelay, this.config.minDelay), this.config.maxDelay);
  }

  /**
   * Calculate adaptive delay multiplier based on system performance
   */
  private calculateAdaptiveDelay(): number {
    const activityLevel = this.getCurrentActivityLevel();
    const avgExecutionTime = this.getAverageExecutionTime();

    // High activity = longer delays to reduce load
    let multiplier = 1.0;

    if (activityLevel > this.config.activityThreshold) {
      multiplier = 1.5; // Increase delay by 50% during high activity
    } else if (activityLevel > this.config.activityThreshold * 0.5) {
      multiplier = 1.2; // Increase delay by 20% during medium activity
    }

    // Slow execution times = longer delays
    if (avgExecutionTime > 1000) { // If average execution > 1 second
      multiplier *= 1.3;
    } else if (avgExecutionTime > 500) { // If average execution > 500ms
      multiplier *= 1.1;
    }

    return Math.min(multiplier, 3.0); // Cap at 3x multiplier
  }

  /**
   * Get current activity level (events per minute)
   */
  private getCurrentActivityLevel(): number {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;

    // Clean old activity records
    this.activityWindow = this.activityWindow.filter(time => time > oneMinuteAgo);

    return this.activityWindow.length;
  }

  /**
   * Get average execution time
   */
  private getAverageExecutionTime(): number {
    if (this.executionTimes.length === 0) return 0;

    const sum = this.executionTimes.reduce((acc, time) => acc + time, 0);
    return sum / this.executionTimes.length;
  }

  /**
   * Record activity for adaptive debouncing
   */
  private recordActivity(): void {
    const now = Date.now();
    this.activityWindow.push(now);

    // Keep only last 100 activities for memory efficiency
    if (this.activityWindow.length > 100) {
      this.activityWindow = this.activityWindow.slice(-50);
    }
  }

  /**
   * Check if execution should be forced due to pending operations
   */
  private shouldForceExecution(): boolean {
    return this.pendingTasks.size >= this.config.maxPendingOperations;
  }

  /**
   * Execute a specific task
   */
  private async executeTask(taskId: string): Promise<void> {
    const task = this.pendingTasks.get(taskId);
    if (!task) {
      this.log(`Task ${taskId} not found for execution`);
      return;
    }

    this.log(`Executing task ${taskId}`, { type: task.type, priority: task.priority });

    const startTime = Date.now();

    try {
      // Clean up timer and task from pending
      if (this.debounceTimers.has(taskId)) {
        clearTimeout(this.debounceTimers.get(taskId)!);
        this.debounceTimers.delete(taskId);
      }
      this.pendingTasks.delete(taskId);

      // Execute the callback
      await task.callback();

      // Record execution time
      const executionTime = Date.now() - startTime;
      this.recordExecutionTime(executionTime);

      this.log(`Task ${taskId} completed in ${executionTime}ms`);

    } catch (error) {
      this.log(`Task ${taskId} failed`, error);
    }
  }

  /**
   * Execute all pending tasks in priority order
   */
  private async executeAllPendingTasks(): Promise<void> {
    if (this.isExecuting) {
      this.log('Already executing tasks, skipping batch execution');
      return;
    }

    this.isExecuting = true;
    const tasksToExecute = Array.from(this.pendingTasks.values());

    // Sort by priority (critical > high > medium > low)
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    tasksToExecute.sort((a, b) => {
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      // If same priority, sort by timestamp (older first)
      return a.timestamp - b.timestamp;
    });

    this.log(`Executing ${tasksToExecute.length} pending tasks in batch`);

    for (const task of tasksToExecute) {
      await this.executeTask(task.id);
    }

    this.isExecuting = false;
  }

  /**
   * Record execution time for performance monitoring
   */
  private recordExecutionTime(executionTime: number): void {
    this.executionTimes.push(executionTime);
    this.lastExecutionTime = Date.now();

    // Keep only last 50 execution times for memory efficiency
    if (this.executionTimes.length > 50) {
      this.executionTimes = this.executionTimes.slice(-25);
    }
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.debug) {
      console.log(`[SmartDebouncer] ${message}`, data || '');
    }
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    this.cancelAll();
    this.activityWindow = [];
    this.executionTimes = [];
    this.log('SmartDebouncer destroyed');
  }
}