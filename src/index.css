/* Global reset */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Root styles */
html {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  height: 100%;
  margin: 0;
  background: #ffffff;
  color: #333333;
  overflow: hidden;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Form elements reset */
button, input, select, textarea {
  font-family: inherit;
  font-size: inherit;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
}

/* Remove default input styles */
input, select, textarea {
  background: none;
  border: none;
  outline: none;
}

/* Link styles */
a {
  color: #667eea;
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #5a6fd8;
}

/* Utility classes */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
