import { useState, useEffect, useCallback, useMemo } from 'react';

/**
 * 虚拟滚动配置接口
 */
interface VirtualScrollConfig {
  /** 容器高度（像素） */
  containerHeight: number;
  /** 每项高度（像素） */
  itemHeight: number;
  /** 缓冲区项目数量（上下各添加的额外项目） */
  overscan?: number;
  /** 滚动防抖延迟（毫秒） */
  scrollDebounce?: number;
}

/**
 * 虚拟滚动状态接口
 */
interface VirtualScrollState<T> {
  /** 开始索引 */
  startIndex: number;
  /** 结束索引 */
  endIndex: number;
  /** 可见项目数组 */
  visibleItems: T[];
  /** 总高度 */
  totalHeight: number;
  /** 偏移量 */
  offsetY: number;
}

/**
 * 虚拟滚动Hook
 * 用于优化大量列表项的渲染性能
 * 
 * @param items - 完整的数据项数组
 * @param config - 虚拟滚动配置
 * @returns 虚拟滚动状态和滚动处理函数
 */
export function useVirtualScrolling<T>(
  items: T[],
  config: VirtualScrollConfig
): {
  virtualState: VirtualScrollState<T>;
  onScroll: (event: React.UIEvent<HTMLDivElement>) => void;
  scrollToIndex: (index: number) => void;
} {
  const {
    containerHeight,
    itemHeight,
    overscan = 5,
    scrollDebounce = 16
  } = config;

  const [scrollTop, setScrollTop] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);

  // 计算虚拟滚动状态
  const virtualState = useMemo((): VirtualScrollState<T> => {
    if (!items.length) {
      return {
        startIndex: 0,
        endIndex: 0,
        visibleItems: [],
        totalHeight: 0,
        offsetY: 0
      };
    }

    const totalHeight = items.length * itemHeight;
    const visibleItemCount = Math.ceil(containerHeight / itemHeight);
    
    // 计算可见范围
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const endIndex = Math.min(
      items.length - 1,
      startIndex + visibleItemCount + overscan * 2
    );

    // 获取可见项目
    const visibleItems = items.slice(startIndex, endIndex + 1);
    
    // 计算偏移量
    const offsetY = startIndex * itemHeight;

    return {
      startIndex,
      endIndex,
      visibleItems,
      totalHeight,
      offsetY
    };
  }, [items, scrollTop, containerHeight, itemHeight, overscan]);

  // 防抖的滚动处理
  const debouncedScrollEnd = useCallback(() => {
    const timeoutId = setTimeout(() => {
      setIsScrolling(false);
    }, scrollDebounce);

    return () => clearTimeout(timeoutId);
  }, [scrollDebounce]);

  // 滚动事件处理
  const onScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = event.currentTarget.scrollTop;
    
    setScrollTop(newScrollTop);
    setIsScrolling(true);
    
    // 防抖处理滚动结束
    debouncedScrollEnd();
  }, [debouncedScrollEnd]);

  // 滚动到指定索引
  const scrollToIndex = useCallback((index: number) => {
    const targetScrollTop = Math.max(0, index * itemHeight);
    setScrollTop(targetScrollTop);
  }, [itemHeight]);

  // 性能监控（开发环境）
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const visibleCount = virtualState.endIndex - virtualState.startIndex + 1;
      console.debug(`[VirtualScroll] Rendering ${visibleCount}/${items.length} items`, {
        startIndex: virtualState.startIndex,
        endIndex: virtualState.endIndex,
        scrollTop,
        isScrolling
      });
    }
  }, [virtualState, scrollTop, isScrolling, items.length]);

  return {
    virtualState,
    onScroll,
    scrollToIndex
  };
}

/**
 * 虚拟滚动项目组件的Props接口
 */
export interface VirtualScrollItemProps {
  /** 项目索引 */
  index: number;
  /** 项目高度 */
  height: number;
  /** 是否正在滚动 */
  isScrolling?: boolean;
}

/**
 * 获取虚拟滚动的性能统计
 */
export function getVirtualScrollStats(
  totalItems: number,
  visibleItems: number
): {
  renderRatio: number;
  memoryReduction: number;
  description: string;
} {
  const renderRatio = totalItems > 0 ? visibleItems / totalItems : 0;
  const memoryReduction = 1 - renderRatio;
  
  return {
    renderRatio,
    memoryReduction,
    description: `渲染 ${visibleItems}/${totalItems} 项目 (节省 ${(memoryReduction * 100).toFixed(1)}% 内存)`
  };
}