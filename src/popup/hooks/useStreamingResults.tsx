/**
 * 流式搜索结果Hook
 * 
 * 将搜索结果分批渐进式显示，提升首屏展示速度和用户体验
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import type { SearchResultItem } from '../../models';

/**
 * 流式结果Hook配置接口
 */
interface UseStreamingResultsProps {
  /** 完整的搜索结果数组 */
  results: SearchResultItem[];
  /** 每批显示的结果数量，默认10个 */
  batchSize?: number;
  /** 批次间的延迟时间，默认50ms */
  delay?: number;
  /** 是否启用流式加载，默认true */
  enabled?: boolean;
}

/**
 * 流式结果Hook返回值接口
 */
interface UseStreamingResultsReturn {
  /** 当前显示的结果 */
  displayedResults: SearchResultItem[];
  /** 是否正在流式加载 */
  isStreaming: boolean;
  /** 加载进度 (0-100) */
  progress: number;
  /** 是否已完成所有加载 */
  isComplete: boolean;
}

/**
 * 流式搜索结果Hook
 */
export const useStreamingResults = ({
  results,
  batchSize = 10,
  delay = 50,
  enabled = true
}: UseStreamingResultsProps): UseStreamingResultsReturn => {
  const [displayedResults, setDisplayedResults] = useState<SearchResultItem[]>([]);
  const [currentBatch, setCurrentBatch] = useState(0);
  const [isStreaming, setIsStreaming] = useState(false);
  
  // 用于清理定时器的ref
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const requestRef = useRef<number | null>(null);

  // 计算总批次数
  const totalBatches = Math.ceil(results.length / batchSize);
  
  // 计算进度
  const progress = totalBatches > 0 ? Math.round((currentBatch / totalBatches) * 100) : 0;
  
  // 是否完成
  const isComplete = currentBatch >= totalBatches;

  /**
   * 清理所有定时器和动画帧
   */
  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    if (requestRef.current) {
      cancelAnimationFrame(requestRef.current);
      requestRef.current = null;
    }
  }, []);

  /**
   * 重置流式加载状态
   */
  const reset = useCallback(() => {
    cleanup();
    setDisplayedResults([]);
    setCurrentBatch(0);
    setIsStreaming(false);
  }, [cleanup]);

  /**
   * 添加下一批结果
   */
  const addNextBatch = useCallback(() => {
    if (currentBatch >= totalBatches) {
      setIsStreaming(false);
      return;
    }

    const startIndex = currentBatch * batchSize;
    const endIndex = Math.min(startIndex + batchSize, results.length);
    const nextBatch = results.slice(startIndex, endIndex);

    // 使用requestAnimationFrame优化渲染性能
    requestRef.current = requestAnimationFrame(() => {
      setDisplayedResults(prev => [...prev, ...nextBatch]);
      setCurrentBatch(prev => prev + 1);

      // 如果还有更多批次，设置下一次的定时器
      if (currentBatch + 1 < totalBatches) {
        timeoutRef.current = setTimeout(addNextBatch, delay);
      } else {
        setIsStreaming(false);
      }
    });
  }, [currentBatch, totalBatches, batchSize, results, delay]);

  /**
   * 启动流式加载
   */
  const startStreaming = useCallback(() => {
    if (!enabled || results.length === 0) {
      setDisplayedResults(results);
      setIsStreaming(false);
      return;
    }

    // 如果结果数量较少，直接显示所有结果
    if (results.length <= batchSize) {
      setDisplayedResults(results);
      setCurrentBatch(1);
      setIsStreaming(false);
      return;
    }

    // 重置状态并开始流式加载
    reset();
    setIsStreaming(true);
    
    // 立即显示第一批结果
    const firstBatch = results.slice(0, batchSize);
    setDisplayedResults(firstBatch);
    setCurrentBatch(1);

    // 设置后续批次的定时器
    if (results.length > batchSize) {
      timeoutRef.current = setTimeout(addNextBatch, delay);
    } else {
      setIsStreaming(false);
    }
  }, [enabled, results, batchSize, delay, reset, addNextBatch]);

  // 当results变化时，启动流式加载
  useEffect(() => {
    startStreaming();
    
    // 清理函数
    return cleanup;
  }, [startStreaming, cleanup]);

  // 组件卸载时清理
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    displayedResults,
    isStreaming,
    progress,
    isComplete
  };
};

export default useStreamingResults;