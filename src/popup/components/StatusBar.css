/* ============================================================================
   Status Bar Component Styles
   Fixed layout to prevent overlapping content
   ============================================================================ */

   .hg-status-bar {
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    font-size: 12px;
  }
  
  /* ============================================================================
     Main Status Section
     ============================================================================ */
  
  .hg-status-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    min-height: 48px; /* Ensure minimum height */
    gap: 16px; /* Space between content and actions */
  }
  
  .hg-status-content {
    flex: 1;
    min-width: 0; /* Allow content to shrink */
  }
  
  /* Loading State */
  .hg-status-loading {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
  }
  
  .hg-loading-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .hg-loading-text {
    font-size: 12px;
    color: #6b7280;
  }
  
  /* Metrics Display */
  .hg-status-metrics {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }
  
  .hg-metric-group {
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
  }
  
  .hg-metric-icon {
    font-size: 14px;
    line-height: 1;
  }
  
  .hg-metric-content {
    display: flex;
    flex-direction: column;
    gap: 1px;
  }
  
  .hg-metric-value {
    font-size: 12px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.2;
  }
  
  .hg-metric-label {
    font-size: 10px;
    color: #6b7280;
    line-height: 1.2;
  }
  
  /* Specific metric styling */
  .hg-results-metric .hg-metric-value {
    color: #059669;
  }
  
  .hg-performance-metric .hg-metric-value {
    color: #dc2626;
  }
  
  .hg-database-metric .hg-metric-value {
    color: #7c3aed;
  }
  
  .hg-health-metric .hg-metric-value {
    color: #059669;
  }
  
  /* ============================================================================
     Action Buttons
     ============================================================================ */
  
  .hg-status-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-shrink: 0; /* Don't shrink action buttons */
  }
  
  .hg-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: transparent;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
  }
  
  .hg-action-btn:hover {
    background: #f3f4f6;
    border-color: #d1d5db;
    color: #374151;
  }
  
  .hg-action-btn.active {
    background: #dbeafe;
    border-color: #93c5fd;
    color: #1d4ed8;
  }
  
  .hg-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* Icon animations */
  .hg-expand-icon {
    font-size: 12px;
    transition: transform 0.2s ease;
  }
  
  .hg-expand-icon.expanded {
    transform: rotate(180deg);
  }
  
  .hg-refresh-icon {
    font-size: 12px;
    transition: transform 0.3s ease;
  }
  
  .hg-refresh-icon.spinning {
    animation: spin 1s linear infinite;
  }
  
  .hg-settings-icon {
    font-size: 12px;
  }
  
  /* ============================================================================
     Details Panel
     ============================================================================ */
  
  .hg-status-details {
    border-top: 1px solid #e5e7eb;
    background: #ffffff;
    padding: 16px 20px;
    animation: slideDown 0.2s ease-out;
  }
  
  .hg-details-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }
  
  .hg-details-title {
    font-size: 13px;
    font-weight: 600;
    color: #1f2937;
  }
  
  .hg-last-update {
    font-size: 11px;
    color: #9ca3af;
  }
  
  /* Details Grid */
  .hg-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 16px;
  }
  
  .hg-detail-card {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
  }
  
  .hg-detail-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 8px;
  }
  
  .hg-detail-icon {
    font-size: 14px;
  }
  
  .hg-detail-title {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
  }
  
  .hg-detail-stats {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  .hg-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .hg-detail-label {
    font-size: 11px;
    color: #6b7280;
  }
  
  .hg-detail-value {
    font-size: 11px;
    font-weight: 600;
    color: #1f2937;
  }
  
  .hg-performance-badge {
    padding: 2px 6px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 4px;
    font-size: 10px;
  }
  
  /* ============================================================================
     Quick Actions
     ============================================================================ */
  
  .hg-quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
  
  .hg-quick-action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 11px;
    color: #374151;
  }
  
  .hg-quick-action-btn:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
  }
  
  .hg-action-icon {
    font-size: 12px;
  }
  
  .hg-action-text {
    font-weight: 500;
  }
  
  /* ============================================================================
     Animations
     ============================================================================ */
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* ============================================================================
     Responsive Design
     ============================================================================ */
  
  @media (max-width: 600px) {
    .hg-status-main {
      padding: 10px 16px;
      gap: 12px;
    }
  
    .hg-status-metrics {
      gap: 12px;
    }
  
    .hg-metric-group {
      gap: 4px;
    }
  
    .hg-status-details {
      padding: 12px 16px;
    }
  
    .hg-details-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  
    .hg-quick-actions {
      gap: 6px;
    }
  
    .hg-quick-action-btn {
      padding: 6px 10px;
      font-size: 10px;
    }
  }
  
  @media (max-width: 400px) {
    .hg-status-metrics {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  
    .hg-status-main {
      flex-direction: column;
      align-items: stretch;
      gap: 8px;
    }
  
    .hg-status-actions {
      justify-content: center;
    }
  }
  