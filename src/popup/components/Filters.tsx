/**
 * 搜索过滤器组件 - 紧凑内联版本
 *
 * 提供时间范围、域名过滤和排序选项的紧凑版本
 */

import React, { useState, useCallback } from "react"
import type { SearchOptions } from "../../models"
import { I18nManager } from "../../i18n/I18nManager"

/**
 * 过滤器属性接口
 */
interface FiltersProps {
  /** 当前过滤器 */
  filters: Partial<SearchOptions>
  /** 过滤器变化回调 */
  onChange: (filters: Partial<SearchOptions>) => void
}

/**
 * Get time range options with i18n
 */
const getTimeRangeOptions = () => {
  const i18n = I18nManager.getInstance();
  return [
    { label: i18n.getTranslation("filters.all"), value: null, icon: "🕐" },
    { label: i18n.getTranslation("filters.today"), value: "today", icon: "📅" },
    { label: i18n.getTranslation("filters.thisWeek"), value: "this_week", icon: "📆" },
    { label: i18n.getTranslation("filters.thisMonth"), value: "this_month", icon: "🗓️" },
  ];
};

/**
 * Get sort options with i18n
 */
const getSortOptions = () => {
  const i18n = I18nManager.getInstance();
  return [
    { label: i18n.getTranslation("filters.relevance"), value: "relevance", icon: "🎯" },
    { label: i18n.getTranslation("filters.time"), value: "time", icon: "⏰" },
    { label: i18n.getTranslation("filters.accessCount"), value: "accessCount", icon: "🔢" },
  ];
};

/**
 * 紧凑过滤器组件
 */
export const Filters: React.FC<FiltersProps> = ({ filters, onChange }) => {
  const [showDomainInput, setShowDomainInput] = useState(false)
  const [customDomain, setCustomDomain] = useState("")

  /**
   * 获取时间范围
   */
  const getTimeRange = (rangeType: string | null): { start: number; end: number } | undefined => {
    if (!rangeType) return undefined

    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    switch (rangeType) {
      case "today":
        return {
          start: today.getTime(),
          end: now.getTime(),
        }

      case "this_week":
        const thisWeekStart = new Date(today)
        thisWeekStart.setDate(today.getDate() - today.getDay())
        return {
          start: thisWeekStart.getTime(),
          end: now.getTime(),
        }

      case "this_month":
        const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1)
        return {
          start: thisMonthStart.getTime(),
          end: now.getTime(),
        }

      default:
        return undefined
    }
  }

  /**
   * 处理时间范围变化
   */
  const handleTimeRangeChange = useCallback(
    (rangeType: string | null) => {
      const timeRange = getTimeRange(rangeType)
      onChange({ ...filters, timeRange })
    },
    [filters, onChange],
  )

  /**
   * 处理排序变化
   */
  const handleSortChange = useCallback(
    (sortBy: string) => {
      onChange({ ...filters, sortBy: sortBy as "relevance" | "time" | "accessCount" })
    },
    [filters, onChange],
  )

  /**
   * 处理域名过滤
   */
  const handleDomainFilter = useCallback(
    (domain: string) => {
      const currentDomains = filters.domains || []
      const isSelected = currentDomains.includes(domain)

      const newDomains = isSelected ? currentDomains.filter((d) => d !== domain) : [...currentDomains, domain]

      onChange({ ...filters, domains: newDomains.length > 0 ? newDomains : undefined })
    },
    [filters, onChange],
  )

  /**
   * 添加自定义域名
   */
  const handleAddCustomDomain = useCallback(() => {
    if (customDomain.trim()) {
      handleDomainFilter(customDomain.trim())
      setCustomDomain("")
      setShowDomainInput(false)
    }
  }, [customDomain, handleDomainFilter])

  /**
   * 清除所有过滤器
   */
  const handleClearFilters = useCallback(() => {
    onChange({})
  }, [onChange])

  /**
   * 检查是否有活动过滤器
   */
  const hasActiveFilters = Boolean(
    filters.timeRange || filters.domains?.length || (filters.sortBy && filters.sortBy !== "relevance"),
  )

  /**
   * 获取当前时间范围选项
   */
  const getCurrentTimeRange = () => {
    const timeRangeOptions = getTimeRangeOptions();
    if (!filters.timeRange) return timeRangeOptions[0];

    // 简化判断逻辑，基于时间范围的存在性
    const today = new Date().setHours(0, 0, 0, 0);
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    weekStart.setHours(0, 0, 0, 0);

    if (filters.timeRange.start >= today) return timeRangeOptions[1]; // today
    if (filters.timeRange.start >= weekStart.getTime()) return timeRangeOptions[2]; // this_week
    return timeRangeOptions[3]; // this_month
  };

  return (
    <div className="filters-inline">
      {/* 时间范围选择器 */}
      <div className="filter-dropdown">
        <select
          value={getCurrentTimeRange().value || "all"}
          onChange={(e) => handleTimeRangeChange(e.target.value === "all" ? null : e.target.value)}
          className="filter-select-compact"
          title="时间范围"
        >
          {getTimeRangeOptions().map((option) => (
            <option key={option.value || "all"} value={option.value || "all"}>
              {option.icon} {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* 排序选择器 */}
      <div className="filter-dropdown">
        <select
          value={filters.sortBy || "relevance"}
          onChange={(e) => handleSortChange(e.target.value)}
          className="filter-select-compact"
          title="排序方式"
        >
          {getSortOptions().map((option) => (
            <option key={option.value} value={option.value}>
              {option.icon} {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* 域名过滤按钮 */}
      <div className="filter-domain-container">
        <button
          type="button"
          onClick={() => setShowDomainInput(!showDomainInput)}
          className={`filter-btn-compact ${filters.domains?.length ? "active" : ""}`}
          title={`域名过滤 ${filters.domains?.length ? `(${filters.domains.length})` : ""}`}
        >
          🌐{filters.domains?.length ? <span className="filter-count">{filters.domains.length}</span> : null}
        </button>

        {/* 域名输入弹出框 */}
        {showDomainInput && (
          <div className="domain-input-popup">
            <div className="domain-input-header">
              <input
                type="text"
                value={customDomain}
                onChange={(e) => setCustomDomain(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleAddCustomDomain()
                  } else if (e.key === "Escape") {
                    setShowDomainInput(false)
                  }
                }}
                placeholder="输入域名..."
                className="domain-input-compact"
                autoFocus
              />
              <button
                type="button"
                onClick={handleAddCustomDomain}
                className="add-btn-compact"
                disabled={!customDomain.trim()}
              >
                ✓
              </button>
            </div>

            {/* 快捷域名 */}
            <div className="quick-domains">
              {["github.com", "stackoverflow.com", "google.com"].map((domain) => (
                <button
                  key={domain}
                  type="button"
                  onClick={() => handleDomainFilter(domain)}
                  className={`quick-domain-btn ${filters.domains?.includes(domain) ? "active" : ""}`}
                >
                  {domain}
                </button>
              ))}
            </div>

            {/* 已选择的域名 */}
            {filters.domains && filters.domains.length > 0 && (
              <div className="selected-domains-compact">
                {filters.domains.map((domain) => (
                  <span key={domain} className="selected-domain-compact">
                    {domain}
                    <button type="button" onClick={() => handleDomainFilter(domain)} className="remove-domain-compact">
                      ✕
                    </button>
                  </span>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* 清除过滤器按钮 */}
      {hasActiveFilters && (
        <button type="button" onClick={handleClearFilters} className="clear-filters-compact" title="清除所有过滤器">
          ✕
        </button>
      )}
    </div>
  )
}
