/**
 * 搜索栏组件
 * 
 * 提供搜索输入、清除按钮和加载状态显示
 */

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { hybridSearchService } from '../../services';
import { SyntaxHelpWithTrigger } from './SyntaxHelp';
import { debounce } from '../../utils';

/**
 * 搜索栏属性接口
 */
interface SearchBarProps {
  /** 搜索值 */
  value: string;
  /** 值变化回调 */
  onChange: (value: string) => void;
  /** 清除回调 */
  onClear: () => void;
  /** 是否正在加载 */
  isLoading?: boolean;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
}

/**
 * 搜索栏组件 - 使用React.memo优化渲染性能
 */
export const SearchBar: React.FC<SearchBarProps> = React.memo(({
  value,
  onChange,
  onClear,
  isLoading = false,
  placeholder = '搜索...',
  autoFocus = true
}) => {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const blurTimerRef = useRef<number | null>(null);

  /**
   * 获取搜索建议的核心函数
   */
  const fetchSuggestionsCore = useCallback(async (query: string) => {
    if (query.length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const suggestions = await hybridSearchService.getSuggestions(query, 5);
      setSuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
      setSelectedSuggestionIndex(-1);
    } catch (error) {
      console.error('Failed to fetch suggestions:', error);
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, []);

  /**
   * 防抖的建议获取函数
   */
  const fetchSuggestions = useMemo(
    () => debounce(fetchSuggestionsCore, 200),  // 200ms延迟，比搜索更快响应
    [fetchSuggestionsCore]
  );

  // 自动聚焦
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // 清理计时器，防止内存泄漏
  useEffect(() => {
    return () => {
      if (blurTimerRef.current) {
        clearTimeout(blurTimerRef.current);
        blurTimerRef.current = null;
      }
      // 清理防抖函数
      fetchSuggestions.cancel();
    };
  }, [fetchSuggestions]);

  /**
   * 处理输入变化
   */
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    // 直接调用onChange，让父组件处理
    onChange(newValue);
    fetchSuggestions(newValue);
  }, [onChange, fetchSuggestions]);

  /**
   * 处理键盘事件
   */
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) {
      if (e.key === 'Escape') {
        onClear();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;

      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;

      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0) {
          const selectedSuggestion = suggestions[selectedSuggestionIndex];
          onChange(selectedSuggestion);
          setShowSuggestions(false);
        }
        break;

      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  }, [showSuggestions, suggestions, selectedSuggestionIndex, onChange, onClear]);

  /**
   * 处理建议点击
   */
  const handleSuggestionClick = useCallback((suggestion: string) => {
    onChange(suggestion);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
  }, [onChange]);

  /**
   * 处理清除按钮点击
   */
  const handleClearClick = useCallback(() => {
    // 取消防抖的建议获取
    fetchSuggestions.cancel();
    
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
    onClear();
    
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [onClear, fetchSuggestions]);

  /**
   * 处理输入框失焦
   */
  const handleBlur = useCallback(() => {
    // 清理之前的计时器
    if (blurTimerRef.current) {
      clearTimeout(blurTimerRef.current);
      blurTimerRef.current = null;
    }
    
    // 延迟隐藏建议，以便点击建议项能够正常工作
    blurTimerRef.current = window.setTimeout(() => {
      setShowSuggestions(false);
      setSelectedSuggestionIndex(-1);
      blurTimerRef.current = null;
    }, 150);
  }, []);

  /**
   * 处理输入框聚焦
   */
  const handleFocus = useCallback(() => {
    if (suggestions.length > 0) {
      setShowSuggestions(true);
    }
  }, [suggestions.length]);

  return (
    <div className="search-bar">
      <div className="search-input-container">
        {/* 搜索图标 */}
        <div className={`search-icon ${isLoading ? 'loading' : ''}`}>
          {isLoading ? (
            <div className="loading-spinner" />
          ) : (
            <span className="search-icon-symbol">🔍</span>
          )}
        </div>

        {/* 输入框 */}
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={handleFocus}
          placeholder={placeholder}
          className={`search-input ${isLoading ? 'loading' : ''} ${value ? 'has-value' : ''}`}
        />

        {/* 语法帮助按钮 */}
        <div className="search-help-container">
          <SyntaxHelpWithTrigger />
        </div>

        {/* 清除按钮 */}
        {value && (
          <button
            type="button"
            onClick={handleClearClick}
            className="clear-button"
            title="清除搜索"
          >
            ✕
          </button>
        )}
      </div>

      {/* 搜索建议 */}
      {showSuggestions && suggestions.length > 0 && (
        <div ref={suggestionsRef} className="suggestions-dropdown">
          {suggestions.map((suggestion, index) => (
            <div
              key={suggestion}
              className={`suggestion-item ${
                index === selectedSuggestionIndex ? 'selected' : ''
              }`}
              onClick={() => handleSuggestionClick(suggestion)}
              onMouseEnter={() => setSelectedSuggestionIndex(index)}
            >
              <span className="suggestion-icon">💡</span>
              <span className="suggestion-text">{suggestion}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
});

SearchBar.displayName = 'SearchBar';
