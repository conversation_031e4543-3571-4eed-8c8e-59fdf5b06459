/**
 * SearchProgress component styles
 * 
 * Styles for the real-time search progress indicator
 */

.search-progress {
  margin: 8px 0;
  padding: 12px;
  background: var(--bg-secondary, #f5f5f5);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
}

/* Dark mode support */
.dark-mode .search-progress {
  background: var(--bg-secondary-dark, #2a2a2a);
}

/* Header with spinner */
.search-progress-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.search-progress-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top-color: var(--primary-color, #007bff);
  border-radius: 50%;
  animation: spinner-rotate 0.8s linear infinite;
}

@keyframes spinner-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.search-progress-text {
  color: var(--text-secondary, #666);
  font-weight: 500;
}

/* Engine status list */
.search-progress-engines {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.engine-status {
  padding: 8px 12px;
  background: var(--bg-primary, #fff);
  border-radius: 6px;
  border: 1px solid var(--border-color, #e0e0e0);
  transition: all 0.2s ease;
}

.dark-mode .engine-status {
  background: var(--bg-primary-dark, #1a1a1a);
  border-color: var(--border-color-dark, #444);
}

/* Engine header */
.engine-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.engine-name {
  font-weight: 500;
  color: var(--text-primary, #333);
}

.dark-mode .engine-name {
  color: var(--text-primary-dark, #f0f0f0);
}

.engine-result-count {
  font-size: 12px;
  color: var(--text-secondary, #666);
}

/* Progress bar */
.engine-progress-bar {
  height: 4px;
  background: var(--progress-bg, #e0e0e0);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.dark-mode .engine-progress-bar {
  background: var(--progress-bg-dark, #444);
}

.engine-progress-fill {
  height: 100%;
  background: var(--primary-color, #007bff);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Status indicators */
.engine-complete-indicator {
  display: inline-block;
  color: var(--success-color, #28a745);
  font-weight: bold;
  margin-top: 4px;
}

.engine-error-message {
  color: var(--error-color, #dc3545);
  font-size: 12px;
  margin-top: 4px;
}

/* Engine status modifiers */
.engine-status.engine-searching {
  border-color: var(--primary-color, #007bff);
  background: var(--primary-bg-light, #f0f7ff);
}

.dark-mode .engine-status.engine-searching {
  background: var(--primary-bg-dark, #001f3f);
  border-color: var(--primary-color-dark, #0056b3);
}

.engine-status.engine-complete {
  border-color: var(--success-color, #28a745);
}

.engine-status.engine-error {
  border-color: var(--error-color, #dc3545);
  background: var(--error-bg-light, #fff5f5);
}

.dark-mode .engine-status.engine-error {
  background: var(--error-bg-dark, #3f0000);
}

/* Summary section */
.search-progress-summary {
  padding: 8px 0;
  text-align: center;
  color: var(--text-secondary, #666);
  font-weight: 500;
}

.summary-text {
  display: inline-block;
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .search-progress {
    padding: 8px;
    font-size: 13px;
  }
  
  .engine-status {
    padding: 6px 8px;
  }
  
  .engine-result-count {
    font-size: 11px;
  }
}

/* Animation for appearance */
.search-progress {
  animation: slideIn 0.2s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Compact mode */
.search-progress.compact {
  padding: 8px;
}

.search-progress.compact .search-progress-engines {
  gap: 4px;
}

.search-progress.compact .engine-status {
  padding: 4px 8px;
}

.search-progress.compact .engine-progress-bar {
  height: 2px;
}