/**
 * 搜索结果骨架屏组件
 * 
 * 在搜索结果加载时显示占位符，提升用户体验感知速度
 */

/**
 * 骨架屏组件属性接口
 */
interface SkeletonLoaderProps {
  /** 骨架项数量，默认5个 */
  count?: number;
  /** 单项高度，默认80px */
  height?: string;
  /** 是否启用动画，默认true */
  animate?: boolean;
  /** 自定义CSS类名 */
  className?: string;
}

/**
 * 单个骨架项组件
 */
const SkeletonItem: React.FC<{ height: string; animate: boolean }> = ({ 
  height, 
  animate 
}) => {
  // 生成随机宽度以模拟真实内容
  const titleWidth = `${Math.floor(Math.random() * 15) + 80}%`; // 80%-95%
  const urlWidth = `${Math.floor(Math.random() * 15) + 60}%`;   // 60%-75%
  const line1Width = `${Math.floor(Math.random() * 20) + 75}%`; // 75%-95%
  const line2Width = `${Math.floor(Math.random() * 25) + 50}%`; // 50%-75%

  return (
    <div 
      className={`skeleton-item ${animate ? 'skeleton-animate' : ''}`}
      style={{ minHeight: height }}
    >
      {/* 标题行 */}
      <div className="skeleton-content">
        <div className="skeleton-title">
          <div 
            className="skeleton-line skeleton-line-title"
            style={{ width: titleWidth }}
          />
        </div>
        
        {/* URL行 */}
        <div className="skeleton-url">
          <div 
            className="skeleton-line skeleton-line-url"
            style={{ width: urlWidth }}
          />
        </div>
        
        {/* 内容行 */}
        <div className="skeleton-text">
          <div 
            className="skeleton-line skeleton-line-text"
            style={{ width: line1Width }}
          />
          <div 
            className="skeleton-line skeleton-line-text"
            style={{ width: line2Width }}
          />
        </div>
      </div>
    </div>
  );
};

/**
 * 骨架屏加载器组件
 */
export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  count = 5,
  height = '80px',
  animate = true,
  className = ''
}) => {
  return (
    <div className={`skeleton-loader ${className}`} role="status" aria-label="加载中...">
      {Array.from({ length: count }, (_, index) => (
        <SkeletonItem 
          key={index} 
          height={height} 
          animate={animate}
        />
      ))}
      
      {/* 屏幕阅读器友好的加载提示 */}
      <span className="sr-only">正在加载搜索结果...</span>
    </div>
  );
};

export default SkeletonLoader;