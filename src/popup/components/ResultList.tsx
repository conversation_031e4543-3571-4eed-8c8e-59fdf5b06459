/**
 * 搜索结果列表组件
 * 
 * 显示搜索结果，包含页面标题、URL、内容片段高亮和访问时间
 */

import React, { useMemo } from 'react';
import type { SearchResultItem } from '../../models';
import { formatTimestamp } from '../../models';
import { parseQuery } from '../../services/query-parser';
import { Highlight, HighlightWithContext } from '../../components/Highlight';
import { useStreamingResults } from '../hooks/useStreamingResults';
import '../../components/Highlight.css';

/**
 * 结果列表属性接口
 */
interface ResultListProps {
  /** 搜索结果 */
  results: SearchResultItem[];
  /** 是否正在加载 */
  isLoading?: boolean;
  /** 结果点击回调 */
  onResultClick: (result: SearchResultItem) => void;
  /** 搜索查询（用于高亮） */
  query?: string;
  /** 每页显示数量 */
  pageSize?: number;
  /** 是否启用流式加载，默认true */
  enableStreaming?: boolean;
  /** 流式加载批次大小，默认10 */
  streamingBatchSize?: number;
}

/**
 * 结果项属性接口
 */
interface ResultItemProps {
  /** 结果项 */
  result: SearchResultItem;
  /** 点击回调 */
  onClick: (result: SearchResultItem) => void;
  /** 搜索查询 */
  query?: string;
}

/**
 * 单个结果项组件
 */
const ResultItem: React.FC<ResultItemProps> = ({ result, onClick, query }) => {
  const { page, score, highlights } = result;

  /**
   * 解析查询以获取高亮关键词
   */
  const parsedQuery = useMemo(() => {
    if (!query) return null;
    return parseQuery(query);
  }, [query]);

  /**
   * 获取所有高亮关键词（包括普通关键词和精确短语）
   */
  const highlightKeywords = useMemo(() => {
    if (!parsedQuery) return [];
    return [...parsedQuery.keywords, ...parsedQuery.exact];
  }, [parsedQuery]);

  /**
   * 处理点击事件
   */
  const handleClick = () => {
    onClick(result);
  };

  /**
   * 处理键盘事件
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onClick(result);
    }
  };

  /**
   * 获取域名图标
   */
  const getDomainIcon = (domain: string) => {
    // 常见网站的图标映射
    const iconMap: Record<string, string> = {
      'github.com': '🐙',
      'stackoverflow.com': '📚',
      'developer.mozilla.org': '🦎',
      'google.com': '🔍',
      'youtube.com': '📺',
      'twitter.com': '🐦',
      'facebook.com': '📘',
      'linkedin.com': '💼',
      'reddit.com': '🤖',
      'wikipedia.org': '📖'
    };

    return iconMap[domain] || '🌐';
  };

  /**
   * 格式化相关性分数
   */
  const formatScore = (score: number) => {
    return Math.round((1 - score) * 100);
  };

  /**
   * 渲染高亮内容
   */
  const renderHighlights = () => {
    if (!highlights || highlights.length === 0) {
      // 如果没有预生成的高亮片段，使用页面内容的开头部分
      return (
        <div className="result-content">
          <HighlightWithContext
            text={page.content}
            keywords={highlightKeywords}
            className="highlight-content"
            contextLength={75}
            maxLength={300}
          />
        </div>
      );
    }

    // 使用预生成的高亮片段
    return (
      <div className="result-highlights">
        {highlights.slice(0, 3).map((highlight, index) => {
          return (
            <div key={index} className="result-highlight">
              <Highlight
                text={highlight}
                keywords={highlightKeywords}
                className="highlight-content"
                maxLength={250}
              />
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div
      className="result-item"
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`打开页面: ${page.title}`}
    >
      {/* 结果头部 */}
      <div className="result-header">
        <div className="result-title-section">
          <span className="domain-icon">{getDomainIcon(page.domain)}</span>
          <h3 className="result-title" title={page.title}>
            <Highlight
              text={page.title}
              keywords={highlightKeywords}
              className="highlight-title"
              maxLength={100}
            />
          </h3>
          <div className="result-score" title={`相关性: ${formatScore(score)}%`}>
            {formatScore(score)}%
          </div>
        </div>

        <div className="result-url" title={page.url}>
          <Highlight
            text={page.url}
            keywords={highlightKeywords}
            className="highlight-url"
            maxLength={80}
          />
        </div>
      </div>

      {/* 结果内容 */}
      <div className="result-body">
        {renderHighlights()}
      </div>

      {/* 结果元信息 */}
      <div className="result-meta">
        <div className="result-meta-left">
          <span className="visit-time" title="访问时间">
            📅 {formatTimestamp(page.visitTime)}
          </span>
          <span className="access-count" title="访问次数">
            👁️ {page.accessCount} 次
          </span>
          <span className="domain" title="网站域名">
            🌐 {page.domain}
          </span>
        </div>
        
        <div className="result-meta-right">
          <span className="content-length" title="内容长度">
            📄 {Math.round(page.content.length / 1000)}k 字符
          </span>
        </div>
      </div>
    </div>
  );
};

/**
 * 加载状态组件
 */
const LoadingState: React.FC = () => (
  <div className="loading-state">
    <div className="loading-spinner-large" />
    <p>正在搜索...</p>
  </div>
);

/**
 * 空状态组件
 */
const EmptyState: React.FC<{ query?: string }> = ({ query }) => (
  <div className="empty-state">
    <span className="empty-icon">🔍</span>
    <h3>未找到相关结果</h3>
    {query && (
      <p>没有找到包含 "<strong><Highlight text={query} keywords={[]} /></strong>" 的页面</p>
    )}
    <div className="empty-tips">
      <p>建议：</p>
      <ul>
        <li>检查拼写是否正确</li>
        <li>尝试使用不同的关键词</li>
        <li>使用更简短的搜索词</li>
        <li>调整过滤条件</li>
      </ul>
    </div>
  </div>
);

/**
 * 结果列表组件
 */
export const ResultList: React.FC<ResultListProps> = ({
  results,
  isLoading = false,
  onResultClick,
  query,
  pageSize = 20,
  enableStreaming = true,
  streamingBatchSize = 10
}) => {
  /**
   * 分页处理的结果
   */
  const paginatedResults = useMemo(() => {
    return results.slice(0, pageSize);
  }, [results, pageSize]);

  /**
   * 流式加载Hook
   */
  const {
    displayedResults,
    isStreaming,
    progress
  } = useStreamingResults({
    results: paginatedResults,
    batchSize: streamingBatchSize,
    delay: 50,
    enabled: enableStreaming && !isLoading
  });

  /**
   * 最终显示的结果（根据是否启用流式加载）
   */
  const finalResults = enableStreaming && !isLoading ? displayedResults : paginatedResults;

  /**
   * 渲染内容
   */
  const renderContent = () => {
    if (isLoading) {
      return <LoadingState />;
    }

    if (results.length === 0) {
      return <EmptyState query={query} />;
    }

    return (
      <div className="results-container">
        {/* 结果统计 */}
        <div className="results-summary">
          <p>
            找到 <strong>{results.length}</strong> 个相关结果
            {results.length > pageSize && (
              <span className="results-truncated">
                （显示前 {pageSize} 个）
              </span>
            )}
            {/* 流式加载进度指示 */}
            {enableStreaming && isStreaming && (
              <span className="streaming-progress">
                <span className="streaming-indicator">正在加载中...</span>
                <span className="streaming-progress-bar">
                  <span 
                    className="streaming-progress-fill" 
                    style={{ width: `${progress}%` }}
                  />
                </span>
              </span>
            )}
          </p>
        </div>

        {/* 结果列表 */}
        <div className="results-list">
          {finalResults.map((result, index) => (
            <ResultItem
              key={`${result.page.id}-${index}`}
              result={result}
              onClick={onResultClick}
              query={query}
            />
          ))}
        </div>

        {/* 更多结果提示 */}
        {results.length > pageSize && (
          <div className="more-results-hint">
            <p>还有 {results.length - pageSize} 个结果未显示</p>
            <p className="hint-text">尝试使用更具体的搜索词来缩小范围</p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="result-list">
      {renderContent()}
    </div>
  );
};
