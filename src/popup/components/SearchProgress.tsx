/**
 * SearchProgress - Real-time search progress indicator component
 * 
 * Displays which search engines are active, their progress, and result counts.
 * 
 * @module SearchProgress
 * @version 1.0
 * @since 2025-06-27
 */

import React, { useEffect, useState } from 'react';
import type { ProgressiveSearchEvent } from '../../search/hybrid/ProgressiveSearch';
import type { EngineResultMetadata } from '../../search/hybrid/ResultMerger';
import './SearchProgress.css';

/**
 * Props for SearchProgress component
 */
export interface SearchProgressProps {
  /** Whether search is currently active */
  isSearching: boolean;
  /** Progressive search events */
  progressEvents?: ProgressiveSearchEvent[];
  /** Engine metadata from result merger */
  engineMetadata?: EngineResultMetadata[];
  /** Whether to show detailed progress */
  showDetails?: boolean;
  /** CSS class name */
  className?: string;
}

/**
 * Engine display information
 */
interface EngineInfo {
  type: string;
  displayName: string;
  status: 'idle' | 'searching' | 'complete' | 'error';
  resultCount: number;
  progress: number;
  error?: string;
}

/**
 * SearchProgress component
 * Displays real-time search progress with engine status and result counts
 */
export const SearchProgress: React.FC<SearchProgressProps> = ({
  isSearching,
  progressEvents = [],
  engineMetadata = [],
  showDetails = true,
  className = ''
}) => {
  const [engines, setEngines] = useState<Map<string, EngineInfo>>(new Map());
  const [totalResults, setTotalResults] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  // Initialize engine states
  useEffect(() => {
    const initialEngines = new Map<string, EngineInfo>();
    
    // Define available engines
    const engineTypes = ['string', 'fulltext'];
    const displayNames: Record<string, string> = {
      string: 'Text Match',
      fulltext: 'Full Text'
    };
    
    engineTypes.forEach(type => {
      initialEngines.set(type, {
        type,
        displayName: displayNames[type] || type,
        status: 'idle',
        resultCount: 0,
        progress: 0
      });
    });
    
    setEngines(initialEngines);
  }, []);

  // Process progress events
  useEffect(() => {
    if (progressEvents.length === 0) return;
    
    const latestEvent = progressEvents[progressEvents.length - 1];
    const updatedEngines = new Map(engines);
    
    switch (latestEvent.type) {
      case 'start':
        // Reset all engines to searching
        updatedEngines.forEach((engine) => {
          engine.status = 'searching';
          engine.resultCount = 0;
          engine.progress = 0;
          engine.error = undefined;
        });
        setIsComplete(false);
        break;
        
      case 'engine-start':
        if (latestEvent.engineType) {
          const engine = updatedEngines.get(latestEvent.engineType);
          if (engine) {
            engine.status = 'searching';
            engine.progress = 10; // Show minimal progress
          }
        }
        break;
        
      case 'engine-progress':
        if (latestEvent.engineType && latestEvent.progress) {
          const engine = updatedEngines.get(latestEvent.engineType);
          if (engine) {
            engine.progress = Math.min(latestEvent.progress, 90); // Cap at 90%
            if (latestEvent.totalResults !== undefined) {
              engine.resultCount = latestEvent.totalResults;
            }
          }
        }
        break;
        
      case 'engine-complete':
        if (latestEvent.engineType) {
          const engine = updatedEngines.get(latestEvent.engineType);
          if (engine) {
            engine.status = 'complete';
            engine.progress = 100;
            if (latestEvent.totalResults !== undefined) {
              engine.resultCount = latestEvent.totalResults;
            }
          }
        }
        break;
        
      case 'engine-cancelled':
        if (latestEvent.engineType) {
          const engine = updatedEngines.get(latestEvent.engineType);
          if (engine) {
            engine.status = 'idle';
            engine.progress = 0;
          }
        }
        break;
        
      case 'error':
        if (latestEvent.engineType) {
          const engine = updatedEngines.get(latestEvent.engineType);
          if (engine) {
            engine.status = 'error';
            engine.error = latestEvent.error?.message || 'Search failed';
          }
        }
        break;
        
      case 'complete':
        setIsComplete(true);
        break;
    }
    
    setEngines(updatedEngines);
  }, [progressEvents]);

  // Update engine metadata
  useEffect(() => {
    if (engineMetadata.length === 0) return;
    
    const updatedEngines = new Map(engines);
    let total = 0;
    
    engineMetadata.forEach(metadata => {
      const engine = updatedEngines.get(metadata.engineType);
      if (engine) {
        engine.resultCount = metadata.resultCount;
        if (metadata.isComplete) {
          engine.status = 'complete';
          engine.progress = 100;
        }
        total += metadata.resultCount;
      }
    });
    
    setEngines(updatedEngines);
    setTotalResults(total);
  }, [engineMetadata]);

  // Reset when search stops
  useEffect(() => {
    if (!isSearching) {
      const updatedEngines = new Map(engines);
      updatedEngines.forEach(engine => {
        if (engine.status === 'searching') {
          engine.status = 'idle';
          engine.progress = 0;
        }
      });
      setEngines(updatedEngines);
    }
  }, [isSearching]);

  if (!isSearching && totalResults === 0) {
    return null;
  }

  const activeEngines = Array.from(engines.values()).filter(
    engine => engine.status !== 'idle'
  );

  return (
    <div className={`search-progress ${className}`}>
      {isSearching && (
        <div className="search-progress-header">
          <div className="search-progress-spinner" />
          <span className="search-progress-text">
            Searching{activeEngines.length > 0 && ` (${activeEngines.length} engines)`}
          </span>
        </div>
      )}
      
      {showDetails && activeEngines.length > 0 && (
        <div className="search-progress-engines">
          {activeEngines.map(engine => (
            <div key={engine.type} className={`engine-status engine-${engine.status}`}>
              <div className="engine-header">
                <span className="engine-name">{engine.displayName}</span>
                <span className="engine-result-count">
                  {engine.resultCount > 0 && `${engine.resultCount} results`}
                </span>
              </div>
              
              {engine.status === 'searching' && (
                <div className="engine-progress-bar">
                  <div 
                    className="engine-progress-fill"
                    style={{ width: `${engine.progress}%` }}
                  />
                </div>
              )}
              
              {engine.status === 'complete' && (
                <div className="engine-complete-indicator">✓</div>
              )}
              
              {engine.status === 'error' && (
                <div className="engine-error-message">{engine.error}</div>
              )}
            </div>
          ))}
        </div>
      )}
      
      {!isSearching && totalResults > 0 && (
        <div className="search-progress-summary">
          <span className="summary-text">
            Found {totalResults} total results
            {isComplete && ' (search complete)'}
          </span>
        </div>
      )}
    </div>
  );
};

export default SearchProgress;