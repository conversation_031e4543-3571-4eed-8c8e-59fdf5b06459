/**
 * Search Module Exports
 * 
 * Centralized exports for all search-related services and utilities.
 */

export { ResultRanker } from './ResultRanker';
export { QueryProcessor } from './QueryProcessor';

// Fulltext search exports
export { LunrSearchEngine } from './fulltext/LunrSearchEngine';
export { LunrIndexStorage } from './fulltext/LunrIndexStorage';

// Hybrid search exports  
export { StringMatcher } from './hybrid/StringMatcher';
export { HybridSearchEngine } from './hybrid/HybridSearchEngine';
export { SearchCoordinator } from './hybrid/SearchCoordinator';
export { ResultMerger } from './hybrid/ResultMerger';
export { QueryAnalyzer } from './hybrid/QueryAnalyzer';

export type {
  RankingConfig,
  RankingFeatures,
  RankedResult
} from './ResultRanker';

export type {
  ProcessedQuery,
  QueryEntity,
  QueryFilters,
  QueryIntent,
  EntityType,
  QueryProcessingConfig
} from './QueryProcessor';