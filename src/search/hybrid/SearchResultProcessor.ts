/**
 * SearchResultProcessor - Focused result processing module
 * 
 * Handles result filtering, snippet generation, and post-processing
 * for hybrid search results.
 * 
 * @module SearchResultProcessor
 * @version 4.0
 * @since 2025-06-23
 */

import type { SearchResult, SearchOptions } from './HybridSearchEngine';

/**
 * Configuration for result processing
 */
export interface ProcessorConfig {
  maxSnippetLength: number;
  highlightTags: { start: string; end: string };
  defaultSnippetLength: number;
  domainWhitelist?: string[];
  domainBlacklist?: string[];
}

/**
 * SearchResultProcessor handles post-processing of search results
 * Separated from main search engine for better modularity and testability
 */
export class SearchResultProcessor {
  private readonly config: ProcessorConfig;

  constructor(config: Partial<ProcessorConfig> = {}) {
    this.config = {
      maxSnippetLength: 200,
      highlightTags: { start: '<mark>', end: '</mark>' },
      defaultSnippetLength: 150,
      domainWhitelist: [],
      domainBlacklist: [],
      ...config
    };
  }

  /**
   * Applies filters to search results based on search options
   */
  applyFilters(
    results: SearchResult[],
    options: SearchOptions
  ): SearchResult[] {
    let filteredResults = [...results];

    // Apply domain filters
    if ((options as any).domains && (options as any).domains.length > 0) {
      filteredResults = filteredResults.filter(result => {
        const domain = this.extractDomain(result.url);
        return (options as any).domains!.some((filterDomain: string) => 
          domain.includes(filterDomain.toLowerCase())
        );
      });
    }

    // Apply blacklist filters
    if ((options as any).excludeDomains && (options as any).excludeDomains.length > 0) {
      filteredResults = filteredResults.filter(result => {
        const domain = this.extractDomain(result.url);
        return !(options as any).excludeDomains!.some((excludeDomain: string) => 
          domain.includes(excludeDomain.toLowerCase())
        );
      });
    }

    // Apply time range filters
    if (options.timeRange) {
      filteredResults = filteredResults.filter(result => 
        result.lastVisitTime >= options.timeRange!.start &&
        result.lastVisitTime <= options.timeRange!.end
      );
    }

    // Apply minimum score threshold
    if ((options as any).minScore !== undefined) {
      filteredResults = filteredResults.filter(result => 
        result.combinedScore >= (options as any).minScore!
      );
    }

    return filteredResults;
  }

  /**
   * Generates contextual snippets with highlighted query terms
   */
  generateSnippets(
    results: SearchResult[],
    query: string,
    maxLength: number = this.config.defaultSnippetLength
  ): SearchResult[] {
    const queryWords = this.extractQueryWords(query);

    return results.map(result => {
      if (result.snippet) {
        return result; // Already has snippet
      }

      const snippet = this.createSnippet(
        result.content,
        queryWords,
        maxLength
      );

      const highlights = this.extractHighlights(
        result.content,
        queryWords
      );

      return {
        ...result,
        snippet,
        highlights
      };
    });
  }

  /**
   * Removes duplicate results based on URL
   */
  removeDuplicates(results: SearchResult[]): SearchResult[] {
    const seen = new Set<string>();
    return results.filter(result => {
      if (seen.has(result.url)) {
        return false;
      }
      seen.add(result.url);
      return true;
    });
  }

  /**
   * Applies diversity filtering to reduce similar results
   */
  applyDiversityFilter(
    results: SearchResult[],
    threshold: number = 0.8
  ): SearchResult[] {
    const diverseResults: SearchResult[] = [];
    
    for (const result of results) {
      const isDiverse = diverseResults.every(existing => 
        this.calculateSimilarity(result, existing) < threshold
      );
      
      if (isDiverse) {
        diverseResults.push(result);
      }
    }

    return diverseResults;
  }

  /**
   * Sorts results by combined score and applies secondary sorting
   */
  sortResults(
    results: SearchResult[],
    _options: SearchOptions
  ): SearchResult[] {
    return results.sort((a, b) => {
      // Primary sort: combined score (descending)
      if (a.combinedScore !== b.combinedScore) {
        return b.combinedScore - a.combinedScore;
      }

      // Secondary sort: visit count (descending)
      if (a.visitCount !== b.visitCount) {
        return b.visitCount - a.visitCount;
      }

      // Tertiary sort: last visit time (descending)
      return b.lastVisitTime - a.lastVisitTime;
    });
  }

  // ======================
  // Private Helper Methods
  // ======================

  /**
   * Extracts domain from URL
   * @private
   */
  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.toLowerCase();
    } catch {
      return '';
    }
  }

  /**
   * Extracts meaningful words from query
   * @private
   */
  private extractQueryWords(query: string): string[] {
    return query
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
  }

  /**
   * Creates a contextual snippet around query terms
   * @private
   */
  private createSnippet(
    content: string,
    queryWords: string[],
    maxLength: number
  ): string {
    if (!content || content.length <= maxLength) {
      return content;
    }

    // Find the best snippet position based on query word density
    const bestPosition = this.findBestSnippetPosition(
      content,
      queryWords,
      maxLength
    );

    let snippet = content.substr(bestPosition, maxLength);

    // Ensure snippet doesn't cut words in half
    if (bestPosition > 0) {
      const firstSpace = snippet.indexOf(' ');
      if (firstSpace > 0 && firstSpace < 20) {
        snippet = snippet.substr(firstSpace + 1);
      }
    }

    if (bestPosition + maxLength < content.length) {
      const lastSpace = snippet.lastIndexOf(' ');
      if (lastSpace > maxLength - 20) {
        snippet = snippet.substr(0, lastSpace);
      }
      snippet += '...';
    }

    if (bestPosition > 0) {
      snippet = '...' + snippet;
    }

    // Highlight query terms
    return this.highlightTerms(snippet, queryWords);
  }

  /**
   * Finds the optimal position for snippet based on query word density
   * @private
   */
  private findBestSnippetPosition(
    content: string,
    queryWords: string[],
    maxLength: number
  ): number {
    if (content.length <= maxLength) {
      return 0;
    }

    let bestPosition = 0;
    let bestScore = 0;

    // Try different positions and calculate scores
    for (let i = 0; i <= content.length - maxLength; i += Math.floor(maxLength / 4)) {
      const snippet = content.substr(i, maxLength);
      const score = this.calculateSnippetScore(snippet, queryWords);
      
      if (score > bestScore) {
        bestScore = score;
        bestPosition = i;
      }
    }

    return bestPosition;
  }

  /**
   * Calculates snippet quality score based on query word frequency
   * @private
   */
  private calculateSnippetScore(snippet: string, queryWords: string[]): number {
    const lowerSnippet = snippet.toLowerCase();
    let score = 0;

    queryWords.forEach(word => {
      const matches = (lowerSnippet.match(new RegExp(word, 'g')) || []).length;
      score += matches * word.length; // Longer words get higher weight
    });

    return score;
  }

  /**
   * Highlights query terms in text
   * @private
   */
  private highlightTerms(text: string, queryWords: string[]): string {
    let highlightedText = text;

    queryWords.forEach(word => {
      const regex = new RegExp(`(${word})`, 'gi');
      highlightedText = highlightedText.replace(
        regex,
        `${this.config.highlightTags.start}$1${this.config.highlightTags.end}`
      );
    });

    return highlightedText;
  }

  /**
   * Extracts highlighted terms from content
   * @private
   */
  private extractHighlights(content: string, queryWords: string[]): string[] {
    const highlights: string[] = [];
    const lowerContent = content.toLowerCase();

    queryWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      const matches = lowerContent.match(regex) || [];
      highlights.push(...matches);
    });

    return [...new Set(highlights)]; // Remove duplicates
  }

  /**
   * Calculates similarity between two results (simplified)
   * @private
   */
  private calculateSimilarity(result1: SearchResult, result2: SearchResult): number {
    // Simple domain-based similarity for diversity filtering
    const domain1 = this.extractDomain(result1.url);
    const domain2 = this.extractDomain(result2.url);
    
    if (domain1 === domain2) {
      return 0.9; // High similarity for same domain
    }

    // Title similarity (simplified)
    const title1Words = new Set(result1.title.toLowerCase().split(/\s+/));
    const title2Words = new Set(result2.title.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...title1Words].filter(x => title2Words.has(x)));
    const union = new Set([...title1Words, ...title2Words]);
    
    return intersection.size / union.size;
  }
}