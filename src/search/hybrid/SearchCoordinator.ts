/**
 * SearchCoordinator - Orchestrates hybrid search operations
 * 
 * The SearchCoordinator serves as the central orchestrator for the hybrid search engine,
 * coordinating between string search, fulltext search, and result merging to provide
 * a unified and optimized search experience.
 * 
 * Key responsibilities:
 * - Query analysis and strategy determination
 * - Parallel execution of string and fulltext searches
 * - Result merging and ranking coordination
 * - Performance monitoring and statistics collection
 * - Error handling and graceful fallbacks
 * 
 * @example
 * ```typescript
 * const coordinator = new SearchCoordinator();
 * 
 * // Execute a comprehensive search
 * const result = await coordinator.executeSearch('React hooks tutorial', {
 *   limit: 20,
 *   domains: ['react.dev', 'github.com']
 * });
 * 
 * console.log('Found:', result.results.length, 'results');
 * console.log('Search time:', result.stats.totalTime, 'ms');
 * console.log('Strategy used:', result.analysis.strategy);
 * ```
 * 
 * @module SearchCoordinator
 * @version 4.0
 * @since 2025-06-23
 */

import { QueryAnalyzer, SearchStrategy } from './QueryAnalyzer';
import type { QueryAnalysisResult } from './QueryAnalyzer';
import { ResultMerger } from './ResultMerger';
import type { SearchResult, SearchOptions } from './HybridSearchEngine';
import { searchService } from '../../services/search.service';

/**
 * Merge strategy for combining results from different search engines
 */
export const MergeStrategy = {
  WEIGHTED_AVERAGE: 'weighted_average',
  MAX_SCORE: 'max_score',
  HARMONIC_MEAN: 'harmonic_mean'
} as const;
export type MergeStrategy = typeof MergeStrategy[keyof typeof MergeStrategy];

/**
 * Ranking algorithm for final result ordering
 */
export const RankingAlgorithm = {
  SCORE_BASED: 'score_based',
  POPULARITY_BOOST: 'popularity_boost',
  HYBRID_RANKING: 'hybrid_ranking'
} as const;
export type RankingAlgorithm = typeof RankingAlgorithm[keyof typeof RankingAlgorithm];

/**
 * Search execution statistics
 */
export interface SearchExecutionStats {
  totalTime: number;
  queryAnalysisTime: number;
  stringSearchTime: number;
  mergingTime: number;
  filteringTime: number;
  stringResultCount: number;
  finalResultCount: number;
  strategy: SearchStrategy;
  cacheHit: boolean;
}

/**
 * Search strategy configuration
 */
export interface StrategyConfig {
  stringEnabled: boolean;
  mergeStrategy: MergeStrategy;
  rankingAlgorithm: RankingAlgorithm;
  timeoutMs: number;
  fallbackToString: boolean;
}

/**
 * SearchCoordinator orchestrates the entire hybrid search process
 */
export class SearchCoordinator {
  private readonly queryAnalyzer: QueryAnalyzer;
  private readonly resultMerger: ResultMerger;
  private readonly defaultConfig: StrategyConfig;
  private lastExecutionStats: SearchExecutionStats | null = null;

  constructor() {
    this.queryAnalyzer = new QueryAnalyzer();
    this.resultMerger = new ResultMerger();
    
    this.defaultConfig = {
      stringEnabled: true,
      mergeStrategy: MergeStrategy.WEIGHTED_AVERAGE,
      rankingAlgorithm: RankingAlgorithm.HYBRID_RANKING,
      timeoutMs: 5000,
      fallbackToString: true
    };
  }

  /**
   * Executes hybrid search with full coordination
   * 
   * This is the main entry point for performing a comprehensive hybrid search.
   * The method orchestrates query analysis, parallel search execution, result
   * merging, and performance monitoring.
   * 
   * @param query - The search query string
   * @param options - Search options including filters, limits, and preferences
   * @param config - Strategy configuration for customizing search behavior
   * 
   * @returns Promise resolving to search results with statistics and analysis
   * 
   * @example
   * ```typescript
   * // Basic search
   * const result = await coordinator.executeSearch('machine learning');
   * 
   * // Advanced search with options
   * const result = await coordinator.executeSearch(
   *   'React performance optimization',
   *   {
   *     limit: 30,
   *     domains: ['react.dev', 'github.com'],
   *     minScore: 0.4,
   *     timeRange: {
   *       start: Date.now() - 7 * 24 * 60 * 60 * 1000, // Last 7 days
   *       end: Date.now()
   *     }
   *   },
   *   {
     *     mergeStrategy: MergeStrategy.WEIGHTED_AVERAGE,
   *     timeoutMs: 5000
   *   }
   * );
   * 
   * console.log(`Found ${result.results.length} results`);
   * console.log(`Search completed in ${result.stats.totalTime}ms`);
   * console.log(`Strategy: ${result.analysis.strategy}`);
   * console.log(`Confidence: ${result.analysis.confidence}`);
   * ```
   * 
   * @throws {Error} When search options are invalid
   * @throws {Error} When search timeout is exceeded
   */
  async executeSearch(
    query: string,
    options: SearchOptions = {},
    config: Partial<StrategyConfig> = {}
  ): Promise<{
    results: SearchResult[];
    stats: SearchExecutionStats;
    analysis: QueryAnalysisResult;
  }> {
    const startTime = performance.now();
    const mergedConfig = { ...this.defaultConfig, ...config };

    // Step 1: Analyze query
    const analysisStartTime = performance.now();
    const analysis = this.queryAnalyzer.analyzeQuery(query);
    const queryAnalysisTime = performance.now() - analysisStartTime;

    // Step 2: Determine search strategy based on analysis
    const strategy = this.determineSearchStrategy(analysis, mergedConfig);

    // Step 3: Execute searches in parallel
    const searchPromises = this.createSearchPromises(query, options, strategy, mergedConfig);
    
    // const searchStartTime = performance.now();
    const searchResults = await this.executeSearches(searchPromises, mergedConfig.timeoutMs);
    // const totalSearchTime = performance.now() - searchStartTime;

    // Step 4: Merge results
    const mergeStartTime = performance.now();
    const mergedResults = this.mergeSearchResults(
      [],
      searchResults.string,
      analysis.weights,
      strategy
    );
    const mergingTime = performance.now() - mergeStartTime;

    // Step 5: Post-processing would happen here
    // (This will be handled by SearchResultProcessor in the refactored architecture)

    const totalTime = performance.now() - startTime;

    // Compile execution statistics
    const stats: SearchExecutionStats = {
      totalTime,
      queryAnalysisTime,
      stringSearchTime: searchResults.timings.string,
      mergingTime,
      filteringTime: 0, // Will be filled by processor
      stringResultCount: searchResults.string.length,
      finalResultCount: mergedResults.length,
      strategy: analysis.strategy,
      cacheHit: false // Will be determined by cache manager
    };

    this.lastExecutionStats = stats;

    return {
      results: mergedResults,
      stats,
      analysis
    };
  }

  /**
   * Gets the last execution statistics
   */
  getLastExecutionStats(): SearchExecutionStats | null {
    return this.lastExecutionStats;
  }

  /**
   * Validates search options and provides defaults
   */
  validateAndNormalizeOptions(options: SearchOptions): SearchOptions {
    const normalized: SearchOptions = {
      limit: Math.min(options.limit || 50, 200), // Max 200 results
      offset: Math.max(options.offset || 0, 0),
      // domains: (options as any).domains?.map((d: string) => d.toLowerCase()) || [],
      // excludeDomains: (options as any).excludeDomains?.map((d: string) => d.toLowerCase()) || [],
      timeRange: options.timeRange,
      // minScore: Math.max((options as any).minScore || 0, 0),
      includeContent: options.includeContent ?? true
      // sortBy: (options as any).sortBy || 'relevance'
    };

    // Validate time range
    if (normalized.timeRange) {
      const { start, end } = normalized.timeRange;
      if (start >= end) {
        throw new Error('Invalid time range: start must be before end');
      }
      if (start < 0 || end < 0) {
        throw new Error('Invalid time range: times must be positive');
      }
    }

    return normalized;
  }

  // ======================
  // Private Coordination Methods
  // ======================

  /**
   * Determines the optimal search strategy based on query analysis
   * @private
   */
  private determineSearchStrategy(
    analysis: QueryAnalysisResult,
    config: StrategyConfig
  ): StrategyConfig {
    const strategy = { ...config };

    // Override based on query characteristics
    switch (analysis.strategy) {
      case SearchStrategy.STRING_ONLY:
        strategy.stringEnabled = true;
        break;
      
      case SearchStrategy.FULLTEXT_ONLY:
        strategy.stringEnabled = true;
        break;
      
      case SearchStrategy.HYBRID_BALANCED:
        strategy.mergeStrategy = MergeStrategy.HARMONIC_MEAN;
        strategy.rankingAlgorithm = RankingAlgorithm.HYBRID_RANKING;
        break;
    }

    return strategy;
  }

  /**
   * Creates search promises based on strategy
   * @private
   */
  private createSearchPromises(
    query: string,
    options: SearchOptions,
    strategy: StrategyConfig,
    _config: StrategyConfig
  ): {
    semantic?: Promise<{ results: SearchResult[]; time: number }>;
    string?: Promise<{ results: SearchResult[]; time: number }>;
  } {
    const promises: any = {};

    if (strategy.stringEnabled) {
      promises.string = this.executeStringSearch(query, options);
    }

    return promises;
  }


  /**
   * Executes string search with timing
   * @private
   */
  private async executeStringSearch(
    query: string,
    options: SearchOptions
  ): Promise<{ results: SearchResult[]; time: number }> {
    const startTime = performance.now();
    
    try {
      const rawResults = await searchService.search(query, {
        query,
        limit: (options.limit || 50) * 2
        // includeContent: options.includeContent
      });

      const results = (rawResults as any[]).map((result: any) => this.convertToSearchResult(result, 'string'));
      const time = performance.now() - startTime;

      return { results, time };
    } catch (error) {
      console.warn('String search failed:', error);
      return { results: [], time: performance.now() - startTime };
    }
  }

  /**
   * Executes searches with timeout and error handling
   * @private
   */
  private async executeSearches(
    promises: {
      semantic?: Promise<{ results: SearchResult[]; time: number }>;
      string?: Promise<{ results: SearchResult[]; time: number }>;
    },
    timeoutMs: number
  ): Promise<{
    semantic: SearchResult[];
    string: SearchResult[];
    timings: { semantic: number; string: number };
  }> {
    const results = {
      semantic: [] as SearchResult[],
      string: [] as SearchResult[],
      timings: { semantic: 0, string: 0 }
    };

    // Execute with timeout
    const executeWithTimeout = async <T>(
      promise: Promise<T>,
      timeout: number
    ): Promise<T | null> => {
      try {
        return await Promise.race([
          promise,
          new Promise<null>((_, reject) => 
            setTimeout(() => reject(new Error('Search timeout')), timeout)
          )
        ]);
      } catch (error) {
        console.warn('Search promise failed:', error);
        return null;
      }
    };

    // Execute searches in parallel
    const searchPromises: Promise<any>[] = [];

    if (promises.semantic) {
      searchPromises.push(
        executeWithTimeout(promises.semantic, timeoutMs).then(result => {
          if (result) {
            results.semantic = result.results;
            results.timings.semantic = result.time;
          }
        })
      );
    }

    if (promises.string) {
      searchPromises.push(
        executeWithTimeout(promises.string, timeoutMs).then(result => {
          if (result) {
            results.string = result.results;
            results.timings.string = result.time;
          }
        })
      );
    }

    await Promise.all(searchPromises);
    return results;
  }

  /**
   * Merges search results using the result merger
   * @private
   */
  private mergeSearchResults(
    semanticResults: SearchResult[],
    stringResults: SearchResult[],
    weights: { string: number; fulltext: number },
    _strategy: StrategyConfig
  ): SearchResult[] {
    // Reset merger for new results
    this.resultMerger.reset();
    
    // Add semantic results (now using string type since semantic removed)
    if (semanticResults.length > 0) {
      this.resultMerger.addResults('string', semanticResults, true);
    }
    
    // Add string results
    if (stringResults.length > 0) {
      this.resultMerger.addResults('string', stringResults, true);
    }
    
    // Get merged results with proper configuration
    return this.resultMerger.getMergedResults({
      searchConfig: {
        // semantic removed - AI functionality disabled
        traditional: { enabled: true, weight: weights.string, alwaysShow: true },
        fulltext: { enabled: false, weight: 0 }
      }
    });
  }

  /**
   * Converts different result formats to unified SearchResult
   * @private
   */
  private convertToSearchResult(
    result: any,
    source: 'semantic' | 'string'
  ): SearchResult {
    // This is a simplified conversion - in real implementation,
    // this would handle the differences between semantic and string search results
    
    return {
      id: result.id || result.url,
      url: result.url,
      title: result.title || '',
      content: result.content || '',
      lastVisitTime: result.lastVisitTime || result.visitTime || Date.now(),
      visitCount: result.visitCount || 1,
      
      // Initialize scores based on source
      // semanticScore removed - AI functionality disabled
      stringScore: source === 'string' ? (result.score || result.relevance || 0) : 0,
      fulltextScore: 0, // Not used in current implementation
      combinedScore: result.score || result.similarity || result.relevance || 0,
      relevanceScore: result.relevance || result.score || 0,
      
      // Metadata
      snippet: result.snippet,
      highlights: result.highlights || [],
      domain: this.extractDomain(result.url),
      language: result.language || 'unknown'
    };
  }

  /**
   * Extracts domain from URL
   * @private
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname.toLowerCase();
    } catch {
      return '';
    }
  }
}