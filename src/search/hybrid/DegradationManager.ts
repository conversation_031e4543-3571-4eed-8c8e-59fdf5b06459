/**
 * Search Degradation Manager
 * 
 * Implements search performance degradation and recovery mechanisms:
 * - Timeout detection and auto degradation to faster modes
 * - Error recovery and graceful fallback strategies
 * - Performance mode switching under stress
 * - User experience preservation during degradation
 * 
 * @module DegradationManager
 * @version 4.0
 * @since 2025-06-24
 * @tdd-phase Green
 */

import { SearchStrategy } from './QueryAnalyzer';

/**
 * Degradation configuration options
 */
export interface DegradationConfig {
  maxStringTimeout: number;
  enableAutoDegradation: boolean;
  fallbackStrategy: SearchStrategy;
  maxConsecutiveFailures: number;
  recoveryThreshold: number;
  backoffMultiplier: number;
  maxBackoffMs: number;
}

/**
 * Degradation info attached to search results
 */
export interface DegradationInfo {
  triggered: boolean;
  reason: string;
  fallbackStrategy: SearchStrategy;
  degradationLevel: number;
  canRecover: boolean;
  nextRetryAt?: number;
}

/**
 * Performance info attached to search results
 */
export interface PerformanceInfo {
  mode: 'cpu';
  currentLoad: number;
  degradationActive: boolean;
  fallbackReason?: string;
}

/**
 * User message for search results
 */
export interface UserMessage {
  type: 'info' | 'warning' | 'error';
  message: string;
  suggestion: string;
}

/**
 * Technical info for debugging
 */
export interface TechnicalInfo {
  degradationReason: string;
  activeComponents: string[];
  failedComponents?: string[];
  performanceMetrics: {
    responseTime: number;
    cacheHitRate: number;
    errorRate: number;
  };
}

/**
 * Search quality metrics for monitoring
 */
export interface SearchQuality {
  query: string;
  relevanceScore: number;
  responseTime: number;
  resultCount: number;
  userSatisfaction?: number;
}

/**
 * Recovery strategy definition
 */
export interface RecoveryStrategy {
  action: string;
  backoffMs: number;
  maxRetries: number;
  priority: number;
}

/**
 * Tracks search performance and degradation state
 */
export class DegradationTracker {
  private failureCount = new Map<string, number>();
  private successCount = new Map<string, number>();
  private lastFailureTime = new Map<string, number>();
  private backoffUntil = new Map<string, number>();
  private degradationLevel = 0;
  private readonly config: DegradationConfig;

  constructor(config: Partial<DegradationConfig> = {}) {
    this.config = {
      maxStringTimeout: 200,
      enableAutoDegradation: true,
      fallbackStrategy: SearchStrategy.STRING_ONLY,
      maxConsecutiveFailures: 3,
      recoveryThreshold: 5,
      backoffMultiplier: 2,
      maxBackoffMs: 10000,
      ...config
    };
  }

  /**
   * Record a timeout event
   */
  recordTimeout(component: string, duration: number): void {
    const failures = this.failureCount.get(component) || 0;
    this.failureCount.set(component, failures + 1);
    this.lastFailureTime.set(component, Date.now());

    // Increase degradation level based on severity
    if (component === 'string' && duration > this.config.maxStringTimeout) {
      this.degradationLevel = Math.min(this.degradationLevel + 1, 5);
    }

    this.updateBackoff(component);
  }

  /**
   * Record a failure event
   */
  recordFailure(component: string): void {
    const failures = this.failureCount.get(component) || 0;
    this.failureCount.set(component, failures + 1);
    this.lastFailureTime.set(component, Date.now());
    this.degradationLevel = Math.min(this.degradationLevel + 2, 5);
    this.updateBackoff(component);
  }

  /**
   * Record a successful operation
   */
  recordSuccess(component: string, responseTime: number): void {
    const successes = this.successCount.get(component) || 0;
    this.successCount.set(component, successes + 1);

    // If we have enough successes, start recovery
    if (successes >= this.config.recoveryThreshold) {
      this.degradationLevel = Math.max(this.degradationLevel - 1, 0);
      
      // Reset success count so we can continue recovery
      this.successCount.set(component, 0);
      
      // Clear failure count if we're recovering well
      if (this.degradationLevel === 0) {
        this.failureCount.set(component, 0);
      }
      
      // Clear backoff if performance is good
      if (responseTime < this.config.maxStringTimeout * 2) {
        this.backoffUntil.delete(component);
      }
      
      // If performance is excellent, recover faster
      if (responseTime < this.config.maxStringTimeout) {
        this.degradationLevel = Math.max(this.degradationLevel - 1, 0);
        // If we reach zero degradation level, reset failure count
        if (this.degradationLevel === 0) {
          this.failureCount.set(component, 0);
        }
      }
    }
  }

  /**
   * Check if component should be forced to use fallback mode
   */
  shouldForceFallback(component: string): boolean {
    const failures = this.failureCount.get(component) || 0;
    return failures >= this.config.maxConsecutiveFailures;
  }

  /**
   * Get current degradation level (0-5)
   */
  getCurrentDegradationLevel(): number {
    return this.degradationLevel;
  }

  /**
   * Get backoff delay for component
   */
  getBackoffDelay(component: string): number {
    const failures = this.failureCount.get(component) || 0;
    if (failures === 0) return 0;

    const baseDelay = 1000; // 1 second base
    const delay = Math.min(
      baseDelay * Math.pow(this.config.backoffMultiplier, failures - 1),
      this.config.maxBackoffMs
    );
    
    return delay;
  }

  /**
   * Check if component is in backoff period
   */
  isInBackoff(component: string): boolean {
    const backoffUntil = this.backoffUntil.get(component);
    return backoffUntil ? Date.now() < backoffUntil : false;
  }

  /**
   * Get the configuration
   */
  getConfig(): DegradationConfig {
    return this.config;
  }

  /**
   * Update backoff time for component
   */
  private updateBackoff(component: string): void {
    const delay = this.getBackoffDelay(component);
    this.backoffUntil.set(component, Date.now() + delay);
  }

  /**
   * Reset degradation state
   */
  reset(): void {
    this.failureCount.clear();
    this.successCount.clear();
    this.lastFailureTime.clear();
    this.backoffUntil.clear();
    this.degradationLevel = 0;
  }
}

/**
 * Manages concurrent search requests to prevent overload
 */
export class ConcurrencyThrottle {
  private currentConcurrency = 0;
  private readonly maxConcurrent: number;
  private readonly queueTimeout: number;
  private readonly queue: Array<() => void> = [];

  constructor(config: { maxConcurrent: number; queueTimeout: number }) {
    this.maxConcurrent = config.maxConcurrent;
    this.queueTimeout = config.queueTimeout;
  }

  /**
   * Execute function with concurrency throttling
   */
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error('Queue timeout'));
      }, this.queueTimeout);

      const executeWhenReady = async () => {
        if (this.currentConcurrency >= this.maxConcurrent) {
          this.queue.push(executeWhenReady);
          return;
        }

        clearTimeout(timeoutId);
        this.currentConcurrency++;

        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.currentConcurrency--;
          const next = this.queue.shift();
          if (next) {
            setTimeout(next, 0); // Process next in queue
          }
        }
      };

      executeWhenReady();
    });
  }

  /**
   * Get current concurrency level
   */
  getCurrentConcurrency(): number {
    return this.currentConcurrency;
  }
}

/**
 * Monitors search quality and suggests improvements
 */
export class SearchQualityMonitor {
  private searchHistory: SearchQuality[] = [];
  private readonly maxHistory = 1000;

  /**
   * Record search result for quality monitoring
   */
  recordSearchResult(quality: SearchQuality): void {
    this.searchHistory.push(quality);
    
    // Keep only recent history
    if (this.searchHistory.length > this.maxHistory) {
      this.searchHistory.shift();
    }
  }

  /**
   * Get quality metrics
   */
  getQualityMetrics(): {
    averageRelevance: number;
    averageResponseTime: number;
    qualityDegradationRisk: number;
    recentTrend: 'improving' | 'stable' | 'degrading';
  } {
    if (this.searchHistory.length === 0) {
      return {
        averageRelevance: 0,
        averageResponseTime: 0,
        qualityDegradationRisk: 0,
        recentTrend: 'stable'
      };
    }

    const recent = this.searchHistory.slice(-10);
    const older = this.searchHistory.slice(-20, -10);

    const avgRelevance = recent.reduce((sum, s) => sum + s.relevanceScore, 0) / recent.length;
    const avgResponseTime = recent.reduce((sum, s) => sum + s.responseTime, 0) / recent.length;
    
    // Calculate quality degradation risk
    const qualityDegradationRisk = this.calculateDegradationRisk(recent);
    
    // Determine trend
    let recentTrend: 'improving' | 'stable' | 'degrading' = 'stable';
    if (older.length > 0) {
      const olderAvgRelevance = older.reduce((sum, s) => sum + s.relevanceScore, 0) / older.length;
      const relevanceDelta = avgRelevance - olderAvgRelevance;
      
      if (relevanceDelta > 0.1) recentTrend = 'improving';
      else if (relevanceDelta < -0.1) recentTrend = 'degrading';
    }

    return {
      averageRelevance: avgRelevance,
      averageResponseTime: avgResponseTime,
      qualityDegradationRisk,
      recentTrend
    };
  }

  /**
   * Get quality preservation strategies
   */
  getQualityPreservationStrategies(): string[] {
    const metrics = this.getQualityMetrics();
    const strategies: string[] = [];

    if (metrics.qualityDegradationRisk > 0.5) {
      strategies.push('reduce_concurrent_searches');
    }

    if (metrics.averageResponseTime > 1000) {
      strategies.push('enable_aggressive_caching');
    }

    if (metrics.averageRelevance < 0.6) {
      strategies.push('optimize_search_accuracy');
    }

    return strategies;
  }

  /**
   * Calculate quality degradation risk (0-1)
   */
  private calculateDegradationRisk(recent: SearchQuality[]): number {
    if (recent.length < 3) return 0;

    const avgRelevance = recent.reduce((sum, s) => sum + s.relevanceScore, 0) / recent.length;
    const avgResponseTime = recent.reduce((sum, s) => sum + s.responseTime, 0) / recent.length;
    
    // Risk factors
    let risk = 0;
    
    // Low relevance increases risk
    if (avgRelevance < 0.5) risk += 0.3;
    else if (avgRelevance < 0.7) risk += 0.1;
    
    // High response time increases risk
    if (avgResponseTime > 1000) risk += 0.3;
    else if (avgResponseTime > 500) risk += 0.1;
    
    // Consistency matters - high variance increases risk
    const relevanceVariance = this.calculateVariance(recent.map(s => s.relevanceScore));
    if (relevanceVariance > 0.1) risk += 0.2;

    return Math.min(risk, 1);
  }

  /**
   * Calculate variance of array
   */
  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, diff) => sum + diff, 0) / values.length;
  }
}

/**
 * Manages recovery from system errors
 */
export class RecoveryManager {
  private errorHistory = new Map<string, number[]>();
  private recoveryStrategies = new Map<string, RecoveryStrategy>();

  constructor() {
    // Initialize default recovery strategies
    this.setupDefaultStrategies();
  }

  /**
   * Record an error
   */
  recordError(errorType: string, _error: Error): void {
    const history = this.errorHistory.get(errorType) || [];
    history.push(Date.now());
    
    // Keep only recent errors (last hour)
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    const recentErrors = history.filter(time => time > oneHourAgo);
    
    this.errorHistory.set(errorType, recentErrors);
  }

  /**
   * Get recovery strategy for error type
   */
  getRecoveryStrategy(errorType: string): RecoveryStrategy {
    const strategy = this.recoveryStrategies.get(errorType);
    if (!strategy) {
      return {
        action: 'retry_with_backoff',
        backoffMs: 1000,
        maxRetries: 3,
        priority: 1
      };
    }

    // Adjust strategy based on error frequency
    const errorCount = this.errorHistory.get(errorType)?.length || 0;
    if (errorCount > 5) {
      return {
        ...strategy,
        backoffMs: strategy.backoffMs * 2,
        maxRetries: Math.max(strategy.maxRetries - 1, 1)
      };
    }

    return strategy;
  }

  /**
   * Execute function with recovery
   */
  async executeWithRecovery<T>(
    fn: () => Promise<T>,
    errorType: string
  ): Promise<T> {
    const strategy = this.getRecoveryStrategy(errorType);
    let lastError: Error;

    for (let attempt = 0; attempt <= strategy.maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          // Apply backoff delay
          await new Promise(resolve => setTimeout(resolve, strategy.backoffMs * attempt));
        }

        return await fn();
      } catch (error) {
        lastError = error as Error;
        this.recordError(errorType, lastError);

        if (attempt === strategy.maxRetries) {
          throw lastError;
        }
      }
    }

    throw lastError!;
  }

  /**
   * Setup default recovery strategies
   */
  private setupDefaultStrategies(): void {
    this.recoveryStrategies.set('system_overload', {
      action: 'reduce_load_and_retry',
      backoffMs: 2000,
      maxRetries: 3,
      priority: 2
    });

    this.recoveryStrategies.set('search_engine_failure', {
      action: 'fallback_to_string_search',
      backoffMs: 500,
      maxRetries: 1,
      priority: 3
    });

    this.recoveryStrategies.set('network_error', {
      action: 'retry_with_exponential_backoff',
      backoffMs: 1000,
      maxRetries: 5,
      priority: 1
    });

    this.recoveryStrategies.set('index_corruption', {
      action: 'rebuild_index',
      backoffMs: 5000,
      maxRetries: 1,
      priority: 4
    });
  }
}

/**
 * Main degradation manager that orchestrates all degradation mechanisms
 */
export class DegradationManager {
  private tracker: DegradationTracker;
  private throttle: ConcurrencyThrottle;
  private qualityMonitor: SearchQualityMonitor;
  private recoveryManager: RecoveryManager;

  constructor(config: Partial<DegradationConfig> = {}) {
    this.tracker = new DegradationTracker(config);
    this.throttle = new ConcurrencyThrottle({
      maxConcurrent: 3,
      queueTimeout: 2000
    });
    this.qualityMonitor = new SearchQualityMonitor();
    this.recoveryManager = new RecoveryManager();
  }

  /**
   * Check if degradation should be triggered
   */
  shouldDegrade(component: string, responseTime?: number): boolean {
    // Check backoff status
    if (this.tracker.isInBackoff(component)) {
      return true;
    }

    // Check degradation level
    if (this.tracker.getCurrentDegradationLevel() > 2) {
      return true;
    }

    // Check timeout
    if (responseTime && component === 'string' && responseTime > this.tracker.getConfig().maxStringTimeout * 2) {
      return true;
    }

    return false;
  }

  /**
   * Create degradation info for search results
   */
  createDegradationInfo(
    triggered: boolean,
    reason: string,
    fallbackStrategy: SearchStrategy
  ): DegradationInfo {
    return {
      triggered,
      reason,
      fallbackStrategy,
      degradationLevel: this.tracker.getCurrentDegradationLevel(),
      canRecover: this.tracker.getCurrentDegradationLevel() < 3,
      nextRetryAt: triggered ? Date.now() + this.tracker.getBackoffDelay('string') : undefined
    };
  }

  /**
   * Create performance info for search results
   */
  createPerformanceInfo(mode: 'cpu'): PerformanceInfo {
    return {
      mode,
      currentLoad: this.throttle.getCurrentConcurrency(),
      degradationActive: this.tracker.getCurrentDegradationLevel() > 0,
      fallbackReason: this.tracker.getCurrentDegradationLevel() > 0 ? 'degraded_performance' : undefined
    };
  }

  /**
   * Create user message for search results
   */
  createUserMessage(degradationInfo: DegradationInfo): UserMessage {
    if (!degradationInfo.triggered) {
      return {
        type: 'info',
        message: 'Search completed successfully',
        suggestion: 'Results are optimized for your query'
      };
    }

    switch (degradationInfo.reason) {
      case 'string_timeout':
        return {
          type: 'info',
          message: 'Search is using optimized mode for better performance',
          suggestion: 'Results may be delivered faster with good accuracy'
        };
      
      case 'search_engine_failure':
        return {
          type: 'warning',
          message: 'Search is temporarily using fallback mode',
          suggestion: 'Using simplified matching for reliable results'
        };
      
      default:
        return {
          type: 'info',
          message: 'Search is using optimized performance mode',
          suggestion: 'Results are prioritized for speed and reliability'
        };
    }
  }

  /**
   * Create technical info for debugging
   */
  createTechnicalInfo(
    degradationReason: string,
    activeComponents: string[],
    failedComponents?: string[]
  ): TechnicalInfo {
    const qualityMetrics = this.qualityMonitor.getQualityMetrics();
    
    return {
      degradationReason,
      activeComponents,
      failedComponents,
      performanceMetrics: {
        responseTime: qualityMetrics.averageResponseTime,
        cacheHitRate: 0.8, // This would come from performance optimizer
        errorRate: failedComponents ? failedComponents.length / (activeComponents.length + failedComponents.length) : 0
      }
    };
  }

  /**
   * Get the degradation tracker
   */
  getTracker(): DegradationTracker {
    return this.tracker;
  }

  /**
   * Get the concurrency throttle
   */
  getThrottle(): ConcurrencyThrottle {
    return this.throttle;
  }

  /**
   * Get the quality monitor
   */
  getQualityMonitor(): SearchQualityMonitor {
    return this.qualityMonitor;
  }

  /**
   * Get the recovery manager
   */
  getRecoveryManager(): RecoveryManager {
    return this.recoveryManager;
  }
}

// Export singleton instance for easy use
export const degradationManager = new DegradationManager();