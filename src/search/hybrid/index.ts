/**
 * Hybrid Search Engine - Modular Architecture
 * 
 * A comprehensive hybrid search solution that combines traditional search
 * with traditional string matching for optimal search results.
 * 
 * @module HybridSearch
 * @version 4.0
 * @since 2025-06-23
 */

// Core analysis and strategy
export {
  QueryAnalyzer,
  QueryType,
  SearchStrategy
} from './QueryAnalyzer';

export type {
  SearchWeights,
  QueryAnalysisResult,
  ConfidenceFactors,
  LanguageDistribution
} from './QueryAnalyzer';

// Result merging and ranking
export {
  resultMerger,
  ResultMerger
} from './ResultMerger';

export type {
  MergeOptions,
  MergeStatistics,
  EngineResultMetadata
} from './ResultMerger';

// Search coordination
export {
  SearchCoordinator
} from './SearchCoordinator';

export type {
  SearchExecutionStats,
  StrategyConfig
} from './SearchCoordinator';

// Result processing
export {
  SearchResultProcessor
} from './SearchResultProcessor';

export type {
  ProcessorConfig
} from './SearchResultProcessor';

// Caching management
export {
  SearchCacheManager,
  HybridSearchCacheManager
} from './SearchCacheManager';

export type {
  CacheStatistics,
  CacheConfig
} from './SearchCacheManager';

// String matching engine
export {
  StringMatcher,
  stringSearch,
  defaultStringMatcher
} from './StringMatcher';

export type {
  StringMatcherConfig
} from './StringMatcher';

// Progressive search
export {
  ProgressiveSearch,
  ProgressiveSearchFactory,
  progressiveSearch
} from './ProgressiveSearch';

export type {
  ProgressiveSearchOptions,
  ProgressiveSearchEvent,
  ProgressiveSearchEventType,
  SearchExecutor,
  SearchExecutorFunction
} from './ProgressiveSearch';

// Main hybrid search engine (legacy support)
export {
  HybridSearchEngine
} from './HybridSearchEngine';

export type {
  SearchResult,
  SearchOptions,
  TimeRange,
  HybridSearchResults,
  HybridSearchConfig
} from './HybridSearchEngine';

// ======================
// Convenience Factory Functions
// ======================

/**
 * Creates a fully configured hybrid search system
 */
export async function createHybridSearchSystem(config: {
  caching?: Partial<import('./SearchCacheManager').CacheConfig>;
  processing?: Partial<import('./SearchResultProcessor').ProcessorConfig>;
  strategy?: Partial<import('./SearchCoordinator').StrategyConfig>;
} = {}) {
  const { HybridSearchCacheManager } = await import('./SearchCacheManager');
  const { SearchResultProcessor } = await import('./SearchResultProcessor');
  const { SearchCoordinator } = await import('./SearchCoordinator');
  
  const cacheManager = new HybridSearchCacheManager(config.caching);
  const resultProcessor = new SearchResultProcessor(config.processing);
  const searchCoordinator = new SearchCoordinator();

  return {
    coordinator: searchCoordinator,
    processor: resultProcessor,
    cache: cacheManager,
    
    /**
     * Convenience method for complete search with all modules
     */
    async search(query: string, options: import('./HybridSearchEngine').SearchOptions = {}) {
      // Check cache first
      const cacheKey = `${query}:${JSON.stringify(options)}`;
      const cachedResult = cacheManager.getSearchResults(cacheKey);
      
      if (cachedResult) {
        return {
          ...cachedResult,
          fromCache: true
        };
      }

      // Execute search
      const searchResult = await searchCoordinator.executeSearch(query, options);
      
      // Process results
      const processedResults = resultProcessor.applyFilters(
        searchResult.results,
        options
      );
      
      const finalResults = resultProcessor.generateSnippets(
        processedResults,
        query
      );

      const result = {
        results: finalResults,
        stats: searchResult.stats,
        analysis: searchResult.analysis,
        fromCache: false
      };

      // Cache result
      cacheManager.setSearchResults(cacheKey, result);

      return result;
    },

    /**
     * Get comprehensive statistics
     */
    getStatistics() {
      return {
        cache: cacheManager.getCombinedStatistics(),
        lastExecution: searchCoordinator.getLastExecutionStats()
      };
    },

    /**
     * Clean up resources
     */
    destroy() {
      cacheManager.destroy();
    }
  };
}

/**
 * Default hybrid search instance for backward compatibility
 */
export const defaultHybridSearch = await createHybridSearchSystem();

/**
 * Convenience search function for simple use cases
 */
export async function hybridSearch(
  query: string,
  options: import('./HybridSearchEngine').SearchOptions = {}
) {
  const searchSystem = await createHybridSearchSystem();
  return searchSystem.search(query, options);
}

// ======================
// Type Exports for TypeScript
// ======================

// Additional type exports
export type {
  CacheStatistics as CacheStats,
  CacheConfig as CacheConfiguration
} from './SearchCacheManager';

export type {
  // Coordinator types
  SearchExecutionStats as ExecutionStats,
  StrategyConfig as SearchStrategyConfig
} from './SearchCoordinator';

export type {
  // Processor types
  ProcessorConfig as ResultProcessorConfig
} from './SearchResultProcessor';

export type {
  // Merger types
  MergeOptions as ResultMergeOptions,
  MergeStatistics as ResultMergeStats
} from './ResultMerger';

// Re-export main types for convenience
export type {
  SearchResult as HybridSearchResult,
  SearchOptions as HybridSearchOptions
} from './HybridSearchEngine';