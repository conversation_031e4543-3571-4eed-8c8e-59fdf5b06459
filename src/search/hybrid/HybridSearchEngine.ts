/**
 * HybridSearchEngine - Core Traditional Search Implementation
 * 
 * Combines string search with fulltext search (Lunr.js)
 * to provide optimal search results based on query type and user intent.
 * 
 * @module HybridSearchEngine
 * @version 4.1
 * @since 2025-06-23
 */

import { QueryAnalyzer, SearchStrategy } from './QueryAnalyzer';
import type { QueryAnalysisResult } from './QueryAnalyzer';
// Traditional search engines: string matching + fulltext (Lunr)
import { searchService } from '../../services/search.service';
import { settingsService } from '../../services/settings.service';
import { searchConfigService, type UnifiedSearchConfig } from '../../services/search-config.service';
import { searchConfigListener, type SearchConfigChangeEvent } from '../../services/search-config-listener';
import type { SearchResultItem, SearchEngineConfig, Page } from '../../models';
import { performanceOptimizer } from './PerformanceOptimizer';
import { 
  DegradationManager, 
  type DegradationConfig, 
  type DegradationInfo, 
  type PerformanceInfo, 
  type UserMessage, 
  type TechnicalInfo 
} from './DegradationManager';
import { lunrSearchEngine } from '../fulltext/LunrSearchEngine';
import { 
  ProgressiveSearch, 
  ProgressiveSearchFactory,
  type ProgressiveSearchOptions,
  type ProgressiveSearchEvent,
  type SearchExecutor
} from './ProgressiveSearch';

/**
 * Individual search result from either string or fulltext search
 */
export interface SearchResult {
  id: string;
  url: string;
  title: string;
  content: string;
  lastVisitTime: number;
  visitCount: number;
  
  // Search scoring
  stringScore: number;
  fulltextScore: number;
  combinedScore: number;
  relevanceScore: number;
  
  // Metadata
  snippet?: string;
  highlights?: string[];
  domain?: string;
  language?: string;
}

/**
 * Time range filter for search results
 */
export interface TimeRange {
  start: number;
  end: number;
}

/**
 * Search options for customizing hybrid search behavior
 */
export interface SearchOptions {
  limit?: number;
  offset?: number;
  domainFilter?: string[];
  filterMode?: 'include' | 'exclude';
  timeRange?: TimeRange;
  forceStrategy?: SearchStrategy;
  includeContent?: boolean;
  highlightTerms?: boolean;
  cacheResults?: boolean;
  maxTimeout?: number;
}

/**
 * Complete search results from hybrid engine
 */
export interface HybridSearchResults {
  // Query information
  query: string;
  cleanedQuery: string;
  strategy: SearchStrategy;
  language: string;
  
  // Search result sets
  stringResults: SearchResult[];
  fulltextResults: SearchResult[];
  mergedResults: SearchResult[];
  
  // Metadata
  totalResults: number;
  responseTime: number;
  confidence: number;
  executionPath: string;
  fromCache: boolean;
  
  // Error handling
  fallbackUsed: boolean;
  sanitized: boolean;
  error?: string;
  warnings: string[];
  
  // Degradation support
  degradationInfo?: DegradationInfo;
  performanceInfo?: PerformanceInfo;
  userMessage?: UserMessage;
  technicalInfo?: TechnicalInfo;
  partialFailure?: boolean;
  failedComponents?: string[];
}

/**
 * Configuration for HybridSearchEngine
 */
export interface HybridSearchConfig {
  queryAnalyzer?: QueryAnalyzer;
  stringSearchThreshold?: number;
  cacheSize?: number;
  maxResults?: number;
  timeoutMs?: number;
  degradationConfig?: DegradationConfig;
}

/**
 * HybridSearchEngine implementation
 * This is a placeholder implementation for TDD Red phase.
 * The actual implementation will be created in the Green phase.
 */
export class HybridSearchEngine {
  private readonly queryAnalyzer: QueryAnalyzer;
  private readonly config: Required<HybridSearchConfig>;
  private readonly degradationManager: DegradationManager;
  private searchEngineConfig: SearchEngineConfig | null = null;
  private unifiedSearchConfig: UnifiedSearchConfig | null = null;
  private readonly progressiveSearch: ProgressiveSearch;
  private readonly configListenerId: string;

  constructor(config: HybridSearchConfig = {}) {
    this.queryAnalyzer = config.queryAnalyzer || new QueryAnalyzer();
    
    // Default degradation configuration
    const defaultDegradationConfig: DegradationConfig = {
      maxStringTimeout: 200,
      enableAutoDegradation: true,
      fallbackStrategy: SearchStrategy.STRING_ONLY,
      maxConsecutiveFailures: 3,
      recoveryThreshold: 5,
      backoffMultiplier: 2,
      maxBackoffMs: 10000
    };
    
    this.config = {
      queryAnalyzer: this.queryAnalyzer,
      stringSearchThreshold: 0.5,
      cacheSize: 100,
      maxResults: 50,
      timeoutMs: 5000,
      degradationConfig: defaultDegradationConfig,
      ...config
    };
    
    // Generate unique ID for this instance
    this.configListenerId = `hybrid-search-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    
    this.degradationManager = new DegradationManager(this.config.degradationConfig);
    this.progressiveSearch = new ProgressiveSearch();
    
    // Load initial configuration from the unified service (async but don't block constructor)
    this.loadUnifiedConfig().catch(error => {
      console.error('[HybridSearchEngine] Failed to load initial config in constructor:', error);
    });
    
    // Listen for configuration changes
    this.setupConfigListener();
  }

  /**
   * Setup configuration change listener
   * @private
   */
  private setupConfigListener(): void {
    searchConfigListener.addListener(this.configListenerId, this.handleConfigChange.bind(this));
    console.log(`[HybridSearchEngine] Registered config listener: ${this.configListenerId}`);
  }

  /**
   * Handle configuration changes
   * @private
   */
  private handleConfigChange(event: SearchConfigChangeEvent): void {
    console.log('[HybridSearchEngine] Configuration changed, updating internal config');
    
    // Store previous configuration for comparison
    const previousConfig = {
      maxResults: this.config.maxResults,
      timeoutMs: this.config.timeoutMs,
      stringSearchThreshold: this.config.stringSearchThreshold,
      engines: this.searchEngineConfig ? {
        traditional: {
          enabled: this.searchEngineConfig.traditional.enabled,
          weight: this.searchEngineConfig.traditional.weight
        },
        fulltext: {
          enabled: this.searchEngineConfig.fulltext.enabled,
          weight: this.searchEngineConfig.fulltext.weight
        }
      } : null
    };
    
    // Update internal configuration
    this.unifiedSearchConfig = event.config;
    this.searchEngineConfig = event.config.engines;
    
    // Update internal config with user settings
    this.config.maxResults = event.config.behavior.maxResults;
    this.config.timeoutMs = event.config.behavior.searchTimeoutMs;
    this.config.stringSearchThreshold = event.config.behavior.fuzzySearchThreshold;
    
    // Validate the new configuration
    this.validateLoadedConfig();
    
    // Clear cache if configuration significantly changed
    const configChanged = this.hasSignificantConfigChange(previousConfig, event.config);
    if (configChanged) {
      console.log('[HybridSearchEngine] Significant config change detected, clearing cache');
      this.clearCache();
    }
    
    console.log('[HybridSearchEngine] Applied config changes:', {
      previous: previousConfig,
      current: {
        maxResults: this.config.maxResults,
        timeoutMs: this.config.timeoutMs,
        stringSearchThreshold: this.config.stringSearchThreshold,
        engines: {
          traditional: {
            enabled: this.searchEngineConfig.traditional.enabled,
            weight: this.searchEngineConfig.traditional.weight
          },
          fulltext: {
            enabled: this.searchEngineConfig.fulltext.enabled,
            weight: this.searchEngineConfig.fulltext.weight
          }
        }
      },
      cacheCleared: configChanged
    });
  }

  /**
   * Check if configuration change is significant enough to clear cache
   * @private
   */
  private hasSignificantConfigChange(
    previous: { 
      engines?: { traditional: { enabled: boolean; weight: number }; fulltext: { enabled: boolean; weight: number } } | null; 
      stringSearchThreshold?: number 
    },
    current: UnifiedSearchConfig
  ): boolean {
    if (!previous.engines) return true;
    
    // Check if engine enablement changed
    if (previous.engines.traditional.enabled !== current.engines.traditional.enabled ||
        previous.engines.fulltext.enabled !== current.engines.fulltext.enabled) {
      return true;
    }
    
    // Check if engine weights changed significantly (>5%)
    const tradWeightDiff = Math.abs(previous.engines.traditional.weight - current.engines.traditional.weight);
    const fulltextWeightDiff = Math.abs(previous.engines.fulltext.weight - current.engines.fulltext.weight);
    if (tradWeightDiff > 0.05 || fulltextWeightDiff > 0.05) {
      return true;
    }
    
    // Check if search behavior changed significantly
    if (previous.stringSearchThreshold !== undefined && 
        Math.abs(previous.stringSearchThreshold - current.behavior.fuzzySearchThreshold) > 0.1) {
      return true;
    }
    
    return false;
  }

  /**
   * Load unified search configuration from the service
   * @private
   */
  private async loadUnifiedConfig(): Promise<void> {
    try {
      console.log('[HybridSearchEngine] Loading unified configuration...');
      this.unifiedSearchConfig = await searchConfigService.getUnifiedConfig();
      this.searchEngineConfig = this.unifiedSearchConfig.engines;
      
      // Update internal config with user settings
      const oldConfig = {
        maxResults: this.config.maxResults,
        timeoutMs: this.config.timeoutMs,
        stringSearchThreshold: this.config.stringSearchThreshold
      };
      
      this.config.maxResults = this.unifiedSearchConfig.behavior.maxResults;
      this.config.timeoutMs = this.unifiedSearchConfig.behavior.searchTimeoutMs;
      this.config.stringSearchThreshold = this.unifiedSearchConfig.behavior.fuzzySearchThreshold;
      
      console.log('[HybridSearchEngine] Successfully loaded unified config:', {
        previous: oldConfig,
        current: {
          maxResults: this.config.maxResults,
          timeoutMs: this.config.timeoutMs,
          stringSearchThreshold: this.config.stringSearchThreshold
        },
        engines: {
          traditional: {
            enabled: this.searchEngineConfig.traditional.enabled,
            weight: this.searchEngineConfig.traditional.weight
          },
          fulltext: {
            enabled: this.searchEngineConfig.fulltext.enabled,
            weight: this.searchEngineConfig.fulltext.weight
          }
        }
      });
      
      // Validate configuration
      this.validateLoadedConfig();
      
    } catch (error) {
      console.error('[HybridSearchEngine] Failed to load unified config:', error);
      console.warn('[HybridSearchEngine] Using default configuration values');
      
      // Ensure we have fallback values if config loading fails
      if (!this.unifiedSearchConfig) {
        console.log('[HybridSearchEngine] Setting fallback configuration');
        this.config.maxResults = 50;
        this.config.timeoutMs = 5000;
        this.config.stringSearchThreshold = 0.3;
      }
    }
  }

  /**
   * Validate the loaded configuration
   * @private
   */
  private validateLoadedConfig(): void {
    if (!this.unifiedSearchConfig) {
      console.warn('[HybridSearchEngine] No unified config loaded');
      return;
    }

    // Validate behavior settings
    const behavior = this.unifiedSearchConfig.behavior;
    if (behavior.maxResults < 1 || behavior.maxResults > 200) {
      console.warn(`[HybridSearchEngine] Invalid maxResults: ${behavior.maxResults}, using default`);
      this.config.maxResults = 50;
    }

    if (behavior.searchTimeoutMs < 100 || behavior.searchTimeoutMs > 60000) {
      console.warn(`[HybridSearchEngine] Invalid timeout: ${behavior.searchTimeoutMs}ms, using default`);
      this.config.timeoutMs = 5000;
    }

    if (behavior.fuzzySearchThreshold < 0 || behavior.fuzzySearchThreshold > 1) {
      console.warn(`[HybridSearchEngine] Invalid threshold: ${behavior.fuzzySearchThreshold}, using default`);
      this.config.stringSearchThreshold = 0.3;
    }

    // Validate engine weights
    const engines = this.unifiedSearchConfig.engines;
    const totalWeight = engines.traditional.weight + engines.fulltext.weight;
    if (Math.abs(totalWeight - 1.0) > 0.01) {
      console.warn(`[HybridSearchEngine] Engine weights don't sum to 1.0: ${totalWeight}, this may affect result ranking`);
    }

    console.log('[HybridSearchEngine] Configuration validation completed');
  }

  /**
   * Initialize the hybrid search engine with pages data
   * This ensures both string search (Fuse.js) and fulltext search (Lunr.js) indexes are built
   */
  async initialize(pages: Page[]): Promise<void> {
    try {
      // Initialize string search (Fuse.js) through searchService
      await searchService.buildIndex();

      // Initialize Lunr search engine with persistence and consistency checks
      // This method handles index loading, document mapping, and consistency validation
      await lunrSearchEngine.initializeWithPersistence(pages);

      console.log(`[HybridSearchEngine] Initialized with ${pages.length} pages`);
    } catch (error) {
      console.error('[HybridSearchEngine] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Update the search indexes with new pages data
   */
  async updateIndex(pages: Page[]): Promise<void> {
    try {
      // Update string search index
      await searchService.buildIndex();
      
      // Rebuild fulltext search index
      await lunrSearchEngine.createIndex(pages);
      
      console.log(`[HybridSearchEngine] Updated index with ${pages.length} pages`);
    } catch (error) {
      console.error('[HybridSearchEngine] Failed to update index:', error);
      throw error;
    }
  }

  /**
   * Performs hybrid search combining string and fulltext matching
   * GREEN PHASE: Full implementation
   */
  async search(query: string, options: SearchOptions = {}): Promise<HybridSearchResults> {
    const startTime = performance.now();
    
    // Ensure unified search configuration is loaded and valid
    try {
      await this.loadUnifiedConfig();
      if (!this.unifiedSearchConfig) {
        console.warn('[HybridSearchEngine] No valid configuration available, using defaults');
      }
    } catch (error) {
      console.error('[HybridSearchEngine] Failed to load config before search:', error);
      // Continue with existing config or defaults
    }
    
    // Validate and sanitize options
    const validatedOptions = this.validateOptions(options);
    
    // Check performance optimizer cache first if enabled
    if (validatedOptions.cacheResults !== false) {
      const cached = performanceOptimizer.getCachedResult(query, validatedOptions);
      if (cached) {
        return cached;
      }
    }

    try {
      // Analyze the query
      const analysis = this.queryAnalyzer.analyzeQuery(query);
      
      // Handle empty queries
      if (!analysis.cleanedQuery || !analysis.cleanedQuery.trim()) {
        return this.createEmptyResult(query, analysis, startTime);
      }

      // Check which search engines are enabled
      const stringEnabled = this.searchEngineConfig?.traditional.enabled ?? true;
      const fulltextEnabled = this.searchEngineConfig?.fulltext?.enabled ?? true;
      
      // Determine execution strategy
      const strategy = validatedOptions.forceStrategy || analysis.strategy;
      let degradationTriggered = false;
      let degradationReason = '';
      
      // If no engines are enabled, return empty results
      if (!stringEnabled && !fulltextEnabled) {
        return this.createEmptyResult(query, analysis, startTime);
      }

      let stringResults: SearchResult[] = [];
      let fulltextResults: SearchResult[] = [];
      let executionPath = '';
      let fallbackUsed = false;
      const warnings: string[] = [];
      const failedComponents: string[] = [];

      try {
        // Execute search based on strategy
        switch (strategy) {
          case SearchStrategy.STRING_ONLY:
            if (stringEnabled) {
              stringResults = await this.performStringSearchWithDegradation(analysis, validatedOptions);
            }
            executionPath = 'string_only';
            break;
            
          case SearchStrategy.FULLTEXT_ONLY:
            if (fulltextEnabled) {
              fulltextResults = await this.performFulltextSearchWithDegradation(analysis, validatedOptions);
            }
            executionPath = 'fulltext_only';
            break;
            
          case SearchStrategy.HYBRID_BALANCED:
            try {
              // Run both string search and fulltext search in parallel
              const promises: Promise<SearchResult[]>[] = [];
              
              if (stringEnabled) {
                promises.push(
                  this.degradationManager.getThrottle().execute(() =>
                    this.performStringSearchWithDegradation(analysis, validatedOptions)
                  )
                );
              }
              
              if (fulltextEnabled) {
                promises.push(
                  this.degradationManager.getThrottle().execute(() =>
                    this.performFulltextSearchWithDegradation(analysis, validatedOptions)
                  )
                );
              }
              
              if (promises.length === 0) {
                // No engines enabled
                return this.createEmptyResult(query, analysis, startTime);
              }
              
              const results = await Promise.all(promises);
              
              // Handle results based on what was enabled
              if (stringEnabled && fulltextEnabled) {
                // Both engines: string and fulltext
                [stringResults, fulltextResults] = results;
              } else if (stringEnabled) {
                // Only string search enabled
                stringResults = results[0];
              } else if (fulltextEnabled) {
                // Only fulltext search enabled
                fulltextResults = results[0];
              }
              
              executionPath = 'hybrid_balanced';
            } catch (error) {
              this.degradationManager.getTracker().recordFailure('hybrid');
              failedComponents.push('search_engines');
              warnings.push('Hybrid search failed, using fallback');
              
              // Try fallback to string search
              if (stringEnabled) {
                stringResults = await this.performStringSearchWithDegradation(analysis, validatedOptions);
              }
              fallbackUsed = true;
              degradationTriggered = true;
              degradationReason = 'hybrid_failure';
              executionPath = 'string_fallback';
            }
            break;
            
          case SearchStrategy.FALLBACK:
          default:
            if (stringEnabled) {
              stringResults = await this.performStringSearchWithDegradation(analysis, validatedOptions);
            } else if (fulltextEnabled) {
              fulltextResults = await this.performFulltextSearchWithDegradation(analysis, validatedOptions);
            }
            executionPath = 'fallback';
            break;
        }
      } catch (searchError) {
        return this.handleSearchErrorWithDegradation(searchError as Error, analysis, validatedOptions);
      }

      // Merge and rank search results
      const mergedResults = this.mergeSearchResults(stringResults, fulltextResults);
      
      // Apply filters
      const filteredResults = this.applyFilters(mergedResults, validatedOptions);
      
      // Generate snippets if requested
      const finalResults = validatedOptions.highlightTerms 
        ? this.generateSnippets(filteredResults, analysis.cleanedQuery, true)
        : filteredResults;

      // Apply limit
      const limitedResults = validatedOptions.limit 
        ? finalResults.slice(0, validatedOptions.limit)
        : finalResults;

      // Calculate metrics
      const { responseTime } = this.calculateMetrics(startTime, limitedResults.length);

      // Record performance metrics for degradation tracking
      if (stringResults.length > 0) {
        this.degradationManager.getTracker().recordSuccess('string', responseTime);
      }
      if (fulltextResults.length > 0) {
        this.degradationManager.getTracker().recordSuccess('fulltext', responseTime);
      }

      // Record search quality
      if (limitedResults.length > 0) {
        const avgRelevance = limitedResults.reduce((sum, r) => sum + r.relevanceScore, 0) / limitedResults.length;
        this.degradationManager.getQualityMonitor().recordSearchResult({
          query,
          relevanceScore: avgRelevance,
          responseTime,
          resultCount: limitedResults.length
        });
      }

      // Create degradation info
      const degradationInfo = this.degradationManager.createDegradationInfo(
        degradationTriggered,
        degradationReason,
        strategy
      );

      // Create performance info
      const performanceInfo = this.degradationManager.createPerformanceInfo('cpu');

      // Create user message
      const userMessage = this.degradationManager.createUserMessage(degradationInfo);

      // Create technical info
      const technicalInfo = this.degradationManager.createTechnicalInfo(
        degradationReason,
        ['string_search', ...(fulltextResults.length > 0 ? ['fulltext_search'] : [])],
        failedComponents.length > 0 ? failedComponents : undefined
      );

      // Build result object with degradation info
      const result: HybridSearchResults = {
        query,
        cleanedQuery: analysis.cleanedQuery,
        strategy,
        language: analysis.language,
        stringResults,
        fulltextResults: fulltextResults,
        mergedResults: limitedResults,
        totalResults: limitedResults.length,
        responseTime,
        confidence: analysis.confidence,
        executionPath,
        fromCache: false,
        fallbackUsed,
        sanitized: analysis.sanitized,
        warnings,
        degradationInfo,
        performanceInfo,
        userMessage,
        technicalInfo,
        partialFailure: failedComponents.length > 0,
        failedComponents: failedComponents.length > 0 ? failedComponents : undefined
      };

      // Cache the result using performance optimizer if enabled
      if (validatedOptions.cacheResults !== false) {
        performanceOptimizer.cacheResult(query, validatedOptions, result);
      }

      // Update performance metrics
      performanceOptimizer.updateMetrics(responseTime);

      return result;

    } catch (error) {
      return this.handleSearchErrorWithDegradation(error as Error, { query } as QueryAnalysisResult, validatedOptions);
    }
  }


  /**
   * Performs traditional string-based search
   * @private
   */
  private async performStringSearch(
    analysis: QueryAnalysisResult,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    try {
      const searchOptions = {
        query: analysis.cleanedQuery || analysis.query,
        timeRange: options.timeRange,
        domains: options.domainFilter,
        limit: options.limit || 20,
        sortBy: 'relevance' as const
      };

      const stringResults = await searchService.search(
        analysis.cleanedQuery || analysis.query,
        searchOptions
      );

      // Convert search service results to HybridSearchEngine format
      const results: SearchResult[] = stringResults.map((item: SearchResultItem) => ({
        id: item.page.id,
        url: item.page.url,
        title: item.page.title,
        content: item.page.content || '',
        lastVisitTime: item.page.visitTime,
        visitCount: item.page.accessCount || 1,
        stringScore: item.score || 0.5,
        fulltextScore: 0,
        combinedScore: item.score || 0.5,
        relevanceScore: item.score || 0.5,
        snippet: item.highlights?.[0] || '',
        highlights: item.highlights || [],
        domain: item.page.domain,
        language: analysis.language || item.page.language || 'en'
      }));

      return results;
      
    } catch (error) {
      throw new Error(`String search failed: ${error}`);
    }
  }

  /**
   * Performs fulltext search using Lunr
   * @private
   */
  private async performFulltextSearch(
    analysis: QueryAnalysisResult,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    try {
      const lunrOptions = {
        limit: options.limit || 20,
        fuzzy: true,
        fields: undefined, // Search all fields by default
        exactPhrases: analysis.filters?.exactPhrases || [],
        excludeTerms: analysis.filters?.exclude || []
      };

      const lunrResults = await lunrSearchEngine.search(
        analysis.cleanedQuery || analysis.query,
        lunrOptions
      );

      // Convert Lunr search results to HybridSearchEngine format
      const results: SearchResult[] = lunrResults.map((item: SearchResultItem) => ({
        id: item.page.id,
        url: item.page.url,
        title: item.page.title,
        content: item.page.content || '',
        lastVisitTime: item.page.visitTime,
        visitCount: item.page.accessCount || 1,
        stringScore: 0,
        fulltextScore: item.score || 0.5, // Lunr score already normalized to 0-1
        combinedScore: item.score || 0.5,
        relevanceScore: item.score || 0.5,
        snippet: item.highlights?.[0] || '',
        highlights: item.highlights || [],
        domain: item.page.domain,
        language: analysis.language || item.page.language || 'en'
      }));

      return results;
      
    } catch (error) {
      throw new Error(`Fulltext search failed: ${error}`);
    }
  }


  /**
   * Applies filters and options to search results
   * @private
   */
  private applyFilters(
    results: SearchResult[],
    options: SearchOptions
  ): SearchResult[] {
    let filteredResults = [...results];
    
    // Apply domain filter
    if (options.domainFilter && options.domainFilter.length > 0) {
      filteredResults = filteredResults.filter(result => {
        try {
          const domain = new URL(result.url).hostname;
          const matches = options.domainFilter!.some(filter => 
            domain.includes(filter)
          );
          
          return options.filterMode === 'exclude' ? !matches : matches;
        } catch {
          // Invalid URL, include by default
          return options.filterMode !== 'exclude';
        }
      });
    }
    
    // Apply time range filter
    if (options.timeRange) {
      filteredResults = filteredResults.filter(result => 
        result.lastVisitTime >= options.timeRange!.start &&
        result.lastVisitTime <= options.timeRange!.end
      );
    }
    
    return filteredResults;
  }


  /**
   * Generates search result snippets and highlights
   * @private
   */
  private generateSnippets(
    results: SearchResult[],
    query: string,
    highlightTerms: boolean
  ): SearchResult[] {
    const queryTerms = query.toLowerCase().split(/\s+/);
    
    return results.map(result => {
      let snippet = result.content;
      const highlights: string[] = [];
      
      if (highlightTerms) {
        // Find and highlight query terms
        queryTerms.forEach(term => {
          if (term.length > 2) {
            const regex = new RegExp(`(${term})`, 'gi');
            snippet = snippet.replace(regex, '<mark>$1</mark>');
            
            // Extract highlighted terms
            const matches = result.content.match(new RegExp(term, 'gi'));
            if (matches) {
              highlights.push(...matches);
            }
          }
        });
      }
      
      // Create snippet (first 200 characters)
      const snippetLength = 200;
      if (snippet.length > snippetLength) {
        snippet = snippet.substring(0, snippetLength) + '...';
      }
      
      return {
        ...result,
        snippet,
        highlights: [...new Set(highlights)] // Remove duplicates
      };
    });
  }


  /**
   * Handles search errors and fallback strategies
   * @private
   */
  // @ts-ignore - Unused method for future use
  private async _handleSearchError(
    error: Error,
    analysis: QueryAnalysisResult,
    options: SearchOptions
  ): Promise<HybridSearchResults> {
    const startTime = performance.now();
    
    // Try fallback search strategy
    try {
      const fallbackResults = await this.performStringSearch(analysis, options);
      const { responseTime } = this.calculateMetrics(startTime, fallbackResults.length);
      
      return {
        query: analysis.query || '',
        cleanedQuery: analysis.cleanedQuery || '',
        strategy: SearchStrategy.FALLBACK,
        language: analysis.language || 'unknown',
        stringResults: fallbackResults,
        fulltextResults: [],
        mergedResults: fallbackResults,
        totalResults: fallbackResults.length,
        responseTime,
        confidence: 0.3,
        executionPath: 'error_fallback',
        fromCache: false,
        fallbackUsed: true,
        sanitized: analysis.sanitized || false,
        error: undefined,
        warnings: [`Search error: ${error.message}`]
      };
    } catch (fallbackError) {
      // Complete failure
      const { responseTime } = this.calculateMetrics(startTime, 0);
      
      return {
        query: analysis.query || '',
        cleanedQuery: analysis.cleanedQuery || '',
        strategy: SearchStrategy.FALLBACK,
        language: analysis.language || 'unknown',
        stringResults: [],
        fulltextResults: [],
        mergedResults: [],
        totalResults: 0,
        responseTime,
        confidence: 0,
        executionPath: 'error_complete_failure',
        fromCache: false,
        fallbackUsed: true,
        sanitized: analysis.sanitized || false,
        error: error.message.includes('Database') ? 'Database search failed' : error.message,
        warnings: [`Search failed: ${error.message}`]
      };
    }
  }

  /**
   * Validates and sanitizes search options
   * @private
   */
  private validateOptions(options: SearchOptions): SearchOptions {
    // Use user-configured maxResults from unified config
    const effectiveMaxResults = this.unifiedSearchConfig?.behavior.maxResults || this.config.maxResults;
    const effectiveTimeout = this.unifiedSearchConfig?.behavior.searchTimeoutMs || this.config.timeoutMs;
    
    return {
      limit: Math.min(options.limit || effectiveMaxResults, effectiveMaxResults),
      offset: Math.max(options.offset || 0, 0),
      domainFilter: options.domainFilter || [],
      filterMode: options.filterMode || 'include',
      timeRange: options.timeRange,
      forceStrategy: options.forceStrategy,
      includeContent: options.includeContent !== false,
      highlightTerms: options.highlightTerms !== false,
      cacheResults: options.cacheResults !== false,
      maxTimeout: options.maxTimeout || effectiveTimeout
    };
  }

  /**
   * Calculates performance metrics for search
   * @private
   */
  private calculateMetrics(
    startTime: number,
    resultCount: number
  ): { responseTime: number; resultsPerSecond: number } {
    const endTime = performance.now();
    const responseTime = Math.round(endTime - startTime);
    const resultsPerSecond = responseTime > 0 ? Math.round((resultCount * 1000) / responseTime) : 0;
    
    return { responseTime, resultsPerSecond };
  }

  /**
   * Performs progressive search with streaming results
   * Executes all enabled search engines in parallel and streams results as they arrive
   */
  async *searchProgressive(
    query: string,
    options: ProgressiveSearchOptions = {}
  ): AsyncGenerator<SearchResult[], void, unknown> {
    // Load search engine configuration
    this.searchEngineConfig = await settingsService.getSearchConfig();
    
    // Analyze the query
    const analysis = this.queryAnalyzer.analyzeQuery(query);
    
    // Handle empty queries
    if (!analysis.cleanedQuery || !analysis.cleanedQuery.trim()) {
      return; // Empty generator for empty queries
    }
    
    // Create executors for traditional search engines only
    const executors: SearchExecutor[] = [];
    
    // String search executor
    if (this.searchEngineConfig?.traditional.enabled) {
      executors.push(
        ProgressiveSearch.createExecutor(
          'string',
          (analysis, opts) => this.performStringSearchWithDegradation(analysis, opts),
          200 // 200ms timeout for string search
        )
      );
    }
    
    // Fulltext search executor
    if (this.searchEngineConfig?.fulltext?.enabled ?? true) {
      executors.push(
        ProgressiveSearch.createExecutor(
          'fulltext',
          (analysis, opts) => this.performFulltextSearchWithDegradation(analysis, opts),
          300 // 300ms timeout for fulltext search
        )
      );
    }
    
    // If no executors are enabled, return empty
    if (executors.length === 0) {
      return;
    }
    
    // Execute search with streaming
    yield* this.progressiveSearch.executeStreaming(executors, analysis, options);
  }

  /**
   * Performs progressive search with result collection
   * Returns a promise that resolves when all engines complete
   */
  async searchProgressiveCollect(
    query: string,
    options: ProgressiveSearchOptions = {}
  ): Promise<HybridSearchResults> {
    const startTime = performance.now();
    
    // Load search engine configuration
    this.searchEngineConfig = await settingsService.getSearchConfig();
    
    // Analyze the query
    const analysis = this.queryAnalyzer.analyzeQuery(query);
    
    // Handle empty queries
    if (!analysis.cleanedQuery || !analysis.cleanedQuery.trim()) {
      return this.createEmptyResult(query, analysis, startTime);
    }
    
    // Create executors
    const executors = this.createSearchExecutors(analysis, options);
    
    if (executors.length === 0) {
      return this.createEmptyResult(query, analysis, startTime);
    }
    
    // Collect results from all engines
    const allResults: Map<string, SearchResult[]> = new Map();
    const errors: string[] = [];
    const failedComponents: string[] = [];
    
    // Progress callback to collect results
    const progressCallback = (event: ProgressiveSearchEvent) => {
      if (event.type === 'engine-complete' && event.engineType && event.results) {
        allResults.set(event.engineType, event.results);
      } else if (event.type === 'error' && event.engineType) {
        errors.push(`${event.engineType}: ${event.error?.message}`);
        failedComponents.push(event.engineType);
      }
      
      // Call user's progress callback if provided
      if (options.onProgress) {
        options.onProgress(event);
      }
    };
    
    // Execute search with progress tracking
    const progressiveOptions: ProgressiveSearchOptions = {
      ...options,
      onProgress: progressCallback,
      enableStreaming: true
    };
    
    const iterator = await this.progressiveSearch.executeParallel(
      executors,
      analysis,
      progressiveOptions
    );
    
    // Consume all results
    for await (const _ of iterator) {
      // Results are streamed but we collect them all for final processing
    }
    
    // Extract results from search engines
    const stringResults = allResults.get('string') || [];
    const fulltextResults = allResults.get('fulltext') || [];
    
    // Merge search results
    const mergedResults = this.mergeSearchResults(stringResults, fulltextResults);
    
    // Apply filters and limits
    const filteredResults = this.applyFilters(mergedResults, options);
    const finalResults = options.highlightTerms 
      ? this.generateSnippets(filteredResults, analysis.cleanedQuery, true)
      : filteredResults;
    const limitedResults = options.limit 
      ? finalResults.slice(0, options.limit)
      : finalResults;
    
    // Calculate metrics
    const { responseTime } = this.calculateMetrics(startTime, limitedResults.length);
    
    // Determine strategy
    const strategy = options.forceStrategy || analysis.strategy;
    
    return {
      query,
      cleanedQuery: analysis.cleanedQuery,
      strategy,
      language: analysis.language,
      stringResults,
      fulltextResults: fulltextResults,
      mergedResults: limitedResults,
      totalResults: limitedResults.length,
      responseTime,
      confidence: analysis.confidence,
      executionPath: 'progressive_parallel',
      fromCache: false,
      fallbackUsed: failedComponents.length > 0,
      sanitized: analysis.sanitized,
      warnings: errors,
      partialFailure: failedComponents.length > 0,
      failedComponents: failedComponents.length > 0 ? failedComponents : undefined
    };
  }

  /**
   * Create search executors based on configuration
   * @private
   */
  private createSearchExecutors(
    _analysis: QueryAnalysisResult,
    _options: SearchOptions
  ): SearchExecutor[] {
    // Create search functions bound to this instance
    const searchFunctions = {
      string: this.performStringSearchWithDegradation.bind(this),
      fulltext: this.performFulltextSearchWithDegradation.bind(this)
    };
    
    // Use factory to create standard executors
    const config = {
      string: {
        enabled: this.searchEngineConfig?.traditional.enabled ?? true,
        timeout: 200
      },
      fulltext: {
        enabled: this.searchEngineConfig?.fulltext?.enabled ?? true,
        timeout: 300
      }
    };
    
    return ProgressiveSearchFactory.createStandardExecutors(config, searchFunctions);
  }

  /**
   * Cancel ongoing progressive search
   */
  cancelProgressiveSearch(): void {
    this.progressiveSearch.cancel();
  }

  /**
   * Clears the result cache
   */
  clearCache(): void {
    performanceOptimizer.clearCache();
  }

  /**
   * Gets current cache statistics
   */
  getCacheStats(): { size: number; hitRate: number; maxSize: number } {
    return performanceOptimizer.getCacheStats();
  }

  /**
   * Gets current configuration status for testing and debugging
   */
  getConfigStatus(): {
    isConfigLoaded: boolean;
    searchEngineConfig: SearchEngineConfig | null;
    behaviorConfig: {
      maxResults: number;
      timeoutMs: number;
      stringSearchThreshold: number;
    };
    configListenerId: string;
  } {
    return {
      isConfigLoaded: this.unifiedSearchConfig !== null,
      searchEngineConfig: this.searchEngineConfig,
      behaviorConfig: {
        maxResults: this.config.maxResults,
        timeoutMs: this.config.timeoutMs,
        stringSearchThreshold: this.config.stringSearchThreshold
      },
      configListenerId: this.configListenerId
    };
  }

  /**
   * Updates configuration
   */
  updateConfig(newConfig: Partial<HybridSearchConfig>): void {
    Object.assign(this.config, newConfig);
    
    // Clear cache if cache size changed
    if (newConfig.cacheSize) {
      const currentStats = this.getCacheStats();
      if (newConfig.cacheSize < currentStats.size) {
        this.clearCache();
      }
    }
  }

  /**
   * Cleanup method to remove configuration listeners
   * Should be called when the engine is no longer needed
   */
  dispose(): void {
    searchConfigListener.removeListener(this.configListenerId);
    console.log(`[HybridSearchEngine] Disposed config listener: ${this.configListenerId}`);
  }

  /**
   * Creates empty result for empty queries
   * @private
   */
  private createEmptyResult(
    query: string,
    analysis: QueryAnalysisResult,
    startTime: number
  ): HybridSearchResults {
    const { responseTime } = this.calculateMetrics(startTime, 0);
    
    return {
      query,
      cleanedQuery: analysis.cleanedQuery,
      strategy: SearchStrategy.FALLBACK,
      language: analysis.language,
      stringResults: [],
      fulltextResults: [],
      mergedResults: [],
      totalResults: 0,
      responseTime,
      confidence: 0,
      executionPath: 'empty_query',
      fromCache: false,
      fallbackUsed: false,
      sanitized: analysis.sanitized,
      error: 'Empty query provided',
      warnings: []
    };
  }



  /**
   * Performs string search with degradation support
   * @private
   */
  private async performStringSearchWithDegradation(
    analysis: QueryAnalysisResult,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    const startTime = performance.now();
    
    try {
      const result = await this.performStringSearch(analysis, options);
      
      // Record success
      const responseTime = performance.now() - startTime;
      this.degradationManager.getTracker().recordSuccess('string', responseTime);

      return result;
    } catch (error) {
      this.degradationManager.getTracker().recordFailure('string');
      throw error;
    }
  }

  /**
   * Performs fulltext search with degradation support
   * @private
   */
  private async performFulltextSearchWithDegradation(
    analysis: QueryAnalysisResult,
    options: SearchOptions
  ): Promise<SearchResult[]> {
    const startTime = performance.now();
    
    try {
      const result = await this.performFulltextSearch(analysis, options);
      
      // Record success
      const responseTime = performance.now() - startTime;
      this.degradationManager.getTracker().recordSuccess('fulltext', responseTime);

      return result;
    } catch (error) {
      this.degradationManager.getTracker().recordFailure('fulltext');
      throw error;
    }
  }

  /**
   * Merges results from string search and fulltext search
   * @private
   */
  private mergeSearchResults(
    stringResults: SearchResult[],
    fulltextResults: SearchResult[]
  ): SearchResult[] {
    // Use page ID as unique key instead of URL to preserve all history entries
    const mergedMap = new Map<string, SearchResult>();
    
    // Use configured engine weights, fall back to defaults if not available
    let stringWeight = this.searchEngineConfig?.traditional.weight ?? 0.7;
    let fulltextWeight = this.searchEngineConfig?.fulltext.weight ?? 0.3;
    
    // Validate and normalize weights
    const totalWeight = stringWeight + fulltextWeight;
    if (totalWeight === 0) {
      // If both weights are 0, use defaults
      stringWeight = 0.7;
      fulltextWeight = 0.3;
      console.warn('[HybridSearchEngine] Both engine weights are 0, using defaults');
    } else if (Math.abs(totalWeight - 1.0) > 0.01) {
      // If weights don't sum to 1, normalize them
      stringWeight = stringWeight / totalWeight;
      fulltextWeight = fulltextWeight / totalWeight;
      console.warn(`[HybridSearchEngine] Engine weights normalized from ${totalWeight.toFixed(3)} to 1.0`);
    }
    
    // Debug logging for score analysis
    console.log('[HybridSearchEngine] Score Analysis Debug:');
    console.log(`  String results: ${stringResults.length}, Fulltext results: ${fulltextResults.length}`);
    console.log(`  Using weights - String: ${stringWeight.toFixed(3)}, Fulltext: ${fulltextWeight.toFixed(3)}`);
    
    if (stringResults.length > 0) {
      const stringScores = stringResults.map(r => r.stringScore);
      console.log(`  String scores range: ${Math.min(...stringScores).toFixed(4)} - ${Math.max(...stringScores).toFixed(4)}`);
      console.log(`  String scores avg: ${(stringScores.reduce((a, b) => a + b, 0) / stringScores.length).toFixed(4)}`);
    }
    
    if (fulltextResults.length > 0) {
      const fulltextScores = fulltextResults.map(r => r.fulltextScore);
      console.log(`  Fulltext scores range: ${Math.min(...fulltextScores).toFixed(4)} - ${Math.max(...fulltextScores).toFixed(4)}`);
      console.log(`  Fulltext scores avg: ${(fulltextScores.reduce((a, b) => a + b, 0) / fulltextScores.length).toFixed(4)}`);
    }
    
    // Add string results first
    stringResults.forEach((result, index) => {
      const combinedScore = result.stringScore * stringWeight;
      mergedMap.set(result.id, {
        ...result,
        combinedScore,
        relevanceScore: combinedScore // 确保relevanceScore与combinedScore一致
      });
      
      // Debug log first few results
      if (index < 3) {
        console.log(`  String[${index}]: ${result.title.substring(0, 50)}... | stringScore: ${result.stringScore.toFixed(4)} | combinedScore: ${combinedScore.toFixed(4)}`);
      }
    });
    
    // Add fulltext results, combining if ID already exists
    fulltextResults.forEach((result, index) => {
      const existing = mergedMap.get(result.id);
      if (existing) {
        // Combine scores for duplicates (same page ID)
        const newCombinedScore = 
          existing.stringScore * stringWeight +
          result.fulltextScore * fulltextWeight;
        existing.combinedScore = newCombinedScore;
        existing.relevanceScore = newCombinedScore; // 确保relevanceScore与combinedScore一致
        
        // Debug combined score
        if (index < 3) {
          console.log(`  Combined[${index}]: ${result.title.substring(0, 50)}... | stringScore: ${existing.stringScore.toFixed(4)} | fulltextScore: ${result.fulltextScore.toFixed(4)} | finalScore: ${newCombinedScore.toFixed(4)}`);
        }
        
        // Merge highlights
        if (result.highlights && result.highlights.length > 0) {
          existing.highlights = [...new Set([
            ...(existing.highlights || []),
            ...result.highlights
          ])].slice(0, 3); // Limit to 3 highlights
        }
      } else {
        // New result from fulltext only
        const combinedScore = result.fulltextScore * fulltextWeight;
        mergedMap.set(result.id, {
          ...result,
          combinedScore,
          relevanceScore: combinedScore // 确保relevanceScore与combinedScore一致
        });
        
        // Debug log first few results
        if (index < 3) {
          console.log(`  Fulltext[${index}]: ${result.title.substring(0, 50)}... | fulltextScore: ${result.fulltextScore.toFixed(4)} | combinedScore: ${combinedScore.toFixed(4)}`);
        }
      }
    });
    
    // Convert to array and sort by combined score
    const mergedResults = Array.from(mergedMap.values());
    
    // Debug before sorting
    console.log(`  Before sorting - merged results: ${mergedResults.length}`);
    if (mergedResults.length > 0) {
      const scores = mergedResults.map(r => r.combinedScore);
      console.log(`  Combined scores range: ${Math.min(...scores).toFixed(4)} - ${Math.max(...scores).toFixed(4)}`);
      console.log(`  Combined scores avg: ${(scores.reduce((a, b) => a + b, 0) / scores.length).toFixed(4)}`);
    }
    
    mergedResults.sort((a, b) => {
      // Primary sort by combined score (降序：高分排前面)
      const scoreDiff = b.combinedScore - a.combinedScore;
      
      // 优化阈值：当分数差异大于0.01时认为是不同的相关性级别
      if (Math.abs(scoreDiff) > 0.01) {
        return scoreDiff;
      }
      
      // Secondary sort by visit time (降序：新访问排前面)
      return b.lastVisitTime - a.lastVisitTime;
    });
    
    // Debug after sorting - show top results
    console.log(`  After sorting - Top 5 results:`);
    mergedResults.slice(0, 5).forEach((result, index) => {
      const timeStr = new Date(result.lastVisitTime).toISOString().substring(0, 19);
      console.log(`    [${index}] Score: ${result.combinedScore.toFixed(4)} | Time: ${timeStr} | Title: ${result.title.substring(0, 60)}...`);
    });
    
    return mergedResults;
  }

  /**
   * Handles search errors with degradation support
   * @private
   */
  private handleSearchErrorWithDegradation(
    error: Error,
    analysis: QueryAnalysisResult,
    _options: SearchOptions
  ): HybridSearchResults {
    // Record the error in degradation tracker
    this.degradationManager.getTracker().recordFailure('system');

    // Create degradation info for error scenario
    const degradationInfo = this.degradationManager.createDegradationInfo(
      true,
      'system_error',
      SearchStrategy.FALLBACK
    );

    // Create performance info
    const performanceInfo = this.degradationManager.createPerformanceInfo('cpu');

    // Create user message for error
    const userMessage: UserMessage = {
      type: 'error',
      message: 'Search is temporarily unavailable',
      suggestion: 'Please try again in a moment'
    };

    // Create technical info
    const technicalInfo = this.degradationManager.createTechnicalInfo(
      'system_error',
      [],
      ['search_engine']
    );

    const startTime = performance.now();
    const { responseTime } = this.calculateMetrics(startTime, 0);
    
    return {
      query: analysis.query || '',
      cleanedQuery: analysis.cleanedQuery || '',
      strategy: SearchStrategy.FALLBACK,
      language: analysis.language || 'unknown',
      stringResults: [],
      fulltextResults: [],
      mergedResults: [],
      totalResults: 0,
      responseTime,
      confidence: 0,
      executionPath: 'error_with_degradation',
      fromCache: false,
      fallbackUsed: true,
      sanitized: analysis.sanitized || false,
      error: error.message,
      warnings: [`Search failed: ${error.message}`],
      degradationInfo,
      performanceInfo,
      userMessage,
      technicalInfo,
      partialFailure: true,
      failedComponents: ['search_engine']
    };
  }

}