/**
 * QueryAnalyzer - Search Engine Query Analysis Module
 * 
 * Analyzes user queries to determine optimal search strategy and weights
 * for traditional search engines (string + fulltext matching).
 * 
 * @module QueryAnalyzer
 * @version 4.1
 * @since 2025-06-23
 */

import { QueryProcessor } from '../QueryProcessor';

/**
 * Types of queries identified by the analyzer
 */
export const QueryType = {
  EXACT_MATCH: 'exact_match',
  FILE_SEARCH: 'file_search', 
  ERROR_CODE: 'error_code',
  URL_FRAGMENT: 'url_fragment',
  CONCEPTUAL: 'conceptual',
  QUESTION: 'question',
  COMPARATIVE: 'comparative',
  HYBRID: 'hybrid',
  SINGLE_CHAR: 'single_char',
  SHORT: 'short',
  LONG: 'long',
  EMPTY: 'empty'
} as const;

export type QueryType = typeof QueryType[keyof typeof QueryType];

/**
 * Search strategies for traditional search engines
 */
export const SearchStrategy = {
  STRING_ONLY: 'string_only',      // 100% string matching
  FULLTEXT_ONLY: 'fulltext_only',  // 100% fulltext search
  HYBRID_BALANCED: 'balanced',     // 50% string, 50% fulltext
  FALLBACK: 'fallback'             // Default fallback strategy
} as const;

export type SearchStrategy = typeof SearchStrategy[keyof typeof SearchStrategy];

/**
 * Search weights for combining traditional search results
 */
export interface SearchWeights {
  string: number;
  fulltext: number;
}

/**
 * Confidence scoring factors
 */
export interface ConfidenceFactors {
  technicalTerms: number;
  intentKeywords: number;
  queryLength: number;
  specialPatterns: number;
}

/**
 * Language distribution for mixed-language queries
 */
export interface LanguageDistribution {
  en: number;
  zh: number;
  ja: number;
  other: number;
}

/**
 * Query analysis result
 */
export interface QueryAnalysisResult {
  // Core classification
  query: string;
  cleanedQuery: string;
  type: QueryType;
  strategy: SearchStrategy;
  weights: SearchWeights;
  confidence: number;
  
  // Language detection
  language: 'en' | 'zh' | 'ja' | 'mixed' | 'unknown';
  languageDistribution?: LanguageDistribution;
  
  // Query characteristics
  technicalTerms: string[];
  contextWords: string[];
  conceptWords: string[];
  questionWords: string[];
  comparisonTerms: string[];
  
  // Special patterns
  errorCodeDetected: boolean;
  urlPattern: boolean;
  hasEmojis: boolean;
  hasSpecialChars: boolean;
  sanitized: boolean;
  
  // Metadata
  originalLength: number;
  truncated: boolean;
  confidenceFactors: ConfidenceFactors;
  
  // Advanced search filters
  filters?: {
    site?: string;
    timeRange?: {
      start: number;
      end: number;
    };
    fileType?: string;
    exclude?: string[];
    domain?: string[];
    exactPhrases?: string[];
  };
}

/**
 * QueryAnalyzer implementation
 * This is a placeholder implementation for TDD Red phase.
 * The actual implementation will be created in the Green phase.
 */
export class QueryAnalyzer {
  private readonly maxQueryLength = 1000;
  private readonly technicalTermsDict: Set<string>;
  private readonly conceptualKeywords: Set<string>;
  private readonly questionWords: Set<string>;
  private readonly comparisonWords: Set<string>;
  private readonly queryProcessor: QueryProcessor;

  constructor() {
    this.queryProcessor = new QueryProcessor();
    // Initialize dictionaries - these will be properly implemented in Green phase
    this.technicalTermsDict = new Set([
      'react', 'javascript', 'typescript', 'node', 'express',
      'api', 'css', 'html', 'vue', 'angular', 'performance',
      'optimization', 'database', 'sql', 'mongodb', 'aws',
      'docker', 'kubernetes', 'microservices', 'authentication'
    ]);

    this.conceptualKeywords = new Set([
      'how', 'what', 'why', 'when', 'where', 'best', 'practices',
      'patterns', 'guide', 'tutorial', 'learn', 'explain',
      'understand', 'implement', 'optimize', 'design'
    ]);

    this.questionWords = new Set([
      'how', 'what', 'why', 'when', 'where', 'which', 'who'
    ]);

    this.comparisonWords = new Set([
      'vs', 'versus', 'compared', 'comparison', 'difference',
      'between', 'against', 'or'
    ]);
  }

  /**
   * Analyzes a query and returns strategy recommendation
   * GREEN PHASE: Full implementation
   */
  analyzeQuery(query: string): QueryAnalysisResult {
    // Process query with QueryProcessor to extract advanced filters
    const processedQuery = this.queryProcessor.process(query);
    
    // Sanitize input
    const { cleaned, sanitized } = this.sanitizeQuery(query);
    const originalLength = query.length;
    const truncated = cleaned.length !== originalLength;

    // Handle empty queries
    if (!cleaned.trim()) {
      return this.createEmptyQueryResult(query, cleaned, sanitized, originalLength);
    }

    // Detect language
    const { language, distribution } = this.detectLanguage(cleaned);

    // Extract query characteristics
    const characteristics = this.analyzeCharacteristics(cleaned);

    // Classify query type
    const type = this.classifyQueryType(cleaned, characteristics);

    // Select search strategy
    const strategy = this.selectSearchStrategy(type, characteristics);

    // Calculate weights
    const weights = this.calculateWeights(strategy);

    // Calculate confidence
    const { confidence, factors } = this.calculateConfidence(characteristics, type);

    return {
      query: query,
      cleanedQuery: processedQuery.cleanText || cleaned,
      type,
      strategy,
      weights,
      confidence,
      language: language as any,
      languageDistribution: distribution,
      technicalTerms: characteristics.technicalTerms,
      contextWords: characteristics.contextWords,
      conceptWords: characteristics.conceptWords,
      questionWords: characteristics.questionWords,
      comparisonTerms: characteristics.comparisonTerms,
      errorCodeDetected: characteristics.errorCodeDetected,
      urlPattern: characteristics.urlPattern,
      hasEmojis: characteristics.hasEmojis,
      hasSpecialChars: characteristics.hasSpecialChars,
      sanitized,
      originalLength,
      truncated,
      confidenceFactors: factors,
      filters: processedQuery.filters
    };
  }

  /**
   * Determines query type based on patterns and content
   * @private
   */
  private classifyQueryType(query: string, characteristics: any): QueryType {
    // Check for exact match (quoted strings)
    if (query.startsWith('"') && query.endsWith('"')) {
      return QueryType.EXACT_MATCH;
    }

    // Check for file search
    if (query.includes('filename:') || query.includes('file:')) {
      return QueryType.FILE_SEARCH;
    }

    // Check for single character
    if (query.trim().length === 1) {
      return QueryType.SINGLE_CHAR;
    }

    // Check for very short queries
    if (query.trim().length <= 3) {
      return QueryType.SHORT;
    }

    // Check for very long queries
    if (query.split(' ').length > 10) {
      return QueryType.LONG;
    }

    // Check for error codes
    if (characteristics.errorCodeDetected) {
      return QueryType.ERROR_CODE;
    }

    // Check for URL fragments
    if (characteristics.urlPattern) {
      return QueryType.URL_FRAGMENT;
    }

    // Check for actual questions (have question marks or clear question structure)
    if (this.isActualQuestion(query, characteristics)) {
      return QueryType.QUESTION;
    }

    // Check for conceptual queries (including question words used in non-question context)
    if (characteristics.conceptWords.length > 0 || characteristics.questionWords.length > 0) {
      return QueryType.CONCEPTUAL;
    }

    // Check for comparisons
    if (characteristics.comparisonTerms.length > 0) {
      return QueryType.COMPARATIVE;
    }

    // Default to hybrid for technical queries with context
    if (characteristics.technicalTerms.length > 0 && characteristics.contextWords.length > 0) {
      return QueryType.HYBRID;
    }

    // Fallback to conceptual for remaining cases
    return QueryType.CONCEPTUAL;
  }

  /**
   * Selects optimal search strategy based on query type
   * @private
   */
  private selectSearchStrategy(type: QueryType, _characteristics: any): SearchStrategy {
    switch (type) {
      case QueryType.EXACT_MATCH:
      case QueryType.ERROR_CODE:
      case QueryType.URL_FRAGMENT:
      case QueryType.FILE_SEARCH:
      case QueryType.SINGLE_CHAR:
      case QueryType.SHORT:
        return SearchStrategy.STRING_ONLY;
      
      case QueryType.CONCEPTUAL:
      case QueryType.QUESTION:
      case QueryType.COMPARATIVE:
      case QueryType.LONG:
        return SearchStrategy.FULLTEXT_ONLY;
      
      case QueryType.HYBRID:
        return SearchStrategy.HYBRID_BALANCED;
      
      case QueryType.EMPTY:
        return SearchStrategy.FALLBACK;
      
      default:
        return SearchStrategy.HYBRID_BALANCED;
    }
  }

  /**
   * Calculates search weights based on strategy
   * @private
   */
  private calculateWeights(strategy: SearchStrategy): SearchWeights {
    switch (strategy) {
      case SearchStrategy.STRING_ONLY:
        return { string: 1.0, fulltext: 0.0 };
      
      case SearchStrategy.FULLTEXT_ONLY:
        return { string: 0.0, fulltext: 1.0 };
      
      case SearchStrategy.HYBRID_BALANCED:
        return { string: 0.5, fulltext: 0.5 };
      
      case SearchStrategy.FALLBACK:
        return { string: 1.0, fulltext: 0.0 };
      
      default:
        return { string: 0.5, fulltext: 0.5 };
    }
  }

  /**
   * Detects language of the query
   * @private
   */
  private detectLanguage(query: string): { language: string; distribution?: LanguageDistribution } {
    const chars = query.split('');
    const distribution: LanguageDistribution = { en: 0, zh: 0, ja: 0, other: 0 };
    
    chars.forEach(char => {
      const code = char.charCodeAt(0);
      
      // Chinese characters (CJK Unified Ideographs)
      if ((code >= 0x4E00 && code <= 0x9FFF) || 
          (code >= 0x3400 && code <= 0x4DBF)) {
        distribution.zh++;
      }
      // Japanese Hiragana and Katakana
      else if ((code >= 0x3040 && code <= 0x309F) || 
               (code >= 0x30A0 && code <= 0x30FF)) {
        distribution.ja++;
      }
      // English/Latin characters
      else if ((code >= 0x0041 && code <= 0x005A) || 
               (code >= 0x0061 && code <= 0x007A)) {
        distribution.en++;
      }
      // Other characters
      else if (char.trim()) {
        distribution.other++;
      }
    });

    const total = distribution.en + distribution.zh + distribution.ja + distribution.other;
    if (total === 0) {
      return { language: 'unknown' };
    }

    // Normalize to percentages
    Object.keys(distribution).forEach(key => {
      distribution[key as keyof LanguageDistribution] /= total;
    });

    // Determine primary language
    let primaryLanguage = 'unknown';
    let maxScore = 0;
    let languageCount = 0;

    if (distribution.zh > 0.1) { languageCount++; }
    if (distribution.ja > 0.1) { languageCount++; }
    if (distribution.en > 0.1) { languageCount++; }

    if (languageCount > 1) {
      primaryLanguage = 'mixed';
    } else {
      if (distribution.zh > maxScore) { maxScore = distribution.zh; primaryLanguage = 'zh'; }
      if (distribution.ja > maxScore) { maxScore = distribution.ja; primaryLanguage = 'ja'; }
      if (distribution.en > maxScore) { maxScore = distribution.en; primaryLanguage = 'en'; }
    }

    return { 
      language: primaryLanguage,
      distribution: languageCount > 1 ? distribution : undefined
    };
  }


  /**
   * Calculates confidence score for the analysis
   * @private
   */
  private calculateConfidence(characteristics: any, type: QueryType): { confidence: number; factors: ConfidenceFactors } {
    const factors: ConfidenceFactors = {
      technicalTerms: Math.min(characteristics.technicalTerms.length * 0.2, 0.4),
      intentKeywords: Math.min((characteristics.conceptWords.length + characteristics.questionWords.length) * 0.15, 0.3),
      queryLength: this.calculateLengthScore(characteristics.query),
      specialPatterns: this.calculatePatternScore(characteristics, type)
    };

    // Base confidence from type certainty
    let baseConfidence = 0.5;
    switch (type) {
      case QueryType.EXACT_MATCH:
      case QueryType.FILE_SEARCH:
        baseConfidence = 0.95;
        break;
      case QueryType.ERROR_CODE:
      case QueryType.URL_FRAGMENT:
        baseConfidence = 0.8;
        break;
      case QueryType.QUESTION:
      case QueryType.COMPARATIVE:
        baseConfidence = 0.85;
        break;
      case QueryType.SINGLE_CHAR:
      case QueryType.EMPTY:
        baseConfidence = 0.1;
        break;
      case QueryType.SHORT:
        baseConfidence = 0.4;
        break;
      default:
        baseConfidence = 0.6;
    }

    const totalFactorScore = Object.values(factors).reduce((sum, score) => sum + score, 0);
    const confidence = Math.min(baseConfidence + totalFactorScore, 1.0);

    return { confidence, factors };
  }

  /**
   * Sanitizes query input to prevent injection attacks
   * @private
   */
  private sanitizeQuery(query: string): { cleaned: string; sanitized: boolean } {
    if (!query || typeof query !== 'string') {
      return { cleaned: '', sanitized: true };
    }

    let cleaned = query;
    let sanitized = false;

    // Remove potentially dangerous patterns
    const dangerousPatterns = [
      /<script[\s\S]*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /DROP\s+TABLE/gi,
      /DELETE\s+FROM/gi,
      /INSERT\s+INTO/gi,
      /UPDATE\s+SET/gi,
      /\${.*?}/gi, // Template injection
      /\.\.\/+/g,   // Path traversal
    ];

    dangerousPatterns.forEach(pattern => {
      if (pattern.test(cleaned)) {
        cleaned = cleaned.replace(pattern, '');
        sanitized = true;
      }
    });

    // Truncate if too long
    if (cleaned.length > this.maxQueryLength) {
      cleaned = cleaned.substring(0, this.maxQueryLength);
      sanitized = true;
    }

    // Remove emojis and special Unicode characters for cleaner processing
    const originalCleaned = cleaned;
    cleaned = cleaned.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[⚛️🎨🚀📡🌟💫]/gu, '');
    
    if (cleaned !== originalCleaned) {
      sanitized = true;
    }

    // Basic HTML entity decoding for common cases
    cleaned = cleaned
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'");

    // Clean up extra whitespace
    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    return { cleaned, sanitized };
  }

  /**
   * Creates empty query result
   * @private
   */
  private createEmptyQueryResult(
    query: string,
    cleaned: string,
    sanitized: boolean,
    originalLength: number
  ): QueryAnalysisResult {
    return {
      query,
      cleanedQuery: cleaned,
      type: QueryType.EMPTY,
      strategy: SearchStrategy.FALLBACK,
      weights: { string: 1, fulltext: 0 },
      confidence: 0,
      language: 'unknown',
      technicalTerms: [],
      contextWords: [],
      conceptWords: [],
      questionWords: [],
      comparisonTerms: [],
      errorCodeDetected: false,
      urlPattern: false,
      hasEmojis: false,
      hasSpecialChars: false,
      sanitized,
      originalLength,
      truncated: false,
      confidenceFactors: {
        technicalTerms: 0,
        intentKeywords: 0,
        queryLength: 0,
        specialPatterns: 0
      },
      filters: undefined
    };
  }

  /**
   * Analyzes query characteristics
   * @private
   */
  private analyzeCharacteristics(query: string) {
    const words = query.toLowerCase().split(/\s+/);
    
    // Extract different types of words
    const technicalTerms = words.filter(word => this.technicalTermsDict.has(word));
    const conceptWords = words.filter(word => this.conceptualKeywords.has(word));
    const questionWords = words.filter(word => this.questionWords.has(word));
    const comparisonTerms = words.filter(word => this.comparisonWords.has(word));
    
    // Context words (non-technical, non-functional words)
    const contextWords = words.filter(word => 
      !this.technicalTermsDict.has(word) &&
      !this.conceptualKeywords.has(word) &&
      !this.questionWords.has(word) &&
      !this.comparisonWords.has(word) &&
      word.length > 2
    );

    // Detect patterns
    const errorCodeDetected = this.detectErrorCodes(query);
    const urlPattern = this.detectUrlPattern(query);
    const hasEmojis = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u.test(query);
    const hasSpecialChars = /[^\w\s\-.,!?'"()]/u.test(query);

    return {
      query,
      words,
      technicalTerms,
      contextWords,
      conceptWords,
      questionWords,
      comparisonTerms,
      errorCodeDetected,
      urlPattern,
      hasEmojis,
      hasSpecialChars
    };
  }

  /**
   * Determines if a query is an actual question vs conceptual query with question words
   * @private
   */
  private isActualQuestion(query: string, _characteristics: any): boolean {
    // Has question mark
    if (query.includes('?')) {
      return true;
    }

    // Starts with question words and has question structure
    const trimmed = query.trim().toLowerCase();
    const questionStarters = ['how does', 'how do', 'how can', 'how to', 'what is', 'what are', 'what does', 'what do', 'why does', 'why do', 'why is', 'why are', 'when does', 'when do', 'when is', 'when are', 'where does', 'where do', 'where is', 'where are', 'which is', 'which are', 'who is', 'who are'];
    
    // Only consider it a question if it starts with question words AND has question structure
    const startsWithQuestion = questionStarters.some(starter => trimmed.startsWith(starter));
    
    // Question patterns that indicate actual questions
    const questionPatterns = [
      /^(how|what|why|when|where|which|who)\s+(does|do|can|is|are|was|were|will|would|should|could)\s+/i,
      /^(how|what|why|when|where|which|who)\s+to\s+/i,
      /\b(benefits?|advantages?|disadvantages?|differences?)\s+of\b/i
    ];
    
    const hasQuestionPattern = questionPatterns.some(pattern => pattern.test(query));
    
    return startsWithQuestion || hasQuestionPattern;
  }

  /**
   * Detects error codes in query
   * @private
   */
  private detectErrorCodes(query: string): boolean {
    const errorPatterns = [
      /\b\d{3}\s+error\b/i,        // 404 error
      /\bERR_[A-Z_]+\b/,           // ERR_CONNECTION_REFUSED
      /\b[A-Z][a-z]*Error:/,       // TypeError:, SyntaxError:
      /\b(fatal|error|exception)\b/i
    ];
    
    return errorPatterns.some(pattern => pattern.test(query));
  }

  /**
   * Detects URL patterns in query
   * @private
   */
  private detectUrlPattern(query: string): boolean {
    const urlPatterns = [
      /\b[a-z]+\.[a-z]{2,}\b/i,          // domain.com
      /\bhttps?:\/\/\S+/i,               // http://example.com
      /\b\w+\.[a-z]{2,}\/\S*/i,          // example.com/path
      /\b(www\.|api\.|docs\.)/i          // www., api., docs.
    ];
    
    return urlPatterns.some(pattern => pattern.test(query));
  }

  /**
   * Calculates length-based confidence score
   * @private
   */
  private calculateLengthScore(query: string): number {
    const wordCount = query.split(/\s+/).length;
    
    if (wordCount === 1) return 0.1;
    if (wordCount === 2) return 0.15;
    if (wordCount >= 3 && wordCount <= 6) return 0.2;
    if (wordCount >= 7 && wordCount <= 10) return 0.15;
    return 0.1; // Very long queries
  }

  /**
   * Calculates pattern-based confidence score
   * @private
   */
  private calculatePatternScore(characteristics: any, _type: QueryType): number {
    let score = 0;
    
    if (characteristics.errorCodeDetected) score += 0.2;
    if (characteristics.urlPattern) score += 0.2;
    if (characteristics.hasEmojis) score += 0.1;
    
    return Math.min(score, 0.3);
  }
}