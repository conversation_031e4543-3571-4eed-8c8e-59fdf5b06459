/**
 * SearchCacheManager - Focused caching module for hybrid search
 * 
 * Handles intelligent caching of search results with LRU eviction,
 * cache invalidation, and performance monitoring.
 * 
 * @module SearchCacheManager
 * @version 4.0
 * @since 2025-06-23
 */

/**
 * Cache entry with metadata for LRU management
 */
interface CacheEntry<T> {
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  keyHash: string;
}

/**
 * Cache statistics for monitoring and debugging
 */
export interface CacheStatistics {
  size: number;
  maxSize: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  evictionCount: number;
  averageAccessCount: number;
  oldestEntryAge: number;
}

/**
 * Configuration for cache behavior
 */
export interface CacheConfig {
  maxSize: number;
  expiryMs: number;
  cleanupIntervalMs: number;
  enableAutoCleanup: boolean;
}

/**
 * Generic cache manager with LRU eviction and intelligent cleanup
 */
export class SearchCacheManager<T> {
  private readonly cache = new Map<string, CacheEntry<T>>();
  private readonly config: CacheConfig;
  private hitCount = 0;
  private missCount = 0;
  private evictionCount = 0;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 1000,
      expiryMs: 5 * 60 * 1000, // 5 minutes
      cleanupIntervalMs: 60 * 1000, // 1 minute
      enableAutoCleanup: true,
      ...config
    };

    if (this.config.enableAutoCleanup) {
      this.startAutoCleanup();
    }
  }

  /**
   * Retrieves value from cache if available and not expired
   */
  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.missCount++;
      return null;
    }

    const now = Date.now();
    
    // Check if entry has expired
    if (now - entry.timestamp > this.config.expiryMs) {
      this.cache.delete(key);
      this.missCount++;
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = now;
    this.hitCount++;
    
    return entry.value;
  }

  /**
   * Stores value in cache with LRU eviction if needed
   */
  set(key: string, value: T): void {
    // Remove existing entry to update it
    if (this.cache.has(key)) {
      this.cache.delete(key);
    }

    // Evict if cache is full
    if (this.cache.size >= this.config.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    const now = Date.now();
    const entry: CacheEntry<T> = {
      value,
      timestamp: now,
      accessCount: 0,
      lastAccessed: now,
      keyHash: this.hashKey(key)
    };

    this.cache.set(key, entry);
  }

  /**
   * Checks if key exists in cache and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    const now = Date.now();
    if (now - entry.timestamp > this.config.expiryMs) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Removes specific key from cache
   */
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * Clears all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.hitCount = 0;
    this.missCount = 0;
    this.evictionCount = 0;
  }

  /**
   * Gets current cache size
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * Forces cleanup of expired entries
   */
  cleanup(): number {
    const now = Date.now();
    let removedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.config.expiryMs) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    return removedCount;
  }

  /**
   * Gets comprehensive cache statistics
   */
  getStatistics(): CacheStatistics {
    const totalRequests = this.hitCount + this.missCount;
    const hitRate = totalRequests > 0 ? this.hitCount / totalRequests : 0;

    // Calculate average access count and oldest entry age
    let totalAccessCount = 0;
    let oldestTimestamp = Date.now();

    for (const entry of this.cache.values()) {
      totalAccessCount += entry.accessCount;
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp;
      }
    }

    const averageAccessCount = this.cache.size > 0 ? totalAccessCount / this.cache.size : 0;
    const oldestEntryAge = Date.now() - oldestTimestamp;

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate,
      evictionCount: this.evictionCount,
      averageAccessCount,
      oldestEntryAge
    };
  }

  /**
   * Invalidates cache entries matching a pattern
   */
  invalidatePattern(pattern: RegExp): number {
    let removedCount = 0;

    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key);
        removedCount++;
      }
    }

    return removedCount;
  }

  /**
   * Gets all keys currently in cache
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Stops auto cleanup if enabled
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
  }

  // ======================
  // Private Helper Methods
  // ======================

  /**
   * Evicts the least recently used entry
   * @private
   */
  private evictLeastRecentlyUsed(): void {
    if (this.cache.size === 0) {
      return;
    }

    let lruKey: string | null = null;
    let lruTime = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < lruTime) {
        lruTime = entry.lastAccessed;
        lruKey = key;
      }
    }

    if (lruKey) {
      this.cache.delete(lruKey);
      this.evictionCount++;
    }
  }

  /**
   * Starts automatic cleanup process
   * @private
   */
  private startAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupIntervalMs);
  }

  /**
   * Creates a simple hash of the key for metadata
   * @private
   */
  private hashKey(key: string): string {
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }
}

/**
 * Specialized cache manager for hybrid search results
 */
export class HybridSearchCacheManager {
  private readonly queryCache: SearchCacheManager<any>;
  private readonly resultCache: SearchCacheManager<any>;
  private readonly analysisCache: SearchCacheManager<any>;

  constructor(config: Partial<CacheConfig> = {}) {
    this.queryCache = new SearchCacheManager({
      maxSize: 500,
      expiryMs: 10 * 60 * 1000, // 10 minutes for query analysis
      ...config
    });

    this.resultCache = new SearchCacheManager({
      maxSize: 1000,
      expiryMs: 5 * 60 * 1000, // 5 minutes for search results
      ...config
    });

    this.analysisCache = new SearchCacheManager({
      maxSize: 200,
      expiryMs: 30 * 60 * 1000, // 30 minutes for query analysis
      ...config
    });
  }

  /**
   * Cache for query analysis results
   */
  getQueryAnalysis(query: string): any | null {
    return this.analysisCache.get(this.normalizeQuery(query));
  }

  setQueryAnalysis(query: string, analysis: any): void {
    this.analysisCache.set(this.normalizeQuery(query), analysis);
  }

  /**
   * Cache for search results
   */
  getSearchResults(cacheKey: string): any | null {
    return this.resultCache.get(cacheKey);
  }

  setSearchResults(cacheKey: string, results: any): void {
    this.resultCache.set(cacheKey, results);
  }

  /**
   * Cache for processed queries
   */
  getProcessedQuery(query: string): any | null {
    return this.queryCache.get(this.normalizeQuery(query));
  }

  setProcessedQuery(query: string, processed: any): void {
    this.queryCache.set(this.normalizeQuery(query), processed);
  }

  /**
   * Invalidates all caches
   */
  invalidateAll(): void {
    this.queryCache.clear();
    this.resultCache.clear();
    this.analysisCache.clear();
  }

  /**
   * Gets combined statistics from all caches
   */
  getCombinedStatistics(): {
    query: CacheStatistics;
    results: CacheStatistics;
    analysis: CacheStatistics;
    total: {
      size: number;
      hitRate: number;
      totalRequests: number;
    };
  } {
    const queryStats = this.queryCache.getStatistics();
    const resultStats = this.resultCache.getStatistics();
    const analysisStats = this.analysisCache.getStatistics();

    const totalSize = queryStats.size + resultStats.size + analysisStats.size;
    const totalHits = queryStats.hitCount + resultStats.hitCount + analysisStats.hitCount;
    const totalRequests = (queryStats.hitCount + queryStats.missCount) +
                         (resultStats.hitCount + resultStats.missCount) +
                         (analysisStats.hitCount + analysisStats.missCount);
    const totalHitRate = totalRequests > 0 ? totalHits / totalRequests : 0;

    return {
      query: queryStats,
      results: resultStats,
      analysis: analysisStats,
      total: {
        size: totalSize,
        hitRate: totalHitRate,
        totalRequests
      }
    };
  }

  /**
   * Destroys all cache managers
   */
  destroy(): void {
    this.queryCache.destroy();
    this.resultCache.destroy();
    this.analysisCache.destroy();
  }

  /**
   * Normalizes query for consistent caching
   * @private
   */
  private normalizeQuery(query: string): string {
    return query.toLowerCase().trim().replace(/\s+/g, ' ');
  }
}