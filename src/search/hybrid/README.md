# Hybrid Search Engine - Modular Architecture

A high-performance, modular hybrid search engine that combines semantic search with traditional string matching for optimal search results.

## Architecture Overview

The hybrid search engine has been refactored into focused, modular components:

- **QueryAnalyzer**: Analyzes queries and determines optimal search strategies
- **SearchCoordinator**: Orchestrates search execution and coordinates between components
- **ResultMerger**: Merges and ranks results from multiple search engines (enhanced with caching)
- **SearchResultProcessor**: Handles post-processing, filtering, and snippet generation
- **SearchCacheManager**: Provides intelligent caching with LRU eviction
- **HybridSearchEngine**: Main engine (legacy compatibility)

## Quick Start

### Basic Usage

```typescript
import { hybridSearch } from './src/search/hybrid';

// Simple search
const results = await hybridSearch('React performance optimization');
console.log(`Found ${results.results.length} results`);

// Search with options
const filteredResults = await hybridSearch('JavaScript patterns', {
  limit: 20,
  domains: ['developer.mozilla.org', 'github.com'],
  timeRange: {
    start: Date.now() - 7 * 24 * 60 * 60 * 1000, // Last 7 days
    end: Date.now()
  },
  minScore: 0.3
});
```

### Advanced Usage with Custom Configuration

```typescript
import { createHybridSearchSystem } from './src/search/hybrid';

// Create customized search system
const searchSystem = createHybridSearchSystem({
  caching: {
    maxSize: 2000,
    expiryMs: 10 * 60 * 1000, // 10 minutes
    enableAutoCleanup: true
  },
  processing: {
    maxSnippetLength: 300,
    highlightTags: { start: '<strong>', end: '</strong>' }
  },
  strategy: {
    timeoutMs: 8000,
    fallbackToString: true
  }
});

// Perform search
const result = await searchSystem.search('machine learning algorithms', {
  limit: 50,
  includeContent: true,
  sortBy: 'relevance'
});

// Access detailed statistics
const stats = searchSystem.getStatistics();
console.log('Cache hit rate:', stats.cache.total.hitRate);
console.log('Search time:', stats.lastExecution?.totalTime);

// Clean up when done
searchSystem.destroy();
```

## Individual Components

### QueryAnalyzer

Analyzes queries to determine the optimal search strategy:

```typescript
import { QueryAnalyzer, QueryType, SearchStrategy } from './src/search/hybrid';

const analyzer = new QueryAnalyzer();

// Analyze a query
const analysis = analyzer.analyzeQuery('how to optimize React performance');

console.log('Query type:', analysis.type); // QueryType.CONCEPTUAL
console.log('Strategy:', analysis.strategy); // SearchStrategy.SEMANTIC_FIRST
console.log('Weights:', analysis.weights); // { semantic: 0.8, string: 0.2 }
console.log('Confidence:', analysis.confidence); // 0.85
console.log('Language:', analysis.language); // 'en'
console.log('Technical terms:', analysis.technicalTerms); // ['react', 'performance', 'optimize']
```

#### Query Types and Strategies

| Query Type | Description | Example | Strategy |
|------------|-------------|---------|----------|
| `EXACT_MATCH` | Quoted strings | `"React hooks tutorial"` | `STRING_FIRST` |
| `FILE_SEARCH` | File searches | `filename:config.json` | `STRING_ONLY` |
| `ERROR_CODE` | Error codes | `404 error fix` | `STRING_FIRST` |
| `CONCEPTUAL` | Concept queries | `machine learning best practices` | `SEMANTIC_FIRST` |
| `QUESTION` | Questions | `How does React work?` | `SEMANTIC_FIRST` |
| `COMPARATIVE` | Comparisons | `React vs Vue` | `SEMANTIC_FIRST` |
| `HYBRID` | Technical with context | `React performance in large apps` | `HYBRID_BALANCED` |

### SearchCoordinator

Orchestrates the entire search process:

```typescript
import { SearchCoordinator } from './src/search/hybrid';

const coordinator = new SearchCoordinator();

// Execute comprehensive search
const searchResult = await coordinator.executeSearch(
  'TypeScript best practices',
  {
    limit: 30,
    domains: ['typescript-lang.org'],
    minScore: 0.4
  },
  {
    semanticEnabled: true,
    stringEnabled: true,
    mergeStrategy: MergeStrategy.WEIGHTED_AVERAGE,
    timeoutMs: 6000
  }
);

console.log('Results:', searchResult.results.length);
console.log('Analysis:', searchResult.analysis);
console.log('Stats:', searchResult.stats);

// Access execution statistics
const stats = coordinator.getLastExecutionStats();
console.log('Total time:', stats?.totalTime);
console.log('Semantic time:', stats?.semanticSearchTime);
console.log('String time:', stats?.stringSearchTime);
console.log('Strategy used:', stats?.strategy);
```

### ResultMerger (Enhanced with Caching)

Merges and ranks results with advanced caching:

```typescript
import { ResultMerger, MergeStrategy, RankingAlgorithm } from './src/search/hybrid';

const merger = new ResultMerger();

// Merge results with caching
const mergedResults = merger.mergeResults(
  semanticResults,
  stringResults,
  { semantic: 0.7, string: 0.3 },
  {
    strategy: MergeStrategy.HARMONIC_MEAN,
    rankingAlgorithm: RankingAlgorithm.TIME_DECAY,
    maxResults: 50,
    diversityFilter: true,
    timeDecayFactor: 0.1
  }
);

// Get merge statistics
const stats = merger.getLastMergeStatistics();
console.log('Processing time:', stats?.processingTimeMs);
console.log('Duplicates removed:', stats?.duplicatesRemoved);

// Get cache statistics  
const cacheStats = merger.getCacheStatistics();
console.log('Cache size:', cacheStats.resultCacheSize);
console.log('Cache hit rate:', cacheStats.cacheHitRate);

// Clear caches when needed
merger.clearCaches();
```

#### Merge Strategies

- **WEIGHTED_AVERAGE**: Combines scores using weighted average
- **MAX_SCORE**: Takes the maximum score from either search
- **HARMONIC_MEAN**: Uses harmonic mean for balanced results
- **GEOMETRIC_MEAN**: Uses geometric mean for conservative scoring
- **RANK_FUSION**: Combines based on result rankings

#### Ranking Algorithms

- **SCORE_BASED**: Pure score-based ranking
- **TIME_DECAY**: Applies time decay to favor recent results
- **POPULARITY_BOOST**: Boosts results with higher visit counts
- **DOMAIN_AUTHORITY**: Considers domain authority in ranking
- **FRESHNESS_BOOST**: Boosts recently visited pages
- **HYBRID_RANKING**: Combines multiple ranking factors

### SearchResultProcessor

Handles result post-processing:

```typescript
import { SearchResultProcessor } from './src/search/hybrid';

const processor = new SearchResultProcessor({
  maxSnippetLength: 250,
  highlightTags: { start: '<em>', end: '</em>' }
});

// Apply filters
const filteredResults = processor.applyFilters(results, {
  domains: ['github.com', 'stackoverflow.com'],
  excludeDomains: ['spam-site.com'],
  timeRange: { start: startTime, end: endTime },
  minScore: 0.3
});

// Generate snippets with highlights
const processedResults = processor.generateSnippets(
  filteredResults,
  'React hooks performance',
  200
);

// Remove duplicates
const uniqueResults = processor.removeDuplicates(processedResults);

// Apply diversity filtering
const diverseResults = processor.applyDiversityFilter(uniqueResults, 0.8);

// Sort results
const sortedResults = processor.sortResults(diverseResults, options);
```

### SearchCacheManager

Intelligent caching with LRU eviction:

```typescript
import { SearchCacheManager, HybridSearchCacheManager } from './src/search/hybrid';

// Generic cache
const cache = new SearchCacheManager({
  maxSize: 1000,
  expiryMs: 5 * 60 * 1000, // 5 minutes
  enableAutoCleanup: true
});

// Store and retrieve
cache.set('key', { data: 'value' });
const result = cache.get('key');

// Specialized hybrid search cache
const hybridCache = new HybridSearchCacheManager();

// Cache query analysis
hybridCache.setQueryAnalysis('react hooks', analysisResult);
const cachedAnalysis = hybridCache.getQueryAnalysis('react hooks');

// Cache search results
hybridCache.setSearchResults('search:react:hooks', searchResults);
const cachedResults = hybridCache.getSearchResults('search:react:hooks');

// Get statistics
const stats = hybridCache.getCombinedStatistics();
console.log('Total cache hit rate:', stats.total.hitRate);
console.log('Query cache size:', stats.query.size);
console.log('Results cache size:', stats.results.size);

// Cleanup
hybridCache.invalidateAll();
hybridCache.destroy();
```

## Configuration Options

### Search Options

```typescript
interface SearchOptions {
  limit?: number;           // Max results (default: 50, max: 200)
  offset?: number;          // Results offset (default: 0)
  domains?: string[];       // Include only these domains
  excludeDomains?: string[]; // Exclude these domains
  timeRange?: {             // Time range filter
    start: number;
    end: number;
  };
  minScore?: number;        // Minimum relevance score
  includeContent?: boolean; // Include full content (default: true)
  sortBy?: 'relevance' | 'time' | 'popularity'; // Sort order
}
```

### Cache Configuration

```typescript
interface CacheConfig {
  maxSize: number;          // Maximum cache entries
  expiryMs: number;         // Cache expiry time in milliseconds
  cleanupIntervalMs: number; // Auto cleanup interval
  enableAutoCleanup: boolean; // Enable automatic cleanup
}
```

### Strategy Configuration

```typescript
interface StrategyConfig {
  semanticEnabled: boolean;     // Enable semantic search
  stringEnabled: boolean;       // Enable string search
  mergeStrategy: MergeStrategy; // How to merge results
  rankingAlgorithm: RankingAlgorithm; // How to rank results
  timeoutMs: number;           // Search timeout
  fallbackToString: boolean;   // Fallback on semantic failure
}
```

## Performance Considerations

### Caching Strategy

The hybrid search engine implements intelligent caching at multiple levels:

1. **Query Analysis Cache**: Caches query analysis results (30 min expiry)
2. **Search Results Cache**: Caches final search results (5 min expiry)
3. **Processed Query Cache**: Caches query processing (10 min expiry)
4. **Result Merge Cache**: Caches merged results with LRU eviction

### Optimization Tips

```typescript
// 1. Use appropriate limits to reduce processing time
const results = await hybridSearch('query', { limit: 20 });

// 2. Enable caching for repeated queries
const searchSystem = createHybridSearchSystem({
  caching: { maxSize: 2000, expiryMs: 10 * 60 * 1000 }
});

// 3. Use domain filters to reduce search space
const results = await hybridSearch('query', {
  domains: ['trusted-site.com']
});

// 4. Set appropriate timeouts
const results = await coordinator.executeSearch('query', {}, {
  timeoutMs: 3000 // 3 second timeout
});

// 5. Monitor performance with statistics
const stats = searchSystem.getStatistics();
if (stats.cache.total.hitRate < 0.3) {
  // Consider increasing cache size or expiry time
}
```

### Memory Management

```typescript
// Clean up caches periodically
setInterval(() => {
  searchSystem.cache.cleanup();
}, 5 * 60 * 1000); // Every 5 minutes

// Monitor memory usage
const stats = searchSystem.getStatistics();
console.log('Cache memory usage:', {
  totalEntries: stats.cache.total.size,
  hitRate: stats.cache.total.hitRate
});

// Destroy when no longer needed
searchSystem.destroy();
```

## Error Handling

```typescript
try {
  const results = await hybridSearch('query');
} catch (error) {
  if (error.message.includes('timeout')) {
    // Handle timeout
    console.log('Search timed out, using cached results...');
  } else if (error.message.includes('Invalid')) {
    // Handle validation errors
    console.log('Invalid search options:', error.message);
  } else {
    // Handle other errors
    console.error('Search failed:', error);
  }
}

// Graceful fallback
const searchWithFallback = async (query: string) => {
  try {
    return await hybridSearch(query);
  } catch (error) {
    console.warn('Hybrid search failed, falling back to string search');
    // Fallback to string-only search
    return await searchService.search(query);
  }
};
```

## Testing

The modular architecture makes testing easier:

```typescript
// Mock individual components
jest.mock('./SearchCoordinator');
jest.mock('./SearchCacheManager');

// Test with custom configurations
const testSearchSystem = createHybridSearchSystem({
  caching: { maxSize: 10, expiryMs: 1000 },
  strategy: { timeoutMs: 100 }
});

// Verify caching behavior
const key = 'test-query';
cache.set(key, testData);
expect(cache.get(key)).toEqual(testData);

// Test search coordination
const mockCoordinator = new SearchCoordinator();
const result = await mockCoordinator.executeSearch('test', {});
expect(result.results).toHaveLength(5);
```

## Migration from Legacy Code

If you're migrating from the monolithic HybridSearchEngine:

```typescript
// Old way
const engine = new HybridSearchEngine();
const results = await engine.search('query');

// New way (backward compatible)
const results = await hybridSearch('query');

// New way (with full control)
const searchSystem = createHybridSearchSystem();
const results = await searchSystem.search('query');
```

The new modular architecture provides:
- Better testability through focused components
- Enhanced performance with intelligent caching
- Improved maintainability with clear separation of concerns
- Greater flexibility for customization
- Better error handling and monitoring capabilities

## Best Practices

1. **Use appropriate search strategies** based on query types
2. **Monitor cache hit rates** and adjust cache settings accordingly
3. **Set reasonable timeouts** to prevent hanging searches
4. **Clean up resources** when components are no longer needed
5. **Handle errors gracefully** with appropriate fallbacks
6. **Use domain filters** to reduce search space when possible
7. **Monitor performance statistics** to identify bottlenecks