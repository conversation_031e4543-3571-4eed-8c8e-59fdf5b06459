/**
 * ResultMerger - Real-time search result merging with deduplication
 * 
 * <PERSON>les progressive merging of search results from multiple engines,
 * maintaining sorted order and deduplicating by URL.
 * 
 * @module ResultMerger
 * @version 1.0
 * @since 2025-06-27
 */

import type { SearchResult } from './HybridSearchEngine';
import type { SearchEngineConfig } from '../../models';

/**
 * Engine result metadata for tracking
 */
export interface EngineResultMetadata {
  engineType: 'string' | 'lunr';
  resultCount: number;
  isComplete: boolean;
  timestamp: number;
}

/**
 * Merge statistics
 */
export interface MergeStatistics {
  totalResults: number;
  uniqueResults: number;
  duplicatesRemoved: number;
  resultsPerEngine: Record<string, number>;
  mergeTime: number;
}

/**
 * Options for result merging
 */
export interface MergeOptions {
  /** Search engine configuration with weights */
  searchConfig: SearchEngineConfig;
  /** Maximum results to keep */
  maxResults?: number;
  /** Whether to ensure traditional results are included */
  ensureTraditionalResults?: boolean;
  /** Minimum traditional results to include when ensureTraditionalResults is true */
  minTraditionalResults?: number;
}

/**
 * ResultMerger class
 * Handles real-time merging of search results with deduplication
 */
export class ResultMerger {
  private mergedResults: Map<string, SearchResult> = new Map();
  private engineMetadata: Map<string, EngineResultMetadata> = new Map();
  private mergeStartTime: number = 0;

  constructor() {
    this.reset();
  }

  /**
   * Resets merger state for new search
   */
  reset(): void {
    this.mergedResults.clear();
    this.engineMetadata.clear();
    this.mergeStartTime = Date.now();
  }

  /**
   * Adds results from a search engine progressively
   * Merges with existing results maintaining sorted order
   */
  addResults(
    engineType: 'string' | 'lunr',
    results: SearchResult[],
    isComplete: boolean = false
  ): void {
    // Update engine metadata
    const metadata = this.engineMetadata.get(engineType) || {
      engineType,
      resultCount: 0,
      isComplete: false,
      timestamp: Date.now()
    };
    
    metadata.resultCount += results.length;
    metadata.isComplete = isComplete;
    metadata.timestamp = Date.now();
    this.engineMetadata.set(engineType, metadata);

    // Merge results
    for (const result of results) {
      const existing = this.mergedResults.get(result.url);
      
      if (existing) {
        // Update existing result with better scores
        existing.stringScore = Math.max(existing.stringScore, result.stringScore);
        existing.combinedScore = Math.max(existing.combinedScore, result.combinedScore);
        existing.relevanceScore = Math.max(existing.relevanceScore, result.relevanceScore);
        
        // Merge highlights
        if (result.highlights && result.highlights.length > 0) {
          existing.highlights = this.mergeHighlights(existing.highlights, result.highlights);
        }
        
        // Update snippet if new one is better
        if (result.snippet && (!existing.snippet || result.snippet.length > existing.snippet.length)) {
          existing.snippet = result.snippet;
        }
      } else {
        // Add new result
        this.mergedResults.set(result.url, { ...result });
      }
    }
  }

  /**
   * Gets current merged results with proper ordering
   */
  getMergedResults(options: MergeOptions): SearchResult[] {
    const { searchConfig, maxResults = 50, ensureTraditionalResults = true, minTraditionalResults = 3 } = options;
    
    // Convert map to array
    let results = Array.from(this.mergedResults.values());
    
    // Apply weighted scoring based on configuration
    results = this.applyWeightedScoring(results, searchConfig);
    
    // Sort by combined score
    results.sort((a, b) => b.combinedScore - a.combinedScore);
    
    // Ensure traditional results are included if required
    if (ensureTraditionalResults && searchConfig.traditional.alwaysShow) {
      results = this.ensureTraditionalResultsIncluded(results, minTraditionalResults);
    }
    
    // Apply limit
    return results.slice(0, maxResults);
  }

  /**
   * Gets merge statistics
   */
  getStatistics(): MergeStatistics {
    const resultsPerEngine: Record<string, number> = {};
    
    for (const [engine, metadata] of this.engineMetadata.entries()) {
      resultsPerEngine[engine] = metadata.resultCount;
    }
    
    const totalResults = Object.values(resultsPerEngine).reduce((sum, count) => sum + count, 0);
    const uniqueResults = this.mergedResults.size;
    
    return {
      totalResults,
      uniqueResults,
      duplicatesRemoved: totalResults - uniqueResults,
      resultsPerEngine,
      mergeTime: Date.now() - this.mergeStartTime
    };
  }

  /**
   * Gets engine metadata
   */
  getEngineMetadata(): EngineResultMetadata[] {
    return Array.from(this.engineMetadata.values());
  }

  /**
   * Checks if all engines have completed
   */
  isComplete(): boolean {
    if (this.engineMetadata.size === 0) return false;
    
    return Array.from(this.engineMetadata.values()).every(metadata => metadata.isComplete);
  }

  /**
   * Applies weighted scoring based on search configuration
   */
  private applyWeightedScoring(results: SearchResult[], config: SearchEngineConfig): SearchResult[] {
    // Normalize weights to sum to 1
    const totalWeight = config.traditional.weight + config.fulltext.weight;
    
    const traditionalWeight = config.traditional.weight / totalWeight;
    const fulltextWeight = config.fulltext.weight / totalWeight;
    
    return results.map(result => {
      // Calculate weighted combined score
      // String score represents both traditional string search and Lunr fulltext
      const traditionalScore = result.stringScore;
      
      result.combinedScore = traditionalScore * (traditionalWeight + fulltextWeight);
      
      return result;
    });
  }

  /**
   * Ensures minimum traditional results are included
   */
  private ensureTraditionalResultsIncluded(
    results: SearchResult[],
    minCount: number
  ): SearchResult[] {
    // Find traditional results (those with stringScore > 0)
    const traditionalResults = results.filter(r => r.stringScore > 0);
    const nonTraditionalResults = results.filter(r => r.stringScore === 0);
    
    // If we have enough traditional results in top positions, return as is
    const topResults = results.slice(0, minCount);
    const topTraditionalCount = topResults.filter(r => r.stringScore > 0).length;
    
    if (topTraditionalCount >= minCount) {
      return results;
    }
    
    // Otherwise, ensure we have at least minCount traditional results
    const neededTraditional = traditionalResults.slice(0, minCount);
    const remainingSlots = results.length - neededTraditional.length;
    
    // Merge traditional and non-traditional results
    const remainingResults = [
      ...traditionalResults.slice(minCount),
      ...nonTraditionalResults
    ].sort((a, b) => b.combinedScore - a.combinedScore)
      .slice(0, remainingSlots);
    
    return [...neededTraditional, ...remainingResults];
  }

  /**
   * Merges highlight arrays, removing duplicates
   */
  private mergeHighlights(existing?: string[], additional?: string[]): string[] {
    if (!existing && !additional) return [];
    if (!existing) return additional!;
    if (!additional) return existing;
    
    const uniqueHighlights = new Set([...existing, ...additional]);
    return Array.from(uniqueHighlights);
  }
}

// Export singleton instance
export const resultMerger = new ResultMerger();