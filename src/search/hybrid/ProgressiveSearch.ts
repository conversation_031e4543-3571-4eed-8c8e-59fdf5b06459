/**
 * ProgressiveSearch - Progressive search with result streaming
 * 
 * Provides async iterator patterns for streaming search results as they arrive
 * from multiple search engines executing in parallel.
 * 
 * @module ProgressiveSearch
 * @version 1.0
 * @since 2025-06-27
 */

import type { QueryAnalysisResult } from './QueryAnalyzer';
import type { SearchResult, SearchOptions } from './HybridSearchEngine';

/**
 * Event types for progressive search
 */
export type ProgressiveSearchEventType = 
  | 'start'
  | 'engine-start'
  | 'engine-progress'
  | 'engine-complete'
  | 'engine-cancelled'
  | 'complete'
  | 'error';

/**
 * Progressive search event
 */
export interface ProgressiveSearchEvent {
  type: ProgressiveSearchEventType;
  engineType?: string;
  results?: SearchResult[];
  totalResults?: number;
  progress?: number;
  error?: Error;
  timestamp: number;
}

/**
 * Options for progressive search
 */
export interface ProgressiveSearchOptions extends SearchOptions {
  /** Callback for progress updates */
  onProgress?: (event: ProgressiveSearchEvent) => void;
  /** Enable streaming results as they arrive */
  enableStreaming?: boolean;
  /** Batch size for streaming results */
  streamBatchSize?: number;
  /** Cancel signal for aborting search */
  signal?: AbortSignal;
}

/**
 * Search executor function type
 */
export type SearchExecutorFunction = (
  analysis: QueryAnalysisResult,
  options: SearchOptions
) => Promise<SearchResult[]>;

/**
 * Search executor configuration
 */
export interface SearchExecutor {
  /** Engine type identifier */
  engineType: string;
  /** Execute search function */
  execute: SearchExecutorFunction;
  /** Timeout in milliseconds */
  timeout?: number;
  /** Priority (higher = executed first) */
  priority?: number;
}

/**
 * Result accumulator for progressive search
 */
interface ResultAccumulator {
  engineType: string;
  results: SearchResult[];
  complete: boolean;
  error?: Error;
}

/**
 * ProgressiveSearch implementation
 * Manages parallel execution of multiple search engines with result streaming
 */
export class ProgressiveSearch {
  private abortController: AbortController | null = null;
  private activeSearches: Map<string, Promise<SearchResult[]>> = new Map();

  /**
   * Executes multiple search engines in parallel with streaming results
   * Returns an async iterator that yields results as they arrive
   */
  async *executeStreaming(
    executors: SearchExecutor[],
    analysis: QueryAnalysisResult,
    options: ProgressiveSearchOptions = {}
  ): AsyncGenerator<SearchResult[], void, unknown> {
    // Create abort controller if not provided
    this.abortController = new AbortController();
    const signal = options.signal || this.abortController.signal;
    
    // Emit start event
    this.emitEvent('start', options.onProgress);
    
    // Sort executors by priority
    const sortedExecutors = [...executors].sort((a, b) => 
      (b.priority || 0) - (a.priority || 0)
    );
    
    // Result accumulators
    const accumulators = new Map<string, ResultAccumulator>();
    const streamBatchSize = options.streamBatchSize || 10;
    
    // Start all searches in parallel
    const searchPromises = sortedExecutors.map(executor => 
      this.executeWithTimeout(executor, analysis, options, signal)
        .then(results => {
          // Store results
          const accumulator = accumulators.get(executor.engineType) || {
            engineType: executor.engineType,
            results: [],
            complete: false
          };
          accumulator.results = results;
          accumulator.complete = true;
          accumulators.set(executor.engineType, accumulator);
          
          // Emit complete event
          this.emitEvent('engine-complete', options.onProgress, {
            engineType: executor.engineType,
            results,
            totalResults: results.length
          });
          
          return results;
        })
        .catch(error => {
          // Handle errors
          const accumulator = accumulators.get(executor.engineType) || {
            engineType: executor.engineType,
            results: [],
            complete: true,
            error
          };
          accumulator.error = error;
          accumulator.complete = true;
          accumulators.set(executor.engineType, accumulator);
          
          // Emit error event
          this.emitEvent('error', options.onProgress, {
            engineType: executor.engineType,
            error
          });
          
          return [];
        })
    );
    
    // Store active searches
    sortedExecutors.forEach((executor, index) => {
      this.activeSearches.set(executor.engineType, searchPromises[index]);
    });
    
    // Stream results as they arrive
    if (options.enableStreaming !== false) {
      let completed = 0;
      let lastYieldedCount = 0;
      
      while (completed < searchPromises.length) {
        // Check for new results
        const currentResults: SearchResult[] = [];
        
        for (const accumulator of accumulators.values()) {
          currentResults.push(...accumulator.results);
        }
        
        // Yield new results if we have enough or if search is complete
        if (currentResults.length - lastYieldedCount >= streamBatchSize || 
            completed === searchPromises.length - 1) {
          const newResults = currentResults.slice(lastYieldedCount);
          if (newResults.length > 0) {
            yield newResults;
            lastYieldedCount = currentResults.length;
          }
        }
        
        // Wait for next result
        try {
          await Promise.race([
            Promise.race(searchPromises.filter((_, index) => 
              !accumulators.has(sortedExecutors[index].engineType) ||
              !accumulators.get(sortedExecutors[index].engineType)!.complete
            )),
            new Promise(resolve => setTimeout(resolve, 50)) // Check every 50ms
          ]);
        } catch {
          // All promises rejected or settled
        }
        
        // Count completed
        completed = Array.from(accumulators.values()).filter(a => a.complete).length;
      }
      
      // Yield any remaining results
      const finalResults: SearchResult[] = [];
      for (const accumulator of accumulators.values()) {
        finalResults.push(...accumulator.results);
      }
      
      const remainingResults = finalResults.slice(lastYieldedCount);
      if (remainingResults.length > 0) {
        yield remainingResults;
      }
    } else {
      // Wait for all results and yield once
      await Promise.allSettled(searchPromises);
      
      const allResults: SearchResult[] = [];
      for (const accumulator of accumulators.values()) {
        allResults.push(...accumulator.results);
      }
      
      if (allResults.length > 0) {
        yield allResults;
      }
    }
    
    // Emit complete event
    this.emitEvent('complete', options.onProgress);
    
    // Clear active searches
    this.activeSearches.clear();
  }

  /**
   * Executes multiple search engines in parallel
   * Returns an async iterator that can be consumed for progressive results
   */
  async executeParallel(
    executors: SearchExecutor[],
    analysis: QueryAnalysisResult,
    options: ProgressiveSearchOptions = {}
  ): Promise<AsyncIterableIterator<SearchResult[]>> {
    return this.executeStreaming(executors, analysis, options);
  }

  /**
   * Executes a single search engine with timeout
   */
  private async executeWithTimeout(
    executor: SearchExecutor,
    analysis: QueryAnalysisResult,
    options: SearchOptions,
    signal: AbortSignal
  ): Promise<SearchResult[]> {
    // Emit engine start event
    const progressOptions = options as ProgressiveSearchOptions;
    this.emitEvent('engine-start', progressOptions.onProgress, {
      engineType: executor.engineType
    });
    
    // Create timeout promise
    const timeout = executor.timeout || 5000;
    const timeoutPromise = new Promise<never>((_, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Search timeout after ${timeout}ms`));
      }, timeout);
      
      // Clear timeout if aborted
      signal.addEventListener('abort', () => {
        clearTimeout(timeoutId);
        reject(new Error('Search cancelled'));
      });
    });
    
    try {
      // Race between search and timeout
      const results = await Promise.race([
        executor.execute(analysis, options),
        timeoutPromise
      ]);
      
      return results;
    } catch (error) {
      // Check if cancelled
      if (signal.aborted) {
        this.emitEvent('engine-cancelled', progressOptions.onProgress, {
          engineType: executor.engineType
        });
      }
      throw error;
    }
  }

  /**
   * Cancels ongoing progressive search
   */
  cancel(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
    this.activeSearches.clear();
  }

  /**
   * Checks if search is currently active
   */
  isActive(): boolean {
    return this.activeSearches.size > 0;
  }

  /**
   * Gets active search engine types
   */
  getActiveEngines(): string[] {
    return Array.from(this.activeSearches.keys());
  }

  /**
   * Emits a progress event
   */
  private emitEvent(
    type: ProgressiveSearchEventType,
    onProgress?: (event: ProgressiveSearchEvent) => void,
    additionalData?: Partial<ProgressiveSearchEvent>
  ): void {
    if (onProgress) {
      onProgress({
        type,
        timestamp: Date.now(),
        ...additionalData
      });
    }
  }

  /**
   * Creates a search executor from a function
   */
  static createExecutor(
    engineType: string,
    execute: SearchExecutorFunction,
    timeout?: number,
    priority?: number
  ): SearchExecutor {
    return {
      engineType,
      execute,
      timeout,
      priority
    };
  }
}

/**
 * Factory for creating standard search executors
 */
export class ProgressiveSearchFactory {
  /**
   * Creates standard search executors based on configuration
   */
  static createStandardExecutors(
    config: {
      string?: { enabled: boolean; timeout?: number; priority?: number };
      fulltext?: { enabled: boolean; timeout?: number; priority?: number };
    },
    searchFunctions: {
      string: SearchExecutorFunction;
      fulltext: SearchExecutorFunction;
    }
  ): SearchExecutor[] {
    const executors: SearchExecutor[] = [];
    
    
    if (config.string?.enabled) {
      executors.push(ProgressiveSearch.createExecutor(
        'string',
        searchFunctions.string,
        config.string.timeout || 200,
        config.string.priority || 2
      ));
    }
    
    if (config.fulltext?.enabled) {
      executors.push(ProgressiveSearch.createExecutor(
        'fulltext',
        searchFunctions.fulltext,
        config.fulltext.timeout || 300,
        config.fulltext.priority || 1
      ));
    }
    
    return executors;
  }
}

// Export singleton instance
export const progressiveSearch = new ProgressiveSearch();