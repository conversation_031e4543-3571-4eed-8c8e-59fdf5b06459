import { QueryProcessor } from './QueryProcessor';

describe('QueryProcessor', () => {
  let processor: QueryProcessor;

  beforeEach(() => {
    processor = new QueryProcessor();
  });

  describe('Advanced Search Syntax', () => {
    it('should extract exact phrases from quoted strings', () => {
      const result = processor.process('"Claude Code" documentation');
      
      expect(result.filters.exactPhrases).toEqual(['Claude Code']);
      expect(result.cleanText).toBe('documentation');
    });

    it('should extract multiple exact phrases', () => {
      const result = processor.process('"Claude Code" "React Hooks" tutorial');
      
      expect(result.filters.exactPhrases).toEqual(['Claude Code', 'React Hooks']);
      expect(result.cleanText).toBe('tutorial');
    });

    it('should extract exclude terms with minus sign', () => {
      const result = processor.process('Claude -Code -API');
      
      expect(result.filters.exclude).toEqual(['Code', 'API']);
      expect(result.cleanText).toBe('Claude');
    });

    it('should extract site filter', () => {
      const result = processor.process('Claude Code site:claude.ai');
      
      expect(result.filters.site).toBe('claude.ai');
      expect(result.cleanText).toBe('Claude Code');
    });

    it('should handle combined syntax', () => {
      const result = processor.process('"Claude Code" -API site:claude.ai documentation');
      
      expect(result.filters.exactPhrases).toEqual(['Claude Code']);
      expect(result.filters.exclude).toEqual(['API']);
      expect(result.filters.site).toBe('claude.ai');
      expect(result.cleanText).toBe('documentation');
    });

    it('should handle empty quotes', () => {
      const result = processor.process('Claude "" Code');
      
      expect(result.filters.exactPhrases).toBeUndefined();
      expect(result.cleanText).toBe('Claude Code');
    });

    it('should handle quoted strings with special characters', () => {
      const result = processor.process('"Claude Code v3.0" features');
      
      expect(result.filters.exactPhrases).toEqual(['Claude Code v3.0']);
      expect(result.cleanText).toBe('features');
    });

    it('should handle OR logic when no exact phrases are specified', () => {
      const result = processor.process('Claude Code documentation');
      
      expect(result.filters.exactPhrases).toBeUndefined();
      expect(result.cleanText).toBe('Claude Code documentation');
    });
  });
});