/**
 * Result Ranker
 * 
 * Traditional ranking system that combines multiple signals to produce optimal search results.
 * Implements scoring with time decay, access patterns, and content features.
 */

import type { SearchResultItem, Page } from '../models';
import type { ProcessedQuery } from './QueryProcessor';

export interface RankingConfig {
  // Weight for different ranking factors (should sum to 1.0)
  timeDecayWeight: number;
  accessCountWeight: number;
  
  // Content-specific boosts
  titleBoost: number;
  urlBoost: number;
  contentBoost: number;
  exactMatchBoost: number;
  
  // Time decay parameters
  timeDecayHalfLife: number; // milliseconds
}

export interface RankingFeatures {
  timeScore: number;
  accessScore: number;
  titleRelevance: number;
  urlRelevance: number;
  contentRelevance: number;
  exactMatchCount: number;
  domainAuthority: number;
  contentLength: number;
  titleLength: number;
}

export interface RankedResult extends SearchResultItem {
  rankingScore: number;
  features: RankingFeatures;
  explanation?: string[];
}

export class ResultRanker {
  private config: RankingConfig;
  private domainScores = new Map<string, number>();

  constructor(config: RankingConfig) {
    this.config = { ...config };
    this.initializeDomainScores();
  }

  /**
   * Rank search results using traditional scoring
   */
  public async rankResults(
    results: SearchResultItem[],
    query: ProcessedQuery
  ): Promise<RankedResult[]> {
    if (results.length === 0) {
      return [];
    }

    const startTime = performance.now();
    
    try {
      // Extract features for all results
      const rankedResults: RankedResult[] = [];
      
      for (const result of results) {
        const features = this.extractFeatures(result, query);
        const rankingScore = this.calculateHybridScore(features);
        const explanation = this.generateExplanation(features, this.config);
        
        rankedResults.push({
          ...result,
          rankingScore,
          features,
          explanation,
        });
      }

      // Sort by ranking score (descending)
      rankedResults.sort((a, b) => b.rankingScore - a.rankingScore);

      // Apply diversity and post-processing
      const diversifiedResults = this.applyDiversification(rankedResults);
      
      const processingTime = performance.now() - startTime;
      console.log(`[ResultRanker] Ranked ${results.length} results in ${processingTime.toFixed(2)}ms`);
      
      return diversifiedResults;
      
    } catch (error) {
      console.error('[ResultRanker] Ranking failed:', error);
      
      // Fallback: return original results with basic scores
      return results.map((result, index) => ({
        ...result,
        rankingScore: result.score || 1 - (index * 0.01),
        features: this.getDefaultFeatures(),
        explanation: ['Ranking failed - using fallback scores'],
      }));
    }
  }

  /**
   * Extract ranking features from a search result
   */
  private extractFeatures(
    result: SearchResultItem,
    query: ProcessedQuery
  ): RankingFeatures {
    const page = result.page;
    const now = Date.now();
    

    // Time score with exponential decay
    const timeDiff = now - page.visitTime;
    const timeScore = Math.exp(-timeDiff / this.config.timeDecayHalfLife);

    // Access score (normalized)
    const accessScore = this.normalizeAccessCount(page.accessCount);

    // Content relevance scores
    const titleRelevance = this.calculateTextRelevance(page.title, query);
    const urlRelevance = this.calculateTextRelevance(page.url, query);
    const contentRelevance = this.calculateTextRelevance(page.content, query);

    // Exact match count
    const exactMatchCount = this.countExactMatches(page, query);

    // Domain authority
    const domainAuthority = this.getDomainScore(page.domain);

    // Content metrics
    const contentLength = page.content.length;
    const titleLength = page.title.length;

    return {
      timeScore,
      accessScore,
      titleRelevance,
      urlRelevance,
      contentRelevance,
      exactMatchCount,
      domainAuthority,
      contentLength,
      titleLength,
    };
  }

  /**
   * Calculate traditional ranking score (vector similarity removed)
   */
  private calculateHybridScore(features: RankingFeatures): number {
    // Start with base score from content relevance
    let score = 0;
    
    // Add time decay factor
    score += features.timeScore * this.config.timeDecayWeight;
    
    // Add access count factor
    score += features.accessScore * this.config.accessCountWeight;
    
    // Apply content boosts
    const titleScore = features.titleRelevance * this.config.titleBoost;
    const urlScore = features.urlRelevance * this.config.urlBoost;
    const contentScore = features.contentRelevance * this.config.contentBoost;
    
    // Weighted average of content scores
    const contentWeight = 0.1; // Adjust base content weight
    score += (titleScore + urlScore + contentScore) * contentWeight;
    
    // Apply exact match boost
    if (features.exactMatchCount > 0) {
      score *= (1 + features.exactMatchCount * this.config.exactMatchBoost * 0.1);
    }
    
    // Apply domain authority boost
    score *= (1 + features.domainAuthority * 0.1);
    
    // Apply content quality factors
    score *= this.calculateContentQualityMultiplier(features);
    
    return Math.max(0, Math.min(1, score)); // Clamp to [0, 1]
  }

  /**
   * Calculate text relevance score
   */
  private calculateTextRelevance(text: string, query: ProcessedQuery): number {
    if (!text || !query.keywords.length) {
      return 0;
    }

    const textLower = text.toLowerCase();
    let relevanceScore = 0;
    const totalTerms = query.keywords.length;

    for (const keyword of query.keywords) {
      const keywordLower = keyword.toLowerCase();
      
      // Exact match
      if (textLower.includes(keywordLower)) {
        relevanceScore += 1;
        
        // Bonus for word boundary matches
        const wordBoundaryRegex = new RegExp(`\\b${this.escapeRegExp(keywordLower)}\\b`, 'gi');
        if (wordBoundaryRegex.test(text)) {
          relevanceScore += 0.5;
        }
      }
      
      // Partial matches (for longer keywords)
      if (keyword.length > 4) {
        const partialMatches = this.findPartialMatches(textLower, keywordLower);
        relevanceScore += partialMatches * 0.3;
      }
    }

    return Math.min(1, relevanceScore / totalTerms);
  }

  /**
   * Count exact phrase matches
   */
  private countExactMatches(page: Page, query: ProcessedQuery): number {
    let exactMatches = 0;
    const searchText = `${page.title} ${page.content} ${page.url}`.toLowerCase();
    
    // Check for exact phrase matches
    for (const keyword of query.keywords) {
      const keywordLower = keyword.toLowerCase();
      const matches = (searchText.match(new RegExp(this.escapeRegExp(keywordLower), 'gi')) || []).length;
      exactMatches += matches;
    }
    
    return exactMatches;
  }

  /**
   * Find partial matches for longer terms
   */
  private findPartialMatches(text: string, term: string): number {
    if (term.length < 4) return 0;
    
    let matches = 0;
    const minLength = Math.max(3, Math.floor(term.length * 0.7));
    
    for (let i = 0; i <= term.length - minLength; i++) {
      const partial = term.substring(i, i + minLength);
      if (text.includes(partial)) {
        matches++;
      }
    }
    
    return matches;
  }

  /**
   * Normalize access count to [0, 1] range
   */
  private normalizeAccessCount(accessCount: number): number {
    // Use logarithmic scaling for access count
    if (accessCount <= 0) return 0;
    
    // Assume max access count of 100 for normalization
    const maxAccessCount = 100;
    const logAccessCount = Math.log(accessCount + 1);
    const logMaxAccessCount = Math.log(maxAccessCount + 1);
    
    return Math.min(1, logAccessCount / logMaxAccessCount);
  }

  /**
   * Calculate content quality multiplier
   */
  private calculateContentQualityMultiplier(features: RankingFeatures): number {
    let multiplier = 1.0;
    
    // Prefer content with reasonable length
    const idealContentLength = 1000; // characters
    const contentLengthFactor = Math.min(1, features.contentLength / idealContentLength);
    multiplier *= (0.8 + contentLengthFactor * 0.2);
    
    // Prefer pages with descriptive titles
    const idealTitleLength = 50; // characters
    const titleLengthFactor = Math.min(1, features.titleLength / idealTitleLength);
    multiplier *= (0.9 + titleLengthFactor * 0.1);
    
    return multiplier;
  }

  /**
   * Apply result diversification to avoid redundancy
   */
  private applyDiversification(results: RankedResult[]): RankedResult[] {
    if (results.length <= 10) {
      return results; // No need to diversify small result sets
    }

    const diversified: RankedResult[] = [];
    const seenDomains = new Set<string>();
    const domainCounts = new Map<string, number>();
    
    // First pass: add top results while limiting domain repetition
    for (const result of results) {
      const domain = result.page.domain;
      const domainCount = domainCounts.get(domain) || 0;
      
      // Allow more results from high-authority domains
      const maxFromDomain = this.getDomainScore(domain) > 0.7 ? 4 : 2;
      
      if (domainCount < maxFromDomain || diversified.length < 5) {
        diversified.push(result);
        domainCounts.set(domain, domainCount + 1);
        seenDomains.add(domain);
      }
      
      if (diversified.length >= 20) break; // Limit total results
    }

    // Second pass: fill remaining slots with best remaining results
    const remaining = results.filter(r => !diversified.includes(r));
    const slotsLeft = Math.max(0, 30 - diversified.length);
    
    diversified.push(...remaining.slice(0, slotsLeft));
    
    return diversified;
  }

  /**
   * Generate ranking explanation (vector similarity removed)
   */
  private generateExplanation(features: RankingFeatures, _config: RankingConfig): string[] {
    const explanation: string[] = [];
    
    
    // Time factor
    if (features.timeScore > 0.1) {
      explanation.push(`Recent visit bonus: ${(features.timeScore * 100).toFixed(1)}%`);
    }
    
    // Access count
    if (features.accessScore > 0.1) {
      explanation.push(`Frequently visited: ${(features.accessScore * 100).toFixed(1)}%`);
    }
    
    // Content relevance
    if (features.titleRelevance > 0.1) {
      explanation.push(`Title match: ${(features.titleRelevance * 100).toFixed(1)}%`);
    }
    
    if (features.contentRelevance > 0.1) {
      explanation.push(`Content match: ${(features.contentRelevance * 100).toFixed(1)}%`);
    }
    
    // Exact matches
    if (features.exactMatchCount > 0) {
      explanation.push(`${features.exactMatchCount} exact match${features.exactMatchCount > 1 ? 'es' : ''}`);
    }
    
    // Domain authority
    if (features.domainAuthority > 0.5) {
      explanation.push('Trusted domain');
    }
    
    return explanation;
  }

  /**
   * Initialize domain scores based on common patterns
   */
  private initializeDomainScores(): void {
    // High-authority domains
    const highAuthority = [
      'github.com', 'stackoverflow.com', 'developer.mozilla.org', 'docs.microsoft.com',
      'wikipedia.org', 'medium.com', 'arxiv.org', 'scholar.google.com'
    ];
    
    const mediumAuthority = [
      'reddit.com', 'news.ycombinator.com', 'dev.to', 'hashnode.com',
      'youtube.com', 'vimeo.com', 'slideshare.net'
    ];
    
    highAuthority.forEach(domain => this.domainScores.set(domain, 0.9));
    mediumAuthority.forEach(domain => this.domainScores.set(domain, 0.7));
  }

  /**
   * Get domain authority score
   */
  private getDomainScore(domain: string): number {
    return this.domainScores.get(domain) || 0.5; // Default score for unknown domains
  }

  /**
   * Get default features for fallback cases (vector similarity removed)
   */
  private getDefaultFeatures(): RankingFeatures {
    return {
      timeScore: 0.5,
      accessScore: 0.1,
      titleRelevance: 0,
      urlRelevance: 0,
      contentRelevance: 0,
      exactMatchCount: 0,
      domainAuthority: 0.5,
      contentLength: 0,
      titleLength: 0,
    };
  }

  /**
   * Update ranking configuration
   */
  public updateConfig(newConfig: Partial<RankingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('[ResultRanker] Configuration updated');
  }

  /**
   * Add or update domain score
   */
  public setDomainScore(domain: string, score: number): void {
    this.domainScores.set(domain, Math.max(0, Math.min(1, score)));
  }

  /**
   * Get current configuration
   */
  public getConfig(): RankingConfig {
    return { ...this.config };
  }

  /**
   * Escape special regex characters
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}