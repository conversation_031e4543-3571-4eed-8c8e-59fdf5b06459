/**
 * Query Processor
 * 
 * Advanced query processing system that understands user intent and extracts
 * meaningful components from natural language search queries.
 */

export interface ProcessedQuery {
  originalQuery: string;
  cleanText: string;
  intent: QueryIntent;
  entities: QueryEntity[];
  keywords: string[];
  filters: QueryFilters;
}

export interface QueryEntity {
  text: string;
  type: EntityType;
  confidence: number;
  startIndex: number;
  endIndex: number;
}

export interface QueryFilters {
  site?: string;
  timeRange?: {
    start: number;
    end: number;
  };
  fileType?: string;
  exclude?: string[];
  domain?: string[];
  exactPhrases?: string[];
}

export type QueryIntent = 
  | 'search'        // General search query
  | 'navigation'    // Looking for a specific page/site
  | 'informational' // Seeking information about a topic
  | 'transactional' // Looking to perform an action
  | 'temporal'      // Time-based query
  | 'similarity'    // Find similar content
  | 'analytical';   // Comparative or analytical query

export type EntityType = 
  | 'person'
  | 'organization'
  | 'location'
  | 'technology'
  | 'concept'
  | 'product'
  | 'date'
  | 'url'
  | 'domain'
  | 'file'
  | 'language';

export interface QueryProcessingConfig {
  enableEntityExtraction: boolean;
  enableIntentDetection: boolean;
  enableKeywordExpansion: boolean;
  minKeywordLength: number;
  maxKeywords: number;
  stemming: boolean;
  stopWordsRemoval: boolean;
}

export class QueryProcessor {
  private config: QueryProcessingConfig;
  private originalQuery: string = '';
  private stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
    'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
    'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
    'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
  ]);

  private technologyTerms = new Set([
    'javascript', 'typescript', 'python', 'java', 'react', 'vue', 'angular',
    'node.js', 'express', 'mongodb', 'postgresql', 'mysql', 'docker', 'kubernetes',
    'aws', 'azure', 'gcp', 'api', 'rest', 'graphql', 'html', 'css', 'git',
    'github', 'stackoverflow', 'npm', 'yarn', 'webpack', 'babel', 'eslint'
  ]);

  private intentPatterns = new Map<QueryIntent, RegExp[]>([
    ['navigation', [
      /^(go to|visit|open|show me)\s+/i,
      /\.(com|org|net|io|dev)$/i,
      /^https?:\/\//i
    ]],
    ['temporal', [
      /\b(today|yesterday|last week|last month|this week|this month|recent)\b/i,
      /\b(before|after|since|until|during)\b/i,
      /\b\d{4}(-\d{2}(-\d{2})?)?/,
      /\b(january|february|march|april|may|june|july|august|september|october|november|december)\b/i
    ]],
    ['similarity', [
      /\b(similar to|like|related to|comparable)\b/i,
      /\b(find more|show similar|related pages)\b/i
    ]],
    ['analytical', [
      /\b(compare|versus|vs|difference between|contrast)\b/i,
      /\b(pros and cons|advantages|disadvantages)\b/i,
      /\b(analysis|analyze|evaluation|review)\b/i
    ]],
    ['transactional', [
      /\b(how to|tutorial|guide|learn|setup|install|configure)\b/i,
      /\b(download|buy|purchase|subscribe|sign up)\b/i
    ]]
  ]);

  constructor(config: Partial<QueryProcessingConfig> = {}) {
    this.config = {
      enableEntityExtraction: true,
      enableIntentDetection: true,
      enableKeywordExpansion: true,
      minKeywordLength: 2,
      maxKeywords: 20,
      stemming: false, // Simple implementation doesn't include stemming
      stopWordsRemoval: true,
      ...config
    };
  }

  /**
   * Process a search query into structured components
   */
  public process(query: string): ProcessedQuery {
    if (!query || query.trim().length === 0) {
      return this.getEmptyQuery();
    }

    const originalQuery = query.trim();
    this.originalQuery = originalQuery; // Store for use in other methods
    
    try {
      // Extract filters first (site:, exclude:, etc.)
      const { cleanQuery, filters } = this.extractFilters(originalQuery);
      
      // Detect query intent
      const intent = this.config.enableIntentDetection 
        ? this.detectIntent(cleanQuery) 
        : 'search';
      
      // Extract entities
      const entities = this.config.enableEntityExtraction 
        ? this.extractEntities(cleanQuery) 
        : [];
      
      // Extract keywords
      const keywords = this.extractKeywords(cleanQuery, entities);
      
      // Create clean text for embedding
      // Use the cleanQuery directly to preserve original case
      const cleanText = cleanQuery;
      
      return {
        originalQuery,
        cleanText,
        intent,
        entities,
        keywords,
        filters
      };
      
    } catch (error) {
      console.error('[QueryProcessor] Processing failed:', error);
      return {
        originalQuery,
        cleanText: originalQuery,
        intent: 'search',
        entities: [],
        keywords: this.extractBasicKeywords(originalQuery),
        filters: {}
      };
    }
  }

  /**
   * Extract advanced search filters from query
   */
  private extractFilters(query: string): { cleanQuery: string; filters: QueryFilters } {
    const filters: QueryFilters = {};
    let cleanQuery = query;

    // Extract exact phrases first (quoted strings)
    const exactPhraseMatches = query.match(/"([^"]*)"/g);
    if (exactPhraseMatches) {
      filters.exactPhrases = exactPhraseMatches
        .map(match => match.replace(/"/g, '').trim())
        .filter(phrase => phrase.length > 0); // Filter out empty quotes
      
      // Only set exactPhrases if we have non-empty phrases
      if (filters.exactPhrases.length === 0) {
        delete filters.exactPhrases;
      }
      
      // Remove exact phrases from clean query
      cleanQuery = cleanQuery.replace(/"[^"]*"/g, ' ').replace(/\s+/g, ' ').trim();
    }

    // Exclude terms: -term or NOT term (process before site filter to avoid conflicts)
    const excludeMatches = cleanQuery.match(/(?:^|\s)-(\w+)/g);
    if (excludeMatches) {
      filters.exclude = excludeMatches.map(match => 
        match.trim().substring(1) // Remove the minus sign
      );
      // Remove exclude patterns from clean query
      excludeMatches.forEach(match => {
        cleanQuery = cleanQuery.replace(match, '');
      });
      cleanQuery = cleanQuery.replace(/\s+/g, ' ').trim();
    }

    // Site filter: site:example.com
    const siteMatch = cleanQuery.match(/\bsite:([^\s]+)/i);
    if (siteMatch) {
      filters.site = siteMatch[1];
      cleanQuery = cleanQuery.replace(siteMatch[0], '').trim();
    }

    // File type filter: filetype:pdf or ext:pdf
    const fileTypeMatch = cleanQuery.match(/\b(?:filetype|ext):([^\s]+)/i);
    if (fileTypeMatch) {
      filters.fileType = fileTypeMatch[1];
      cleanQuery = cleanQuery.replace(fileTypeMatch[0], '').trim();
    }

    // Time range filters
    const timeFilter = this.extractTimeFilter(query);
    if (timeFilter) {
      filters.timeRange = timeFilter;
      // Remove time expressions from clean query
      cleanQuery = this.removeTimeExpressions(cleanQuery);
    }

    return { cleanQuery: cleanQuery.trim(), filters };
  }

  /**
   * Detect query intent based on patterns
   */
  private detectIntent(query: string): QueryIntent {
    const queryLower = query.toLowerCase();
    
    for (const [intent, patterns] of this.intentPatterns.entries()) {
      for (const pattern of patterns) {
        if (pattern.test(queryLower)) {
          return intent;
        }
      }
    }
    
    // Default intent based on query characteristics
    if (queryLower.includes('?')) {
      return 'informational';
    }
    
    if (this.isUrlLike(query)) {
      return 'navigation';
    }
    
    return 'search';
  }

  /**
   * Extract named entities from query
   */
  private extractEntities(query: string): QueryEntity[] {
    const entities: QueryEntity[] = [];
    const queryLower = query.toLowerCase();
    
    // Technology entities
    for (const tech of this.technologyTerms) {
      const index = queryLower.indexOf(tech);
      if (index !== -1) {
        entities.push({
          text: query.substring(index, index + tech.length),
          type: 'technology',
          confidence: 0.9,
          startIndex: index,
          endIndex: index + tech.length
        });
      }
    }
    
    // URL entities
    const urlMatches = query.matchAll(/https?:\/\/[^\s]+/g);
    for (const match of urlMatches) {
      entities.push({
        text: match[0],
        type: 'url',
        confidence: 1.0,
        startIndex: match.index!,
        endIndex: match.index! + match[0].length
      });
    }
    
    // Domain entities
    const domainMatches = query.matchAll(/\b[a-zA-Z0-9-]+\.[a-zA-Z]{2,}\b/g);
    for (const match of domainMatches) {
      if (!match[0].includes('http')) { // Avoid duplicating URLs
        entities.push({
          text: match[0],
          type: 'domain',
          confidence: 0.8,
          startIndex: match.index!,
          endIndex: match.index! + match[0].length
        });
      }
    }
    
    // Date entities
    const dateMatches = query.matchAll(/\b\d{4}(-\d{2}){0,2}\b/g);
    for (const match of dateMatches) {
      entities.push({
        text: match[0],
        type: 'date',
        confidence: 0.9,
        startIndex: match.index!,
        endIndex: match.index! + match[0].length
      });
    }
    
    // Programming language entities
    const langPattern = /\b(javascript|typescript|python|java|c\+\+|c#|php|ruby|swift|kotlin|rust|go)\b/gi;
    const langMatches = query.matchAll(langPattern);
    for (const match of langMatches) {
      entities.push({
        text: match[0],
        type: 'language',
        confidence: 0.95,
        startIndex: match.index!,
        endIndex: match.index! + match[0].length
      });
    }
    
    // Sort by position and remove overlaps
    return this.removeOverlappingEntities(entities.sort((a, b) => a.startIndex - b.startIndex));
  }

  /**
   * Extract keywords for search
   */
  private extractKeywords(query: string, entities: QueryEntity[]): string[] {
    // Extract filters to get exact phrases
    const { filters } = this.extractFilters(this.originalQuery || query);
    
    // Remove entity text from query to avoid duplication
    let textForKeywords = query;
    entities.forEach(entity => {
      textForKeywords = textForKeywords.replace(entity.text, ' ');
    });
    
    // Basic keyword extraction
    const words = textForKeywords
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .split(/\s+/)
      .filter(word => 
        word.length >= this.config.minKeywordLength &&
        (!this.config.stopWordsRemoval || !this.stopWords.has(word))
      );
    
    // Add entity text as keywords
    const entityKeywords = entities.map(entity => entity.text.toLowerCase());
    
    // Add exact phrases as keywords (preserve as whole phrases)
    const phraseKeywords = filters.exactPhrases || [];
    
    // Combine and deduplicate
    const allKeywords = [...new Set([...words, ...entityKeywords, ...phraseKeywords])];
    
    // Apply keyword expansion if enabled
    let expandedKeywords = allKeywords;
    if (this.config.enableKeywordExpansion) {
      expandedKeywords = this.expandKeywords(allKeywords);
    }
    
    // Limit number of keywords
    return expandedKeywords.slice(0, this.config.maxKeywords);
  }

  /**
   * Create clean text for embedding generation
   * @deprecated Currently not used but kept for future implementation
   */
  // @ts-ignore - Unused method for future use
  private createCleanText(_query: string, entities: QueryEntity[], keywords: string[]): string {
    // Start with keywords
    let cleanText = keywords.join(' ');
    
    // Add important entity text
    const importantEntities = entities.filter(e => 
      e.confidence > 0.8 && !['url', 'domain'].includes(e.type)
    );
    
    if (importantEntities.length > 0) {
      const entityText = importantEntities.map(e => e.text).join(' ');
      cleanText = `${cleanText} ${entityText}`;
    }
    
    return cleanText.trim();
  }

  /**
   * Extract time filter from query
   */
  private extractTimeFilter(query: string): { start: number; end: number } | null {
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;
    const oneMonth = 30 * oneDay;
    
    const queryLower = query.toLowerCase();
    
    if (queryLower.includes('today')) {
      const startOfDay = new Date();
      startOfDay.setHours(0, 0, 0, 0);
      return { start: startOfDay.getTime(), end: now };
    }
    
    if (queryLower.includes('yesterday')) {
      const yesterday = new Date(now - oneDay);
      yesterday.setHours(0, 0, 0, 0);
      const endOfYesterday = new Date(yesterday.getTime() + oneDay - 1);
      return { start: yesterday.getTime(), end: endOfYesterday.getTime() };
    }
    
    if (queryLower.includes('last week') || queryLower.includes('past week')) {
      return { start: now - oneWeek, end: now };
    }
    
    if (queryLower.includes('last month') || queryLower.includes('past month')) {
      return { start: now - oneMonth, end: now };
    }
    
    if (queryLower.includes('this week')) {
      const startOfWeek = new Date();
      const day = startOfWeek.getDay();
      const diff = startOfWeek.getDate() - day;
      startOfWeek.setDate(diff);
      startOfWeek.setHours(0, 0, 0, 0);
      return { start: startOfWeek.getTime(), end: now };
    }
    
    return null;
  }

  /**
   * Remove time expressions from query text
   */
  private removeTimeExpressions(query: string): string {
    const timePatterns = [
      /\b(today|yesterday|last week|last month|this week|this month)\b/gi,
      /\b(before|after|since|until|during)\s+\w+/gi,
      /\b\d{4}(-\d{2}(-\d{2})?)?/g
    ];
    
    let cleaned = query;
    timePatterns.forEach(pattern => {
      cleaned = cleaned.replace(pattern, ' ');
    });
    
    return cleaned.replace(/\s+/g, ' ').trim();
  }

  /**
   * Expand keywords with synonyms and related terms
   */
  private expandKeywords(keywords: string[]): string[] {
    const synonymMap = new Map([
      ['js', ['javascript']],
      ['ts', ['typescript']],
      ['py', ['python']],
      ['react', ['reactjs']],
      ['vue', ['vuejs']],
      ['css', ['stylesheet', 'styles']],
      ['api', ['endpoint', 'service']],
      ['db', ['database']],
      ['config', ['configuration', 'settings']],
      ['docs', ['documentation']],
      ['tutorial', ['guide', 'howto']],
      ['bug', ['error', 'issue', 'problem']],
      ['fix', ['solution', 'resolve']]
    ]);
    
    const expanded = [...keywords];
    
    keywords.forEach(keyword => {
      const synonyms = synonymMap.get(keyword);
      if (synonyms) {
        expanded.push(...synonyms);
      }
    });
    
    return [...new Set(expanded)]; // Remove duplicates
  }

  /**
   * Remove overlapping entities
   */
  private removeOverlappingEntities(entities: QueryEntity[]): QueryEntity[] {
    const result: QueryEntity[] = [];
    
    for (const entity of entities) {
      const overlaps = result.some(existing => 
        (entity.startIndex >= existing.startIndex && entity.startIndex < existing.endIndex) ||
        (entity.endIndex > existing.startIndex && entity.endIndex <= existing.endIndex)
      );
      
      if (!overlaps) {
        result.push(entity);
      }
    }
    
    return result;
  }

  /**
   * Check if query looks like a URL
   */
  private isUrlLike(query: string): boolean {
    return /^https?:\/\//.test(query) || 
           /\w+\.(com|org|net|io|dev|co)/i.test(query);
  }

  /**
   * Extract basic keywords as fallback
   */
  private extractBasicKeywords(query: string): string[] {
    return query
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length >= 2 && !this.stopWords.has(word))
      .slice(0, 10);
  }

  /**
   * Get empty query structure
   */
  private getEmptyQuery(): ProcessedQuery {
    return {
      originalQuery: '',
      cleanText: '',
      intent: 'search',
      entities: [],
      keywords: [],
      filters: {}
    };
  }

  /**
   * Update processor configuration
   */
  public updateConfig(newConfig: Partial<QueryProcessingConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Add custom technology terms
   */
  public addTechnologyTerms(terms: string[]): void {
    terms.forEach(term => this.technologyTerms.add(term.toLowerCase()));
  }

  /**
   * Add custom stop words
   */
  public addStopWords(words: string[]): void {
    words.forEach(word => this.stopWords.add(word.toLowerCase()));
  }

  /**
   * Get current configuration
   */
  public getConfig(): QueryProcessingConfig {
    return { ...this.config };
  }
}