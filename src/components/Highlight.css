/**
 * Highlight Component Styles
 * 
 * CSS styles for text highlighting components
 */

/* Base highlight styles */
.search-highlight {
  background-color: #ffeb3b;
  color: #000;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

/* Dark mode highlight */
@media (prefers-color-scheme: dark) {
  .search-highlight {
    background-color: #ffc107;
    color: #000;
  }
}

/* Highlight container */
.highlight-container {
  line-height: 1.4;
  word-break: break-word;
}

/* Multi-line highlight container */
.multiline-highlight-container {
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-word;
}

.multiline-highlight-container .highlight-line {
  margin-bottom: 0.25em;
}

.multiline-highlight-container .highlight-line:last-child {
  margin-bottom: 0;
}

/* Highlight with context */
.highlight-with-context {
  line-height: 1.4;
  word-break: break-word;
}

.highlight-with-context .context-snippet {
  display: inline;
}

.highlight-with-context .context-separator {
  color: #666;
  font-style: italic;
  margin: 0 0.25em;
}

/* Dark mode context separator */
@media (prefers-color-scheme: dark) {
  .highlight-with-context .context-separator {
    color: #999;
  }
}

/* Alternative highlight styles for different contexts */
.highlight-title .search-highlight {
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: 600;
}

.highlight-content .search-highlight {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.highlight-url .search-highlight {
  background-color: #e8f5e8;
  color: #388e3c;
  font-family: monospace;
  font-size: 0.9em;
}

/* Dark mode alternative styles */
@media (prefers-color-scheme: dark) {
  .highlight-title .search-highlight {
    background-color: #1976d2;
    color: #fff;
  }

  .highlight-content .search-highlight {
    background-color: #7b1fa2;
    color: #fff;
  }

  .highlight-url .search-highlight {
    background-color: #388e3c;
    color: #fff;
  }
}

/* Subtle highlight for secondary matches */
.search-highlight.secondary {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  background-image: none;
}

@media (prefers-color-scheme: dark) {
  .search-highlight.secondary {
    background-color: #333;
    color: #ccc;
    border-color: #555;
  }
}

/* Animated highlight for new matches */
.search-highlight.animated {
  animation: highlightPulse 0.6s ease-in-out;
}

@keyframes highlightPulse {
  0% {
    background-color: #ffeb3b;
    transform: scale(1);
  }
  50% {
    background-color: #ff9800;
    transform: scale(1.05);
  }
  100% {
    background-color: #ffeb3b;
    transform: scale(1);
  }
}

/* Accessibility improvements */
.search-highlight:focus {
  outline: 2px solid #2196f3;
  outline-offset: 1px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .search-highlight {
    background-color: #000;
    color: #fff;
    border: 2px solid #fff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .search-highlight.animated {
    animation: none;
  }
}

/* Print styles */
@media print {
  .search-highlight {
    background-color: transparent !important;
    color: #000 !important;
    border: 1px solid #000;
    font-weight: bold;
  }
}

/* Compact highlight for small spaces */
.highlight-compact .search-highlight {
  padding: 0 1px;
  font-size: 0.9em;
  border-radius: 1px;
}

/* Large highlight for emphasis */
.highlight-large .search-highlight {
  padding: 2px 4px;
  font-size: 1.1em;
  border-radius: 4px;
  font-weight: 600;
}

/* Gradient highlight effect */
.highlight-gradient .search-highlight {
  background: linear-gradient(135deg, #ffeb3b 0%, #ffc107 100%);
  color: #000;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

@media (prefers-color-scheme: dark) {
  .highlight-gradient .search-highlight {
    background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
  }
}

/* Multiple keyword highlighting with different colors */
.search-highlight.keyword-1 { background-color: #ffeb3b; }
.search-highlight.keyword-2 { background-color: #e1f5fe; color: #0277bd; }
.search-highlight.keyword-3 { background-color: #f3e5f5; color: #7b1fa2; }
.search-highlight.keyword-4 { background-color: #e8f5e8; color: #388e3c; }
.search-highlight.keyword-5 { background-color: #fff3e0; color: #f57c00; }

@media (prefers-color-scheme: dark) {
  .search-highlight.keyword-1 { background-color: #ffc107; color: #000; }
  .search-highlight.keyword-2 { background-color: #0277bd; color: #fff; }
  .search-highlight.keyword-3 { background-color: #7b1fa2; color: #fff; }
  .search-highlight.keyword-4 { background-color: #388e3c; color: #fff; }
  .search-highlight.keyword-5 { background-color: #f57c00; color: #fff; }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .search-highlight {
    padding: 1px;
    font-size: 0.95em;
  }
  
  .highlight-large .search-highlight {
    padding: 1px 2px;
    font-size: 1em;
  }
}

/* Focus and selection states */
.search-highlight:hover {
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.search-highlight::selection {
  background-color: #2196f3;
  color: #fff;
}

/* Truncation ellipsis styling */
.highlight-container .ellipsis,
.multiline-highlight-container .ellipsis {
  color: #666;
  font-style: italic;
  font-weight: normal;
}

@media (prefers-color-scheme: dark) {
  .highlight-container .ellipsis,
  .multiline-highlight-container .ellipsis {
    color: #999;
  }
}
