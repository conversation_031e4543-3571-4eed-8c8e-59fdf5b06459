/**
 * Text Highlighting Component
 * 
 * Highlights search terms in text content with <mark> tags
 */

/**
 * Highlight component props
 */
interface HighlightProps {
  /** Text content to highlight */
  text: string;
  
  /** Search terms to highlight */
  keywords: string[];
  
  /** Additional CSS classes */
  className?: string;
  
  /** Whether to highlight case-sensitively */
  caseSensitive?: boolean;
  
  /** Maximum length of text to display */
  maxLength?: number;
  
  /** Whether to show ellipsis when text is truncated */
  showEllipsis?: boolean;
}

/**
 * Escape special regex characters in a string
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Create a regex pattern for highlighting multiple keywords
 */
function createHighlightPattern(keywords: string[], caseSensitive: boolean = false): RegExp | null {
  if (!keywords || keywords.length === 0) {
    return null;
  }

  // Filter out empty keywords and escape special characters
  const validKeywords = keywords
    .filter(keyword => keyword && keyword.trim().length > 0)
    .map(keyword => escapeRegExp(keyword.trim()));

  if (validKeywords.length === 0) {
    return null;
  }

  // Create pattern that matches any of the keywords
  const pattern = validKeywords.join('|');
  const flags = caseSensitive ? 'g' : 'gi';
  
  return new RegExp(`(${pattern})`, flags);
}

/**
 * Highlight text with search keywords
 */
function highlightText(text: string, keywords: string[], caseSensitive: boolean = false): React.ReactNode[] {
  if (!text || !keywords || keywords.length === 0) {
    return [text];
  }

  const pattern = createHighlightPattern(keywords, caseSensitive);
  if (!pattern) {
    return [text];
  }

  // Split text by matches and create highlighted elements
  const parts = text.split(pattern);
  const result: React.ReactNode[] = [];

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i];
    
    if (i % 2 === 0) {
      // Non-matching part
      if (part) {
        result.push(part);
      }
    } else {
      // Matching part - highlight it
      result.push(
        <mark key={i} className="search-highlight">
          {part}
        </mark>
      );
    }
  }

  return result;
}

/**
 * Truncate text to specified length while preserving word boundaries
 */
function truncateText(text: string, maxLength: number, showEllipsis: boolean = true): string {
  if (!text || text.length <= maxLength) {
    return text;
  }

  // Find the last space before the max length to avoid cutting words
  let truncateAt = maxLength;
  for (let i = maxLength; i >= 0; i--) {
    if (text[i] === ' ') {
      truncateAt = i;
      break;
    }
  }

  const truncated = text.substring(0, truncateAt);
  return showEllipsis ? truncated + '...' : truncated;
}

/**
 * Highlight Component
 * 
 * Renders text with highlighted search terms
 */
export const Highlight: React.FC<HighlightProps> = ({
  text,
  keywords,
  className = '',
  caseSensitive = false,
  maxLength,
  showEllipsis = true
}) => {
  // Handle empty or invalid input
  if (!text || typeof text !== 'string') {
    return <span className={`highlight-container ${className}`}></span>;
  }

  // Truncate text if maxLength is specified
  let displayText = text;
  if (maxLength && maxLength > 0) {
    displayText = truncateText(text, maxLength, showEllipsis);
  }

  // Highlight the text
  const highlightedContent = highlightText(displayText, keywords, caseSensitive);

  return (
    <span className={`highlight-container ${className}`}>
      {highlightedContent}
    </span>
  );
};

/**
 * Multi-line Highlight Component
 * 
 * Handles text with line breaks and preserves formatting
 */
interface MultilineHighlightProps extends HighlightProps {
  /** Whether to preserve line breaks */
  preserveLineBreaks?: boolean;
}

export const MultilineHighlight: React.FC<MultilineHighlightProps> = ({
  text,
  keywords,
  className = '',
  caseSensitive = false,
  maxLength,
  showEllipsis = true,
  preserveLineBreaks = true
}) => {
  // Handle empty or invalid input
  if (!text || typeof text !== 'string') {
    return <div className={className}></div>;
  }

  // Truncate text if maxLength is specified
  let displayText = text;
  if (maxLength && maxLength > 0) {
    displayText = truncateText(text, maxLength, showEllipsis);
  }

  if (!preserveLineBreaks) {
    // Single line mode
    const highlightedContent = highlightText(displayText, keywords, caseSensitive);
    return (
      <div className={`multiline-highlight-container ${className}`}>
        {highlightedContent}
      </div>
    );
  }

  // Multi-line mode - split by line breaks and highlight each line
  const lines = displayText.split(/\r?\n/);
  
  return (
    <div className={`multiline-highlight-container ${className}`}>
      {lines.map((line, index) => (
        <div key={index} className="highlight-line">
          {highlightText(line, keywords, caseSensitive)}
        </div>
      ))}
    </div>
  );
};

/**
 * Smart Highlight Component
 * 
 * Automatically chooses between single-line and multi-line highlighting
 */
export const SmartHighlight: React.FC<HighlightProps> = (props) => {
  const { text } = props;
  
  // Check if text contains line breaks
  const hasLineBreaks = text && text.includes('\n');
  
  if (hasLineBreaks) {
    return <MultilineHighlight {...props} />;
  } else {
    return <Highlight {...props} />;
  }
};

/**
 * Highlight with Context Component
 * 
 * Shows highlighted text with surrounding context
 */
interface HighlightWithContextProps extends HighlightProps {
  /** Number of characters to show before and after matches */
  contextLength?: number;
  
  /** Separator between context snippets */
  separator?: string;
}

export const HighlightWithContext: React.FC<HighlightWithContextProps> = ({
  text,
  keywords,
  className = '',
  caseSensitive = false,
  contextLength = 50,
  separator = ' ... '
}) => {
  // Handle empty or invalid input
  if (!text || typeof text !== 'string' || !keywords || keywords.length === 0) {
    return <span className={className}>{text}</span>;
  }

  const pattern = createHighlightPattern(keywords, caseSensitive);
  if (!pattern) {
    return <span className={className}>{text}</span>;
  }

  // Find all matches with their positions
  const matches: Array<{ start: number; end: number; text: string }> = [];
  let match;
  
  while ((match = pattern.exec(text)) !== null) {
    matches.push({
      start: match.index,
      end: match.index + match[0].length,
      text: match[0]
    });
    
    // Prevent infinite loop on zero-length matches
    if (match.index === pattern.lastIndex) {
      pattern.lastIndex++;
    }
  }

  if (matches.length === 0) {
    return <span className={className}>{text}</span>;
  }

  // Create context snippets around matches
  const snippets: React.ReactNode[] = [];
  
  matches.forEach((match, index) => {
    const contextStart = Math.max(0, match.start - contextLength);
    const contextEnd = Math.min(text.length, match.end + contextLength);
    
    let snippet = text.substring(contextStart, contextEnd);
    
    // Add ellipsis if we're not at the beginning/end
    if (contextStart > 0) snippet = '...' + snippet;
    if (contextEnd < text.length) snippet = snippet + '...';
    
    // Highlight the snippet
    const highlightedSnippet = highlightText(snippet, keywords, caseSensitive);
    
    snippets.push(
      <span key={index} className="context-snippet">
        {highlightedSnippet}
      </span>
    );
    
    // Add separator between snippets (except for the last one)
    if (index < matches.length - 1) {
      snippets.push(
        <span key={`sep-${index}`} className="context-separator">
          {separator}
        </span>
      );
    }
  });

  return (
    <span className={`highlight-with-context ${className}`}>
      {snippets}
    </span>
  );
};
