/**
 * Language Selector Component
 * 
 * Allows users to change the interface language
 */

import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { SUPPORTED_LANGUAGES, changeLanguage, type SupportedLanguage } from '../i18n';

interface LanguageSelectorProps {
  /** Additional CSS class name */
  className?: string;
  /** Show label text */
  showLabel?: boolean;
  /** Compact mode (smaller UI) */
  compact?: boolean;
}

/**
 * Language Selector Component
 */
export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  className = '',
  showLabel = true,
  compact = false
}) => {
  const { t, i18n } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(
    i18n.language as SupportedLanguage
  );
  const [isChanging, setIsChanging] = useState(false);

  // Update current language when i18n language changes
  useEffect(() => {
    const handleLanguageChanged = (lng: string) => {
      setCurrentLanguage(lng as SupportedLanguage);
    };

    i18n.on('languageChanged', handleLanguageChanged);
    
    return () => {
      i18n.off('languageChanged', handleLanguageChanged);
    };
  }, [i18n]);

  /**
   * Handle language change
   */
  const handleLanguageChange = async (event: React.ChangeEvent<HTMLSelectElement>) => {
    const newLanguage = event.target.value as SupportedLanguage;
    
    if (newLanguage === currentLanguage || isChanging) {
      return;
    }

    setIsChanging(true);

    try {
      await changeLanguage(newLanguage);
      setCurrentLanguage(newLanguage);
    } catch (error) {
      console.error('Failed to change language:', error);
      // Revert selection on error
      event.target.value = currentLanguage;
    } finally {
      setIsChanging(false);
    }
  };

  return (
    <div className={`language-selector ${compact ? 'compact' : ''} ${className}`}>
      {showLabel && !compact && (
        <label htmlFor="language-select" className="language-label">
          {t('common.language')}
        </label>
      )}
      
      <div className="language-select-container">
        <select
          id="language-select"
          value={currentLanguage}
          onChange={handleLanguageChange}
          disabled={isChanging}
          className={`language-select ${isChanging ? 'changing' : ''}`}
          title={t('common.language')}
        >
          {Object.entries(SUPPORTED_LANGUAGES).map(([code, name]) => (
            <option key={code} value={code}>
              {name}
            </option>
          ))}
        </select>
        
        {isChanging && (
          <div className="language-loading">
            <span className="loading-spinner" />
          </div>
        )}
      </div>
    </div>
  );
};

// Default styles (can be overridden)
const defaultStyles = `
.language-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.language-selector.compact {
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.language-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 4px;
}

.language-select-container {
  position: relative;
  display: inline-block;
}

.language-select {
  appearance: none;
  background-color: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 32px 8px 12px;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.language-select:hover {
  border-color: #9ca3af;
  background-color: #f9fafb;
}

.language-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.language-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f3f4f6;
}

.language-select.changing {
  background-image: none;
  padding-right: 40px;
}

.language-loading {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Compact mode adjustments */
.language-selector.compact .language-select {
  min-width: 120px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .language-label {
    color: #f3f4f6;
  }
  
  .language-select {
    background-color: #374151;
    border-color: #4b5563;
    color: #f3f4f6;
  }
  
  .language-select:hover {
    border-color: #6b7280;
    background-color: #4b5563;
  }
  
  .language-select:disabled {
    background-color: #2d3748;
  }
}
`;

// Inject default styles if not already present
if (typeof document !== 'undefined' && !document.getElementById('language-selector-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'language-selector-styles';
  styleElement.textContent = defaultStyles;
  document.head.appendChild(styleElement);
}