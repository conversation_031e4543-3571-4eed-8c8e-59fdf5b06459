/**
 * Recall Options App Component
 * 
 * Main component for the Chrome extension options page
 */

import React, { useState, useEffect, useCallback } from 'react';
import { HistoryManagement } from './components/HistoryManagement';
import { BlacklistManagement } from './components/BlacklistManagement';
import { DataBackup } from './components/DataBackup';
import { SearchSettings } from './components/SearchSettings';
import { About } from './components/About';
// APIKeyManager removed - AI functionality disabled
// ReadingAssistant removed - AI functionality disabled
import { LanguageSettings } from './components/LanguageSettings';
import { I18nManager } from '../i18n/I18nManager';
import './components/HistoryManagement.css';
import './components/BlacklistManagement.css';
import './components/DataBackup.css';
import './components/SearchSettings.css';
import './components/About.css';
// APIKeyManager.css removed - AI functionality disabled
// ReadingAssistant.css removed - AI functionality disabled

/**
 * Available option pages
 */
export type OptionPage = 
  | 'history-management'
  | 'blacklist-management' 
  | 'data-backup'
  | 'search-settings'
  | 'language-settings'
  | 'about';

/**
 * Navigation item interface
 */
interface NavigationItem {
  id: OptionPage;
  label: string;
  icon: string;
  description: string;
}

/**
 * Get navigation items with translations
 */
const getNavigationItems = (t: (key: string) => string): NavigationItem[] => [
  // Core Search & Navigation (Most Frequently Used)
  {
    id: 'search-settings',
    label: t('options.navigation.searchSettings.label'),
    icon: '🔍',
    description: t('options.navigation.searchSettings.description')
  },
  {
    id: 'history-management',
    label: t('options.navigation.historyManagement.label'),
    icon: '📚',
    description: t('options.navigation.historyManagement.description')
  },
  // AI & Assistant Features removed - AI functionality disabled
  // Data Management & Security
  {
    id: 'blacklist-management',
    label: t('options.navigation.blacklistManagement.label'),
    icon: '🛡️',
    description: t('options.navigation.blacklistManagement.description')
  },
  {
    id: 'data-backup',
    label: t('options.navigation.dataBackup.label'),
    icon: '💾',
    description: t('options.navigation.dataBackup.description')
  },
  // Settings
  {
    id: 'language-settings',
    label: t('options.navigation.languageSettings.label'),
    icon: '🌐',
    description: t('options.navigation.languageSettings.description')
  },
  // Information
  {
    id: 'about',
    label: t('options.navigation.about.label'),
    icon: 'ℹ️',
    description: t('options.navigation.about.description')
  }
];

/**
 * Options App Component
 */
export const OptionsApp: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<OptionPage>('search-settings');
  const [isLoading, setIsLoading] = useState(true);
  const [focusedIndex, setFocusedIndex] = useState(-1);

  // i18n Management
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string, interpolations?: Record<string, any>) => {
    return i18nManager.getTranslation(key, interpolations);
  }, [i18nManager]);

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  // Initialize the app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize i18n manager and load current language
        await i18nManager.loadLanguageResources(i18nManager.getCurrentLanguage());

        // Simulate initialization delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Get saved page preference from storage
        const result = await (globalThis as any).chrome.storage.local.get(['optionsCurrentPage']);
        if (result.optionsCurrentPage) {
          setCurrentPage(result.optionsCurrentPage);
        }
        
        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize options app:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
    
    // API key navigation events removed - AI functionality disabled
  }, [i18nManager]);

  // Save current page to storage when it changes
  useEffect(() => {
    if (!isLoading) {
      (globalThis as any).chrome.storage.local.set({ optionsCurrentPage: currentPage });
    }
  }, [currentPage, isLoading]);

  /**
   * Handle navigation item click
   */
  const handleNavigationClick = (pageId: OptionPage) => {
    setCurrentPage(pageId);
  };

  // Get translated navigation items (memoized)
  const navigationItems = getNavigationItems(t);

  /**
   * Handle keyboard navigation
   */
  const handleKeyDown = (event: React.KeyboardEvent, index: number) => {
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        const nextIndex = (index + 1) % navigationItems.length;
        setFocusedIndex(nextIndex);
        // Focus the next menu item
        const nextButton = document.querySelector(`[data-nav-index="${nextIndex}"]`) as HTMLButtonElement;
        if (nextButton) {
          nextButton.focus();
        }
        break;
      
      case 'ArrowUp':
        event.preventDefault();
        const prevIndex = index === 0 ? navigationItems.length - 1 : index - 1;
        setFocusedIndex(prevIndex);
        // Focus the previous menu item
        const prevButton = document.querySelector(`[data-nav-index="${prevIndex}"]`) as HTMLButtonElement;
        if (prevButton) {
          prevButton.focus();
        }
        break;
      
      case 'Enter':
      case ' ':
        event.preventDefault();
        handleNavigationClick(navigationItems[index].id);
        break;
    }
  };

  /**
   * Render page content based on current page
   */
  const renderPageContent = () => {
    switch (currentPage) {
      case 'history-management':
        return <HistoryManagement />;

      case 'blacklist-management':
        return <BlacklistManagement />;

      case 'data-backup':
        return <DataBackup />;

      case 'search-settings':
        return <SearchSettings />;

      // api-key-management removed - AI functionality disabled
      // reading-assistant removed - AI functionality disabled

      case 'language-settings':
        return <LanguageSettings />;

      case 'about':
        return <About />;

      default:
        return (
          <div className="page-content">
            <h2>{t('options.pageNotFound')}</h2>
            <p>{t('options.selectOption')}</p>
          </div>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <div className="loading-text">{t('options.loadingConfig')}</div>
      </div>
    );
  }


  return (
    <div className="options-app">
      {/* Header */}
      <header className="options-header">
        <div className="header-content">
          <div className="app-branding">
            <span className="app-icon">🔍</span>
            <h1 className="app-title">Recall</h1>
            <span className="app-subtitle">{t('options.title')}</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="options-main">
        {/* Sidebar Navigation */}
        <nav className="options-sidebar">
          <div className="navigation-list">
            {navigationItems.map((item, index) => {
              const isActive = currentPage === item.id;
              const isFocused = focusedIndex === index;
              const needsSeparator = (index === 1) || (index === 3) || (index === 5) || (index === 6); // After groups
              
              return (
                <React.Fragment key={item.id}>
                  <button
                    className={`nav-item ${isActive ? 'active' : ''} ${isFocused ? 'focused' : ''}`}
                    onClick={() => handleNavigationClick(item.id)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    onFocus={() => setFocusedIndex(index)}
                    title={item.description}
                    aria-current={isActive ? 'page' : undefined}
                    data-nav-index={index}
                    tabIndex={index === 0 || isFocused ? 0 : -1}
                  >
                    <span className="nav-icon">{item.icon}</span>
                    <div className="nav-content">
                      <span className="nav-label">{item.label}</span>
                      <span className="nav-description">{item.description}</span>
                    </div>
                  </button>
                  
                  {/* Add separator after certain groups */}
                  {needsSeparator && (
                    <div className="nav-separator" role="separator" aria-hidden="true"></div>
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </nav>

        {/* Content Area */}
        <main className="options-content">
          {renderPageContent()}
        </main>
      </div>
    </div>
  );
};
