/**
 * Recall Options Page Entry Point
 * 
 * Main entry point for the Chrome extension options page
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import { OptionsApp } from './OptionsApp';
import { I18nManager } from '../i18n/I18nManager';
import './options.css';

// Initialize i18n Manager
const i18nManager = I18nManager.getInstance();

const renderApp = () => {
  const container = document.getElementById('options-root');
  if (!container) {
    throw new Error('Options root element not found');
  }

  const root = createRoot(container);

  // Render the options app
  root.render(
    <React.StrictMode>
      <OptionsApp />
    </React.StrictMode>
  );

  // Log initialization
  console.log('[Recall Options] Options page initialized');
};

// Wait for i18n to be ready before rendering
i18nManager.waitForInitialization().then(() => {
  renderApp();
}).catch(error => {
  console.error("Failed to initialize i18n for options page:", error);
  // Still try to render, it may work with fallback text
  renderApp();
});
