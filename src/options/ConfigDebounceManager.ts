/**
 * Configuration Input Debounce Manager
 * 
 * Manages debouncing for configuration inputs to optimize performance:
 * - Prevents excessive API calls during rapid typing
 * - Batches configuration saves for efficiency  
 * - Provides operation status and memory management
 * - Supports validation debouncing with retry logic
 * 
 * @module ConfigDebounceManager
 * @version 4.0
 * @since 2025-06-24
 */

export interface DebounceConfig {
  inputDebounceMs?: number;
  validationDebounceMs?: number;
  saveDebounceMs?: number;
  maxPendingOperations?: number;
  batchSaveThreshold?: number;
  retryAttempts?: number;
  retryDelayMs?: number;
}

export interface OperationStatus {
  isPending: boolean;
  lastValue: any;
  operationType: 'input' | 'validation' | 'save';
  startTime: number;
  retryCount?: number;
}

export interface MemoryUsage {
  pendingOperations: number;
  activeTimers: number;
  cacheSize: number;
}

interface PendingOperation {
  id: string;
  type: 'input' | 'validation' | 'save';
  field: string;
  value: any;
  callback: Function;
  timerId: NodeJS.Timeout;
  startTime: number;
  retryCount: number;
}

interface BatchedSaveData {
  [key: string]: any;
}

/**
 * Configuration Debounce Manager
 * Handles all debouncing operations for configuration inputs
 */
export class ConfigDebounceManager {
  private config: Required<DebounceConfig>;
  private pendingOperations = new Map<string, PendingOperation>();
  private batchedSaves = new Map<string, any>();
  private saveTimer: NodeJS.Timeout | null = null;
  private saveCallback: ((data: BatchedSaveData) => Promise<void>) | null = null;
  private operationIdCounter = 0;

  constructor(config: DebounceConfig = {}) {
    this.config = {
      inputDebounceMs: 300,
      validationDebounceMs: 500,
      saveDebounceMs: 1000,
      maxPendingOperations: 5,
      batchSaveThreshold: 3,
      retryAttempts: 3,
      retryDelayMs: 1000,
      ...config
    };
  }

  /**
   * Debounce input field changes
   */
  debounceInput(
    field: string, 
    value: any, 
    callback: (field: string, value: any) => void
  ): void {
    this.cancelOperation(field);
    
    // Check if we're exceeding max concurrent operations
    if (this.pendingOperations.size >= this.config.maxPendingOperations) {
      // Cancel oldest operation
      this.cancelOldestOperation();
    }

    const operationId = this.generateOperationId();
    const timerId = setTimeout(() => {
      this.pendingOperations.delete(field);
      try {
        callback(field, value);
      } catch (error) {
        console.error(`Input callback error for field ${field}:`, error);
      }
    }, this.config.inputDebounceMs);

    this.pendingOperations.set(field, {
      id: operationId,
      type: 'input',
      field,
      value,
      callback,
      timerId,
      startTime: Date.now(),
      retryCount: 0
    });
  }

  /**
   * Debounce validation operations
   */
  async debounceValidation(
    field: string, 
    value: any, 
    callback: (value: any) => Promise<any>
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cancelOperation(field);

      const operationId = this.generateOperationId();
      const timerId = setTimeout(async () => {
        this.pendingOperations.delete(field);
        
        try {
          const result = await this.executeWithRetry(callback, value, field);
          resolve(result);
        } catch (error) {
          reject(error);
        }
      }, this.config.validationDebounceMs);

      this.pendingOperations.set(field, {
        id: operationId,
        type: 'validation',
        field,
        value,
        callback,
        timerId,
        startTime: Date.now(),
        retryCount: 0
      });
    });
  }

  /**
   * Debounce save operations with batching
   */
  debounceSave(
    field: string, 
    value: any, 
    callback: (data: BatchedSaveData) => Promise<void>
  ): void {
    // Add to batch
    this.batchedSaves.set(field, value);
    this.saveCallback = callback;

    // Clear existing save timer
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
    }

    // Check if we should force save due to batch threshold
    if (this.batchedSaves.size >= this.config.batchSaveThreshold) {
      this.executeBatchSave();
      return;
    }

    // Set new save timer
    this.saveTimer = setTimeout(() => {
      this.executeBatchSave();
    }, this.config.saveDebounceMs);
  }

  /**
   * Get operation status for a field
   */
  getOperationStatus(field: string): OperationStatus | null {
    const operation = this.pendingOperations.get(field);
    if (!operation) {
      return null;
    }

    return {
      isPending: true,
      lastValue: operation.value,
      operationType: operation.type,
      startTime: operation.startTime,
      retryCount: operation.retryCount
    };
  }

  /**
   * Get count of pending operations
   */
  getPendingOperationsCount(): number {
    return this.pendingOperations.size;
  }

  /**
   * Get memory usage information
   */
  getMemoryUsage(): MemoryUsage {
    return {
      pendingOperations: this.pendingOperations.size,
      activeTimers: this.countActiveTimers(),
      cacheSize: this.batchedSaves.size
    };
  }

  /**
   * Set batch save threshold
   */
  setBatchThreshold(threshold: number): void {
    this.config.batchSaveThreshold = threshold;
  }

  /**
   * Clean up all pending operations and timers
   */
  cleanup(): void {
    // Clear all pending operation timers
    for (const operation of this.pendingOperations.values()) {
      clearTimeout(operation.timerId);
    }
    this.pendingOperations.clear();

    // Clear save timer
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
      this.saveTimer = null;
    }

    // Clear batched saves
    this.batchedSaves.clear();
    this.saveCallback = null;
  }

  /**
   * Cancel operation for specific field
   */
  private cancelOperation(field: string): void {
    const operation = this.pendingOperations.get(field);
    if (operation) {
      clearTimeout(operation.timerId);
      this.pendingOperations.delete(field);
    }
  }

  /**
   * Cancel oldest operation to make room for new ones
   */
  private cancelOldestOperation(): void {
    let oldestField: string | null = null;
    let oldestTime = Date.now();

    for (const [field, operation] of this.pendingOperations) {
      if (operation.startTime < oldestTime) {
        oldestTime = operation.startTime;
        oldestField = field;
      }
    }

    if (oldestField) {
      this.cancelOperation(oldestField);
    }
  }

  /**
   * Execute callback with retry logic
   */
  private async executeWithRetry(
    callback: Function, 
    value: any, 
    _field: string
  ): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        return await callback(value);
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < this.config.retryAttempts) {
          // Wait before retry
          await new Promise(resolve => 
            setTimeout(resolve, this.config.retryDelayMs * attempt)
          );
        }
      }
    }

    throw lastError;
  }

  /**
   * Execute batched save operation
   */
  private async executeBatchSave(): Promise<void> {
    if (this.batchedSaves.size === 0 || !this.saveCallback) {
      return;
    }

    const dataToSave = Object.fromEntries(this.batchedSaves);
    const callback = this.saveCallback;

    // Clear batch and timer
    this.batchedSaves.clear();
    this.saveTimer = null;

    try {
      await callback(dataToSave);
    } catch (error) {
      console.error('Batch save failed:', error);
      
      // Retry save operation
      try {
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelayMs));
        await callback(dataToSave);
      } catch (retryError) {
        console.error('Batch save retry failed:', retryError);
        throw retryError;
      }
    }
  }

  /**
   * Generate unique operation ID
   */
  private generateOperationId(): string {
    return `op_${++this.operationIdCounter}_${Date.now()}`;
  }

  /**
   * Count active timers
   */
  private countActiveTimers(): number {
    let count = this.pendingOperations.size;
    if (this.saveTimer) {
      count++;
    }
    return count;
  }
}

// Export singleton instance for easy use
export const configDebounceManager = new ConfigDebounceManager();