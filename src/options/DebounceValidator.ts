/**
 * Debounce Validator
 * 
 * Handles validation of configuration inputs with debouncing:
 * - API key validation with retry logic
 * - URL and endpoint validation
 * - Configuration format validation
 * - Caching of validation results
 * - Timeout handling for slow validations
 * 
 * @module DebounceValidator  
 * @version 4.0
 * @since 2025-06-24
 */

export interface ValidationResult {
  valid: boolean;
  message?: string;
  error?: string;
  details?: any;
}

export interface ValidationConfig {
  debounceMs?: number;
  validationTimeout?: number;
  retryAttempts?: number;
  retryDelayMs?: number;
  cacheResults?: boolean;
  cacheTTL?: number;
}

interface CachedValidation {
  result: ValidationResult;
  timestamp: number;
  ttl: number;
}

interface PendingValidation {
  field: string;
  value: any;
  callback: (value: any) => Promise<ValidationResult>;
  resolve: (result: ValidationResult) => void;
  reject: (error: Error) => void;
  timerId: NodeJS.Timeout;
  timeoutId: NodeJS.Timeout | null;
  retryCount: number;
}

/**
 * Debounce Validator for configuration inputs
 */
export class DebounceValidator {
  private config: Required<ValidationConfig>;
  private pendingValidations = new Map<string, PendingValidation>();
  private validationCache = new Map<string, CachedValidation>();
  private cleanupInterval: NodeJS.Timeout;

  constructor(config: ValidationConfig = {}) {
    this.config = {
      debounceMs: 400,
      validationTimeout: 2000,
      retryAttempts: 3,
      retryDelayMs: 1000,
      cacheResults: true,
      cacheTTL: 300000, // 5 minutes
      ...config
    };

    // Clean up expired cache entries every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredCache();
    }, 60000);
  }

  /**
   * Validate field with debouncing
   */
  async validateField(
    field: string,
    value: any,
    validationFn: (value: any) => Promise<ValidationResult>
  ): Promise<ValidationResult> {
    // Check cache first
    if (this.config.cacheResults) {
      const cached = this.getCachedResult(field, value);
      if (cached) {
        return cached;
      }
    }

    // Cancel any existing validation for this field
    this.cancelValidation(field);

    return new Promise<ValidationResult>((resolve, reject) => {
      const timerId = setTimeout(async () => {
        const validation = this.pendingValidations.get(field);
        if (!validation) return;

        try {
          const result = await this.executeValidationWithRetry(
            validation.callback,
            validation.value,
            validation.retryCount
          );

          // Cache successful results
          if (this.config.cacheResults && result.valid) {
            this.cacheResult(field, value, result);
          }

          this.pendingValidations.delete(field);
          resolve(result);
        } catch (error) {
          this.pendingValidations.delete(field);
          reject(error);
        }
      }, this.config.debounceMs);

      // Set up timeout for validation
      const timeoutId = setTimeout(() => {
        const validation = this.pendingValidations.get(field);
        if (validation) {
          clearTimeout(validation.timerId);
          this.pendingValidations.delete(field);
          resolve({
            valid: false,
            error: 'Validation timeout',
            message: 'Validation took too long to complete'
          });
        }
      }, this.config.validationTimeout);

      this.pendingValidations.set(field, {
        field,
        value,
        callback: validationFn,
        resolve,
        reject,
        timerId,
        timeoutId,
        retryCount: 0
      });
    });
  }

  /**
   * Validate API key format and connectivity
   */
  async validateApiKey(
    provider: string,
    apiKey: string
  ): Promise<ValidationResult> {
    const validationFn = async (key: string): Promise<ValidationResult> => {
      // Basic format validation first
      const formatValidation = this.validateApiKeyFormat(provider, key);
      if (!formatValidation.valid) {
        return formatValidation;
      }

      // Test connectivity if format is valid
      return this.testApiKeyConnectivity(provider, key);
    };

    return this.validateField(`${provider}-api-key`, apiKey, validationFn);
  }

  /**
   * Validate URL endpoint
   */
  async validateEndpoint(
    field: string,
    url: string
  ): Promise<ValidationResult> {
    const validationFn = async (endpoint: string): Promise<ValidationResult> => {
      try {
        const urlObj = new URL(endpoint);
        
        // Check if it's HTTPS for production
        if (urlObj.protocol !== 'https:' && !urlObj.hostname.includes('localhost')) {
          return {
            valid: false,
            message: 'Endpoint must use HTTPS for security'
          };
        }

        // Test connectivity
        await fetch(endpoint, {
          method: 'HEAD',
          mode: 'no-cors',
          signal: AbortSignal.timeout(this.config.validationTimeout / 2)
        });

        return {
          valid: true,
          message: 'Endpoint is accessible'
        };
      } catch (error) {
        if (error instanceof TypeError && error.message.includes('Invalid URL')) {
          return {
            valid: false,
            message: 'Invalid URL format'
          };
        }

        return {
          valid: false,
          message: 'Unable to connect to endpoint',
          error: (error as Error).message
        };
      }
    };

    return this.validateField(field, url, validationFn);
  }

  /**
   * Validate JSON configuration
   */
  async validateJsonConfig(
    field: string,
    jsonString: string
  ): Promise<ValidationResult> {
    const validationFn = async (json: string): Promise<ValidationResult> => {
      try {
        const parsed = JSON.parse(json);
        
        // Basic structure validation
        if (typeof parsed !== 'object' || parsed === null) {
          return {
            valid: false,
            message: 'Configuration must be a valid JSON object'
          };
        }

        return {
          valid: true,
          message: 'Valid JSON configuration',
          details: parsed
        };
      } catch (error) {
        return {
          valid: false,
          message: 'Invalid JSON format',
          error: (error as Error).message
        };
      }
    };

    return this.validateField(field, jsonString, validationFn);
  }

  /**
   * Get validation status for a field
   */
  getValidationStatus(field: string): {
    isPending: boolean;
    hasCache: boolean;
  } {
    return {
      isPending: this.pendingValidations.has(field),
      hasCache: Array.from(this.validationCache.keys()).some(key => 
        key.startsWith(`${field}:`))
    };
  }

  /**
   * Clear validation cache
   */
  clearCache(): void {
    this.validationCache.clear();
  }

  /**
   * Clear validation cache for specific field
   */
  clearFieldCache(field: string): void {
    const keysToDelete = Array.from(this.validationCache.keys())
      .filter(key => key.startsWith(`${field}:`));
    
    keysToDelete.forEach(key => this.validationCache.delete(key));
  }

  /**
   * Cancel validation for specific field
   */
  cancelValidation(field: string): void {
    const validation = this.pendingValidations.get(field);
    if (validation) {
      clearTimeout(validation.timerId);
      if (validation.timeoutId) {
        clearTimeout(validation.timeoutId);
      }
      this.pendingValidations.delete(field);
    }
  }

  /**
   * Clean up all pending validations and timers
   */
  cleanup(): void {
    // Cancel all pending validations
    for (const validation of this.pendingValidations.values()) {
      clearTimeout(validation.timerId);
      if (validation.timeoutId) {
        clearTimeout(validation.timeoutId);
      }
    }
    this.pendingValidations.clear();

    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Clear cache
    this.validationCache.clear();
  }

  /**
   * Validate API key format based on provider
   */
  private validateApiKeyFormat(provider: string, apiKey: string): ValidationResult {
    if (!apiKey || typeof apiKey !== 'string') {
      return {
        valid: false,
        message: 'API key is required'
      };
    }

    switch (provider.toLowerCase()) {
      case 'openai':
        if (!apiKey.startsWith('sk-') || apiKey.length < 20) {
          return {
            valid: false,
            message: 'OpenAI API key must start with "sk-" and be at least 20 characters'
          };
        }
        break;

      case 'anthropic':
        if (!apiKey.startsWith('sk-ant-') || apiKey.length < 30) {
          return {
            valid: false,
            message: 'Anthropic API key must start with "sk-ant-" and be at least 30 characters'
          };
        }
        break;

      case 'google':
        if (apiKey.length < 32) {
          return {
            valid: false,
            message: 'Google API key must be at least 32 characters'
          };
        }
        break;

      case 'deepseek':
        if (!apiKey.startsWith('sk-') || apiKey.length < 40) {
          return {
            valid: false,
            message: 'DeepSeek API key must start with "sk-" and be at least 40 characters'
          };
        }
        break;

      default:
        if (apiKey.length < 10) {
          return {
            valid: false,
            message: 'API key must be at least 10 characters'
          };
        }
    }

    return {
      valid: true,
      message: 'API key format is valid'
    };
  }

  /**
   * Test API key connectivity
   */
  private async testApiKeyConnectivity(
    provider: string,
    apiKey: string
  ): Promise<ValidationResult> {
    // For now, just simulate connectivity test
    // In real implementation, this would make actual API calls
    await new Promise(resolve => setTimeout(resolve, 100));

    // Mock validation logic
    if (apiKey.includes('invalid')) {
      return {
        valid: false,
        message: 'API key authentication failed'
      };
    }

    return {
      valid: true,
      message: `${provider} API key is valid and authenticated`
    };
  }

  /**
   * Execute validation with retry logic
   */
  private async executeValidationWithRetry(
    validationFn: (value: any) => Promise<ValidationResult>,
    value: any,
    currentRetry: number = 0
  ): Promise<ValidationResult> {
    try {
      return await validationFn(value);
    } catch (error) {
      if (currentRetry < this.config.retryAttempts - 1) {
        // Wait before retry
        await new Promise(resolve => 
          setTimeout(resolve, this.config.retryDelayMs * (currentRetry + 1))
        );
        
        return this.executeValidationWithRetry(validationFn, value, currentRetry + 1);
      }

      // Max retries reached
      return {
        valid: false,
        error: (error as Error).message,
        message: 'Validation failed after multiple attempts'
      };
    }
  }

  /**
   * Get cached validation result
   */
  private getCachedResult(field: string, value: any): ValidationResult | null {
    const cacheKey = this.generateCacheKey(field, value);
    const cached = this.validationCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.result;
    }

    // Remove expired cache entry
    if (cached) {
      this.validationCache.delete(cacheKey);
    }

    return null;
  }

  /**
   * Cache validation result
   */
  private cacheResult(field: string, value: any, result: ValidationResult): void {
    const cacheKey = this.generateCacheKey(field, value);
    this.validationCache.set(cacheKey, {
      result,
      timestamp: Date.now(),
      ttl: this.config.cacheTTL
    });
  }

  /**
   * Generate cache key for field and value
   */
  private generateCacheKey(field: string, value: any): string {
    const valueHash = typeof value === 'string' 
      ? value.slice(0, 20) + (value.length > 20 ? '...' : '')
      : JSON.stringify(value).slice(0, 50);
    
    return `${field}:${valueHash}`;
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, cached] of this.validationCache) {
      if (now - cached.timestamp >= cached.ttl) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.validationCache.delete(key));
  }
}

// Export singleton instance for easy use
export const debounceValidator = new DebounceValidator();