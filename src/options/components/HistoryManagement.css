/**
 * History Management Component Styles
 */

/* Main container */
.history-management {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: #333;
  background-color: #f9f9f9;
  padding: 16px; /* Reduced from 20px to 16px for more space utilization */
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Ensure proper flex behavior */
  overflow: hidden; /* Prevent container overflow */
}

/* Header section */
.history-header {
  padding-bottom: 12px; /* Reduced from 15px to 12px */
  border-bottom: 2px solid #eee;
  margin-bottom: 12px; /* Reduced from 15px to 12px */
  flex-shrink: 0; /* Prevent header from shrinking */
}

.history-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.history-title-section h2 {
  margin: 0;
  font-size: 24px;
  color: #2c3e50;
}

.history-stats {
  display: flex;
  gap: 15px;
  font-size: 13px;
  color: #555;
}

.history-stats span {
  background-color: #ecf0f1;
  padding: 5px 10px;
  border-radius: 12px;
}

/* Controls section (search and sort) */
.history-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section {
  flex-grow: 1;
  margin-right: 20px;
}

.history-search {
  width: 100%;
  padding: 10px 15px;
  border-radius: 6px;
  border: 1px solid #ddd;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.history-search:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.sort-section {
  display: flex;
  align-items: center;
}

.sort-label {
  margin-right: 8px;
  font-size: 14px;
  color: #555;
}

.sort-select {
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ddd;
  background-color: white;
  font-size: 14px;
}

/* List container */
.history-list-container {
  flex-grow: 1;
  position: relative;
  min-height: 0; /* Important for flex child to properly size */
  display: flex;
  flex-direction: column;
}

.history-virtual-list {
  scrollbar-width: thin;
  scrollbar-color: #ccc #f1f1f1;
}

.history-virtual-list::-webkit-scrollbar {
  width: 8px;
}

.history-virtual-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.history-virtual-list::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

/* Individual History Item */
.history-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 10px; /* Reduced from 15px to 12px for more compact layout */
  border-bottom: 1px solid #eee;
  background-color: #fff;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.history-item:hover {
  background-color: #f5faff;
}

.history-item.loading {
  background-color: #fff;
}

.history-item-favicon {
  width: 32px;
  height: 32px;
  margin-right: 15px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-item-favicon img {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.history-item-main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 3px; /* Reduced from 4px to 3px for more compact layout */
  min-width: 0; /* Prevents text overflow issues in flexbox */
}

.history-item-title {
  font-size: 15px;
  font-weight: 600;
  color: #34495e;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-url {
  font-size: 12px;
  color: #2980b9;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-content {
  font-size: 13px;
  color: #555;
  line-height: 1.4;
  /* Multi-line clamp */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;  
  overflow: hidden;
  text-overflow: ellipsis;
}

.highlight-title, .highlight-url, .highlight-content {
  mark {
    background-color: #f1c40f;
    color: #333;
    padding: 1px 0;
  }
}

.history-item-aside {
  flex-shrink: 0;
  width: 120px;
  margin-left: 15px;
  text-align: right;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.history-meta {
  font-size: 12px;
  color: #7f8c8d;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px; /* Reduced from 6px to 4px */
  margin-bottom: 6px; /* Reduced from 10px to 6px */
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.history-actions {
  display: flex;
  justify-content: flex-end;
}

.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #95a5a6;
  padding: 5px;
  border-radius: 50%;
  transition: color 0.2s, background-color 0.2s;
}

.delete-btn:hover {
  color: #e74c3c;
  background-color: #fce8e6;
}

/* Loading and Empty States */
.loading-state, .empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #777;
}

.loading-spinner-large {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .history-management {
    background: #1a1a1a;
    color: #e0e0e0;
  }

  .history-stats span {
    background: #333;
    color: #ccc;
  }

  .history-search,
  .sort-select {
    background: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
  }

  .history-search:focus,
  .sort-select:focus {
    border-color: #64b5f6;
  }

  .history-list-container {
    border-color: #444;
  }

  .history-virtual-list {
    background: #1a1a1a;
  }

  .history-item {
    border-bottom-color: #333;
  }

  .history-item:hover {
    background-color: #2a2a2a;
  }

  .history-item-title {
    color: #e0e0e0;
  }

  .history-item-url {
    color: #aaa;
  }

  .history-item-content {
    color: #bbb;
  }

  .history-meta {
    color: #888;
  }

  .loading-line {
    background: linear-gradient(90deg, #333 25%, #444 50%, #333 75%);
    background-size: 200% 100%;
  }

  .empty-state h3 {
    color: #e0e0e0;
  }

  .empty-state p {
    color: #aaa;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .history-management {
    padding: 1rem;
  }

  .history-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .history-controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .search-section {
    min-width: auto;
  }

  .history-stats {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .history-item {
    padding: 0.75rem;
  }

  .history-header {
    flex-direction: column;
    gap: 0.5rem;
  }

  .history-actions {
    margin-top: 0.25rem;
  }

  .history-meta {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner-large,
  .loading-line {
    animation: none;
  }

  .history-item,
  .delete-btn {
    transition: none;
  }
}

/* Pagination Styles */
.history-pagination {
  background: #fff;
  border-top: 1px solid #eee;
  padding: 16px;
  margin-top: auto;
  flex-shrink: 0;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.pagination-summary {
  color: #666;
  font-weight: 500;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-selector label {
  color: #555;
  font-size: 13px;
}

.page-size-select {
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  font-size: 13px;
  min-width: 60px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-btn:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #bbb;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f9f9f9;
}

.pagination-pages {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-page {
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-page:hover {
  background: #f5f5f5;
  border-color: #bbb;
}

.pagination-page.active {
  background: #3498db;
  border-color: #3498db;
  color: white;
  font-weight: 600;
}

.pagination-ellipsis {
  padding: 0 8px;
  color: #999;
  font-weight: 500;
}

.page-jump {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 12px;
}

.page-jump-input {
  width: 60px;
  padding: 6px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  text-align: center;
}

.page-jump-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.page-jump-btn {
  padding: 6px 12px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.page-jump-btn:hover {
  background: #2980b9;
}

/* Dark Mode Support for Pagination */
@media (prefers-color-scheme: dark) {
  .history-pagination {
    background: #1a1a1a;
    border-top-color: #333;
  }

  .pagination-summary {
    color: #ccc;
  }

  .page-size-selector label {
    color: #bbb;
  }

  .page-size-select,
  .pagination-btn,
  .pagination-page,
  .page-jump-input {
    background: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
  }

  .pagination-btn:hover:not(:disabled),
  .pagination-page:hover {
    background: #333;
    border-color: #555;
  }

  .pagination-btn:disabled {
    background: #222;
    color: #666;
  }

  .pagination-page.active {
    background: #64b5f6;
    border-color: #64b5f6;
    color: #000;
  }

  .pagination-ellipsis {
    color: #666;
  }

  .page-jump-input:focus {
    border-color: #64b5f6;
    box-shadow: 0 0 0 2px rgba(100, 181, 246, 0.2);
  }

  .page-jump-btn {
    background: #64b5f6;
    color: #000;
  }

  .page-jump-btn:hover {
    background: #42a5f5;
  }
}

/* Responsive Design for Pagination */
@media (max-width: 768px) {
  .history-pagination {
    padding: 12px;
  }

  .pagination-info {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 12px;
  }

  .pagination-pages {
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-jump {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .pagination-controls {
    gap: 8px;
  }

  .pagination-btn {
    padding: 6px 8px;
    font-size: 13px;
  }

  .pagination-page {
    min-width: 28px;
    height: 28px;
    font-size: 13px;
  }

  .page-jump-input {
    width: 50px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .history-item {
    border: 2px solid;
  }

  .history-search,
  .sort-select {
    border-width: 3px;
  }

  .search-highlight {
    border: 2px solid;
    font-weight: bold;
  }

  .pagination-btn,
  .pagination-page,
  .page-jump-input,
  .page-jump-btn {
    border-width: 2px;
  }

  .pagination-page.active {
    border-width: 3px;
  }
}
