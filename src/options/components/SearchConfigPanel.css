/**
 * Search Configuration Panel Styles
 */

.search-config-panel {
  max-width: 600px;
  margin: 0 auto;
}

.search-config-panel.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  flex-direction: column;
  gap: 16px;
}

.search-config-panel .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.search-config-panel .loading-text {
  color: #6c757d;
  font-size: 14px;
}

/* Panel Header */
.panel-header {
  margin-bottom: 24px;
}

.panel-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.panel-header p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

/* Messages */
.search-config-panel .message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 14px;
}

.search-config-panel .message.success {
  background-color: #d1e7dd;
  color: #0f5132;
  border: 1px solid #badbcc;
}

.search-config-panel .message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c2c7;
}

.search-config-panel .message button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  line-height: 1;
}

.search-config-panel .message.success button {
  color: #0f5132;
}

.search-config-panel .message.error button {
  color: #721c24;
}

.search-config-panel .message button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Engine Configs */
.engine-configs {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.engine-config {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: box-shadow 0.2s ease;
}

.engine-config:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.engine-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.engine-info {
  flex: 1;
  min-width: 0;
}

.engine-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.always-show-badge {
  font-size: 11px;
  padding: 2px 8px;
  background-color: #dbeafe;
  color: #1e40af;
  border-radius: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.engine-description {
  display: block;
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
  flex-shrink: 0;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .3s;
  border-radius: 12px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  top: 3px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #667eea;
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

input:disabled + .toggle-slider {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Weight Control */
.weight-control {
  margin-left: 64px; /* Align with engine info */
}

.weight-control label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 8px;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.weight-slider {
  flex: 1;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.weight-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.weight-slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.weight-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.weight-slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.weight-slider:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.weight-slider:disabled::-webkit-slider-thumb {
  cursor: not-allowed;
}

.weight-slider:disabled::-moz-range-thumb {
  cursor: not-allowed;
}

.weight-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  min-width: 45px;
  text-align: right;
}

/* Weight Summary */
.weight-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 24px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.summary-value {
  font-size: 18px;
  font-weight: 700;
  transition: color 0.2s ease;
}

.summary-value.valid {
  color: #10b981;
}

.summary-value.invalid {
  color: #ef4444;
}

/* Panel Footer */
.panel-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

.save-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.saving-indicator {
  font-size: 13px;
  color: #667eea;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.saving-indicator::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-outline {
  background-color: transparent;
  color: #667eea;
  border: 1px solid #667eea;
}

.btn-outline:hover:not(:disabled) {
  background-color: #667eea;
  color: white;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .panel-header h3,
  .engine-info h4,
  .weight-value,
  .summary-value {
    color: #f3f4f6;
  }
  
  .panel-header p,
  .engine-description,
  .weight-control label,
  .summary-label {
    color: #9ca3af;
  }
  
  .engine-config {
    background: #1f2937;
    border-color: #374151;
  }
  
  .weight-summary {
    background-color: #111827;
    border-color: #374151;
  }
  
  .panel-footer {
    border-top-color: #374151;
  }
  
  .weight-slider {
    background: #374151;
  }
  
  .always-show-badge {
    background-color: #1e3a8a;
    color: #93bbfc;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .engine-config {
    padding: 16px;
  }
  
  .engine-header {
    gap: 12px;
  }
  
  .weight-control {
    margin-left: 0;
    margin-top: 16px;
  }
  
  .panel-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}