/**
 * About Component
 * 
 * Displays rich information about the Recall extension,
 * drawing from project documentation to provide a comprehensive overview.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { I18nManager } from '../../i18n/I18nManager';
import './About.css';

interface AboutProps {
  className?: string;
}

export const About: React.FC<AboutProps> = ({ className = '' }) => {
  // i18n Management
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string, interpolations?: Record<string, any>) => {
    return i18nManager.getTranslation(key, interpolations);
  }, [i18nManager]);

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  return (
    <div className={`about-page ${className}`}>
      <header className="about-header">
        <img src="/icons/icon-128.png" alt="Recall Logo" className="logo" />
        <h1>{t('options.about.title')}</h1>
        <p className="version">{t('options.about.version', { version: '2.0.0' })}</p>
        <p className="tagline">
          {t('options.about.tagline')}
        </p>
      </header>

      <main className="about-main">
        <section className="about-section features">
          <h2>{t('options.about.features.title')}</h2>
          <div className="feature-list">
            <div className="feature-item">
              <span>🔍</span>
              <h3>{t('options.about.features.items.fullTextSearch.title')}</h3>
              <p>{t('options.about.features.items.fullTextSearch.description')}</p>
            </div>
            <div className="feature-item">
              <span>⚡</span>
              <h3>{t('options.about.features.items.fastResponse.title')}</h3>
              <p>{t('options.about.features.items.fastResponse.description')}</p>
            </div>
            <div className="feature-item">
              <span>🛡️</span>
              <h3>{t('options.about.features.items.privacyFirst.title')}</h3>
              <p>{t('options.about.features.items.privacyFirst.description')}</p>
            </div>
            <div className="feature-item">
              <span>🎯</span>
              <h3>{t('options.about.features.items.smartMatching.title')}</h3>
              <p>{t('options.about.features.items.smartMatching.description')}</p>
            </div>
          </div>
        </section>

        <section className="about-section privacy">
            <h2>{t('options.about.privacy.title')}</h2>
            <p>
              {t('options.about.privacy.description')}
            </p>
        </section>

        <section className="about-section links">
          <h2>{t('options.about.links.title')}</h2>
          <ul>
            <li><a href="https://github.com/penwyp/Recall" target="_blank" rel="noopener noreferrer">{t('options.about.links.items.github')}</a></li>
            <li><a href="#" target="_blank" rel="noopener noreferrer">{t('options.about.links.items.guide')}</a></li>
            <li><a href="#" target="_blank" rel="noopener noreferrer">{t('options.about.links.items.review')}</a></li>
          </ul>
        </section>

      </main>

      <footer className="about-footer">
        <p className="acknowledgements" dangerouslySetInnerHTML={{
          __html: t('options.about.acknowledgements', {
            readability: '<a href="https://github.com/mozilla/readability" target="_blank" rel="noopener noreferrer">Readability.js</a>',
            fusejs: '<a href="https://fusejs.io/" target="_blank" rel="noopener noreferrer">Fuse.js</a>'
          })
        }} />
        <p>{t('options.about.copyright')}</p>
      </footer>
    </div>
  );
};