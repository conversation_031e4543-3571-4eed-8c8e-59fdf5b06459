/**
 * Configuration Input Field Component
 * 
 * React component that provides debounced input with validation:
 * - Debounced input changes to prevent excessive API calls
 * - Real-time validation feedback with loading states
 * - Error handling and retry mechanisms
 * - Accessible design with proper ARIA attributes
 * - Visual feedback for validation states
 * 
 * @module ConfigInputField
 * @version 4.0
 * @since 2025-06-24
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { ConfigDebounceManager } from '../ConfigDebounceManager';
import { DebounceValidator } from '../DebounceValidator';
import type { ValidationResult } from '../DebounceValidator';

export interface ConfigInputFieldProps {
  id: string;
  label: string;
  value: string;
  onChange: (id: string, value: string) => void;
  onValidationChange?: (id: string, result: ValidationResult) => void;
  type?: 'text' | 'password' | 'url' | 'email';
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  debounceMs?: number;
  validationFn?: (value: string) => Promise<ValidationResult>;
  helpText?: string;
  autoComplete?: string;
  className?: string;
}

interface ValidationState {
  isValidating: boolean;
  result: ValidationResult | null;
  error: string | null;
}

/**
 * Configuration Input Field with debouncing and validation
 */
export const ConfigInputField: React.FC<ConfigInputFieldProps> = ({
  id,
  label,
  value,
  onChange,
  onValidationChange,
  type = 'text',
  placeholder,
  disabled = false,
  required = false,
  debounceMs = 300,
  validationFn,
  helpText,
  autoComplete,
  className = ''
}) => {
  const [localValue, setLocalValue] = useState(value);
  const [validationState, setValidationState] = useState<ValidationState>({
    isValidating: false,
    result: null,
    error: null
  });

  const debounceManagerRef = useRef<ConfigDebounceManager>(new ConfigDebounceManager());
  const validatorRef = useRef<DebounceValidator>(new DebounceValidator());
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize debounce manager and validator
  useEffect(() => {
    debounceManagerRef.current = new ConfigDebounceManager({
      inputDebounceMs: debounceMs,
      validationDebounceMs: debounceMs + 200
    });

    validatorRef.current = new DebounceValidator({
      debounceMs: debounceMs + 200
    });

    return () => {
      debounceManagerRef.current?.cleanup();
      validatorRef.current?.cleanup();
    };
  }, [debounceMs]);

  // Sync external value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  // Handle input changes with debouncing
  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setLocalValue(newValue);

    // Debounce the onChange callback
    debounceManagerRef.current?.debounceInput(id, newValue, (fieldId, debouncedValue) => {
      onChange(fieldId, debouncedValue);
      
      // Trigger validation if validation function is provided
      if (validationFn && debouncedValue.trim()) {
        handleValidation(debouncedValue);
      } else if (!debouncedValue.trim()) {
        // Clear validation state for empty values
        setValidationState({
          isValidating: false,
          result: null,
          error: null
        });
        onValidationChange?.(id, { valid: true });
      }
    });
  }, [id, onChange, validationFn, onValidationChange]);

  // Handle validation with debouncing
  const handleValidation = useCallback(async (valueToValidate: string) => {
    if (!validationFn || !validatorRef.current) return;

    setValidationState(prev => ({
      ...prev,
      isValidating: true,
      error: null
    }));

    try {
      const result = await validatorRef.current.validateField(
        id,
        valueToValidate,
        validationFn
      );

      setValidationState({
        isValidating: false,
        result,
        error: null
      });

      onValidationChange?.(id, result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Validation failed';
      
      setValidationState({
        isValidating: false,
        result: null,
        error: errorMessage
      });

      onValidationChange?.(id, {
        valid: false,
        error: errorMessage
      });
    }
  }, [id, validationFn, onValidationChange]);

  // Handle blur event for final validation
  const handleBlur = useCallback(() => {
    if (validationFn && localValue.trim()) {
      handleValidation(localValue);
    }
  }, [validationFn, localValue, handleValidation]);

  // Determine validation status for styling
  const getValidationStatus = (): 'none' | 'validating' | 'valid' | 'invalid' => {
    if (validationState.isValidating) return 'validating';
    if (validationState.error) return 'invalid';
    if (validationState.result?.valid === false) return 'invalid';
    if (validationState.result?.valid === true) return 'valid';
    return 'none';
  };

  const validationStatus = getValidationStatus();

  // Get validation message to display
  const getValidationMessage = (): string => {
    if (validationState.isValidating) return 'Validating...';
    if (validationState.error) return validationState.error;
    if (validationState.result?.message) return validationState.result.message;
    return '';
  };

  const validationMessage = getValidationMessage();

  return (
    <div className={`config-input-field ${className}`}>
      <label 
        htmlFor={id}
        className="config-input-label"
      >
        {label}
        {required && <span className="required-indicator" aria-label="required">*</span>}
      </label>

      <div className="config-input-wrapper">
        <input
          ref={inputRef}
          id={id}
          type={type}
          value={localValue}
          onChange={handleInputChange}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          autoComplete={autoComplete}
          className={`config-input ${validationStatus}`}
          aria-describedby={`${id}-help ${id}-validation`}
          aria-invalid={validationStatus === 'invalid'}
        />

        {/* Validation status indicator */}
        <div className={`validation-indicator ${validationStatus}`}>
          {validationStatus === 'validating' && (
            <span className="spinner" aria-label="Validating">⟳</span>
          )}
          {validationStatus === 'valid' && (
            <span className="success-icon" aria-label="Valid">✓</span>
          )}
          {validationStatus === 'invalid' && (
            <span className="error-icon" aria-label="Invalid">⚠</span>
          )}
        </div>
      </div>

      {/* Help text */}
      {helpText && (
        <div id={`${id}-help`} className="config-input-help">
          {helpText}
        </div>
      )}

      {/* Validation message */}
      {validationMessage && (
        <div 
          id={`${id}-validation`} 
          className={`config-input-validation ${validationStatus}`}
          role="alert"
          aria-live="polite"
        >
          {validationMessage}
        </div>
      )}

      <style>{`
        .config-input-field {
          margin-bottom: 1rem;
        }

        .config-input-label {
          display: block;
          font-weight: 500;
          margin-bottom: 0.5rem;
          color: #374151;
        }

        .required-indicator {
          color: #ef4444;
          margin-left: 0.25rem;
        }

        .config-input-wrapper {
          position: relative;
          display: flex;
          align-items: center;
        }

        .config-input {
          width: 100%;
          padding: 0.75rem 2.5rem 0.75rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s ease-in-out;
        }

        .config-input:focus {
          outline: none;
          border-color: #3b82f6;
          box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .config-input.validating {
          border-color: #f59e0b;
        }

        .config-input.valid {
          border-color: #10b981;
        }

        .config-input.invalid {
          border-color: #ef4444;
        }

        .config-input:disabled {
          background-color: #f9fafb;
          color: #6b7280;
          cursor: not-allowed;
        }

        .validation-indicator {
          position: absolute;
          right: 0.75rem;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 1.5rem;
          height: 1.5rem;
        }

        .spinner {
          animation: spin 1s linear infinite;
          color: #f59e0b;
        }

        .success-icon {
          color: #10b981;
        }

        .error-icon {
          color: #ef4444;
        }

        .config-input-help {
          margin-top: 0.25rem;
          font-size: 0.75rem;
          color: #6b7280;
        }

        .config-input-validation {
          margin-top: 0.25rem;
          font-size: 0.75rem;
          padding: 0.25rem 0;
        }

        .config-input-validation.validating {
          color: #f59e0b;
        }

        .config-input-validation.valid {
          color: #10b981;
        }

        .config-input-validation.invalid {
          color: #ef4444;
        }

        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
          .config-input-label {
            color: #f3f4f6;
          }

          .config-input {
            background-color: #1f2937;
            border-color: #4b5563;
            color: #f3f4f6;
          }

          .config-input:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
          }

          .config-input:disabled {
            background-color: #111827;
            color: #9ca3af;
          }

          .config-input-help {
            color: #9ca3af;
          }
        }
      `}</style>
    </div>
  );
};

export default ConfigInputField;