/**
 * History Management Component
 * 
 * Provides a virtual scrolling interface for managing browsing history
 */

import { useState, useEffect, useCallback, useMemo, useRef, type CSSProperties, type FC, type MouseEvent } from 'react';
import { FixedSizeList as List } from 'react-window';
import { dbService, hybridSearchService } from '../../services';
import { parseQuery } from '../../services/query-parser';
import { Highlight } from '../../components/Highlight';
import type { Page, PaginationOptions } from '../../models';
import { formatTimestamp } from '../../models';
import { I18nManager } from '../../i18n/I18nManager';
import './HistoryManagement.css';

/**
 * History management props
 */
interface HistoryManagementProps {
  className?: string;
}

/**
 * History item props for virtual list
 */
interface HistoryItemProps {
  index: number;
  style: CSSProperties;
  data: {
    items: Page[];
    searchKeywords: string[];
    onItemClick: (page: Page) => void;
    onItemDelete: (page: Page) => void;
    t: (key: string, params?: Record<string, any>) => string;
  };
}

/**
 * History statistics interface
 */
interface HistoryStats {
  totalPages: number;
  totalDomains: number;
  oldestPage: number;
  newestPage: number;
  totalSize: number;
}

/**
 * Pagination component props
 */
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
  onPreviousPage: () => void;
  onNextPage: () => void;
  t: (key: string, params?: Record<string, any>) => string;
}

/**
 * Pagination component
 */
const Pagination: FC<PaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  pageSize,
  onPageChange,
  onPageSizeChange,
  onPreviousPage,
  onNextPage,
  t
}) => {
  const [jumpToPage, setJumpToPage] = useState('');

  const handleJumpToPage = (e: React.FormEvent) => {
    e.preventDefault();
    const pageNum = parseInt(jumpToPage, 10);
    if (pageNum >= 1 && pageNum <= totalPages) {
      onPageChange(pageNum);
      setJumpToPage('');
    }
  };

  const generatePageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 7;
    
    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Smart pagination with ellipsis
      if (currentPage <= 4) {
        // Show first 5 pages + ellipsis + last page
        for (let i = 1; i <= 5; i++) pages.push(i);
        if (totalPages > 6) pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 3) {
        // Show first page + ellipsis + last 5 pages
        pages.push(1);
        if (totalPages > 6) pages.push('...');
        for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);
      } else {
        // Show first page + ellipsis + current-1, current, current+1 + ellipsis + last page
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
        pages.push('...');
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const startItem = (currentPage - 1) * pageSize + 1;
  const endItem = Math.min(currentPage * pageSize, totalItems);

  return (
    <div className="history-pagination">
      <div className="pagination-info">
        <span className="pagination-summary">
          {t('options.historyManagement.pagination.showing', { 
            start: startItem, 
            end: endItem, 
            total: totalItems 
          })}
        </span>
        
        <div className="page-size-selector">
          <label htmlFor="page-size-select">
            {t('options.historyManagement.pagination.itemsPerPage')}:
          </label>
          <select 
            id="page-size-select"
            value={pageSize} 
            onChange={(e) => onPageSizeChange(parseInt(e.target.value, 10))}
            className="page-size-select"
          >
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
            <option value={200}>200</option>
          </select>
        </div>
      </div>

      <div className="pagination-controls">
        <button 
          onClick={onPreviousPage}
          disabled={currentPage <= 1}
          className="pagination-btn pagination-prev"
          title={t('options.historyManagement.pagination.previousPage')}
        >
          ⬅️ {t('options.historyManagement.pagination.previous')}
        </button>

        <div className="pagination-pages">
          {generatePageNumbers().map((page, index) => (
            <span key={index}>
              {typeof page === 'number' ? (
                <button
                  onClick={() => onPageChange(page)}
                  className={`pagination-page ${page === currentPage ? 'active' : ''}`}
                >
                  {page}
                </button>
              ) : (
                <span className="pagination-ellipsis">{page}</span>
              )}
            </span>
          ))}
        </div>

        <button 
          onClick={onNextPage}
          disabled={currentPage >= totalPages}
          className="pagination-btn pagination-next"
          title={t('options.historyManagement.pagination.nextPage')}
        >
          {t('options.historyManagement.pagination.next')} ➡️
        </button>

        <form onSubmit={handleJumpToPage} className="page-jump">
          <input
            type="number"
            min="1"
            max={totalPages}
            value={jumpToPage}
            onChange={(e) => setJumpToPage(e.target.value)}
            placeholder={t('options.historyManagement.pagination.jumpToPage')}
            className="page-jump-input"
          />
          <button type="submit" className="page-jump-btn">
            {t('options.historyManagement.pagination.go')}
          </button>
        </form>
      </div>
    </div>
  );
};

/**
 * Individual history item component
 */
const HistoryItem: FC<HistoryItemProps> = ({ index, style, data }) => {
  const { items, searchKeywords, onItemClick, onItemDelete, t } = data;
  const page = items[index];

  if (!page) {
    return (
      <div style={style} className="history-item loading">
        <div className="loading-placeholder">
          <div className="loading-line title"></div>
          <div className="loading-line url"></div>
          <div className="loading-line content"></div>
        </div>
      </div>
    );
  }

  const handleClick = () => onItemClick(page);
  const handleDelete = (e: MouseEvent) => {
    e.stopPropagation();
    onItemDelete(page);
  };

  return (
    <div style={style} className="history-item" onClick={handleClick}>
      <div className="history-item-favicon">
        <img 
          src={`https://www.google.com/s2/favicons?domain=${page.domain}&sz=32`} 
          alt="" 
          onError={(e) => { e.currentTarget.style.display = 'none'; }}
        />
      </div>
      <div className="history-item-main">
        <div className="history-item-title">
          <Highlight
            text={page.title}
            keywords={searchKeywords}
            className="highlight-title"
            maxLength={100}
          />
        </div>
        <div className="history-item-url">
          <Highlight
            text={page.url}
            keywords={searchKeywords}
            className="highlight-url"
            maxLength={120}
          />
        </div>
        <div className="history-item-content">
          <Highlight
            text={page.content}
            keywords={searchKeywords}
            className="highlight-content"
            maxLength={200}
          />
        </div>
      </div>
      <div className="history-item-aside">
        <div className="history-meta">
          <span className="meta-item" title={t('options.historyManagement.item.accessCountTooltip')}>
            👁️ {page.accessCount}
          </span>
          <span className="meta-item" title={t('options.historyManagement.item.lastVisitTooltip')}>
            📅 {formatTimestamp(page.visitTime)}
          </span>
          <span className="meta-item" title={t('options.historyManagement.item.contentSizeTooltip')}>
            📄 {t('options.historyManagement.item.sizeKB', { size: Math.round(page.content.length / 1024) })}
          </span>
        </div>
        <div className="history-actions">
          <button
            className="delete-btn"
            onClick={handleDelete}
            title={t('options.historyManagement.item.delete')}
            aria-label={t('options.historyManagement.item.deleteAriaLabel', { title: page.title })}
          >
            🗑️
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * History Management Component
 */
export const HistoryManagement: FC<HistoryManagementProps> = ({ className = '' }) => {
  // I18n setup
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string, params?: Record<string, any>) => {
    return i18nManager.getTranslation(key, params);
  }, [i18nManager]);

  const [pages, setPages] = useState<Page[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'visitTime' | 'lastUpdated' | 'domain' | 'id'>('visitTime');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [stats, setStats] = useState<HistoryStats | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize, setPageSize] = useState(50); // Configurable page size

  // Dynamic height calculation
  const containerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const [containerHeight, setContainerHeight] = useState(700); // Increased initial height from 600 to 700

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  // Load current language resources on mount
  useEffect(() => {
    i18nManager.loadLanguageResources(i18nManager.getCurrentLanguage()).catch(console.error);
  }, [i18nManager]);

  /**
   * Calculate available height for the virtual list
   */
  const calculateHeight = useCallback(() => {
    if (!containerRef.current || !headerRef.current) return;
    
    const containerRect = containerRef.current.getBoundingClientRect();
    const headerRect = headerRef.current.getBoundingClientRect();
    
    // Calculate available height: container height minus header height minus padding/margins
    // Account for container padding (16px * 2) + header margins (12px * 2) + border (2px) = 42px
    const availableHeight = containerRect.height - headerRect.height - 42;
    
    // Use more of the available space - remove arbitrary 800px limit
    // Ensure minimum height of 400px for better user experience
    // Allow the list to use almost all available space
    const newHeight = Math.max(400, Math.floor(availableHeight * 0.98)); // Use 98% to leave small buffer
    
    setContainerHeight(newHeight);
  }, []);

  // Calculate height on mount and when window resizes
  useEffect(() => {
    calculateHeight();
    
    const handleResize = () => {
      calculateHeight();
    };
    
    window.addEventListener('resize', handleResize);
    
    // Use ResizeObserver for more accurate container size tracking
    let resizeObserver: ResizeObserver | null = null;
    if (containerRef.current && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(calculateHeight);
      resizeObserver.observe(containerRef.current);
    }
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [calculateHeight]);

  /**
   * Parse search keywords for highlighting
   */
  const searchKeywords = useMemo(() => {
    if (!searchQuery) return [];
    const parsed = parseQuery(searchQuery);
    return [...parsed.keywords, ...parsed.exact];
  }, [searchQuery]);

  /**
   * Load history statistics
   */
  const loadStats = useCallback(async () => {
    try {
      const totalPages = await dbService.getPageCount();
      const allPages = await dbService.getAllPages();
      
      if (allPages.length === 0) {
        setStats({
          totalPages: 0,
          totalDomains: 0,
          oldestPage: 0,
          newestPage: 0,
          totalSize: 0
        });
        return;
      }

      const domains = new Set(allPages.map(p => p.domain));
      const visitTimes = allPages.map(p => p.visitTime);
      const totalSize = allPages.reduce((sum, p) => sum + p.content.length, 0);

      setStats({
        totalPages,
        totalDomains: domains.size,
        oldestPage: Math.min(...visitTimes),
        newestPage: Math.max(...visitTimes),
        totalSize
      });
    } catch (error) {
      console.error('Failed to load history stats:', error);
    }
  }, []);

  /**
   * Load history data for specific page
   */
  const loadHistory = useCallback(async (pageNumber = currentPage) => {
    try {
      setLoading(true);
      
      let results: Page[];
      let total = 0;
      
      if (searchQuery.trim()) {
        // Use hybrid search service for filtered results
        const searchResults = await hybridSearchService.search(searchQuery, { limit: 1000 });
        const allSearchResults = searchResults.map(r => r.page);
        total = allSearchResults.length;
        
        // Calculate pagination for search results
        const startIndex = (pageNumber - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        results = allSearchResults.slice(startIndex, endIndex);
        
        setTotalItems(total);
        setTotalPages(Math.ceil(total / pageSize));
      } else {
        // Get total count first
        total = await dbService.getPageCount();
        setTotalItems(total);
        setTotalPages(Math.ceil(total / pageSize));
        
        // Use offset-based pagination for all results
        const paginationOptions: PaginationOptions = {
          limit: pageSize,
          offset: (pageNumber - 1) * pageSize,
          sortBy,
          sortOrder
        };
        
        const paginatedResult = await dbService.getPagesPaginated(paginationOptions);
        results = paginatedResult.items;
      }

      setPages(results);
      setCurrentPage(pageNumber);
    } catch (error) {
      console.error('Failed to load history:', error);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, sortBy, sortOrder, pageSize, currentPage]);

  /**
   * Pagination handlers
   */
  const goToPage = useCallback((pageNumber: number) => {
    if (pageNumber >= 1 && pageNumber <= totalPages && pageNumber !== currentPage) {
      loadHistory(pageNumber);
    }
  }, [totalPages, currentPage, loadHistory]);

  const goToPreviousPage = useCallback(() => {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  }, [currentPage, goToPage]);

  const goToNextPage = useCallback(() => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  }, [currentPage, totalPages, goToPage]);

  const changePageSize = useCallback((newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1); // Reset to first page when changing page size
  }, []);

  /**
   * Handle search input change
   */
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  /**
   * Handle sort change
   */
  const handleSortChange = useCallback((newSortBy: typeof sortBy, newSortOrder: typeof sortOrder) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setCurrentPage(1); // Reset to first page when sorting
  }, []);

  /**
   * Handle item click - open in new tab
   */
  const handleItemClick = useCallback((page: Page) => {
    chrome.tabs.create({ url: page.url });
  }, []);

  /**
   * Handle item deletion
   */
  const handleItemDelete = useCallback(async (page: Page) => {
    if (!confirm(t('options.historyManagement.actions.deleteConfirm', { title: page.title }))) return;
    
    try {
      await dbService.deletePage(page.id);
      setPages(prev => prev.filter(p => p.id !== page.id));
      await loadStats(); // Refresh stats
    } catch (error) {
      console.error('Failed to delete page:', error);
      alert(t('options.historyManagement.actions.deleteError'));
    }
  }, [loadStats, t]);

  // Load initial data
  useEffect(() => {
    loadHistory(1);
    loadStats();
  }, [searchQuery, sortBy, sortOrder]);

  // Reload data when page size changes
  useEffect(() => {
    if (currentPage > 1) {
      loadHistory(1); // Go back to first page if current page might not exist with new page size
    } else {
      loadHistory(currentPage);
    }
  }, [pageSize]);

  // Prepare data for virtual list
  const listData = useMemo(() => ({
    items: pages,
    searchKeywords,
    onItemClick: handleItemClick,
    onItemDelete: handleItemDelete,
    t
  }), [pages, searchKeywords, handleItemClick, handleItemDelete, t]);

  return (
    <div className={`history-management ${className}`} ref={containerRef}>
       {/* Header with search and controls */}
       <div className="history-header" ref={headerRef}>
         <div className="history-title-section">
           <h2>{t('options.historyManagement.title')}</h2>
           {stats && (
             <div className="history-stats">
               <span>{t('options.historyManagement.stats.totalPages', { count: stats.totalPages })}</span>
               <span>{t('options.historyManagement.stats.totalDomains', { count: stats.totalDomains })}</span>
               <span>{t('options.historyManagement.stats.totalSize', { size: Math.round(stats.totalSize / 1024 / 1024) })}</span>
             </div>
           )}
         </div>
         
         <div className="history-controls">
           <div className="search-section">
             <input
               type="text"
               value={searchQuery}
               onChange={(e) => handleSearchChange(e.target.value)}
               placeholder={t('options.historyManagement.search.placeholder')}
               className="history-search"
             />
           </div>
           
           <div className="sort-section">
             <label htmlFor="sort-select" className="sort-label">{t('options.historyManagement.sorting.label')}</label>
             <select
               id="sort-select"
               value={`${sortBy}-${sortOrder}`}
               onChange={(e) => {
                 const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder];
                 handleSortChange(newSortBy, newSortOrder);
               }}
               className="sort-select"
             >
               <option value="visitTime-desc">{t('options.historyManagement.sorting.options.visitTimeDesc')}</option>
               <option value="visitTime-asc">{t('options.historyManagement.sorting.options.visitTimeAsc')}</option>
               <option value="lastUpdated-desc">{t('options.historyManagement.sorting.options.lastUpdatedDesc')}</option>
               <option value="lastUpdated-asc">{t('options.historyManagement.sorting.options.lastUpdatedAsc')}</option>
               <option value="domain-asc">{t('options.historyManagement.sorting.options.domainAsc')}</option>
               <option value="id-desc">{t('options.historyManagement.sorting.options.idDesc')}</option>
             </select>
           </div>
         </div>
       </div>

      {/* Virtualized List */}
      <div className="history-list-container">
        {loading ? (
          <div className="loading-state">
            <div className="loading-spinner-large" />
            <p>{t('options.historyManagement.loading')}</p>
          </div>
        ) : pages.length === 0 ? (
          <div className="empty-state">
            <span className="empty-icon">📭</span>
            <h3>{t('options.historyManagement.emptyState.noHistory')}</h3>
            {searchQuery ? (
              <p>{t('options.historyManagement.emptyState.noSearchResults', { query: searchQuery })}</p>
            ) : (
              <p>{t('options.historyManagement.emptyState.startBrowsing')}</p>
            )}
          </div>
        ) : (
          <>
            <List
              height={containerHeight - 80} // Reserve space for pagination
              width="100%"
              itemCount={pages.length}
              itemSize={120}
              itemData={listData}
              className="history-virtual-list"
            >
              {HistoryItem}
            </List>
            
            {/* Pagination Component */}
            {totalPages > 1 && (
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                pageSize={pageSize}
                onPageChange={goToPage}
                onPageSizeChange={changePageSize}
                onPreviousPage={goToPreviousPage}
                onNextPage={goToNextPage}
                t={t}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};
