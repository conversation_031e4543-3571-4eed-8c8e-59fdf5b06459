/**
 * Data Backup Component Styles
 * Refactored to align with HistoryManagement styles
 */

/* Main container */
.data-backup {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: #333;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Header section */
.backup-header {
  padding-bottom: 15px;
  border-bottom: 2px solid #eee;
  margin-bottom: 15px;
}

.backup-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.backup-title-section h2 {
  margin: 0;
  font-size: 24px;
  color: #2c3e50;
}

.backup-stats {
  display: flex;
  gap: 15px;
  font-size: 13px;
  color: #555;
}

.backup-stats span {
  background-color: #ecf0f1;
  padding: 5px 10px;
  border-radius: 12px;
}

/* Content container */
.backup-content {
  flex-grow: 1;
  overflow-y: auto; /* Allow scrolling if content overflows */
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

/* Section styling */
.backup-section {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
  transition: box-shadow 0.2s;
}

.backup-section:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.section-header .icon {
  font-size: 24px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #34495e;
}

.section-description {
  font-size: 14px;
  color: #555;
  margin-bottom: 20px;
}

.section-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Danger Zone specific styles */
.backup-section.danger-zone {
  border-color: #e74c3c;
  background-color: #fff6f5;
}

.danger-zone .section-header h3 {
  color: #c0392b;
}

.danger-zone .warning-box {
  padding: 1rem;
  background: #fce8e6;
  border: 1px solid #f8c6c1;
  border-radius: 6px;
  color: #c0392b;
  font-size: 0.875rem;
  line-height: 1.5;
}

.danger-zone .warning-box strong {
  color: #c0392b;
  font-weight: 600;
}


/* Buttons */
.btn {
  padding: 10px 20px;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
  border-color: #2980b9;
}

.btn-secondary {
  background: #f1f1f1;
  color: #333;
  border-color: #ddd;
}

.btn-secondary:hover:not(:disabled) {
  background: #e9e9e9;
}

.btn-danger {
  background: #e74c3c;
  color: white;
  border-color: #e74c3c;
}

.btn-danger:hover:not(:disabled) {
  background: #c0392b;
  border-color: #c0392b;
}


/* Progress Bar */
.progress-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #3498db;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 13px;
  font-weight: 500;
  color: #555;
  min-width: 40px;
  text-align: right;
}

/* Import Results */
.import-results {
  margin-top: 15px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #eee;
}

.import-results h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  background-color: #fff;
  border: 1px solid #eee;
}

.result-label {
  font-weight: 500;
  color: #555;
  font-size: 13px;
}

.result-value {
  font-weight: 600;
  font-size: 13px;
}

.result-item.success .result-value { color: #27ae60; }
.result-item.warning .result-value { color: #f39c12; }

/* Import Errors */
.import-errors {
  margin-top: 15px;
  padding: 1rem;
  background: #fff6f5;
  border-radius: 6px;
  border-left: 4px solid #e74c3c;
}

.import-errors h5 {
  margin: 0 0 0.75rem 0;
  color: #c0392b;
  font-size: 14px;
  font-weight: 600;
}

.import-errors ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #666;
}

.import-errors li {
  margin-bottom: 0.25rem;
  font-size: 13px;
  line-height: 1.4;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .data-backup {
    background: #1a1a1a;
    color: #e0e0e0;
  }
  .backup-header { border-bottom-color: #333; }
  .backup-title-section h2 { color: #e0e0e0; }
  .backup-stats span { background: #333; color: #ccc; }
  .backup-section { background-color: #2a2a2a; border-color: #444; }
  .section-header h3 { color: #e0e0e0; }
  .section-description { color: #aaa; }

  .backup-section.danger-zone {
    border-color: #c0392b;
    background-color: #2a1a1a;
  }
  .danger-zone .warning-box {
    background: #2a1a1a;
    border-color: #c0392b;
    color: #f8c6c1;
  }
  .danger-zone .warning-box strong { color: #f8c6c1; }

  .btn-secondary { background: #333; color: #e0e0e0; border-color: #555; }
  .btn-secondary:hover:not(:disabled) { background: #444; }

  .progress-bar { background: #444; }
  .progress-text { color: #e0e0e0; }

  .import-results { background: #1a1a1a; border-color: #333; }
  .import-results h4 { color: #e0e0e0; }
  .result-item { background-color: #333; border-color: #555; }
  .result-label { color: #ccc; }
  .result-item.success .result-value { color: #2ecc71; }
  .result-item.warning .result-value { color: #f1c40f; }

  .import-errors { background: #2a1a1a; border-left-color: #c0392b; }
  .import-errors h5 { color: #e74c3c; }
  .import-errors ul { color: #ccc; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-backup { padding: 1rem; }
  .backup-title-section { flex-direction: column; align-items: flex-start; gap: 10px; }
  .backup-content { grid-template-columns: 1fr; }
  .results-grid { grid-template-columns: 1fr; }
}
