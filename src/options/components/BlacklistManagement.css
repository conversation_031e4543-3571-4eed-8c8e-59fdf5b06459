/**
 * Blacklist Management Component Styles
 */

/* Main container */
.blacklist-management {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: #333;
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Header section */
.blacklist-header {
  padding-bottom: 15px;
  border-bottom: 2px solid #eee;
  margin-bottom: 15px;
}

.blacklist-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.blacklist-title-section h2 {
  margin: 0;
  font-size: 24px;
  color: #2c3e50;
}

.blacklist-stats {
  display: flex;
  gap: 15px;
  font-size: 13px;
  color: #555;
}

.blacklist-stats span {
  background-color: #ecf0f1;
  padding: 5px 10px;
  border-radius: 12px;
}

/* Controls section (search and sort) */
.blacklist-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.search-section {
  flex-grow: 1;
}

.blacklist-search {
  width: 100%;
  padding: 10px 15px;
  border-radius: 6px;
  border: 1px solid #ddd;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.blacklist-search:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.actions-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* List container */
.blacklist-list-container {
  flex-grow: 1;
  position: relative;
  overflow-y: auto;
}

.blacklist-virtual-list {
  scrollbar-width: thin;
  scrollbar-color: #ccc #f1f1f1;
}

.blacklist-virtual-list::-webkit-scrollbar {
  width: 8px;
}

.blacklist-virtual-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.blacklist-virtual-list::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

/* Individual Blacklist Item */
.blacklist-item {
  display: flex;
  align-items: center;
  padding: 15px 10px;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  transition: background-color 0.2s ease;
}

.blacklist-item:hover {
  background-color: #f5faff;
}

.blacklist-item-favicon {
  width: 32px;
  height: 32px;
  margin-right: 15px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blacklist-item-favicon img {
  width: 24px;
  height: 24px;
  border-radius: 4px;
}

.blacklist-item-main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.blacklist-item-domain {
  font-size: 15px;
  font-weight: 600;
  color: #34495e;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.blacklist-item-reason {
  font-size: 13px;
  color: #555;
  line-height: 1.4;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.blacklist-item-domain mark,
.blacklist-item-reason mark {
  background-color: #f1c40f;
  color: #333;
  padding: 1px 0;
}

.blacklist-item-aside {
  flex-shrink: 0;
  width: 180px;
  margin-left: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.blacklist-meta {
  font-size: 12px;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  gap: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.blacklist-actions {
  display: flex;
  justify-content: flex-end;
}

.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #95a5a6;
  padding: 5px;
  border-radius: 50%;
  transition: color 0.2s, background-color 0.2s;
}

.delete-btn:hover {
  color: #e74c3c;
  background-color: #fce8e6;
}

/* General Button Styles from modal, adapted for header */
.btn {
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #2196f3;
  color: white;
  border-color: #2196f3;
}

.btn-primary:hover:not(:disabled) {
  background: #1976d2;
}

.btn-danger {
  background: #f44336;
  color: white;
  border-color: #f44336;
}

.btn-danger:hover:not(:disabled) {
  background: #d32f2f;
}

.btn-secondary {
  background: #ffffff;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover:not(:disabled) {
    background: #f5f5f5;
}

/* Loading and Empty States */
.loading-state, .empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #777;
}

.loading-spinner-large {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem;
  font-size: 1.25rem;
  color: #2c3e50;
}

.empty-state p {
  margin-bottom: 1.5rem;
  max-width: 300px;
}


/* Modal Styles (kept from original) */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: #fff;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  animation: modal-fade-in 0.3s ease-out;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.add-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.add-form h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  text-align: center;
  color: #2c3e50;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 0.875rem;
}

.form-group input[type="text"] {
  padding: 0.75rem;
  border: 1px solid #e1e5e9;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input[type="text"]:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.2);
}

.form-group input[type="text"].error {
  border-color: #f44336;
}

.error-message {
  color: #f44336;
  font-size: 0.75rem;
  font-weight: 500;
}

.checkbox-group {
  flex-direction: row;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
  width: 1.15rem;
  height: 1.15rem;
  cursor: pointer;
  accent-color: #2196f3;
}

.checkbox-text {
  font-size: 0.875rem;
  color: #555;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 0.5rem;
}


/* Help Tooltip */
.help-tooltip {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: help;
  font-size: 1.25rem;
  color: #95a5a6;
  transition: color 0.2s;
}

.help-tooltip:hover {
    color: #3498db;
}

.help-tooltip .help-tooltip-content {
  position: absolute;
  bottom: 125%;
  right: -20px;
  background-color: #34495e;
  color: #ecf0f1;
  padding: 1rem;
  border-radius: 8px;
  width: 300px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s, visibility 0.2s;
  z-index: 1010;
}

.help-tooltip:hover .help-tooltip-content {
  opacity: 1;
  visibility: visible;
}

.help-tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 30px;
  border-width: 6px;
  border-style: solid;
  border-color: #34495e transparent transparent transparent;
}

.help-tooltip-content h4 {
  margin: 0 0 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  border-bottom: 1px solid #4a627a;
  padding-bottom: 0.5rem;
}

.help-tooltip-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.help-tooltip-content li {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.4;
}
.help-tooltip-content li:last-child {
  margin-bottom: 0;
}

.help-tooltip-content strong {
  color: #ffffff;
  font-weight: 600;
}


/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .blacklist-management {
    background: #1a1a1a;
    color: #e0e0e0;
    border-color: #333;
  }

  .blacklist-header {
    border-bottom-color: #333;
  }

  .blacklist-title-section h2, .empty-state h3 {
      color: #e0e0e0;
  }

  .blacklist-stats span {
    background: #333;
    color: #ccc;
  }

  .blacklist-search {
    background: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
  }

  .blacklist-search:focus {
    border-color: #64b5f6;
    box-shadow: 0 0 0 2px rgba(100, 181, 246, 0.2);
  }

  .blacklist-item {
    background-color: #1a1a1a;
    border-bottom-color: #333;
  }

  .blacklist-item:hover {
    background-color: #2a2a2a;
  }

  .blacklist-item-domain {
    color: #e0e0e0;
  }

  .blacklist-item-reason {
    color: #bbb;
  }
  
  .blacklist-item-domain mark,
  .blacklist-item-reason mark {
    background-color: #d6a100;
    color: #111;
  }

  .blacklist-meta {
    color: #888;
  }

  .delete-btn:hover {
      background-color: #4d2f2c;
  }

  .modal-content, .add-form {
      background-color: #2d3748;
      color: #e2e8f0;
  }

    .add-form h3, .form-group label, .checkbox-text {
        color: #e2e8f0;
    }

    .form-group input[type="text"] {
        background-color: #1a202c;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
  .blacklist-management {
    padding: 1rem;
  }

  .blacklist-header, .blacklist-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .blacklist-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .actions-section {
      justify-content: flex-end;
  }
  
  .blacklist-item-aside {
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;
      width: 100px;
  }
}

/* Domain Suggestions Styles */
.domain-input-container {
  position: relative;
}

.domain-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 300px;
  overflow: hidden;
}

.suggestions-header {
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
  font-size: 12px;
  font-weight: 600;
  color: #666;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestions-count {
  color: #999;
  font-weight: normal;
}

.suggestions-list {
  max-height: 250px;
  overflow-y: auto;
}

.suggestion-item {
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.15s ease;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-favicon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.suggestion-favicon img {
  width: 100%;
  height: 100%;
  border-radius: 2px;
}

.suggestion-main {
  flex: 1;
  min-width: 0;
}

.suggestion-domain {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.suggestion-stats {
  font-size: 12px;
  color: #666;
}

.suggestions-loading {
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.loading-spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #999;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Dark mode styles for domain suggestions */
@media (prefers-color-scheme: dark) {
  .domain-suggestions {
    background: #2d3748;
    border-color: #4a5568;
  }

  .suggestions-header {
    background: #1a202c;
    border-bottom-color: #4a5568;
    color: #a0aec0;
  }

  .suggestion-item {
    border-bottom-color: #4a5568;
  }

  .suggestion-item:hover {
    background-color: #4a5568;
  }

  .suggestion-domain {
    color: #e2e8f0;
  }

  .suggestion-stats {
    color: #a0aec0;
  }

  .suggestions-loading {
    color: #a0aec0;
  }

  .loading-spinner-small {
    border-color: #4a5568;
    border-top-color: #a0aec0;
  }
}
