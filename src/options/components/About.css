/* src/options/components/About.css */

/* General page setup */
.about-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 2rem;
  background-color: var(--color-background, #f9f9f9);
  color: var(--color-text-primary, #333);
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

/* Header */
.about-header {
  margin-bottom: 2.5rem;
  max-width: 600px;
}

.about-header .logo {
  width: 80px;
  height: 80px;
  margin-bottom: 1rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.about-header h1 {
  font-size: 2.25rem;
  margin: 0;
  color: var(--color-heading, #2c3e50);
}

.about-header .version {
  font-size: 0.9rem;
  color: var(--color-text-secondary, #888);
  margin: 0.25rem 0 1rem;
  font-weight: 500;
}

.about-header .tagline {
  margin: 0 auto;
  font-size: 1.05rem;
  line-height: 1.6;
  color: var(--color-text-tertiary, #555);
}

/* Main content */
.about-main {
  width: 100%;
  max-width: 900px;
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

.about-section h2 {
  font-size: 1.4rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  text-align: center;
  font-weight: 600;
  color: var(--color-heading, #34495e);
  position: relative;
}

.about-section h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--color-primary, #3498db);
  border-radius: 2px;
}


/* Features section */
.features .feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  text-align: left;
}

.feature-item {
  background: var(--color-background-card, #fff);
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid var(--color-border, #eee);
  box-shadow: 0 4px 8px rgba(0,0,0,0.04);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.08);
}

.feature-item span {
  font-size: 2rem;
  margin-bottom: 0.75rem;
  line-height: 1;
}

.feature-item h3 {
  font-size: 1.15rem;
  margin: 0 0 0.5rem 0;
  color: var(--color-heading, #2c3e50);
}

.feature-item p {
  font-size: 0.95rem;
  color: var(--color-text-secondary, #666);
  margin: 0;
  line-height: 1.5;
}

/* Privacy and Links Sections */
.privacy, .links {
  background: var(--color-background-card, #fff);
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid var(--color-border, #eee);
  box-shadow: 0 4px 8px rgba(0,0,0,0.04);
}

.privacy p {
  text-align: left;
  line-height: 1.7;
  color: var(--color-text-secondary, #555);
  margin: 0;
}

.links ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.links a {
  text-decoration: none;
  color: var(--color-primary, #3498db);
  font-weight: 500;
  transition: color 0.2s ease;
}

.links a:hover {
  color: var(--color-primary-hover, #2980b9);
}


/* Footer */
.about-footer {
  margin-top: auto;
  padding-top: 2.5rem;
  font-size: 0.9rem;
  color: var(--color-text-secondary, #888);
  width: 100%;
  border-top: 1px solid var(--color-border, #eee);
}

.about-footer .acknowledgements {
  margin-bottom: 0.5rem;
}

.about-footer a {
  color: var(--color-text-tertiary, #555);
  text-decoration: none;
  transition: color 0.2s ease;
}

.about-footer a:hover {
  color: var(--color-text-primary, #333);
}

/* Dark Mode Variables */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: #121212;
    --color-background-card: #1e1e1e;
    --color-text-primary: #e0e0e0;
    --color-text-secondary: #b0b0b0;
    --color-text-tertiary: #888;
    --color-heading: #ffffff;
    --color-border: #333;
    --color-primary: #58a6ff;
    --color-primary-hover: #79b8ff;
  }
}

/* Dark Mode Styles */
@media (prefers-color-scheme: dark) {
  .about-page {
    background-color: var(--color-background);
    color: var(--color-text-primary);
  }

  .about-header .logo {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
  
  .feature-item, .privacy, .links {
    background: var(--color-background-card);
    border-color: var(--color-border);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }
  
  .feature-item:hover {
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
  }
} 