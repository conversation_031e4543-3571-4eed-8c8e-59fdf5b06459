import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';

export interface DomainInfo {
  domain: string;
  visitCount: number;
  lastVisit: Date | null;
  isBlocked: boolean;
}

export interface DomainSelectorProps {
  domains: DomainInfo[];
  onSelectionChange: (selectedDomains: string[]) => void;
  onSearch?: (query: string) => void;
  isLoading?: boolean;
  error?: Error | null;
}

const ITEM_HEIGHT = 60;
const CONTAINER_HEIGHT = 400;

export const DomainSelector: React.FC<DomainSelectorProps> = ({
  domains,
  onSelectionChange,
  onSearch,
  isLoading = false,
  error = null
}) => {
  const [selectedDomains, setSelectedDomains] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      onSearch?.(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, onSearch]);

  // Filter domains based on search query
  const filteredDomains = useMemo(() => {
    if (!debouncedSearchQuery.trim()) {
      return domains;
    }

    const query = debouncedSearchQuery.toLowerCase();
    return domains.filter(domain =>
      domain.domain.toLowerCase().includes(query)
    );
  }, [domains, debouncedSearchQuery]);

  // Handle selection changes
  const handleSelectionChange = useCallback((newSelectedDomains: Set<string>) => {
    setSelectedDomains(newSelectedDomains);
    onSelectionChange(Array.from(newSelectedDomains));
  }, [onSelectionChange]);

  // Handle individual domain selection
  const handleDomainToggle = useCallback((domain: string) => {
    const newSelection = new Set(selectedDomains);
    if (newSelection.has(domain)) {
      newSelection.delete(domain);
    } else {
      newSelection.add(domain);
    }
    handleSelectionChange(newSelection);
  }, [selectedDomains, handleSelectionChange]);

  // Handle select all
  const handleSelectAll = useCallback(() => {
    const allDomains = new Set(filteredDomains.map(d => d.domain));
    handleSelectionChange(allDomains);
  }, [filteredDomains, handleSelectionChange]);

  // Handle clear all
  const handleClearAll = useCallback(() => {
    handleSelectionChange(new Set());
  }, [handleSelectionChange]);

  // Format visit count
  const formatVisitCount = useCallback((count: number): string => {
    if (count === 0) return 'No visits';
    if (count >= 1000) return `${(count / 1000).toFixed(1)}k visits`;
    return `${count} visits`;
  }, []);

  // Format last visit date
  const formatLastVisit = useCallback((date: Date | null): string => {
    if (!date) return 'Never visited';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 30) return `${diffDays} days ago`;
    
    return date.toLocaleDateString();
  }, []);

  // Render individual domain item
  const renderDomainItem = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const domain = filteredDomains[index];
    const isSelected = selectedDomains.has(domain.domain);

    return (
      <div style={style} className="domain-item">
        <div className="domain-item-content">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => handleDomainToggle(domain.domain)}
            data-testid={`domain-checkbox-${domain.domain}`}
            className="domain-checkbox"
          />
          <div className="domain-info">
            <div className="domain-name">{domain.domain}</div>
            <div className="domain-stats">
              <span className="visit-count">{formatVisitCount(domain.visitCount)}</span>
              <span className="last-visit">{formatLastVisit(domain.lastVisit)}</span>
              {domain.isBlocked && <span className="blocked-badge">Blocked</span>}
            </div>
          </div>
        </div>
      </div>
    );
  }, [filteredDomains, selectedDomains, handleDomainToggle, formatVisitCount, formatLastVisit]);

  // Loading state
  if (isLoading) {
    return (
      <div data-testid="domain-selector-loading" className="domain-selector-loading">
        <div className="loading-spinner"></div>
        <p>Loading domains...</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div data-testid="domain-selector-error" className="domain-selector-error">
        <div className="error-icon">⚠️</div>
        <p>Error loading domains: {error.message}</p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }

  // Empty state
  if (domains.length === 0) {
    return (
      <div data-testid="domain-selector-empty" className="domain-selector-empty">
        <div className="empty-icon">📄</div>
        <p>No domains found</p>
        <p className="empty-hint">Visit some websites to see domains here</p>
      </div>
    );
  }

  return (
    <div className="domain-selector">
      {/* Search and controls */}
      <div className="domain-selector-header">
        <input
          type="text"
          placeholder="Search domains..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          data-testid="domain-search-input"
          className="domain-search-input"
        />
        <div className="domain-controls">
          <button
            onClick={handleSelectAll}
            disabled={filteredDomains.length === 0}
            data-testid="select-all-button"
            className="control-button"
          >
            Select All ({filteredDomains.length})
          </button>
          <button
            onClick={handleClearAll}
            disabled={selectedDomains.size === 0}
            data-testid="clear-all-button"
            className="control-button"
          >
            Clear All
          </button>
        </div>
      </div>

      {/* Selection summary */}
      <div className="selection-summary">
        {selectedDomains.size > 0 ? (
          <p>{selectedDomains.size} domain(s) selected</p>
        ) : (
          <p>No domains selected</p>
        )}
      </div>

      {/* Virtual scrolling list */}
      {filteredDomains.length > 0 ? (
        <div data-testid="domain-scroll-container" className="domain-list-container">
          <List
            height={CONTAINER_HEIGHT}
            width="100%"
            itemCount={filteredDomains.length}
            itemSize={ITEM_HEIGHT}
            itemData={filteredDomains}
            overscanCount={5}
          >
            {renderDomainItem}
          </List>
        </div>
      ) : (
        <div className="no-results">
          <p>No domains match your search</p>
        </div>
      )}

      {/* Domain statistics */}
      <div className="domain-stats-summary">
        <p>
          Total: {domains.length} domains, 
          Filtered: {filteredDomains.length} domains,
          Blocked: {domains.filter(d => d.isBlocked).length} domains
        </p>
      </div>

      <style>{`
        .domain-selector {
          width: 100%;
          max-width: 800px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .domain-selector-header {
          margin-bottom: 16px;
        }

        .domain-search-input {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          margin-bottom: 12px;
        }

        .domain-controls {
          display: flex;
          gap: 8px;
        }

        .control-button {
          padding: 6px 12px;
          border: 1px solid #007cba;
          background: #007cba;
          color: white;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }

        .control-button:disabled {
          background: #ccc;
          border-color: #ccc;
          cursor: not-allowed;
        }

        .control-button:hover:not(:disabled) {
          background: #005a87;
        }

        .selection-summary {
          margin-bottom: 12px;
          font-size: 14px;
          color: #666;
        }

        .domain-list-container {
          border: 1px solid #ddd;
          border-radius: 4px;
          margin-bottom: 12px;
        }

        .domain-item {
          border-bottom: 1px solid #eee;
          padding: 8px 12px;
          display: flex;
          align-items: center;
        }

        .domain-item:hover {
          background-color: #f5f5f5;
        }

        .domain-item-content {
          display: flex;
          align-items: center;
          width: 100%;
        }

        .domain-checkbox {
          margin-right: 12px;
        }

        .domain-info {
          flex: 1;
        }

        .domain-name {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .domain-stats {
          font-size: 12px;
          color: #666;
          display: flex;
          gap: 12px;
        }

        .blocked-badge {
          background: #ff4444;
          color: white;
          padding: 2px 6px;
          border-radius: 3px;
          font-size: 10px;
        }

        .domain-stats-summary {
          font-size: 12px;
          color: #666;
          text-align: center;
        }

        .domain-selector-loading,
        .domain-selector-error,
        .domain-selector-empty {
          text-align: center;
          padding: 40px;
        }

        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 4px solid #f3f3f3;
          border-top: 4px solid #007cba;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin: 0 auto 16px;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        .error-icon,
        .empty-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }

        .empty-hint {
          color: #999;
          font-size: 14px;
        }

        .no-results {
          text-align: center;
          padding: 40px;
          color: #666;
        }
      `}</style>
    </div>
  );
};