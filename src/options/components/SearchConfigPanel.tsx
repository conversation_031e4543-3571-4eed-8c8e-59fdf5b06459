/**
 * Search Configuration Panel Component
 * 
 * Provides UI for configuring hybrid search engine weights and behaviors:
 * - Three sliders for weight adjustment (0-100%)
 * - Three toggle switches for enable/disable
 * - Automatic weight normalization to ensure sum equals 100%
 * - Traditional search "always show" indicator
 * - Real-time updates to search configuration
 * 
 * @module SearchConfigPanel
 * @version 1.0
 * @since 2025-01-27
 */

import React, { useState, useEffect, useCallback } from 'react';
import { I18nManager } from '../../i18n/I18nManager';
import type { SearchEngineConfig } from '../../models/settings';
import { 
  DEFAULT_SEARCH_ENGINE_CONFIG,
  normalizeSearchEngineWeights 
} from '../../models/settings';
import { searchConfigService } from '../../services/search-config.service';
import './SearchConfigPanel.css';

interface SearchConfigPanelProps {
  className?: string;
  onConfigChange?: (config: SearchEngineConfig) => void;
}

interface EngineConfigUIState {
  traditional: { enabled: boolean; weight: number };
  fulltext: { enabled: boolean; weight: number };
}

export const SearchConfigPanel: React.FC<SearchConfigPanelProps> = ({ 
  className = '',
  onConfigChange 
}) => {
  const [config, setConfig] = useState<EngineConfigUIState>({
    traditional: { enabled: true, weight: 70 },
    fulltext: { enabled: true, weight: 30 }
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  // i18n Management
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string, interpolations?: Record<string, any>) => {
    return i18nManager.getTranslation(key, interpolations);
  }, [i18nManager]);

  // Language change handler
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  // Load initial configuration
  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      const searchConfig = await searchConfigService.getEngineConfig();
      
      // Convert to UI state (weights as percentages)
      setConfig({
        traditional: { 
          enabled: searchConfig.traditional.enabled, 
          weight: Math.round(searchConfig.traditional.weight * 100) 
        },
        fulltext: { 
          enabled: searchConfig.fulltext.enabled, 
          weight: Math.round(searchConfig.fulltext.weight * 100) 
        }
      });
    } catch (error) {
      console.error('Failed to load search configuration:', error);
      // Use default configuration
      setConfig({
        traditional: { 
          enabled: DEFAULT_SEARCH_ENGINE_CONFIG.traditional.enabled, 
          weight: Math.round(DEFAULT_SEARCH_ENGINE_CONFIG.traditional.weight * 100) 
        },
        fulltext: { 
          enabled: DEFAULT_SEARCH_ENGINE_CONFIG.fulltext.enabled, 
          weight: Math.round(DEFAULT_SEARCH_ENGINE_CONFIG.fulltext.weight * 100) 
        }
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfiguration = async (newConfig: EngineConfigUIState) => {
    setIsSaving(true);
    try {
      // Convert UI state to SearchEngineConfig format (weights as decimals)
      const searchEngineConfig: SearchEngineConfig = {
        traditional: {
          enabled: newConfig.traditional.enabled,
          weight: newConfig.traditional.weight / 100,
          alwaysShow: true // Traditional search always shows results
        },
        fulltext: {
          enabled: newConfig.fulltext.enabled,
          weight: newConfig.fulltext.weight / 100
        }
      };

      // Normalize weights to ensure they sum to 1.0
      const normalizedConfig = normalizeSearchEngineWeights(searchEngineConfig);
      
      // Update the service through searchConfigService
      await searchConfigService.updateEngineConfig(normalizedConfig);
      
      // Update UI state with normalized values
      setConfig({
        traditional: { 
          enabled: normalizedConfig.traditional.enabled, 
          weight: Math.round(normalizedConfig.traditional.weight * 100) 
        },
        fulltext: { 
          enabled: normalizedConfig.fulltext.enabled, 
          weight: Math.round(normalizedConfig.fulltext.weight * 100) 
        }
      });

      setMessage({ type: 'success', text: t('options.search.configSaved') });
      setTimeout(() => setMessage(null), 3000);

      // Notify parent component
      onConfigChange?.(normalizedConfig);
    } catch (error) {
      console.error('Failed to save search configuration:', error);
      setMessage({ type: 'error', text: t('options.search.configSaveFailed') });
    } finally {
      setIsSaving(false);
    }
  };

  const handleToggleChange = (engine: keyof EngineConfigUIState, enabled: boolean) => {
    const newConfig = { ...config };
    newConfig[engine].enabled = enabled;
    
    // If disabling an engine, set its weight to 0
    if (!enabled) {
      newConfig[engine].weight = 0;
      
      // Redistribute weight to other enabled engines
      const engines = ['traditional', 'fulltext'] as const;
      const enabledEngines = engines.filter(e => e !== engine && newConfig[e].enabled);
      
      if (enabledEngines.length > 0) {
        const weightToRedistribute = config[engine].weight;
        const weightPerEngine = Math.floor(weightToRedistribute / enabledEngines.length);
        const remainder = weightToRedistribute - (weightPerEngine * enabledEngines.length);
        
        enabledEngines.forEach((e, index) => {
          newConfig[e].weight += weightPerEngine;
          if (index === 0) {
            newConfig[e].weight += remainder;
          }
        });
      }
    } else {
      // If enabling an engine with 0 weight, give it a default weight
      if (newConfig[engine].weight === 0) {
        newConfig[engine].weight = 20;
        
        // Normalize other weights
        const engines = ['traditional', 'fulltext'] as const;
        const otherEngines = engines.filter(e => e !== engine && newConfig[e].enabled);
        
        if (otherEngines.length > 0) {
          const totalOtherWeight = otherEngines.reduce((sum, e) => sum + newConfig[e].weight, 0);
          const targetOtherWeight = 80; // 100 - 20
          
          otherEngines.forEach(e => {
            newConfig[e].weight = Math.round((newConfig[e].weight / totalOtherWeight) * targetOtherWeight);
          });
        }
      }
    }
    
    setConfig(newConfig);
    saveConfiguration(newConfig);
  };

  const resetToDefaults = () => {
    const defaultConfig: EngineConfigUIState = {
      traditional: { enabled: true, weight: 70 },
      fulltext: { enabled: true, weight: 30 }
    };
    setConfig(defaultConfig);
    saveConfiguration(defaultConfig);
  };

  const handleWeightChange = (engine: keyof EngineConfigUIState, newWeight: number) => {
    const newConfig = { ...config };
    const oldWeight = newConfig[engine].weight;
    const weightDiff = newWeight - oldWeight;
    
    newConfig[engine].weight = newWeight;
    
    // Redistribute the weight difference among other enabled engines
    const engines = ['traditional', 'fulltext'] as const;
    const otherEnabledEngines = engines.filter(e => e !== engine && newConfig[e].enabled);
    
    if (otherEnabledEngines.length > 0) {
      const totalOtherWeight = otherEnabledEngines.reduce((sum, e) => sum + newConfig[e].weight, 0);
      
      if (totalOtherWeight > 0) {
        // Distribute proportionally
        let distributedWeight = 0;
        otherEnabledEngines.forEach((e, index) => {
          if (index === otherEnabledEngines.length - 1) {
            // Last engine gets the remainder to ensure total is 100
            newConfig[e].weight = 100 - newWeight - distributedWeight;
          } else {
            const proportion = newConfig[e].weight / totalOtherWeight;
            const adjustedWeight = Math.round(newConfig[e].weight - (weightDiff * proportion));
            newConfig[e].weight = Math.max(0, Math.min(100, adjustedWeight));
            distributedWeight += newConfig[e].weight;
          }
        });
      } else {
        // If all other weights are 0, distribute equally
        const weightPerEngine = Math.floor((100 - newWeight) / otherEnabledEngines.length);
        otherEnabledEngines.forEach((e, index) => {
          if (index === otherEnabledEngines.length - 1) {
            // Last engine gets the remainder
            newConfig[e].weight = 100 - newWeight - (weightPerEngine * (otherEnabledEngines.length - 1));
          } else {
            newConfig[e].weight = weightPerEngine;
          }
        });
      }
    }
    
    setConfig(newConfig);
    saveConfiguration(newConfig);
  };


  if (isLoading) {
    return (
      <div className="search-config-panel loading">
        <div className="loading-spinner"></div>
        <div className="loading-text">{t('options.search.loadingConfig')}</div>
      </div>
    );
  }

  return (
    <div className={`search-config-panel ${className}`}>
      <div className="panel-header">
        <h3>{t('options.search.hybridSearchConfig')}</h3>
        <p>{t('options.search.hybridSearchDescription')}</p>
      </div>

      {message && (
        <div className={`message ${message.type}`}>
          {message.text}
          <button onClick={() => setMessage(null)}>×</button>
        </div>
      )}

      <div className="engine-configs">

        {/* Traditional Search */}
        <div className="engine-config">
          <div className="engine-header">
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={config.traditional.enabled}
                onChange={(e) => handleToggleChange('traditional', e.target.checked)}
                disabled={isSaving}
              />
              <span className="toggle-slider"></span>
            </label>
            <div className="engine-info">
              <h4>
                {t('options.search.engines.traditional.title')}
                <span className="always-show-badge">{t('options.search.alwaysShow')}</span>
              </h4>
              <span className="engine-description">
                {t('options.search.engines.traditional.description')}
              </span>
            </div>
          </div>
          
          <div className="weight-control">
            <label>{t('options.search.weight')}</label>
            <div className="slider-container">
              <input
                type="range"
                min="0"
                max="100"
                value={config.traditional.weight}
                onChange={(e) => handleWeightChange('traditional', parseInt(e.target.value))}
                disabled={isSaving || !config.traditional.enabled}
                className="weight-slider"
              />
              <span className="weight-value">{config.traditional.weight}%</span>
            </div>
          </div>
        </div>

        {/* Full-text Search */}
        <div className="engine-config">
          <div className="engine-header">
            <label className="toggle-switch">
              <input
                type="checkbox"
                checked={config.fulltext.enabled}
                onChange={(e) => handleToggleChange('fulltext', e.target.checked)}
                disabled={isSaving}
              />
              <span className="toggle-slider"></span>
            </label>
            <div className="engine-info">
              <h4>{t('options.search.engines.fulltext.title')}</h4>
              <span className="engine-description">
                {t('options.search.engines.fulltext.description')}
              </span>
            </div>
          </div>
          
          <div className="weight-control">
            <label>{t('options.search.weight')}</label>
            <div className="slider-container">
              <input
                type="range"
                min="0"
                max="100"
                value={config.fulltext.weight}
                onChange={(e) => handleWeightChange('fulltext', parseInt(e.target.value))}
                disabled={isSaving || !config.fulltext.enabled}
                className="weight-slider"
              />
              <span className="weight-value">{config.fulltext.weight}%</span>
            </div>
          </div>
        </div>
      </div>

      <div className="weight-summary">
        <span className="summary-label">{t('options.search.totalWeight')}:</span>
        <span className={`summary-value ${
          (config.traditional.weight + config.fulltext.weight) === 100 
            ? 'valid' : 'invalid'
        }`}>
          {config.traditional.weight + config.fulltext.weight}%
        </span>
      </div>

      <div className="panel-footer">
        <button 
          className="btn btn-outline"
          onClick={resetToDefaults}
          disabled={isSaving}
        >
          {t('options.common.resetToDefaults')}
        </button>
        
        <div className="save-status">
          {isSaving && <span className="saving-indicator">{t('options.common.saving')}</span>}
        </div>
      </div>
    </div>
  );
};

export default SearchConfigPanel;