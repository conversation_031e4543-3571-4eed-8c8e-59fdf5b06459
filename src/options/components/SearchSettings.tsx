/**
 * Search Settings Component
 * 
 * Configuration page for traditional search behaviors
 */

import React, { useState, useEffect, useCallback } from 'react';
import { I18nManager } from '../../i18n/I18nManager';
import { searchConfigService, type UnifiedSearchConfig, DEFAULT_UNIFIED_SEARCH_CONFIG } from '../../services/search-config.service';
import './SearchSettings.css';

// Use the behavior config from UnifiedSearchConfig
type SearchSettings = UnifiedSearchConfig['behavior'];

const DEFAULT_SETTINGS: SearchSettings = DEFAULT_UNIFIED_SEARCH_CONFIG.behavior;

interface SearchSettingsProps {
  className?: string;
}

export const SearchSettings: React.FC<SearchSettingsProps> = ({ className = '' }) => {
  const [settings, setSettings] = useState<SearchSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  // i18n Management
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string, interpolations?: Record<string, any>) => {
    return i18nManager.getTranslation(key, interpolations);
  }, [i18nManager]);

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const config = await searchConfigService.getBehaviorConfig();
      setSettings(config);
    } catch (error) {
      console.error('Failed to load search settings:', error);
      setSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async (newSettings: SearchSettings) => {
    setIsSaving(true);
    try {
      await searchConfigService.updateBehaviorConfig(newSettings);
      setSettings(newSettings);
      setMessage({ type: 'success', text: t('options.common.saved') });
      
      // Clear message after 3 seconds
      setTimeout(() => setMessage(null), 3000);
    } catch (error) {
      console.error('Failed to save settings:', error);
      setMessage({ type: 'error', text: t('options.common.saveFailed') });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSettingChange = (key: keyof SearchSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);
  };

  const resetToDefaults = async () => {
    if (confirm(t('options.common.resetConfirm'))) {
      setIsSaving(true);
      try {
        await searchConfigService.resetToDefaults();
        await loadSettings(); // Reload from service
        setMessage({ type: 'success', text: t('options.common.saved') });
        setTimeout(() => setMessage(null), 3000);
      } catch (error) {
        console.error('Failed to reset settings:', error);
        setMessage({ type: 'error', text: t('options.common.saveFailed') });
      } finally {
        setIsSaving(false);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="search-settings-page loading">
        <div className="loading-spinner"></div>
        <div className="loading-text">{t('options.search.loading')}</div>
      </div>
    );
  }

  return (
    <div className={`search-settings-page ${className}`}>
      <div className="page-header">
        <h2>{t('options.search.title')}</h2>
        <p>{t('options.search.description')}</p>
      </div>

      {message && (
        <div className={`message ${message.type}`}>
          {message.text}
          <button onClick={() => setMessage(null)}>{t('options.common.close')}</button>
        </div>
      )}

      <div className="settings-sections">
        {/* Search Results Section */}
        <section className="settings-section">
          <div className="section-header">
            <h3>{t('options.search.sections.results.title')}</h3>
            <p>{t('options.search.sections.results.description')}</p>
          </div>
          
          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.maxResults.label')}</label>
              <span className="setting-description">{t('options.search.settings.maxResults.description')}</span>
            </div>
            <div className="setting-control">
              <div className="input-with-unit">
                <input
                  type="number"
                  min="5"
                  max="100"
                  value={settings.maxResults}
                  onChange={(e) => handleSettingChange('maxResults', parseInt(e.target.value))}
                  disabled={isSaving}
                />
                <span className="unit">{t('options.common.units.items')}</span>
              </div>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.searchTimeout.label')}</label>
              <span className="setting-description">{t('options.search.settings.searchTimeout.description')}</span>
            </div>
            <div className="setting-control">
              <div className="input-with-unit">
                <input
                  type="number"
                  min="1000"
                  max="30000"
                  step="1000"
                  value={settings.searchTimeoutMs}
                  onChange={(e) => handleSettingChange('searchTimeoutMs', parseInt(e.target.value))}
                  disabled={isSaving}
                />
                <span className="unit">{t('options.common.units.ms')}</span>
              </div>
            </div>
          </div>
        </section>

        {/* Keyword Search Section */}
        <section className="settings-section">
          <div className="section-header">
            <h3>{t('options.search.sections.keyword.title')}</h3>
            <p>{t('options.search.sections.keyword.description')}</p>
          </div>
          
          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.enableKeywordSearch.label')}</label>
              <span className="setting-description">{t('options.search.settings.enableKeywordSearch.description')}</span>
            </div>
            <div className="setting-control">
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={settings.enableKeywordSearch}
                  onChange={(e) => handleSettingChange('enableKeywordSearch', e.target.checked)}
                  disabled={isSaving}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.fuzzySearchThreshold.label')}</label>
              <span className="setting-description">{t('options.search.settings.fuzzySearchThreshold.description')}</span>
            </div>
            <div className="setting-control">
              <div className="slider-container">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={settings.fuzzySearchThreshold}
                  onChange={(e) => handleSettingChange('fuzzySearchThreshold', parseFloat(e.target.value))}
                  disabled={isSaving || !settings.enableKeywordSearch}
                />
                <span className="slider-value">{settings.fuzzySearchThreshold}</span>
              </div>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.keywordSearchWeight.label')}</label>
              <span className="setting-description">{t('options.search.settings.keywordSearchWeight.description')}</span>
            </div>
            <div className="setting-control">
              <div className="slider-container">
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={settings.keywordSearchWeight}
                  onChange={(e) => handleSettingChange('keywordSearchWeight', parseFloat(e.target.value))}
                  disabled={isSaving || !settings.enableKeywordSearch}
                />
                <span className="slider-value">{Math.round(settings.keywordSearchWeight * 100)}{t('options.common.units.percent')}</span>
              </div>
            </div>
          </div>
        </section>

        {/* Indexing Section */}
        <section className="settings-section">
          <div className="section-header">
            <h3>{t('options.search.sections.indexing.title')}</h3>
            <p>{t('options.search.sections.indexing.description')}</p>
          </div>
          
          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.enableIndexing.label')}</label>
              <span className="setting-description">{t('options.search.settings.enableIndexing.description')}</span>
            </div>
            <div className="setting-control">
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={settings.indexingEnabled}
                  onChange={(e) => handleSettingChange('indexingEnabled', e.target.checked)}
                  disabled={isSaving}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.indexingDelay.label')}</label>
              <span className="setting-description">{t('options.search.settings.indexingDelay.description')}</span>
            </div>
            <div className="setting-control">
              <div className="input-with-unit">
                <input
                  type="number"
                  min="500"
                  max="10000"
                  step="500"
                  value={settings.indexingDelay}
                  onChange={(e) => handleSettingChange('indexingDelay', parseInt(e.target.value))}
                  disabled={isSaving || !settings.indexingEnabled}
                />
                <span className="unit">{t('options.common.units.ms')}</span>
              </div>
            </div>
          </div>
        </section>

        {/* User Experience Section */}
        <section className="settings-section">
          <div className="section-header">
            <h3>{t('options.search.sections.userExperience.title')}</h3>
            <p>{t('options.search.sections.userExperience.description')}</p>
          </div>
          
          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.enableSearchHistory.label')}</label>
              <span className="setting-description">{t('options.search.settings.enableSearchHistory.description')}</span>
            </div>
            <div className="setting-control">
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={settings.enableSearchHistory}
                  onChange={(e) => handleSettingChange('enableSearchHistory', e.target.checked)}
                  disabled={isSaving}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.enableAutoComplete.label')}</label>
              <span className="setting-description">{t('options.search.settings.enableAutoComplete.description')}</span>
            </div>
            <div className="setting-control">
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={settings.enableAutoComplete}
                  onChange={(e) => handleSettingChange('enableAutoComplete', e.target.checked)}
                  disabled={isSaving}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div className="setting-item">
            <div className="setting-info">
              <label>{t('options.search.settings.enableDebugMode.label')}</label>
              <span className="setting-description">{t('options.search.settings.enableDebugMode.description')}</span>
            </div>
            <div className="setting-control">
              <label className="toggle-switch">
                <input
                  type="checkbox"
                  checked={settings.enableDebugMode}
                  onChange={(e) => handleSettingChange('enableDebugMode', e.target.checked)}
                  disabled={isSaving}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </section>
      </div>

      <div className="settings-footer">
        <button 
          className="btn btn-outline"
          onClick={resetToDefaults}
          disabled={isSaving}
        >
          {t('options.common.resetToDefaults')}
        </button>
        
        <div className="save-status">
          {isSaving && <span className="saving-indicator">{t('options.common.saving')}</span>}
        </div>
      </div>
    </div>
  );
}; 