/**
 * Language Settings Component
 * 
 * Allows users to change the interface language in the options page
 */

import React, { useState, useCallback, useEffect } from 'react';
import { LanguageSelector } from '../../i18n/LanguageSelector';
import { I18nManager } from '../../i18n/I18nManager';

/**
 * Language Settings Component
 */
export const LanguageSettings: React.FC = () => {
  const [i18nManager] = useState(() => I18nManager.getInstance());
  const [, forceUpdate] = useState({});

  // Translation helper function
  const t = useCallback((key: string) => {
    return i18nManager.getTranslation(key);
  }, [i18nManager]);

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({});
    };

    i18nManager.addLanguageChangeListener(handleLanguageChange);
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [i18nManager]);

  // Load current language resources on mount
  useEffect(() => {
    i18nManager.loadLanguageResources(i18nManager.getCurrentLanguage()).catch(console.error);
  }, [i18nManager]);

  return (
    <div className="language-settings">
      <div className="section-header">
        <h2 className="section-title">{t('options.navigation.languageSettings.label')}</h2>
        <p className="section-description">
          {t('options.languageSettings.description')}
        </p>
      </div>

      <div className="section-content">
        <div className="setting-group">
          <div className="setting-item">
            <div className="setting-info">
              <h3 className="setting-label">{t('options.languageSettings.interfaceLanguage')}</h3>
              <p className="setting-description">
                {t('options.languageSettings.interfaceDescription')}
              </p>
            </div>
            <div className="setting-control">
              <LanguageSelector className="options-language-selector" />
            </div>
          </div>
        </div>

        <div className="setting-note">
          <div className="note-icon">ℹ️</div>
          <div className="note-content">
            <p>{t('options.languageSettings.note')}</p>
          </div>
        </div>
      </div>
    </div>
  );
};