/**
 * Search Settings Component Styles
 */

.search-settings-page {
  max-width: 800px;
}

.search-settings-page.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  flex-direction: column;
  gap: 16px;
}

.search-settings-page .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.search-settings-page .loading-text {
  color: #6c757d;
  font-size: 14px;
}

/* Messages */
.message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  font-size: 14px;
}

.message.success {
  background-color: #d1e7dd;
  color: #0f5132;
  border: 1px solid #badbcc;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c2c7;
}

.message button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.message.success button {
  color: #0f5132;
}

.message.error button {
  color: #721c24;
}

.message button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Settings Sections */
.settings-sections {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.settings-section {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
}

.section-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.section-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

/* Setting Items */
.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.setting-info {
  flex: 1;
  min-width: 0;
  margin-right: 20px;
}

.setting-info label {
  display: block;
  font-weight: 500;
  color: #495057;
  font-size: 14px;
  margin-bottom: 4px;
}

.setting-description {
  display: block;
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

.setting-control {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .3s;
  border-radius: 12px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  top: 3px;
  background-color: white;
  transition: .3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #667eea;
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

input:disabled + .toggle-slider {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Input with Unit */
.input-with-unit {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-with-unit input {
  width: 80px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
}

.input-with-unit input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-with-unit input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.unit {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

/* Slider */
.slider-container {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 150px;
}

.slider-container input[type="range"] {
  flex: 1;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.slider-container input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
}

.slider-container input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.slider-container input[type="range"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.slider-value {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

/* Buttons */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-outline {
  background-color: transparent;
  color: #667eea;
  border: 1px solid #667eea;
}

.btn-outline:hover:not(:disabled) {
  background-color: #667eea;
  color: white;
}

/* Settings Footer */
.settings-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

.save-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.saving-indicator {
  font-size: 13px;
  color: #667eea;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.saving-indicator::before {
  content: '';
  width: 12px;
  height: 12px;
  border: 2px solid #e9ecef;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .setting-info {
    margin-right: 0;
  }
  
  .setting-control {
    width: 100%;
    justify-content: flex-start;
  }
  
  .input-with-unit input {
    width: 100px;
  }
  
  .slider-container {
    min-width: 200px;
  }
  
  .settings-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .settings-section {
    padding: 16px;
  }
  
  .section-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
  }
  
  .setting-item {
    padding: 12px 0;
  }
  
  .slider-container {
    min-width: 100%;
  }
}