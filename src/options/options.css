/**
 * Recall Options Page Styles
 */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8f9fa;
  color: #495057;
  line-height: 1.5;
}

/* Loading styles */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  flex-direction: column;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #6c757d;
  font-size: 16px;
}

/* Options app layout */
.options-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.options-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.app-branding {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-icon {
  font-size: 32px;
}

.app-title {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
}

.app-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin-left: 8px;
}

/* Main content area */
.options-main {
  flex: 1;
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  min-height: calc(100vh - 88px);
}

/* Sidebar navigation */
.options-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e9ecef;
  padding: 24px 0;
}

.navigation-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: none;
  background: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.nav-item:hover {
  background-color: #f8f9fa;
}

.nav-item.active {
  background-color: #667eea;
  color: white;
}

.nav-item.active .nav-description {
  color: rgba(255, 255, 255, 0.8);
}

.nav-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.nav-content {
  flex: 1;
  min-width: 0;
}

.nav-label {
  display: block;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 2px;
}

.nav-description {
  display: block;
  font-size: 12px;
  color: #6c757d;
  line-height: 1.3;
}

/* Content area */
.options-content {
  flex: 1;
  padding: 32px;
  background: white;
  margin: 24px 24px 24px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Page content */
.page-content h2 {
  margin: 0 0 24px 0;
  font-size: 24px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-content p {
  margin: 0 0 16px 0;
  color: #6c757d;
  line-height: 1.6;
}

/* Feature preview */
.feature-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 24px;
  border-left: 4px solid #667eea;
}

.feature-preview h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.feature-preview ul {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
}

.feature-preview li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* About page specific styles */
.about-content {
  max-width: 600px;
}

.app-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.app-info .app-icon {
  font-size: 48px;
}

.app-details h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
  color: #495057;
}

.version {
  margin: 0 0 8px 0 !important;
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
}

.description {
  margin: 0 !important;
  color: #6c757d;
}

.features-section,
.help-section {
  margin-bottom: 32px;
}

.features-section h3,
.help-section h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #495057;
}

.features-section ul {
  margin: 0;
  padding-left: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 8px;
}

.features-section li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #6c757d;
}

.footer-info {
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.footer-info p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Responsive design */
@media (max-width: 768px) {
  .options-main {
    flex-direction: column;
  }
  
  .options-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }
  
  .navigation-list {
    flex-direction: row;
    overflow-x: auto;
    padding: 0 16px;
    gap: 8px;
  }
  
  .nav-item {
    flex-shrink: 0;
    min-width: 200px;
  }
  
  .options-content {
    margin: 0 16px 16px 16px;
    padding: 24px;
  }
  
  .app-branding {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .app-subtitle {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 16px;
  }
  
  .options-content {
    padding: 16px;
  }
  
  .app-info {
    flex-direction: column;
    text-align: center;
  }
  
  .features-section ul {
    grid-template-columns: 1fr;
  }
}
