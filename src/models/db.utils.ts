/**
 * 数据库工具函数
 * 
 * 提供数据处理、验证和转换的辅助函数
 */

import type { Page, Setting } from './db.model';

/**
 * 生成UUID
 * 用于创建页面的唯一标识符
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 从URL提取域名
 * @param url 完整URL
 * @returns 域名字符串
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    // 如果URL格式不正确，返回原始字符串的一部分
    const match = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/);
    return match ? match[1] : url;
  }
}

/**
 * 清理和标准化页面内容
 * @param content 原始内容
 * @returns 清理后的内容
 */
export function cleanContent(content: string): string {
  return content
    // 移除多余的空白字符
    .replace(/\s+/g, ' ')
    // 移除首尾空白
    .trim()
    // 限制最大长度（避免存储过大的内容）
    .substring(0, 50000);
}

/**
 * 清理和标准化页面标题
 * @param title 原始标题
 * @returns 清理后的标题
 */
export function cleanTitle(title: string): string {
  return title
    .replace(/\s+/g, ' ')
    .trim()
    .substring(0, 500); // 限制标题长度
}

/**
 * 验证页面数据的完整性
 * @param page 页面数据
 * @returns 验证结果
 */
export function validatePage(page: Partial<Page>): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!page.url || typeof page.url !== 'string') {
    errors.push('URL is required and must be a string');
  }

  if (!page.title || typeof page.title !== 'string') {
    errors.push('Title is required and must be a string');
  }

  // 内容可以为空，但如果提供则必须是字符串
  if (page.content !== undefined && typeof page.content !== 'string') {
    errors.push('Content must be a string if provided');
  }

  if (page.visitTime && (typeof page.visitTime !== 'number' || page.visitTime <= 0)) {
    errors.push('Visit time must be a positive number');
  }

  if (page.accessCount && (typeof page.accessCount !== 'number' || page.accessCount < 1)) {
    errors.push('Access count must be a positive number');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 创建新的页面记录
 * @param data 页面基础数据
 * @returns 完整的页面记录
 */
export function createPage(data: {
  url: string;
  title: string;
  content: string;
  contentStatus?: 'extracted' | 'failed' | 'pending' | 'empty';
  extractionError?: string;
  lastExtractionAttempt?: number;
  visitTime?: number; // Allow specifying visitTime for imports
  accessCount?: number; // Allow specifying accessCount for imports
  lastUpdated?: number; // Allow specifying lastUpdated for imports
}): Page {
  const now = Date.now();
  const domain = extractDomain(data.url);

  return {
    id: generateUUID(),
    url: data.url,
    title: cleanTitle(data.title),
    content: cleanContent(data.content || ''), // 允许空内容
    domain,
    visitTime: data.visitTime || now, // Use provided visitTime or current time
    accessCount: data.accessCount || 1,
    lastUpdated: data.lastUpdated || now, // Use provided lastUpdated or current time
    contentStatus: data.contentStatus || (data.content ? 'extracted' : 'empty'),
    extractionError: data.extractionError,
    lastExtractionAttempt: data.lastExtractionAttempt || now
  };
}

/**
 * 更新现有页面记录
 * @param existingPage 现有页面记录
 * @param newData 新的页面数据
 * @returns 更新后的页面记录
 */
export function updatePage(
  existingPage: Page, 
  newData: { 
    title?: string; 
    content?: string;
    contentStatus?: 'extracted' | 'failed' | 'pending' | 'empty';
    extractionError?: string;
    lastExtractionAttempt?: number;
  }
): Page {
  const now = Date.now();
  
  return {
    ...existingPage,
    title: newData.title ? cleanTitle(newData.title) : existingPage.title,
    content: newData.content !== undefined ? cleanContent(newData.content) : existingPage.content,
    accessCount: existingPage.accessCount + 1,
    lastUpdated: now,
    contentStatus: newData.contentStatus || existingPage.contentStatus,
    extractionError: newData.extractionError !== undefined ? newData.extractionError : existingPage.extractionError,
    lastExtractionAttempt: newData.lastExtractionAttempt || now
  };
}

/**
 * 创建设置记录
 * @param key 设置键
 * @param value 设置值
 * @returns 设置记录
 */
export function createSetting(key: string, value: any): Setting {
  const now = Date.now();
  
  return {
    key,
    value,
    createdAt: now,
    updatedAt: now
  };
}

/**
 * 更新设置记录
 * @param existingSetting 现有设置
 * @param newValue 新值
 * @returns 更新后的设置
 */
export function updateSetting(existingSetting: Setting, newValue: any): Setting {
  return {
    ...existingSetting,
    value: newValue,
    updatedAt: Date.now()
  };
}

/**
 * 检查URL是否应该被索引
 * @param url 页面URL
 * @param blacklistDomains 黑名单域名列表
 * @returns 是否应该索引
 */
export function shouldIndexUrl(url: string, blacklistDomains: string[] = []): boolean {
  try {
    const urlObj = new URL(url);
    
    // 排除特殊协议
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return false;
    }
    
    // 排除本地地址
    if (['localhost', '127.0.0.1', '0.0.0.0'].includes(urlObj.hostname)) {
      return false;
    }
    
    // 排除黑名单域名
    const domain = urlObj.hostname;
    if (blacklistDomains.some(blackDomain => 
      domain === blackDomain || domain.endsWith('.' + blackDomain)
    )) {
      return false;
    }
    
    // 排除常见的非内容页面
    const pathname = urlObj.pathname.toLowerCase();
    const excludePatterns = [
      '/api/',
      '/ajax/',
      '/json/',
      '/xml/',
      '.json',
      '.xml',
      '.pdf',
      '.doc',
      '.docx',
      '.xls',
      '.xlsx',
      '.zip',
      '.rar'
    ];
    
    if (excludePatterns.some(pattern => pathname.includes(pattern))) {
      return false;
    }
    
    return true;
  } catch (error) {
    // URL格式错误，不索引
    return false;
  }
}

/**
 * 格式化时间戳为可读字符串
 * @param timestamp 时间戳
 * @returns 格式化的时间字符串
 */
export function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - timestamp;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  } else if (diffDays === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString('zh-CN');
  }
}

/**
 * 计算存储大小（估算）
 * @param page 页面数据
 * @returns 估算的字节大小
 */
export function estimatePageSize(page: Page): number {
  const jsonString = JSON.stringify(page);
  return new Blob([jsonString]).size;
}
