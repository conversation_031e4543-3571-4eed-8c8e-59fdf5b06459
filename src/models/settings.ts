/**
 * Settings model definitions
 * 
 * This file defines the settings-related data structures for the Recall extension
 */

/**
 * Search engine configuration for individual search methods
 */
export interface SearchEngineSettings {
  /** Whether this search engine is enabled */
  enabled: boolean;
  
  /** Weight of this engine in the final score (0-1) */
  weight: number;
  
  /** Whether to always show results from this engine even if score is low */
  alwaysShow?: boolean;
}

/**
 * Search engine configuration interface
 * Controls the behavior of search combining multiple search methods
 */
export interface SearchEngineConfig {
  /** Traditional fuzzy search configuration */
  traditional: SearchEngineSettings;
  
  /** Full-text search configuration */
  fulltext: SearchEngineSettings;
}

/**
 * Default search engine configuration
 * Provides optimal balance between different search methods
 */
export const DEFAULT_SEARCH_ENGINE_CONFIG: SearchEngineConfig = {
  traditional: {
    enabled: true,
    weight: 0.7,
    alwaysShow: true
  },
  fulltext: {
    enabled: true,
    weight: 0.3
  }
};

/**
 * Validates search engine configuration
 * Ensures weights are normalized and within valid ranges
 */
export function validateSearchEngineConfig(config: SearchEngineConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check weight ranges
  const engines = ['traditional', 'fulltext'] as const;
  for (const engine of engines) {
    const settings = config[engine];
    if (settings.weight < 0 || settings.weight > 1) {
      errors.push(`${engine} weight must be between 0 and 1`);
    }
  }
  
  // Check total weight
  const totalWeight = config.traditional.weight + config.fulltext.weight;
  if (Math.abs(totalWeight - 1.0) > 0.01) {
    errors.push(`Total weight must equal 1.0 (current: ${totalWeight})`);
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Normalizes search engine weights to ensure they sum to 1.0
 */
export function normalizeSearchEngineWeights(config: SearchEngineConfig): SearchEngineConfig {
  const enabledEngines = [];
  if (config.traditional.enabled) enabledEngines.push('traditional');
  if (config.fulltext.enabled) enabledEngines.push('fulltext');
  
  if (enabledEngines.length === 0) {
    // If no engines are enabled, enable traditional by default
    return {
      ...config,
      traditional: {
        ...config.traditional,
        enabled: true,
        weight: 1.0
      },
      fulltext: {
        ...config.fulltext,
        weight: 0
      }
    };
  }
  
  // Calculate total weight of enabled engines
  let totalWeight = 0;
  for (const engine of enabledEngines) {
    totalWeight += config[engine as keyof SearchEngineConfig].weight;
  }
  
  // Normalize weights
  const normalized = { ...config };
  if (totalWeight > 0) {
    for (const engine of enabledEngines) {
      const key = engine as keyof SearchEngineConfig;
      normalized[key] = {
        ...normalized[key],
        weight: normalized[key].weight / totalWeight
      };
    }
  } else {
    // If all weights are 0, distribute equally
    const equalWeight = 1.0 / enabledEngines.length;
    for (const engine of enabledEngines) {
      const key = engine as keyof SearchEngineConfig;
      normalized[key] = {
        ...normalized[key],
        weight: equalWeight
      };
    }
  }
  
  // Set disabled engines to 0 weight
  const allEngines = ['traditional', 'fulltext'] as const;
  for (const engine of allEngines) {
    if (!normalized[engine].enabled) {
      normalized[engine] = {
        ...normalized[engine],
        weight: 0
      };
    }
  }
  
  return normalized;
}