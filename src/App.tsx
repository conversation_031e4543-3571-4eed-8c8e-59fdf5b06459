/**
 * Recall Popup - Main Application Component
 * Intelligent History Search with Enhanced UI/UX
 */

/// <reference path="./types/chrome.d.ts" />

import { useState, useEffect, useCallback, useRef, useMemo } from "react"
import { SearchBar } from "./popup/components/SearchBar"
import { ResultList } from "./popup/components/ResultList"
import { VirtualResultList } from "./popup/components/VirtualResultList"
import { Filters } from "./popup/components/Filters"
import { StatusBar } from "./popup/components/StatusBar"
import { SkeletonLoader } from "./popup/components/SkeletonLoader"
import { LanguageSelector } from "./i18n/LanguageSelector"
import { I18nManager } from "./i18n/I18nManager"
import { useSmartScrolling } from "./popup/hooks/useSmartScrolling"
import { hybridSearchService } from "./services"
import type { SearchResultItem, SearchOptions } from "./models"
import { debounce } from "./utils"
import "./App.css"

// ============================================================================
// Types & Constants
// ============================================================================

const ERROR_TYPES = {
  NETWORK: "network",
  SERVICE_INIT: "service_init",
  SEARCH: "search",
  DATABASE: "database",
  UNKNOWN: "unknown",
} as const

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES]

interface ErrorInfo {
  type: ErrorType
  title: string
  message: string
  solutions: string[]
  canRetry: boolean
}

interface AppState {
  searchQuery: string
  searchResults: SearchResultItem[]
  isLoading: boolean
  error: ErrorInfo | null
  totalResults: number
  searchTime: number
  filters: Partial<SearchOptions>
  densityMode: 'compact' | 'comfortable'
}

// ============================================================================
// Utility Functions
// ============================================================================

const formatError = (error: unknown, context: string, t: (key: string) => string): ErrorInfo => {
  const errorMsg = error instanceof Error ? error.message : String(error)

  const errorConfigs: Record<string, Omit<ErrorInfo, "type">> = {
    service_init: {
      title: t("errors.service_init.title"),
      message: t("errors.service_init.message"),
      solutions: [
        t("errors.service_init.solutions.0"),
        t("errors.service_init.solutions.1"),
        t("errors.service_init.solutions.2"),
        t("errors.service_init.solutions.3")
      ],
      canRetry: true,
    },
    search: {
      title: t("errors.search.title"),
      message: t("errors.search.message"),
      solutions: [
        t("errors.search.solutions.0"),
        t("errors.search.solutions.1"),
        t("errors.search.solutions.2"),
        t("errors.search.solutions.3")
      ],
      canRetry: true,
    },
    network: {
      title: t("errors.network.title"),
      message: t("errors.network.message"),
      solutions: [
        t("errors.network.solutions.0"),
        t("errors.network.solutions.1"),
        t("errors.network.solutions.2")
      ],
      canRetry: true,
    },
    database: {
      title: t("errors.database.title"),
      message: t("errors.database.message"),
      solutions: [
        t("errors.database.solutions.0"),
        t("errors.database.solutions.1"),
        t("errors.database.solutions.2"),
        t("errors.database.solutions.3")
      ],
      canRetry: false,
    },
  }

  // Determine error type based on context and message
  let errorType: ErrorType = ERROR_TYPES.UNKNOWN

  if (context === "service_init") {
    errorType = ERROR_TYPES.SERVICE_INIT
  } else if (context === "search" || errorMsg.includes("search")) {
    errorType = ERROR_TYPES.SEARCH
  } else if (errorMsg.includes("network") || errorMsg.includes("fetch")) {
    errorType = ERROR_TYPES.NETWORK
  } else if (errorMsg.includes("database") || errorMsg.includes("IndexedDB")) {
    errorType = ERROR_TYPES.DATABASE
  }

  const config = errorConfigs[errorType] || {
    title: t("errors.unknown.title"),
    message: errorMsg || t("errors.unknown.message"),
    solutions: [
      t("errors.unknown.solutions.0"),
      t("errors.unknown.solutions.1"),
      t("errors.unknown.solutions.2"),
      t("errors.unknown.solutions.3")
    ],
    canRetry: true,
  }

  return { type: errorType, ...config }
}

// ============================================================================
// Main App Component
// ============================================================================

function App() {
  // State Management
  const [state, setState] = useState<AppState>({
    searchQuery: "",
    searchResults: [],
    isLoading: false,
    error: null,
    totalResults: 0,
    searchTime: 0,
    filters: {},
    densityMode: (localStorage.getItem('recall-density-mode') as 'compact' | 'comfortable') || 'compact',
  })

  // 智能滚动决策
  const scrollingDecision = useSmartScrolling(state.searchResults, {
    virtualScrollThreshold: 50,
    containerHeight: 350,
    itemHeight: state.densityMode === 'compact' ? 100 : 120,
    // 可以基于用户设置强制启用/禁用
    forceVirtual: localStorage.getItem('recall-force-virtual') === 'true',
    forceNonVirtual: localStorage.getItem('recall-force-virtual') === 'false'
  })

  // i18n Management
  const [i18nManager] = useState(() => I18nManager.getInstance())
  const [, forceUpdate] = useState({})
  const [i18nReady, setI18nReady] = useState(false)

  // Translation helper function
  const t = useCallback((key: string, interpolations?: Record<string, any>) => {
    return i18nManager.getTranslation(key, interpolations)
  }, [i18nManager])

  // Ref to store the latest search query for debounced execution
  const latestQueryRef = useRef<string>("")
  const latestFiltersRef = useRef<Partial<SearchOptions>>({})

  // Update refs whenever state changes
  useEffect(() => {
    latestQueryRef.current = state.searchQuery
    latestFiltersRef.current = state.filters
  }, [state.searchQuery, state.filters])

  // ============================================================================
  // Service Initialization
  // ============================================================================

  useEffect(() => {
    const initializeServices = async () => {
      try {
        // Initialize i18n manager first and wait for it to be ready
        await i18nManager.waitForInitialization()
        setI18nReady(true)
        console.log("[Popup] I18n initialized successfully")

        // Initialize hybrid search service
        await hybridSearchService.init()
      } catch (error) {
        setState((prev) => ({
          ...prev,
          error: formatError(error, "service_init", t),
        }))
      }
    }

    initializeServices()
  }, [i18nManager, t])

  // Language change handler to force re-render
  useEffect(() => {
    const handleLanguageChange = () => {
      forceUpdate({})
    }

    i18nManager.addLanguageChangeListener(handleLanguageChange)
    return () => {
      i18nManager.removeLanguageChangeListener(handleLanguageChange)
    }
  }, [i18nManager])

  // ============================================================================
  // Search Operations
  // ============================================================================

  const performSearch = useCallback(async (query: string, filters: Partial<SearchOptions> = {}) => {
    // Check if query is stale at the beginning
    if (latestQueryRef.current !== query) {
      return;
    }

    if (!query.trim()) {
      // Double-check query hasn't changed before updating state
      if (latestQueryRef.current === query) {
        setState((prev) => ({
          ...prev,
          searchResults: [],
          totalResults: 0,
          searchTime: 0,
          error: null,
          isLoading: false,
        }));
      }
      return;
    }

    // Only set loading if query is still current
    if (latestQueryRef.current === query) {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));
    }

    try {
      const startTime = Date.now();
      
      // Check staleness before expensive search operation
      if (latestQueryRef.current !== query) {
        return;
      }
      
      const results = await hybridSearchService.search(query, { ...filters, limit: 50 });
      const searchTime = Date.now() - startTime;

      // Ensure we only update state if the query hasn't changed
      if (latestQueryRef.current === query) {
        setState((prev) => ({
          ...prev,
          searchResults: results,
          totalResults: results.length,
          searchTime,
          isLoading: false,
        }));
      }
    } catch (error) {
      // Ensure we only update state if the query hasn't changed
      if (latestQueryRef.current === query) {
        setState((prev) => ({
          ...prev,
          isLoading: false,
          error: formatError(error, "search", t),
        }));
      }
    }
  }, [t])

  // Create debounced search function
  const debouncedSearch = useMemo(
    () => debounce((query: string, filters: Partial<SearchOptions>) => {
      performSearch(query, filters)
    }, 250), // 减少防抖延迟以获得更快的响应
    [performSearch]
  )

  // ============================================================================
  // Event Handlers
  // ============================================================================

  const handleDensityModeToggle = useCallback(() => {
    setState((prev) => {
      const newDensity = prev.densityMode === 'comfortable' ? 'compact' : 'comfortable'
      localStorage.setItem('recall-density-mode', newDensity)
      return { ...prev, densityMode: newDensity }
    })
  }, [])

  const handleQueryChange = useCallback((query: string) => {
    // Handle empty queries immediately to prevent loading state issues
    if (!query.trim()) {
      // Cancel any pending debounced search
      debouncedSearch.cancel();
      
      setState(prev => ({
        ...prev,
        searchQuery: query,
        searchResults: [],
        totalResults: 0,
        searchTime: 0,
        error: null,
        isLoading: false, // Immediately set to false for empty queries
      }));
      return;
    }

    // 立即更新UI状态
    setState(prev => ({ ...prev, searchQuery: query, isLoading: true }));
    // 执行防抖搜索
    debouncedSearch(query, latestFiltersRef.current);
  }, [debouncedSearch]);

  const handleFiltersChange = useCallback(
    (newFilters: Partial<SearchOptions>) => {
      setState((prev) => {
        const updatedFilters = { ...prev.filters, ...newFilters }
        // Update ref immediately
        latestFiltersRef.current = updatedFilters
        
        // If there's a query, perform search with new filters
        if (prev.searchQuery.trim()) {
          debouncedSearch(prev.searchQuery, updatedFilters)
        }
        
        return { ...prev, filters: updatedFilters }
      })
    },
    [debouncedSearch]  // No longer depends on state values
  )

  const handleClearSearch = useCallback(() => {
    // Cancel any pending debounced search first
    debouncedSearch.cancel();
    
    // Clear the latest query ref to prevent race conditions
    latestQueryRef.current = "";
    
    setState((prev) => ({
      ...prev,
      searchQuery: "",
      searchResults: [],
      totalResults: 0,
      searchTime: 0,
      error: null,
      isLoading: false, // 确保清除时停止加载状态
    }));
  }, [debouncedSearch])

  const handleResultClick = useCallback((result: SearchResultItem) => {
    chrome.tabs.create({ url: result.page.url })
  }, [])

  const handleExampleSearch = useCallback(
    (query: string) => {
      handleQueryChange(query)
    },
    [handleQueryChange],
  )

  // ============================================================================
  // Keyboard Shortcuts
  // ============================================================================

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const { key, ctrlKey, metaKey } = event
      const isModifierPressed = ctrlKey || metaKey

      // Focus search shortcuts
      if ((isModifierPressed && (key === "k" || key === "/")) || key === "F3" || (isModifierPressed && key === "f")) {
        event.preventDefault()
        const searchInput = document.querySelector(".search-input") as HTMLInputElement
        searchInput?.focus()
        if (key === "k") searchInput?.select()
        return
      }

      // Escape key handling
      if (key === "Escape") {
        event.preventDefault()
        const activeElement = document.activeElement as HTMLElement

        if (activeElement?.classList.contains("search-input")) {
          if (state.searchQuery) {
            handleClearSearch()
          } else {
            activeElement.blur()
          }
        } else if (state.searchQuery) {
          handleClearSearch()
        }
        return
      }

      // Result navigation (only when results exist)
      if (state.searchResults.length > 0) {
        const resultItems = Array.from(document.querySelectorAll(".result-item")) as HTMLElement[]
        const currentFocus = document.activeElement as HTMLElement
        const currentIndex = resultItems.findIndex((item) => item === currentFocus)

        if (key === "ArrowDown") {
          event.preventDefault()
          const nextIndex = currentIndex + 1
          if (nextIndex < resultItems.length) {
            resultItems[nextIndex].focus()
          } else if (currentIndex === -1 && resultItems.length > 0) {
            resultItems[0].focus()
          }
          return
        }

        if (key === "ArrowUp") {
          event.preventDefault()
          if (currentIndex > 0) {
            resultItems[currentIndex - 1].focus()
          } else if (currentIndex === 0) {
            const searchInput = document.querySelector(".search-input") as HTMLInputElement
            searchInput?.focus()
          }
          return
        }

        if (key === "Enter" && currentFocus?.classList.contains("result-item")) {
          event.preventDefault()
          const resultIndex = resultItems.findIndex((item) => item === currentFocus)
          if (resultIndex >= 0 && state.searchResults[resultIndex]) {
            handleResultClick(state.searchResults[resultIndex])
          }
          return
        }
      }

      // Quick open shortcuts (Ctrl/Cmd + 1-9)
      if (isModifierPressed && key >= "1" && key <= "9") {
        event.preventDefault()
        const index = Number.parseInt(key) - 1
        if (state.searchResults[index]) {
          handleResultClick(state.searchResults[index])
        }
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [state.searchQuery, state.searchResults, handleClearSearch, handleResultClick])

  // ============================================================================
  // Render Components
  // ============================================================================

  const renderErrorState = () => (
    <div className="rc-error-state">
      <ErrorIcon type={state.error!.type} />
      <div className="rc-error-content">
        <h3 className="rc-error-title">{state.error!.title}</h3>
        <p className="rc-error-message">{state.error!.message}</p>

        <div className="rc-error-solutions">
          <h4 className="rc-solutions-title">{t("errors.solutionsTitle")}</h4>
          <ul className="rc-solutions-list">
            {state.error!.solutions.map((solution, index) => (
              <li key={index} className="rc-solution-item">
                <span className="rc-solution-number">{index + 1}</span>
                <span className="rc-solution-text">{solution}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="rc-error-actions">
          {state.error!.canRetry && (
            <button className="rc-retry-button primary" onClick={() => window.location.reload()}>
              {t("errors.reload")}
            </button>
          )}

          <button className="rc-retry-button secondary" onClick={() => setState((prev) => ({ ...prev, error: null }))}>
            {t("errors.closeError")}
          </button>

          <button
            className="rc-retry-button secondary"
            onClick={() => chrome.tabs.create({ url: chrome.runtime.getURL("options.html") })}
          >
            {t("errors.openSettings")}
          </button>
        </div>
      </div>
    </div>
  )

  const renderEmptyState = () => (
    <div className="rc-empty-state">
      <div className="rc-empty-illustration">
        <SearchIcon />
      </div>

      <div className="rc-empty-content">
        <h2 className="rc-empty-title">{t("empty.title")}</h2>
        <p className="rc-empty-description">{t("empty.description")}</p>

        <FeatureList t={t} />
        <SearchTips t={t} />
        <KeyboardShortcuts t={t} />
        <SearchExamples onExampleClick={handleExampleSearch} t={t} />
      </div>
    </div>
  )

  // ============================================================================
  // Main Render
  // ============================================================================

  // Show loading state until i18n is ready
  if (!i18nReady) {
    return (
      <div className="rc-app rc-app-loading">
        <div className="rc-loading-state">
          <div className="rc-loading-spinner"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="rc-app" data-density={state.densityMode}>
      {/* Header */}
      <header className="rc-header">
        <div className="rc-brand">
          <div className="rc-logo">
            <SearchIcon size={20} />
          </div>
          <div className="rc-brand-text">
            <h1 className="rc-title">Recall</h1>
            <span className="rc-subtitle">{t("app.subtitle")}</span>
          </div>
        </div>
        <div className="rc-header-actions">
          <button
            className="rc-density-toggle"
            onClick={handleDensityModeToggle}
            title={t("app.densityMode." + state.densityMode)}
            aria-label={t("app.toggleDensity")}
          >
            <DensityIcon density={state.densityMode} />
          </button>
          <LanguageSelector className="rc-language-selector" />
        </div>
      </header>

      {/* Search Section */}
      <section className="rc-search-section">
        <div className="rc-search-container">
          <SearchBar
            value={state.searchQuery}
            onChange={handleQueryChange}
            onClear={handleClearSearch}
            isLoading={state.isLoading}
            placeholder={t("search.placeholder")}
          />
        </div>

        <div className="rc-filters-container">
          <Filters filters={state.filters} onChange={handleFiltersChange} />
        </div>
      </section>

      {/* Content */}
      <main className="rc-content">
        {state.error ? (
          renderErrorState()
        ) : state.searchQuery.trim() || state.isLoading ? ( // 当加载时也显示结果区域
          <div className="rc-results-container">
            {state.isLoading && state.searchResults.length === 0 ? (
              <SkeletonLoader count={5} />
            ) : scrollingDecision.shouldUseVirtual ? (
              <VirtualResultList
                results={state.searchResults}
                isLoading={state.isLoading}
                onResultClick={handleResultClick}
                query={state.searchQuery}
                containerHeight={scrollingDecision.recommendedConfig.containerHeight}
                itemHeight={scrollingDecision.recommendedConfig.itemHeight}
                enableVirtualScrolling={true}
                enableStreaming={true}
                streamingBatchSize={scrollingDecision.recommendedConfig.streamingBatchSize}
                overscan={scrollingDecision.recommendedConfig.overscan}
              />
            ) : (
              <ResultList
                results={state.searchResults}
                isLoading={state.isLoading}
                onResultClick={handleResultClick}
                query={state.searchQuery}
                enableStreaming={true}
                streamingBatchSize={scrollingDecision.recommendedConfig.streamingBatchSize}
              />
            )}
          </div>
        ) : (
          renderEmptyState()
        )}
      </main>

      {/* Footer */}
      <footer className="rc-footer">
        <StatusBar totalResults={state.totalResults} searchTime={state.searchTime} isLoading={state.isLoading} />
        
        {/* 性能优化提示 */}
        {state.searchResults.length > 0 && process.env.NODE_ENV === 'development' && (
          <div className="performance-info" title={scrollingDecision.reason}>
            <span className="perf-icon">
              {scrollingDecision.shouldUseVirtual ? '⚡' : '📋'}
            </span>
            <span className="perf-text">
              {scrollingDecision.shouldUseVirtual ? '虚拟滚动已启用' : '普通滚动模式'}
            </span>
            {scrollingDecision.performanceExpectation.memoryReduction > 50 && (
              <span className="perf-savings">
                节省 {scrollingDecision.performanceExpectation.memoryReduction.toFixed(0)}% 内存
              </span>
            )}
          </div>
        )}
      </footer>
    </div>
  )
}

// ============================================================================
// Sub Components
// ============================================================================

const ErrorIcon = ({ type }: { type: ErrorType }) => {
  const iconProps = { width: 48, height: 48, viewBox: "0 0 24 24", fill: "none" }

  const icons = {
    [ERROR_TYPES.NETWORK]: (
      <svg {...iconProps}>
        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" strokeWidth="2" fill="none" />
        <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" fill="none" />
        <path d="M1 1l22 22" stroke="currentColor" strokeWidth="2" />
      </svg>
    ),
    [ERROR_TYPES.DATABASE]: (
      <svg {...iconProps}>
        <ellipse cx="12" cy="5" rx="9" ry="3" stroke="currentColor" strokeWidth="2" />
        <path d="M3 5v14c0 1.66 4.03 3 9 3s9-1.34 9-3V5" stroke="currentColor" strokeWidth="2" />
        <path d="M3 12c0 1.66 4.03 3 9 3s9-1.34 9-3" stroke="currentColor" strokeWidth="2" />
        <path d="M15 9l6 6m0-6l-6 6" stroke="currentColor" strokeWidth="2" />
      </svg>
    ),
    [ERROR_TYPES.SEARCH]: (
      <svg {...iconProps}>
        <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2" />
        <path d="M21 21l-4.35-4.35" stroke="currentColor" strokeWidth="2" />
        <path d="M8 11h6M11 8v6" stroke="currentColor" strokeWidth="2" />
      </svg>
    ),
    [ERROR_TYPES.SERVICE_INIT]: (
      <svg {...iconProps}>
        <path
          d="M12 6V4M6 6L5 5M18 6L19 5M6 18L5 19M18 18L19 19M4 12H2M22 12H20M17 12C17 14.761 14.761 17 12 17C9.239 17 7 14.761 7 12C7 9.239 9.239 7 12 7C14.761 7 17 9.239 17 12Z"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
    [ERROR_TYPES.UNKNOWN]: (
      <svg {...iconProps}>
        <path
          d="M12 9V13M12 17H12.01M21 12C21 16.971 16.971 21 12 21C7.029 21 3 16.971 3 12C3 7.029 7.029 3 12 3C16.971 3 21 7.029 21 12Z"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    ),
  }

  return <div className={`rc-error-icon rc-error-${type}`}>{icons[type] || icons[ERROR_TYPES.UNKNOWN]}</div>
}

const SearchIcon = ({ size = 80 }: { size?: number }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <path
      d="M21 21L16.514 16.506L21 21ZM19 10.5C19 15.194 15.194 19 10.5 19C5.806 19 2 15.194 2 10.5C2 5.806 5.806 2 10.5 2C15.194 2 19 5.806 19 10.5Z"
      stroke="currentColor"
      strokeWidth={size > 50 ? "1.5" : "2"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    {size > 50 && (
      <path
        d="M8 10.5H13M8 8H11"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    )}
  </svg>
)

const FeatureList = ({ t }: { t: (key: string) => string }) => (
  <div className="rc-features">
    {[
      { icon: "✓", text: t("empty.features.fuzzyMatch") },
      { icon: "🔍", text: t("empty.features.multiFilter") },
      { icon: "⚡", text: t("empty.features.fastResponse") },
    ].map((feature, index) => (
      <div key={index} className="rc-feature-item">
        <div className="rc-feature-icon">
          <span>{feature.icon}</span>
        </div>
        <span>{feature.text}</span>
      </div>
    ))}
  </div>
)

const SearchTips = ({ t }: { t: (key: string) => string }) => (
  <div className="rc-search-tips">
    <h4 className="rc-tips-title">{t("empty.searchTips.title")}</h4>
    <ul className="rc-tips-list">
      <li>{t("empty.searchTips.items.0")}</li>
      <li>{t("empty.searchTips.items.1")}</li>
      <li>{t("empty.searchTips.items.2")}</li>
    </ul>
  </div>
)

const KeyboardShortcuts = ({ t }: { t: (key: string) => string }) => (
  <div className="rc-keyboard-shortcuts">
    <h4 className="rc-shortcuts-title">⌨️ {t("empty.keyboardShortcuts.title")}</h4>
    <div className="rc-shortcuts-grid">
      {[
        { keys: ["Ctrl", "K"], desc: t("empty.keyboardShortcuts.focusSearch") },
        { keys: ["Esc"], desc: t("empty.keyboardShortcuts.clearSearch") },
        { keys: ["↑", "↓"], desc: t("empty.keyboardShortcuts.browseResults") },
        { keys: ["Enter"], desc: t("empty.keyboardShortcuts.openPage") },
        { keys: ["Ctrl", "1-9"], desc: t("empty.keyboardShortcuts.quickOpen") },
      ].map((shortcut, index) => (
        <div key={index} className="rc-shortcut-item">
          {shortcut.keys.map((key, i) => (
            <span key={i}>
              <kbd className="rc-kbd">{key}</kbd>
              {i < shortcut.keys.length - 1 && <span className="rc-kbd-plus">+</span>}
            </span>
          ))}
          <span className="rc-shortcut-desc">{shortcut.desc}</span>
        </div>
      ))}
    </div>
  </div>
)

const SearchExamples = ({ onExampleClick, t }: { onExampleClick: (query: string) => void; t: (key: string) => string }) => (
  <div className="rc-search-examples">
    <h4 className="rc-examples-title">{t("empty.searchExamples.title")}</h4>
    <div className="rc-examples-grid">
      {[
        { icon: "🔍", query: "React JavaScript", desc: t("empty.searchExamples.basic.description") },
        { icon: "📝", query: '"best practices"', desc: t("empty.searchExamples.exact.description") },
        { icon: "🌐", query: "site:github.com", desc: t("empty.searchExamples.site.description") },
        { icon: "🚫", query: "Vue -tutorial", desc: t("empty.searchExamples.exclude.description") },
      ].map((example, index) => (
        <button
          key={index}
          type="button"
          className="rc-example-btn"
          onClick={() => onExampleClick(example.query)}
          title={`${t("search.placeholder")}: ${example.query}`}
        >
          <span className="rc-example-icon">{example.icon}</span>
          <span className="rc-example-text">{example.query}</span>
          <span className="rc-example-desc">{example.desc}</span>
        </button>
      ))}
    </div>
  </div>
)

const DensityIcon = ({ density }: { density: 'compact' | 'comfortable' }) => {
  if (density === 'compact') {
    return (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
        <path d="M4 6h16M4 10h16M4 14h16M4 18h16" stroke="currentColor" strokeWidth="2"/>
      </svg>
    );
  }
  return (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
      <path d="M4 8h16M4 16h16" stroke="currentColor" strokeWidth="2"/>
    </svg>
  );
};

export default App
