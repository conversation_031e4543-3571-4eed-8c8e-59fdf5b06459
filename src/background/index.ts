/**
 * Background Service Worker for Recall V3.0
 *
 * Enhanced service worker with intelligent indexing scheduler:
 * - Resource-aware task scheduling
 * - Performance monitoring
 * - Automatic content extraction and indexing
 * - Chrome extension lifecycle management
 */

/// <reference path="../types/chrome.d.ts" />

import { dbService, blacklistService } from '../services';
import { backgroundHybridSearchService } from '../services/hybrid-search.background.service';
import { SETTING_KEYS } from '../models';
import { resourceMonitor } from './ResourceMonitor';
import { taskQueue } from './TaskQueue';
import { serviceInitializer } from './ServiceInitializer';

/**
 * 消息类型定义
 */
interface MessageData {
  url: string;
  title: string;
  content: string;
  timestamp: number;
  contentLength: number;
  userAgent?: string;
  referrer?: string;
}

/**
 * 新的页面数据接口（支持内容提取状态）
 */
interface PageDataMessage {
  url: string;
  title: string;
  content: string;
  timestamp: number;
  contentLength: number;
  userAgent?: string;
  referrer?: string;
  domain: string;
  protocol: string;
  extractionSuccess: boolean;
  isValidContent: boolean;
  extractionError?: string | null;
  contentStatus: 'extracted' | 'failed' | 'empty' | 'pending';
}

interface Message {
  type: string;
  data: MessageData | PageDataMessage;
}

/**
 * 响应接口
 */
interface MessageResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  data?: unknown;
}

/**
 * Enhanced Background Service Worker with V3.0 features
 */
class RecallBackground {
  private isInitialized: boolean = false;
  private messageQueue: Message[] = [];
  private processingQueue: boolean = false;
  private isShuttingDown: boolean = false;
  constructor() {
    this.log('Background Service Worker V3.0 initializing...');
    this.init();
  }

  /**
   * 初始化后台服务（V3.0增强版）- 使用并行初始化
   */
  private async init(): Promise<void> {
    try {
      this.log('Initializing V3.0 enhanced background service with parallel loading...');
      const startTime = performance.now();

      // 使用新的并行服务初始化器
      const initResults = await serviceInitializer.initializeAll();
      
      // 检查关键服务是否成功初始化
      const criticalServices = ['database', 'blacklist', 'settings'];
      const failedCritical = initResults.filter(result => 
        criticalServices.includes(result.serviceName) && !result.success
      );
      
      if (failedCritical.length > 0) {
        const failedNames = failedCritical.map(r => r.serviceName).join(', ');
        throw new Error(`Critical services failed to initialize: ${failedNames}`);
      }
      
      const totalTime = performance.now() - startTime;
      this.log(`Service initialization completed in ${totalTime.toFixed(2)}ms`);
      
      // AI operations will be delegated to content scripts and popup contexts

      // 设置消息监听器
      this.setupMessageListeners();

      // 设置扩展生命周期监听器
      this.setupLifecycleListeners();

      // 设置V3.0特有的事件监听
      this.setupV3EventListeners();

      // 处理队列中的消息
      await this.processMessageQueue();

      this.isInitialized = true;
      this.log('Background Service Worker V3.0 initialized successfully');
      
      // 记录初始化统计信息
      const successfulServices = initResults.filter(r => r.success).length;
      const totalServices = initResults.length;
      this.log(`Services initialized: ${successfulServices}/${totalServices}`);

    } catch (error) {
      this.log('Failed to initialize Background Service Worker V3.0', error);
      
      // 记录失败的服务
      const results = serviceInitializer.getAllResults();
      const failed = results.filter(r => !r.success);
      if (failed.length > 0) {
        this.log('Failed services:', failed.map(f => `${f.serviceName}: ${f.error}`));
      }
      
      throw error;
    }
  }

  // Note: Default settings initialization moved to ServiceInitializer for parallel execution

  /**
   * 设置消息监听器
   */
  private setupMessageListeners(): void {
    chrome.runtime.onMessage.addListener((message: Message, sender, sendResponse) => {
      this.handleMessage(message, sender)
        .then(response => sendResponse(response))
        .catch(error => {
          this.log('Message handling error', error);
          sendResponse({
            success: false,
            error: error.message
          });
        });

      return true; // 保持消息通道开放
    });
  }

  /**
   * 设置扩展生命周期监听器
   */
  private setupLifecycleListeners(): void {
    // 扩展安装/更新事件
    chrome.runtime.onInstalled.addListener(async (details) => {
      this.log('Extension installed/updated', details);

      if (details.reason === 'install') {
        await this.handleFirstInstall();
      } else if (details.reason === 'update') {
        await this.handleUpdate(details.previousVersion);
      }
    });

    // 扩展启动事件
    chrome.runtime.onStartup.addListener(() => {
      this.log('Extension startup');
    });

    // 标签页更新事件（用于监听导航）
    chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.url) {
        await this.handleTabUpdate(tabId, tab);
      }
    });
  }

  /**
   * 处理消息
   */
  private async handleMessage(message: Message, sender: chrome.runtime.MessageSender): Promise<MessageResponse> {
    const messageId = this.generateMessageId();

    this.log('Received message', {
      type: message.type,
      messageId,
      sender: sender.tab?.url
    });

    // 如果服务未初始化，将消息加入队列
    if (!this.isInitialized) {
      this.messageQueue.push(message);
      return {
        success: true,
        messageId,
        data: { queued: true }
      };
    }

    try {
      switch (message.type) {
        case 'PAGE_CONTENT_EXTRACTED':
          return await this.handlePageContentExtracted(message.data, messageId);
        
        case 'PAGE_DATA_COLLECTED': // 新的消息类型：总是保存基础记录
          return await this.handlePageDataCollected(message.data as PageDataMessage, messageId);

        case 'GET_SETTINGS':
          return await this.handleGetSettings(messageId);

        case 'UPDATE_SETTING':
          return await this.handleUpdateSetting(message.data, messageId);

        case 'SEARCH_PAGES':
          return await this.handleSearchPages(message.data, messageId);

        case 'GET_STORAGE_INFO':
          return await this.handleGetStorageInfo(messageId);

        case 'HEALTH_CHECK':
          return await this.handleHealthCheck(messageId);

        case 'GET_DEBUG_INFO':
          return await this.handleGetDebugInfo(messageId);

        case 'TEST_DATABASE':
          return await this.handleTestDatabase(messageId);

        case 'CLEAR_ALL_DATA':
          return await this.handleClearAllData(messageId);

        // V3.0 新增消息类型
        case 'MANUAL_INDEX_PAGE':
          return await this.handleManualIndexPage(message.data, messageId);

        case 'BATCH_INDEX_PAGES':
          return await this.handleBatchIndexPages(message.data, messageId);

        case 'REBUILD_INDEX':
          return await this.handleRebuildIndex(message.data, messageId);

        case 'GET_INDEXING_STATS':
          return await this.handleGetIndexingStats(messageId);

        case 'GET_RESOURCE_STATS':
          return await this.handleGetResourceStats(messageId);

        case 'UPDATE_SCHEDULER_CONFIG':
          // Scheduler config updates not supported - AI moved to content scripts
          return {
            success: false,
            messageId,
            error: 'Scheduler configuration no longer supported in service worker'
          };

        // Note: AI functionality has been removed

        default:
          this.log('Unknown message type', message.type);
          return {
            success: false,
            messageId,
            error: `Unknown message type: ${message.type}`
          };
      }
    } catch (error) {
      this.log('Error handling message', error);
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理页面内容提取消息
   */
  private async handlePageContentExtracted(data: MessageData, messageId: string): Promise<MessageResponse> {
    try {
      this.log('Processing page content', {
        url: data.url,
        contentLength: data.contentLength,
        messageId
      });

      // 检查是否启用扩展
      const isEnabled = await dbService.getSetting(SETTING_KEYS.EXTENSION_ENABLED);
      if (!isEnabled) {
        return {
          success: false,
          messageId,
          error: 'Extension is disabled'
        };
      }

      // 检查黑名单
      const url = new URL(data.url);
      const isBlacklisted = await blacklistService.isDomainBlacklisted(url.hostname);
      if (isBlacklisted) {
        this.log('Domain is blacklisted, skipping indexing', {
          domain: url.hostname,
          url: data.url,
          messageId
        });
        return {
          success: false,
          messageId,
          error: 'Domain is blacklisted'
        };
      }

      // 添加页面到数据库
      const page = await dbService.addPage({
        url: data.url,
        title: data.title,
        content: data.content
      });

      this.log('Page content stored successfully', {
        pageId: page.id,
        accessCount: page.accessCount,
        messageId
      });

      return {
        success: true,
        messageId,
        data: {
          pageId: page.id,
          accessCount: page.accessCount,
          isNewPage: page.accessCount === 1
        }
      };

    } catch (error) {
      this.log('Failed to store page content', error);
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理页面数据收集消息（V2版本：总是保存基础记录）
   */
  private async handlePageDataCollected(data: PageDataMessage, messageId: string): Promise<MessageResponse> {
    try {
      this.log('Processing collected page data', {
        url: data.url,
        contentLength: data.contentLength,
        contentStatus: data.contentStatus,
        extractionSuccess: data.extractionSuccess,
        messageId
      });

      // 检查是否启用扩展
      const isEnabled = await dbService.getSetting(SETTING_KEYS.EXTENSION_ENABLED);
      if (!isEnabled) {
        return {
          success: false,
          messageId,
          error: 'Extension is disabled'
        };
      }

      // 检查黑名单
      const url = new URL(data.url);
      const isBlacklisted = await blacklistService.isDomainBlacklisted(url.hostname);
      if (isBlacklisted) {
        this.log('Domain is blacklisted, skipping indexing', {
          domain: url.hostname,
          url: data.url,
          messageId
        });
        return {
          success: false,
          messageId,
          error: 'Domain is blacklisted'
        };
      }

      // 创建页面记录，支持空内容
      const pageRecord = {
        url: data.url,
        title: data.title,
        content: data.content || '', // 允许空内容
        contentStatus: data.contentStatus,
        extractionError: data.extractionError || undefined,
        lastExtractionAttempt: data.timestamp
      };

      // 添加页面到数据库（使用更新的逻辑）
      const page = await dbService.addPageWithStatus(pageRecord);

      // 更新混合搜索索引
      try {
        await backgroundHybridSearchService.addPageToIndex(page);
        this.log('Page added to background search index', { pageId: page.id });
      } catch (indexError) {
        this.log('Failed to update background search index', indexError);
        // 继续执行，不影响页面存储
      }

      this.log('Page data stored successfully', {
        pageId: page.id,
        accessCount: page.accessCount,
        contentStatus: data.contentStatus,
        hasContent: !!data.content,
        messageId
      });

      return {
        success: true,
        messageId,
        data: {
          pageId: page.id,
          accessCount: page.accessCount,
          isNewPage: page.accessCount === 1,
          contentStatus: data.contentStatus,
          extractionSuccess: data.extractionSuccess
        }
      };

    } catch (error) {
      this.log('Failed to store page data', error);
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理获取设置请求
   */
  private async handleGetSettings(messageId: string): Promise<MessageResponse> {
    try {
      const settings = await dbService.getAllSettings();

      return {
        success: true,
        messageId,
        data: settings
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理更新设置请求
   */
  private async handleUpdateSetting(data: any, messageId: string): Promise<MessageResponse> {
    try {
      const { key, value } = data;
      await dbService.setSetting(key, value);

      return {
        success: true,
        messageId,
        data: { key, value }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理搜索页面请求
   */
  private async handleSearchPages(_data: any, messageId: string): Promise<MessageResponse> {
    try {
      // 这里暂时返回所有页面，后续会集成搜索服务
      const pages = await dbService.getAllPages();

      return {
        success: true,
        messageId,
        data: { pages, total: pages.length }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理获取存储信息请求
   */
  private async handleGetStorageInfo(messageId: string): Promise<MessageResponse> {
    try {
      const storageInfo = await dbService.getStorageInfo();

      return {
        success: true,
        messageId,
        data: storageInfo
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理健康检查请求
   */
  private async handleHealthCheck(messageId: string): Promise<MessageResponse> {
    try {
      const healthCheck = await dbService.healthCheck();

      return {
        success: true,
        messageId,
        data: {
          ...healthCheck,
          version: chrome.runtime.getManifest().version,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理获取调试信息请求
   */
  private async handleGetDebugInfo(messageId: string): Promise<MessageResponse> {
    try {
      const [storageInfo, healthCheck] = await Promise.all([
        dbService.getStorageInfo(),
        dbService.healthCheck()
      ]);

      return {
        success: true,
        messageId,
        data: {
          pageCount: storageInfo.pagesCount,
          dbHealthy: healthCheck.isHealthy,
          storageSize: storageInfo.estimatedSize,
          version: chrome.runtime.getManifest().version,
          timestamp: Date.now()
        }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理数据库测试请求
   */
  private async handleTestDatabase(messageId: string): Promise<MessageResponse> {
    try {
      const [storageInfo, healthCheck, allPages] = await Promise.all([
        dbService.getStorageInfo(),
        dbService.healthCheck(),
        dbService.getAllPages()
      ]);

      return {
        success: true,
        messageId,
        data: {
          pageCount: allPages.length,
          storageSize: storageInfo.estimatedSize,
          isHealthy: healthCheck.isHealthy,
          recentPages: allPages.slice(-5).map(page => ({
            id: page.id,
            title: page.title,
            url: page.url,
            timestamp: page.visitTime
          }))
        }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理清空所有数据请求
   */
  private async handleClearAllData(messageId: string): Promise<MessageResponse> {
    try {
      await dbService.clearAllData();
      this.log('All data cleared successfully');

      return {
        success: true,
        messageId,
        data: { message: 'All data cleared successfully' }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理首次安装
   */
  private async handleFirstInstall(): Promise<void> {
    try {
      this.log('Handling first install');

      // 设置欢迎标志
      await dbService.setSetting('first_install_time', Date.now());
      await dbService.setSetting('version', chrome.runtime.getManifest().version);

      // 可以在这里添加欢迎页面或教程

    } catch (error) {
      this.log('Failed to handle first install', error);
    }
  }

  /**
   * 处理扩展更新
   */
  private async handleUpdate(previousVersion?: string): Promise<void> {
    try {
      this.log('Handling extension update', { previousVersion });

      const currentVersion = chrome.runtime.getManifest().version;
      await dbService.setSetting('version', currentVersion);
      await dbService.setSetting('last_update_time', Date.now());

      // 可以在这里添加版本迁移逻辑

    } catch (error) {
      this.log('Failed to handle update', error);
    }
  }

  /**
   * 处理标签页更新
   */
  private async handleTabUpdate(tabId: number, tab: chrome.tabs.Tab): Promise<void> {
    try {
      // 检查是否需要注入 Content Script
      if (tab.url && this.shouldInjectContentScript(tab.url)) {
        await this.injectContentScript(tabId);
      }
    } catch (error) {
      this.log('Failed to handle tab update', error);
    }
  }

  /**
   * 检查是否应该注入 Content Script
   */
  private shouldInjectContentScript(url: string): boolean {
    try {
      const urlObj = new URL(url);

      // 排除特殊协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }

      // 排除扩展页面
      if (urlObj.protocol === 'chrome-extension:') {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * 注入 Content Script
   * Note: Content scripts are already injected via manifest.json,
   * this method is kept for manual injection if needed
   */
  private async injectContentScript(tabId: number): Promise<void> {
    try {
      // Send a message to trigger content extraction instead of injecting
      await chrome.tabs.sendMessage(tabId, { type: 'EXTRACT_CONTENT' });
    } catch (error) {
      // Content script might not be ready yet, this is normal
      this.log('Failed to send message to content script', error);
    }
  }

  /**
   * 处理消息队列
   */
  private async processMessageQueue(): Promise<void> {
    if (this.processingQueue || this.messageQueue.length === 0) {
      return;
    }

    this.processingQueue = true;

    try {
      while (this.messageQueue.length > 0) {
        const message = this.messageQueue.shift();
        if (message) {
          await this.handleMessage(message, {} as chrome.runtime.MessageSender);
        }
      }
    } catch (error) {
      this.log('Error processing message queue', error);
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置V3.0特有的事件监听
   */
  private setupV3EventListeners(): void {
    // 监听扩展卸载事件，进行优雅关闭
    chrome.runtime.onSuspend?.addListener(async () => {
      await this.performGracefulShutdown();
    });

    // 监听任务队列事件
    taskQueue.on('taskCompleted', (task, result) => {
      this.log(`Task completed: ${task.id} (${task.type})`, result);
    });

    taskQueue.on('taskFailed', (task, error) => {
      this.log(`Task failed: ${task.id} (${task.type})`, error);
    });

    // 监听资源监控事件
    resourceMonitor.addListener((metrics) => {
      if (metrics.performanceScore < 0.5) {
        this.log('Performance degradation detected', metrics);
      }
    });

    this.log('V3.0 event listeners setup complete');
  }

  /**
   * 执行优雅关闭
   */
  private async performGracefulShutdown(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    this.log('Performing graceful shutdown...');

    try {
      // Stop task queue
      await taskQueue.shutdown();
      
      // Stop resource monitoring
      resourceMonitor.stopMonitoring();
      
      this.log('Graceful shutdown completed');
    } catch (error) {
      this.log('Error during graceful shutdown', error);
    }
  }


  /**
   * Delegate AI operation to available worker
   */
  private async delegateToAIWorker(message: any): Promise<any> {
    // Try to send to active tab's content script
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]?.id) {
        const response = await chrome.tabs.sendMessage(tabs[0].id, message);
        if (response && response.success) {
          return response.data;
        }
      }
    } catch (error) {
      this.log('Failed to delegate to content script:', error);
    }

    // Try to send to popup if open
    try {
      const response = await chrome.runtime.sendMessage(message);
      if (response && response.success) {
        return response.data;
      }
    } catch (error) {
      this.log('Failed to delegate to popup:', error);
    }

    throw new Error('No AI worker available to handle request');
  }

  /**
   * 处理手动索引页面请求 (simplified without AI)
   */
  private async handleManualIndexPage(data: any, messageId: string): Promise<MessageResponse> {
    try {
      const { url, title } = data;
      
      if (!url) {
        return {
          success: false,
          messageId,
          error: 'URL is required for manual indexing'
        };
      }

      // Store basic page info without AI processing
      const page = await dbService.addPage({
        url,
        title: title || 'Manual Index',
        content: '' // Content will be extracted by content script
      });

      this.log(`Manual indexing stored basic info for: ${url}`);

      return {
        success: true,
        messageId,
        data: { pageId: page.id, note: 'Basic page info stored, AI processing delegated to content script' }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理批量索引页面请求 (simplified without AI)
   */
  private async handleBatchIndexPages(data: any, messageId: string): Promise<MessageResponse> {
    try {
      const { pages } = data;
      
      if (!Array.isArray(pages) || pages.length === 0) {
        return {
          success: false,
          messageId,
          error: 'Pages array is required for batch indexing'
        };
      }

      // Store basic page info without AI processing
      let stored = 0;
      for (const pageData of pages) {
        try {
          await dbService.addPage({
            url: pageData.url,
            title: pageData.title || 'Batch Index',
            content: pageData.content || ''
          });
          stored++;
        } catch (error) {
          this.log(`Failed to store page: ${pageData.url}`, error);
        }
      }

      this.log(`Batch indexing stored ${stored}/${pages.length} pages`);

      return {
        success: true,
        messageId,
        data: { storedPages: stored, totalPages: pages.length, note: 'Basic page info stored, AI processing delegated to content scripts' }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理重建索引请求 (AI delegation)
   */
  private async handleRebuildIndex(data: any, messageId: string): Promise<MessageResponse> {
    try {
      this.log('Index rebuild requested - delegating to AI worker');
      
      // Create delegation message
      const aiMessage: any = {
        type: 'REBUILD_INDEX' as any,
        context: 'background',
        requestId: messageId,
        data: data || { force: false }
      };

      // Delegate to AI worker
      const result = await this.delegateToAIWorker(aiMessage);

      return {
        success: true,
        messageId,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: `Index rebuild requires AI worker: ${(error as Error).message}`
      };
    }
  }

  /**
   * 处理获取索引统计请求 (simplified without scheduler)
   */
  private async handleGetIndexingStats(messageId: string): Promise<MessageResponse> {
    try {
      const queueStats = taskQueue.getStats();
      const storageInfo = await dbService.getStorageInfo();

      return {
        success: true,
        messageId,
        data: {
          queueStats: queueStats,
          storageInfo: storageInfo,
          note: 'AI indexing stats available from content script workers'
        }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }

  /**
   * 处理获取资源统计请求
   */
  private async handleGetResourceStats(messageId: string): Promise<MessageResponse> {
    try {
      const currentMetrics = resourceMonitor.getCurrentMetrics();
      const resourceStats = resourceMonitor.getStats();
      const adaptiveParams = resourceMonitor.getAdaptiveParams();

      return {
        success: true,
        messageId,
        data: {
          currentMetrics,
          resourceStats,
          adaptiveParams
        }
      };
    } catch (error) {
      return {
        success: false,
        messageId,
        error: (error as Error).message
      };
    }
  }


  /**
   * 日志输出
   */
  private log(message: string, data?: unknown): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [Recall Background V3.0] ${message}`, data || '');
  }
}

// 创建并启动 Background Service Worker 实例
new RecallBackground();

// 导出用于测试
declare const module: any;
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { RecallBackground };
}

// 确保模块兼容性
export {};
