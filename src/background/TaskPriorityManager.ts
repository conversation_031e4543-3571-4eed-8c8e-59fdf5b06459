/**
 * Advanced Task Priority Management System for Recall V3.0
 * 
 * Provides dynamic priority adjustment based on:
 * - User interaction patterns
 * - Task aging
 * - Resource availability
 * - Dependencies
 * - Business logic rules
 */

import type { BaseTask, TaskType, TaskPriority } from './TaskQueue';

/**
 * Priority adjustment factors
 */
export interface PriorityFactors {
  /** Age of the task in milliseconds */
  age: number;
  /** User interaction priority boost */
  userInteraction: boolean;
  /** Task failure count (increases priority for retries) */
  failureCount: number;
  /** Number of dependencies */
  dependencyCount: number;
  /** Resource urgency */
  resourceUrgency: number;
  /** Business priority modifier */
  businessPriority: number;
}

/**
 * Priority adjustment result
 */
export interface PriorityAdjustment {
  /** Original priority */
  originalPriority: TaskPriority;
  /** Adjusted priority */
  adjustedPriority: TaskPriority;
  /** Priority boost applied */
  boost: number;
  /** Factors that influenced the adjustment */
  factors: PriorityFactors;
  /** Explanation of adjustment */
  reason: string;
}

/**
 * Task priority configuration
 */
export interface PriorityConfig {
  /** Enable dynamic priority adjustment */
  enableDynamicAdjustment: boolean;
  /** Age-based priority boost (priority levels per hour) */
  agingRate: number;
  /** Maximum age boost (priority levels) */
  maxAgeBoost: number;
  /** User interaction boost (priority levels) */
  userInteractionBoost: number;
  /** Failure retry boost (priority levels per failure) */
  failureBoost: number;
  /** Dependency penalty (priority levels per dependency) */
  dependencyPenalty: number;
  /** Enable starvation prevention */
  enableStarvationPrevention: boolean;
  /** Starvation threshold (milliseconds) */
  starvationThreshold: number;
}

/**
 * Task execution context
 */
export interface TaskContext {
  /** Recent user interactions */
  recentUserInteractions: string[];
  /** Current system load */
  systemLoad: number;
  /** Available resources */
  availableResources: number;
  /** Number of pending tasks by type */
  pendingTasksByType: Record<TaskType, number>;
}

/**
 * Priority queue node
 */
interface PriorityQueueNode {
  task: BaseTask;
  adjustedPriority: number;
  insertTime: number;
}

/**
 * Advanced Task Priority Manager
 */
export class TaskPriorityManager {
  private static instance: TaskPriorityManager;
  private config: PriorityConfig;
  private priorityQueue: PriorityQueueNode[] = [];
  private userInteractions: { type: string; timestamp: number }[] = [];
  private taskContext: TaskContext;
  private adjustmentHistory: PriorityAdjustment[] = [];

  private static readonly DEFAULT_CONFIG: PriorityConfig = {
    enableDynamicAdjustment: true,
    agingRate: 0.1, // 0.1 priority levels per hour
    maxAgeBoost: 2,
    userInteractionBoost: 1,
    failureBoost: 0.5,
    dependencyPenalty: 0.2,
    enableStarvationPrevention: true,
    starvationThreshold: 300000, // 5 minutes
  };

  private constructor(config?: Partial<PriorityConfig>) {
    this.config = { ...TaskPriorityManager.DEFAULT_CONFIG, ...config };
    this.taskContext = this.initializeTaskContext();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<PriorityConfig>): TaskPriorityManager {
    if (!TaskPriorityManager.instance) {
      TaskPriorityManager.instance = new TaskPriorityManager(config);
    }
    return TaskPriorityManager.instance;
  }

  /**
   * Calculate dynamic priority for a task
   */
  public calculateDynamicPriority(task: BaseTask): PriorityAdjustment {
    if (!this.config.enableDynamicAdjustment) {
      return this.createNoAdjustmentResult(task);
    }

    const factors = this.gatherPriorityFactors(task);
    const boost = this.calculatePriorityBoost(factors);
    const adjustedPriority = this.applyPriorityBoost(task.priority, boost);
    const reason = this.generateAdjustmentReason(factors, boost);

    const adjustment: PriorityAdjustment = {
      originalPriority: task.priority,
      adjustedPriority,
      boost,
      factors,
      reason,
    };

    // Store in history for analysis
    this.adjustmentHistory.push(adjustment);
    if (this.adjustmentHistory.length > 1000) {
      this.adjustmentHistory.shift();
    }

    return adjustment;
  }

  /**
   * Add task to priority queue
   */
  public enqueue(task: BaseTask): void {
    const adjustment = this.calculateDynamicPriority(task);
    
    const node: PriorityQueueNode = {
      task,
      adjustedPriority: adjustment.adjustedPriority,
      insertTime: Date.now(),
    };

    // Insert in correct position (binary heap)
    this.priorityQueue.push(node);
    this.heapifyUp(this.priorityQueue.length - 1);

    console.log(
      `[TaskPriorityManager] Enqueued task ${task.id} with priority ${task.priority} -> ${adjustment.adjustedPriority} (${adjustment.reason})`
    );
  }

  /**
   * Get next highest priority task
   */
  public dequeue(): BaseTask | null {
    if (this.priorityQueue.length === 0) {
      return null;
    }

    // Check for starvation before normal dequeue
    if (this.config.enableStarvationPrevention) {
      const starvedTask = this.findStarvedTask();
      if (starvedTask) {
        console.log(`[TaskPriorityManager] Prioritizing starved task ${starvedTask.task.id}`);
        return this.removeTaskFromQueue(starvedTask);
      }
    }

    // Normal dequeue (highest priority)
    const node = this.priorityQueue[0];
    this.priorityQueue[0] = this.priorityQueue[this.priorityQueue.length - 1];
    this.priorityQueue.pop();
    
    if (this.priorityQueue.length > 0) {
      this.heapifyDown(0);
    }

    console.log(`[TaskPriorityManager] Dequeued task ${node.task.id} with adjusted priority ${node.adjustedPriority}`);
    return node.task;
  }

  /**
   * Peek at next task without removing it
   */
  public peek(): BaseTask | null {
    return this.priorityQueue.length > 0 ? this.priorityQueue[0].task : null;
  }

  /**
   * Update user interaction context
   */
  public recordUserInteraction(type: string): void {
    this.userInteractions.push({
      type,
      timestamp: Date.now(),
    });

    // Clean old interactions (older than 1 hour)
    const cutoff = Date.now() - 3600000;
    this.userInteractions = this.userInteractions.filter(
      interaction => interaction.timestamp > cutoff
    );

    // Update task context
    this.taskContext.recentUserInteractions = this.userInteractions
      .map(interaction => interaction.type);
  }

  /**
   * Update system context
   */
  public updateSystemContext(
    systemLoad: number,
    availableResources: number,
    pendingTasksByType: Record<TaskType, number>
  ): void {
    this.taskContext.systemLoad = systemLoad;
    this.taskContext.availableResources = availableResources;
    this.taskContext.pendingTasksByType = pendingTasksByType;

    // Rebalance queue if needed
    this.rebalanceQueue();
  }

  /**
   * Remove specific task from queue
   */
  public removeTask(taskId: string): boolean {
    const index = this.priorityQueue.findIndex(node => node.task.id === taskId);
    if (index === -1) {
      return false;
    }

    // Replace with last element and heapify
    this.priorityQueue[index] = this.priorityQueue[this.priorityQueue.length - 1];
    this.priorityQueue.pop();

    if (index < this.priorityQueue.length) {
      // Heapify both directions
      this.heapifyUp(index);
      this.heapifyDown(index);
    }

    console.log(`[TaskPriorityManager] Removed task ${taskId} from priority queue`);
    return true;
  }

  /**
   * Get queue statistics
   */
  public getStats(): {
    queueLength: number;
    averageWaitTime: number;
    priorityDistribution: Record<TaskPriority, number>;
    adjustmentStats: {
      totalAdjustments: number;
      averageBoost: number;
      starvationPreventions: number;
    };
  } {
    const now = Date.now();
    const priorityDistribution: Record<TaskPriority, number> = {} as Record<TaskPriority, number>;
    let totalWaitTime = 0;

    this.priorityQueue.forEach(node => {
      const priority = node.adjustedPriority as TaskPriority;
      priorityDistribution[priority] = (priorityDistribution[priority] || 0) + 1;
      totalWaitTime += now - node.insertTime;
    });

    const averageWaitTime = this.priorityQueue.length > 0 
      ? totalWaitTime / this.priorityQueue.length 
      : 0;

    const adjustments = this.adjustmentHistory.slice(-100); // Last 100 adjustments
    const averageBoost = adjustments.length > 0
      ? adjustments.reduce((sum, adj) => sum + adj.boost, 0) / adjustments.length
      : 0;

    const starvationPreventions = adjustments.filter(
      adj => adj.reason.includes('starvation')
    ).length;

    return {
      queueLength: this.priorityQueue.length,
      averageWaitTime,
      priorityDistribution,
      adjustmentStats: {
        totalAdjustments: adjustments.length,
        averageBoost,
        starvationPreventions,
      },
    };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<PriorityConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('[TaskPriorityManager] Configuration updated');
  }

  /**
   * Clear the priority queue
   */
  public clear(): void {
    this.priorityQueue = [];
    console.log('[TaskPriorityManager] Priority queue cleared');
  }

  // Private methods

  /**
   * Initialize task context
   */
  private initializeTaskContext(): TaskContext {
    return {
      recentUserInteractions: [],
      systemLoad: 0.1,
      availableResources: 1.0,
      pendingTasksByType: {} as Record<TaskType, number>,
    };
  }

  /**
   * Gather priority factors for a task
   */
  private gatherPriorityFactors(task: BaseTask): PriorityFactors {
    const age = Date.now() - task.createdAt;
    const userInteraction = this.hasRecentUserInteraction(task);
    const failureCount = task.retryCount;
    const dependencyCount = task.metadata.dependencies?.length || 0;
    const resourceUrgency = this.calculateResourceUrgency(task);
    const businessPriority = this.calculateBusinessPriority(task);

    return {
      age,
      userInteraction,
      failureCount,
      dependencyCount,
      resourceUrgency,
      businessPriority,
    };
  }

  /**
   * Calculate priority boost based on factors
   */
  private calculatePriorityBoost(factors: PriorityFactors): number {
    let boost = 0;

    // Age-based boost
    const ageHours = factors.age / (1000 * 60 * 60);
    const ageBoost = Math.min(ageHours * this.config.agingRate, this.config.maxAgeBoost);
    boost += ageBoost;

    // User interaction boost
    if (factors.userInteraction) {
      boost += this.config.userInteractionBoost;
    }

    // Failure retry boost
    boost += factors.failureCount * this.config.failureBoost;

    // Dependency penalty
    boost -= factors.dependencyCount * this.config.dependencyPenalty;

    // Resource urgency
    boost += factors.resourceUrgency;

    // Business priority
    boost += factors.businessPriority;

    return boost;
  }

  /**
   * Apply priority boost to original priority
   */
  private applyPriorityBoost(originalPriority: TaskPriority, boost: number): TaskPriority {
    // Convert priority to number, apply boost, and clamp
    const numericPriority = originalPriority - boost;
    return Math.max(0, Math.min(4, Math.round(numericPriority))) as TaskPriority;
  }

  /**
   * Generate human-readable adjustment reason
   */
  private generateAdjustmentReason(factors: PriorityFactors, boost: number): string {
    if (boost === 0) {
      return 'No adjustment needed';
    }

    const reasons: string[] = [];

    if (factors.age > 300000) { // 5 minutes
      reasons.push('aging');
    }

    if (factors.userInteraction) {
      reasons.push('user interaction');
    }

    if (factors.failureCount > 0) {
      reasons.push('retry');
    }

    if (factors.dependencyCount > 0) {
      reasons.push('dependencies');
    }

    if (factors.resourceUrgency > 0) {
      reasons.push('resource urgency');
    }

    if (factors.businessPriority !== 0) {
      reasons.push('business priority');
    }

    const direction = boost > 0 ? 'increased' : 'decreased';
    return `Priority ${direction} due to: ${reasons.join(', ')}`;
  }

  /**
   * Check if task has recent user interaction
   */
  private hasRecentUserInteraction(task: BaseTask): boolean {
    const taskTypes = ['index_page', 'update_page'];
    if (!taskTypes.includes(task.type)) {
      return false;
    }

    const cutoff = Date.now() - 60000; // 1 minute
    return this.userInteractions.some(
      interaction => interaction.timestamp > cutoff
    );
  }

  /**
   * Calculate resource urgency factor
   */
  private calculateResourceUrgency(task: BaseTask): number {
    const requirements = task.metadata.resourceRequirements;
    if (!requirements) {
      return 0;
    }

    // Higher urgency if system has low resources and task needs them
    const systemLoad = this.taskContext.systemLoad;
    const availableResources = this.taskContext.availableResources;

    if (systemLoad > 0.8 && requirements.cpu > 0.1) {
      return -0.5; // Decrease priority for CPU-heavy tasks when system is loaded
    }

    if (availableResources < 0.2 && requirements.memory > 10) {
      return -0.3; // Decrease priority for memory-heavy tasks when memory is low
    }

    return 0;
  }

  /**
   * Calculate business priority modifier
   */
  private calculateBusinessPriority(task: BaseTask): number {
    // Critical system tasks get higher priority
    if (task.type === 'health_check') {
      return 0.5;
    }

    // Index optimization during low activity
    if (task.type === 'optimize_index' && this.taskContext.systemLoad < 0.3) {
      return 0.3;
    }

    // Batch operations get lower priority to avoid blocking individual requests
    if (task.type === 'batch_index') {
      return -0.2;
    }

    return 0;
  }

  /**
   * Find starved tasks (waiting too long)
   */
  private findStarvedTask(): PriorityQueueNode | null {
    const now = Date.now();
    const threshold = this.config.starvationThreshold;

    for (const node of this.priorityQueue) {
      if (now - node.insertTime > threshold) {
        return node;
      }
    }

    return null;
  }

  /**
   * Remove specific task from queue
   */
  private removeTaskFromQueue(nodeToRemove: PriorityQueueNode): BaseTask {
    const index = this.priorityQueue.indexOf(nodeToRemove);
    
    // Replace with last element and heapify
    this.priorityQueue[index] = this.priorityQueue[this.priorityQueue.length - 1];
    this.priorityQueue.pop();

    if (index < this.priorityQueue.length) {
      this.heapifyUp(index);
      this.heapifyDown(index);
    }

    return nodeToRemove.task;
  }

  /**
   * Rebalance queue based on updated context
   */
  private rebalanceQueue(): void {
    // Recalculate priorities for all tasks in queue
    for (let i = 0; i < this.priorityQueue.length; i++) {
      const node = this.priorityQueue[i];
      const adjustment = this.calculateDynamicPriority(node.task);
      node.adjustedPriority = adjustment.adjustedPriority;
    }

    // Rebuild heap
    this.buildHeap();
  }

  /**
   * Build heap from current array
   */
  private buildHeap(): void {
    const start = Math.floor(this.priorityQueue.length / 2) - 1;
    for (let i = start; i >= 0; i--) {
      this.heapifyDown(i);
    }
  }

  /**
   * Heapify up (for insertion)
   */
  private heapifyUp(index: number): void {
    while (index > 0) {
      const parentIndex = Math.floor((index - 1) / 2);
      
      if (this.priorityQueue[index].adjustedPriority >= this.priorityQueue[parentIndex].adjustedPriority) {
        break;
      }

      this.swap(index, parentIndex);
      index = parentIndex;
    }
  }

  /**
   * Heapify down (for removal)
   */
  private heapifyDown(index: number): void {
    while (true) {
      let minIndex = index;
      const leftChild = 2 * index + 1;
      const rightChild = 2 * index + 2;

      if (leftChild < this.priorityQueue.length &&
          this.priorityQueue[leftChild].adjustedPriority < this.priorityQueue[minIndex].adjustedPriority) {
        minIndex = leftChild;
      }

      if (rightChild < this.priorityQueue.length &&
          this.priorityQueue[rightChild].adjustedPriority < this.priorityQueue[minIndex].adjustedPriority) {
        minIndex = rightChild;
      }

      if (minIndex === index) {
        break;
      }

      this.swap(index, minIndex);
      index = minIndex;
    }
  }

  /**
   * Swap two elements in the heap
   */
  private swap(i: number, j: number): void {
    [this.priorityQueue[i], this.priorityQueue[j]] = [this.priorityQueue[j], this.priorityQueue[i]];
  }

  /**
   * Create no-adjustment result
   */
  private createNoAdjustmentResult(task: BaseTask): PriorityAdjustment {
    return {
      originalPriority: task.priority,
      adjustedPriority: task.priority,
      boost: 0,
      factors: this.gatherPriorityFactors(task),
      reason: 'Dynamic adjustment disabled',
    };
  }
}

/**
 * Export singleton instance
 */
export const taskPriorityManager = TaskPriorityManager.getInstance();