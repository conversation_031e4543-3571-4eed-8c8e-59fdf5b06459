/**
 * Adaptive CPU Throttling System for Recall V3.0
 * 
 * Dynamically adjusts task execution rate based on:
 * - System CPU usage
 * - Memory pressure
 * - Performance metrics
 * - User activity
 * - Device capabilities
 */

import type { ResourceMonitor, PerformanceMetrics, MemoryPressureLevel } from './ResourceMonitor';

/**
 * Throttling configuration
 */
export interface ThrottlingConfig {
  /** Base delay between operations (ms) */
  baseDelay: number;
  /** Maximum allowed delay (ms) */
  maxDelay: number;
  /** CPU usage threshold for throttling */
  cpuThreshold: number;
  /** Memory usage threshold for throttling */
  memoryThreshold: number;
  /** Performance score threshold */
  performanceThreshold: number;
  /** Enable adaptive throttling */
  enabled: boolean;
  /** Throttling sensitivity (0-1, higher = more sensitive) */
  sensitivity: number;
}

/**
 * Throttling levels
 */
export const ThrottlingLevel = {
  NONE: 0,       // No throttling
  LIGHT: 1,      // 25% reduction
  MODERATE: 2,   // 50% reduction  
  HEAVY: 3,      // 75% reduction
  MAXIMUM: 4,    // 90% reduction
} as const;

export type ThrottlingLevel = typeof ThrottlingLevel[keyof typeof ThrottlingLevel];

/**
 * Throttling decision factors
 */
export interface ThrottlingFactors {
  cpuUsage: number;
  memoryPressure: MemoryPressureLevel;
  performanceScore: number;
  deviceTier: string;
  userActivity: boolean;
  backgroundOperationsEnabled: boolean;
}

/**
 * Throttling decision result
 */
export interface ThrottlingDecision {
  /** Throttling level applied */
  level: ThrottlingLevel;
  /** Delay to apply (ms) */
  delay: number;
  /** Explanation of throttling decision */
  reason: string;
  /** Factors that influenced the decision */
  factors: ThrottlingFactors;
  /** Whether operations should be paused entirely */
  shouldPause: boolean;
}

/**
 * User activity detector
 */
class UserActivityDetector {
  private isActive = true;
  private lastActivity = Date.now();
  private activityTimeout = 30000; // 30 seconds
  private listeners: Array<(active: boolean) => void> = [];

  constructor() {
    this.setupListeners();
  }

  public isUserActive(): boolean {
    const now = Date.now();
    return (now - this.lastActivity) < this.activityTimeout;
  }

  public addListener(listener: (active: boolean) => void): void {
    this.listeners.push(listener);
  }

  public removeListener(listener: (active: boolean) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  private setupListeners(): void {
    // Listen for user activity events
    if (typeof document !== 'undefined') {
      const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
      
      events.forEach(event => {
        document.addEventListener(event, this.handleActivity.bind(this), { passive: true });
      });

      // Listen for tab visibility changes
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    }

    // Check activity periodically
    setInterval(this.checkActivity.bind(this), 5000); // Every 5 seconds
  }

  private handleActivity(): void {
    const wasActive = this.isActive;
    this.lastActivity = Date.now();
    this.isActive = true;

    if (!wasActive) {
      this.notifyListeners(true);
    }
  }

  private handleVisibilityChange(): void {
    if (typeof document !== 'undefined') {
      const isVisible = !document.hidden;
      if (isVisible) {
        this.handleActivity();
      }
    }
  }

  private checkActivity(): void {
    const wasActive = this.isActive;
    this.isActive = this.isUserActive();

    if (wasActive !== this.isActive) {
      this.notifyListeners(this.isActive);
    }
  }

  private notifyListeners(active: boolean): void {
    this.listeners.forEach(listener => {
      try {
        listener(active);
      } catch (error) {
        console.error('[UserActivityDetector] Listener error:', error);
      }
    });
  }
}

/**
 * Adaptive CPU Throttler
 */
export class AdaptiveThrottler {
  private static instance: AdaptiveThrottler;
  private config: ThrottlingConfig;
  private resourceMonitor?: ResourceMonitor;
  private userActivityDetector: UserActivityDetector;
  private currentLevel: ThrottlingLevel = ThrottlingLevel.NONE;
  private metricsHistory: PerformanceMetrics[] = [];
  private throttlingHistory: { level: ThrottlingLevel; timestamp: number }[] = [];
  private adaptiveDelayMultiplier = 1.0;

  private static readonly DEFAULT_CONFIG: ThrottlingConfig = {
    baseDelay: 100,
    maxDelay: 5000,
    cpuThreshold: 0.7,
    memoryThreshold: 0.8,
    performanceThreshold: 0.4,
    enabled: true,
    sensitivity: 0.7,
  };

  private constructor(config?: Partial<ThrottlingConfig>) {
    this.config = { ...AdaptiveThrottler.DEFAULT_CONFIG, ...config };
    this.userActivityDetector = new UserActivityDetector();
    this.setupActivityListener();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<ThrottlingConfig>): AdaptiveThrottler {
    if (!AdaptiveThrottler.instance) {
      AdaptiveThrottler.instance = new AdaptiveThrottler(config);
    }
    return AdaptiveThrottler.instance;
  }

  /**
   * Initialize with resource monitor
   */
  public initialize(resourceMonitor: ResourceMonitor): void {
    this.resourceMonitor = resourceMonitor;
    
    // Listen for performance changes
    resourceMonitor.addListener(this.handlePerformanceUpdate.bind(this));
    
    console.log('[AdaptiveThrottler] Initialized with resource monitor');
  }

  /**
   * Get throttling decision for current system state
   */
  public getThrottlingDecision(): ThrottlingDecision {
    if (!this.config.enabled) {
      return this.createNoThrottlingDecision();
    }

    const factors = this.gatherThrottlingFactors();
    const level = this.calculateThrottlingLevel(factors);
    const delay = this.calculateDelay(level, factors);
    const reason = this.getThrottlingReason(level, factors);
    const shouldPause = this.shouldPauseOperations(factors);

    const decision: ThrottlingDecision = {
      level,
      delay,
      reason,
      factors,
      shouldPause,
    };

    // Update current level and history
    if (level !== this.currentLevel) {
      this.currentLevel = level;
      this.throttlingHistory.push({ level, timestamp: Date.now() });
      
      // Keep history size manageable
      if (this.throttlingHistory.length > 100) {
        this.throttlingHistory.shift();
      }

      console.log(`[AdaptiveThrottler] Throttling level changed to ${level}: ${reason}`);
    }

    return decision;
  }

  /**
   * Apply throttling delay
   */
  public async applyThrottling(decision?: ThrottlingDecision): Promise<void> {
    const throttling = decision || this.getThrottlingDecision();
    
    if (throttling.shouldPause) {
      // Wait for better conditions
      await this.waitForBetterConditions();
      return;
    }

    if (throttling.delay > 0) {
      await this.sleep(throttling.delay);
    }
  }

  /**
   * Check if operations should be throttled
   */
  public shouldThrottle(): boolean {
    const decision = this.getThrottlingDecision();
    return decision.level > ThrottlingLevel.NONE || decision.shouldPause;
  }

  /**
   * Get current throttling level
   */
  public getCurrentLevel(): ThrottlingLevel {
    return this.currentLevel;
  }

  /**
   * Get throttling statistics
   */
  public getStats(): {
    currentLevel: ThrottlingLevel;
    currentMultiplier: number;
    levelDistribution: Record<ThrottlingLevel, number>;
    averageDelay: number;
    userActivity: boolean;
  } {
    const levelCounts: Record<ThrottlingLevel, number> = {
      [ThrottlingLevel.NONE]: 0,
      [ThrottlingLevel.LIGHT]: 0,
      [ThrottlingLevel.MODERATE]: 0,
      [ThrottlingLevel.HEAVY]: 0,
      [ThrottlingLevel.MAXIMUM]: 0,
    };

    this.throttlingHistory.forEach(entry => {
      levelCounts[entry.level]++;
    });

    const decision = this.getThrottlingDecision();
    
    return {
      currentLevel: this.currentLevel,
      currentMultiplier: this.adaptiveDelayMultiplier,
      levelDistribution: levelCounts,
      averageDelay: decision.delay,
      userActivity: this.userActivityDetector.isUserActive(),
    };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<ThrottlingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('[AdaptiveThrottler] Configuration updated');
  }

  // Private methods

  /**
   * Setup user activity listener
   */
  private setupActivityListener(): void {
    this.userActivityDetector.addListener((active: boolean) => {
      console.log(`[AdaptiveThrottler] User activity changed: ${active ? 'active' : 'inactive'}`);
    });
  }

  /**
   * Handle performance metrics update
   */
  private handlePerformanceUpdate(metrics: PerformanceMetrics): void {
    this.metricsHistory.push(metrics);
    
    // Keep history size manageable
    if (this.metricsHistory.length > 50) {
      this.metricsHistory.shift();
    }

    // Adjust adaptive multiplier based on performance trend
    this.updateAdaptiveMultiplier(metrics);
  }

  /**
   * Gather current throttling factors
   */
  private gatherThrottlingFactors(): ThrottlingFactors {
    const currentMetrics = this.resourceMonitor?.getCurrentMetrics();
    const adaptiveParams = this.resourceMonitor?.getAdaptiveParams();
    
    return {
      cpuUsage: currentMetrics?.cpuUsage || 0.1,
      memoryPressure: this.resourceMonitor?.getCurrentMemoryPressure() || 'low',
      performanceScore: currentMetrics?.performanceScore || 1.0,
      deviceTier: currentMetrics?.deviceTier || 'unknown',
      userActivity: this.userActivityDetector.isUserActive(),
      backgroundOperationsEnabled: adaptiveParams?.enableBackgroundOps || true,
    };
  }

  /**
   * Calculate appropriate throttling level
   */
  private calculateThrottlingLevel(factors: ThrottlingFactors): ThrottlingLevel {
    let score = 0;

    // CPU usage factor
    if (factors.cpuUsage > this.config.cpuThreshold) {
      score += (factors.cpuUsage - this.config.cpuThreshold) * 3;
    }

    // Memory pressure factor
    switch (factors.memoryPressure) {
      case 'critical':
        score += 3;
        break;
      case 'high':
        score += 2;
        break;
      case 'moderate':
        score += 1;
        break;
    }

    // Performance score factor
    if (factors.performanceScore < this.config.performanceThreshold) {
      score += (this.config.performanceThreshold - factors.performanceScore) * 2;
    }

    // User activity factor
    if (factors.userActivity) {
      score += 1; // Be more conservative when user is active
    }

    // Device tier factor
    if (factors.deviceTier === 'low') {
      score += 1;
    }

    // Apply sensitivity
    score *= this.config.sensitivity;

    // Map score to throttling level
    if (score >= 3.5) return ThrottlingLevel.MAXIMUM;
    if (score >= 2.5) return ThrottlingLevel.HEAVY;
    if (score >= 1.5) return ThrottlingLevel.MODERATE;
    if (score >= 0.5) return ThrottlingLevel.LIGHT;
    
    return ThrottlingLevel.NONE;
  }

  /**
   * Calculate delay based on throttling level
   */
  private calculateDelay(level: ThrottlingLevel, factors: ThrottlingFactors): number {
    const baseDelay = this.config.baseDelay;
    
    // Apply level multiplier
    const levelMultipliers = {
      [ThrottlingLevel.NONE]: 0,
      [ThrottlingLevel.LIGHT]: 1.5,
      [ThrottlingLevel.MODERATE]: 3,
      [ThrottlingLevel.HEAVY]: 6,
      [ThrottlingLevel.MAXIMUM]: 12,
    };

    let delay = baseDelay * levelMultipliers[level];
    
    // Apply adaptive multiplier
    delay *= this.adaptiveDelayMultiplier;
    
    // Apply device-specific adjustments
    if (factors.deviceTier === 'low') {
      delay *= 1.5;
    } else if (factors.deviceTier === 'high') {
      delay *= 0.7;
    }

    // Ensure within bounds
    return Math.min(Math.max(delay, 0), this.config.maxDelay);
  }

  /**
   * Get human-readable throttling reason
   */
  private getThrottlingReason(level: ThrottlingLevel, factors: ThrottlingFactors): string {
    if (level === ThrottlingLevel.NONE) {
      return 'No throttling needed - system performing well';
    }

    const reasons: string[] = [];

    if (factors.cpuUsage > this.config.cpuThreshold) {
      reasons.push(`High CPU usage (${(factors.cpuUsage * 100).toFixed(1)}%)`);
    }

    if (factors.memoryPressure !== 'low') {
      reasons.push(`Memory pressure: ${factors.memoryPressure}`);
    }

    if (factors.performanceScore < this.config.performanceThreshold) {
      reasons.push(`Low performance score (${(factors.performanceScore * 100).toFixed(1)}%)`);
    }

    if (factors.userActivity) {
      reasons.push('User is active');
    }

    if (factors.deviceTier === 'low') {
      reasons.push('Low-tier device');
    }

    return reasons.join(', ') || 'Preventive throttling';
  }

  /**
   * Check if operations should be paused entirely
   */
  private shouldPauseOperations(factors: ThrottlingFactors): boolean {
    // Pause if background operations are disabled
    if (!factors.backgroundOperationsEnabled) {
      return true;
    }

    // Pause if memory is critical
    if (factors.memoryPressure === 'critical') {
      return true;
    }

    // Pause if CPU usage is extremely high
    if (factors.cpuUsage > 0.95) {
      return true;
    }

    return false;
  }

  /**
   * Wait for better system conditions
   */
  private async waitForBetterConditions(): Promise<void> {
    const maxWaitTime = 30000; // 30 seconds
    const checkInterval = 2000;  // 2 seconds
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      await this.sleep(checkInterval);
      
      const factors = this.gatherThrottlingFactors();
      if (!this.shouldPauseOperations(factors)) {
        console.log('[AdaptiveThrottler] System conditions improved, resuming operations');
        return;
      }
    }

    console.log('[AdaptiveThrottler] Max wait time exceeded, proceeding with operations');
  }

  /**
   * Update adaptive delay multiplier based on performance trends
   */
  private updateAdaptiveMultiplier(_metrics: PerformanceMetrics): void {
    if (this.metricsHistory.length < 5) {
      return; // Not enough data
    }

    const recent = this.metricsHistory.slice(-3);
    const older = this.metricsHistory.slice(-6, -3);

    const recentAvgPerformance = recent.reduce((sum, m) => sum + m.performanceScore, 0) / recent.length;
    const olderAvgPerformance = older.reduce((sum, m) => sum + m.performanceScore, 0) / older.length;

    const performanceTrend = recentAvgPerformance - olderAvgPerformance;

    // Adjust multiplier based on trend
    if (performanceTrend < -0.1) {
      // Performance degrading, increase throttling
      this.adaptiveDelayMultiplier = Math.min(this.adaptiveDelayMultiplier * 1.2, 3.0);
    } else if (performanceTrend > 0.1) {
      // Performance improving, reduce throttling
      this.adaptiveDelayMultiplier = Math.max(this.adaptiveDelayMultiplier * 0.9, 0.5);
    }
  }

  /**
   * Create no-throttling decision
   */
  private createNoThrottlingDecision(): ThrottlingDecision {
    return {
      level: ThrottlingLevel.NONE,
      delay: 0,
      reason: 'Throttling disabled',
      factors: this.gatherThrottlingFactors(),
      shouldPause: false,
    };
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Export singleton instance
 */
export const adaptiveThrottler = AdaptiveThrottler.getInstance();