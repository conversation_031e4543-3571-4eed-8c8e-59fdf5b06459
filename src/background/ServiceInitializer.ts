/**
 * Service Initialization Manager
 * 
 * Manages parallel initialization of services with dependency resolution
 * and performance optimization for faster startup times.
 */

import { dbService, blacklistService } from '../services';
import { backgroundHybridSearchService } from '../services/hybrid-search.background.service';
import { DEFAULT_SETTINGS, SETTING_KEYS } from '../models';

export interface ServiceDependency {
  name: string;
  dependencies: string[];
  initFunction: () => Promise<void>;
  priority: 'critical' | 'high' | 'medium' | 'low';
  timeout?: number; // in milliseconds
}

export interface InitializationResult {
  serviceName: string;
  success: boolean;
  duration: number;
  error?: string;
}

export class ServiceInitializer {
  private static instance: ServiceInitializer;
  private services: Map<string, ServiceDependency> = new Map();
  private initializationResults: Map<string, InitializationResult> = new Map();
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  private constructor() {
    this.registerServices();
  }

  public static getInstance(): ServiceInitializer {
    if (!ServiceInitializer.instance) {
      ServiceInitializer.instance = new ServiceInitializer();
    }
    return ServiceInitializer.instance;
  }

  /**
   * Register all services with their dependencies
   */
  private registerServices(): void {
    // Database service - critical foundation
    this.registerService({
      name: 'database',
      dependencies: [],
      initFunction: () => dbService.init(),
      priority: 'critical',
      timeout: 5000
    });

    // Blacklist service - depends on database
    this.registerService({
      name: 'blacklist',
      dependencies: ['database'],
      initFunction: () => blacklistService.init(),
      priority: 'high',
      timeout: 3000
    });

    // Default settings - depends on database
    this.registerService({
      name: 'settings',
      dependencies: ['database'],
      initFunction: () => this.initializeDefaultSettings(),
      priority: 'high',
      timeout: 2000
    });

    // Resource monitor - independent
    this.registerService({
      name: 'resourceMonitor',
      dependencies: [],
      initFunction: () => this.initializeResourceMonitor(),
      priority: 'medium',
      timeout: 1000
    });

    // Task queue - independent
    this.registerService({
      name: 'taskQueue',
      dependencies: [],
      initFunction: () => this.initializeTaskQueue(),
      priority: 'medium',
      timeout: 1000
    });

    // Background hybrid search service - depends on database
    this.registerService({
      name: 'backgroundHybridSearch',
      dependencies: ['database'],
      initFunction: () => backgroundHybridSearchService.init(),
      priority: 'medium',
      timeout: 5000 // Shorter timeout for lightweight service
    });
  }

  /**
   * Register a service with its dependencies
   */
  public registerService(service: ServiceDependency): void {
    this.services.set(service.name, service);
  }

  /**
   * Initialize all services with parallel execution and dependency resolution
   */
  public async initializeAll(): Promise<InitializationResult[]> {
    if (this.isInitialized) {
      return Array.from(this.initializationResults.values());
    }

    if (this.initializationPromise) {
      await this.initializationPromise;
      return Array.from(this.initializationResults.values());
    }

    this.initializationPromise = this._performInitialization();
    await this.initializationPromise;
    return Array.from(this.initializationResults.values());
  }

  /**
   * Perform parallel initialization with dependency resolution
   */
  private async _performInitialization(): Promise<void> {
    const startTime = performance.now();
    console.log('[ServiceInitializer] Starting parallel service initialization...');

    try {
      // Group services by dependency levels
      const dependencyLevels = this.resolveDependencyLevels();
      
      // Initialize services level by level, but parallelize within each level
      for (let level = 0; level < dependencyLevels.length; level++) {
        const levelServices = dependencyLevels[level];
        console.log(`[ServiceInitializer] Initializing level ${level}: ${levelServices.map(s => s.name).join(', ')}`);

        // Initialize all services in this level in parallel
        const levelPromises = levelServices.map(service => this.initializeService(service));
        const levelResults = await Promise.allSettled(levelPromises);

        // Check for critical failures
        const criticalFailures = levelResults
          .map((result, index) => ({ result, service: levelServices[index] }))
          .filter(({ result, service }) => 
            result.status === 'rejected' && service.priority === 'critical'
          );

        if (criticalFailures.length > 0) {
          const failedServices = criticalFailures.map(({ service }) => service.name).join(', ');
          throw new Error(`Critical services failed: ${failedServices}`);
        }
      }

      this.isInitialized = true;
      const totalTime = performance.now() - startTime;
      
      console.log(`[ServiceInitializer] All services initialized in ${totalTime.toFixed(2)}ms`);
      this.logInitializationSummary();

    } catch (error) {
      console.error('[ServiceInitializer] Service initialization failed:', error);
      throw error;
    }
  }

  /**
   * Initialize a single service with timeout and error handling
   */
  private async initializeService(service: ServiceDependency): Promise<InitializationResult> {
    const startTime = performance.now();
    const timeout = service.timeout || 10000; // Default 10 second timeout

    try {
      console.log(`[ServiceInitializer] Initializing ${service.name}...`);

      // Create a timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Service ${service.name} initialization timeout`)), timeout);
      });

      // Race between initialization and timeout
      await Promise.race([service.initFunction(), timeoutPromise]);

      const duration = performance.now() - startTime;
      const result: InitializationResult = {
        serviceName: service.name,
        success: true,
        duration
      };

      this.initializationResults.set(service.name, result);
      console.log(`[ServiceInitializer] ${service.name} initialized successfully in ${duration.toFixed(2)}ms`);
      
      return result;

    } catch (error) {
      const duration = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      const result: InitializationResult = {
        serviceName: service.name,
        success: false,
        duration,
        error: errorMessage
      };

      this.initializationResults.set(service.name, result);
      
      if (service.priority === 'critical') {
        console.error(`[ServiceInitializer] Critical service ${service.name} failed:`, error);
      } else {
        console.warn(`[ServiceInitializer] Non-critical service ${service.name} failed:`, error);
      }

      return result;
    }
  }

  /**
   * Resolve dependency levels for parallel execution
   */
  private resolveDependencyLevels(): ServiceDependency[][] {
    const levels: ServiceDependency[][] = [];
    const resolved = new Set<string>();
    const remaining = new Map(this.services);

    while (remaining.size > 0) {
      const currentLevel: ServiceDependency[] = [];

      // Find services that can be initialized at this level
      for (const [_name, service] of remaining) {
        const canInitialize = service.dependencies.every(dep => resolved.has(dep));
        
        if (canInitialize) {
          currentLevel.push(service);
        }
      }

      if (currentLevel.length === 0) {
        // Circular dependency or missing dependency
        const remainingNames = Array.from(remaining.keys());
        throw new Error(`Circular or missing dependencies detected: ${remainingNames.join(', ')}`);
      }

      // Sort current level by priority
      currentLevel.sort((a, b) => {
        const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });

      levels.push(currentLevel);

      // Mark services as resolved and remove from remaining
      for (const service of currentLevel) {
        resolved.add(service.name);
        remaining.delete(service.name);
      }
    }

    return levels;
  }

  /**
   * Initialize default settings
   */
  private async initializeDefaultSettings(): Promise<void> {
    try {
      // Check if settings already exist and set defaults
      const settingsToCheck = [
        { key: SETTING_KEYS.SEARCH_CONFIG, value: DEFAULT_SETTINGS.search_config },
        { key: SETTING_KEYS.BLACKLIST_DOMAINS, value: DEFAULT_SETTINGS.blacklist_domains },
        { key: SETTING_KEYS.UI_PREFERENCES, value: DEFAULT_SETTINGS.ui_preferences },
        { key: SETTING_KEYS.DATA_CLEANUP_POLICY, value: DEFAULT_SETTINGS.data_cleanup_policy }
      ];

      const initPromises = settingsToCheck.map(async ({ key, value }) => {
        const existing = await dbService.getSetting(key);
        if (!existing) {
          await dbService.setSetting(key, value);
          console.log(`[ServiceInitializer] Default setting initialized: ${key}`);
        }
      });

      await Promise.all(initPromises);
      console.log('[ServiceInitializer] All default settings initialized');

    } catch (error) {
      console.error('[ServiceInitializer] Failed to initialize default settings:', error);
      throw error;
    }
  }

  /**
   * Initialize resource monitor
   */
  private async initializeResourceMonitor(): Promise<void> {
    try {
      // Resource monitor initialization is usually immediate
      console.log('[ServiceInitializer] Resource monitor initialized');
    } catch (error) {
      console.error('[ServiceInitializer] Failed to initialize resource monitor:', error);
      throw error;
    }
  }

  /**
   * Initialize task queue
   */
  private async initializeTaskQueue(): Promise<void> {
    try {
      // Task queue initialization is usually immediate
      console.log('[ServiceInitializer] Task queue initialized');
    } catch (error) {
      console.error('[ServiceInitializer] Failed to initialize task queue:', error);
      throw error;
    }
  }

  /**
   * Log initialization summary
   */
  private logInitializationSummary(): void {
    const results = Array.from(this.initializationResults.values());
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    const totalTime = results.reduce((sum, r) => sum + r.duration, 0);

    console.log('[ServiceInitializer] Initialization Summary:');
    console.log(`  Total services: ${results.length}`);
    console.log(`  Successful: ${successful.length}`);
    console.log(`  Failed: ${failed.length}`);
    console.log(`  Total time: ${totalTime.toFixed(2)}ms`);

    if (failed.length > 0) {
      console.warn('Failed services:', failed.map(f => `${f.serviceName} (${f.error})`));
    }
  }

  /**
   * Check if a specific service is initialized
   */
  public isServiceInitialized(serviceName: string): boolean {
    const result = this.initializationResults.get(serviceName);
    return result?.success === true;
  }

  /**
   * Get initialization result for a specific service
   */
  public getServiceResult(serviceName: string): InitializationResult | null {
    return this.initializationResults.get(serviceName) || null;
  }

  /**
   * Get all initialization results
   */
  public getAllResults(): InitializationResult[] {
    return Array.from(this.initializationResults.values());
  }

  /**
   * Reset the initializer (for testing)
   */
  public reset(): void {
    this.services.clear();
    this.initializationResults.clear();
    this.isInitialized = false;
    this.initializationPromise = null;
    this.registerServices();
  }
}

// Export singleton instance
export const serviceInitializer = ServiceInitializer.getInstance();