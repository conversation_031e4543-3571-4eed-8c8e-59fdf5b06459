/**
 * Resource Monitor for Recall V3.0
 * 
 * Monitors system performance and resource usage to enable adaptive scheduling
 * of indexing tasks. Ensures minimal impact on user browsing experience.
 */

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  /** CPU usage percentage (0-1) */
  cpuUsage: number;
  /** Memory usage in MB */
  memoryUsage: number;
  /** Memory limit in MB */
  memoryLimit: number;
  /** Performance score (0-1, higher is better) */
  performanceScore: number;
  /** Available memory in MB */
  availableMemory: number;
  /** Timestamp of measurement */
  timestamp: number;
  /** Device tier classification */
  deviceTier: DeviceTier;
  /** Detailed memory breakdown */
  memoryBreakdown: MemoryBreakdown;
  /** Performance timing metrics */
  timingMetrics: TimingMetrics;
  /** Resource utilization metrics */
  resourceMetrics: ResourceUtilizationMetrics;
}

/**
 * Detailed memory breakdown
 */
export interface MemoryBreakdown {
  /** Used JS heap size in MB */
  usedJSHeapSize: number;
  /** Total JS heap size in MB */
  totalJSHeapSize: number;
  /** JS heap size limit in MB */
  jsHeapSizeLimit: number;
  /** Memory fragmentation ratio (0-1) */
  fragmentationRatio: number;
  /** Garbage collection frequency (collections per minute) */
  gcFrequency: number;
  /** Memory growth rate (MB per minute) */
  memoryGrowthRate: number;
  /** Memory pressure level */
  pressureLevel: MemoryPressureLevel;
}

/**
 * Performance timing metrics
 */
export interface TimingMetrics {
  /** Frame rendering time (ms) */
  frameTime: number;
  /** Task execution time (ms) */
  taskTime: number;
  /** Event loop lag (ms) */
  eventLoopLag: number;
  /** Resource loading time (ms) */
  resourceLoadTime: number;
  /** Paint timing (ms) */
  paintTiming: number;
  /** Layout timing (ms) */
  layoutTiming: number;
}

/**
 * Resource utilization metrics
 */
export interface ResourceUtilizationMetrics {
  /** Thread utilization (0-1) */
  threadUtilization: number;
  /** Network utilization (0-1) */
  networkUtilization: number;
  /** Storage I/O rate (operations per second) */
  storageIORate: number;
  /** Cache hit ratio (0-1) */
  cacheHitRatio: number;
  /** Background task efficiency (0-1) */
  backgroundTaskEfficiency: number;
  /** Extension overhead (ms per operation) */
  extensionOverhead: number;
}

/**
 * Resource statistics interface
 */
export interface ResourceStats {
  /** Average CPU usage over time */
  averageCpuUsage: number;
  /** Peak memory usage */
  peakMemoryUsage: number;
  /** Performance score trend */
  performanceTrend: number;
  /** Number of measurements */
  measurementCount: number;
  /** Last updated timestamp */
  lastUpdated: number;
  /** Period stats cover (ms) */
  period: number;
  /** Memory statistics */
  memoryStats: MemoryStats;
  /** Performance optimization opportunities */
  optimizationOpportunities: OptimizationOpportunity[];
}

/**
 * Memory statistics
 */
export interface MemoryStats {
  /** Average memory usage (MB) */
  averageMemoryUsage: number;
  /** Peak memory usage (MB) */
  peakMemoryUsage: number;
  /** Memory usage variance */
  memoryVariance: number;
  /** Average fragmentation ratio */
  averageFragmentation: number;
  /** GC frequency trend */
  gcFrequencyTrend: number;
  /** Memory leak indicators */
  leakIndicators: MemoryLeakIndicator[];
}

/**
 * Memory leak indicator
 */
export interface MemoryLeakIndicator {
  /** Type of potential leak */
  type: 'growing_heap' | 'high_fragmentation' | 'frequent_gc' | 'retained_objects';
  /** Severity (0-1) */
  severity: number;
  /** Description */
  description: string;
  /** Suggested action */
  suggestion: string;
}

/**
 * Performance optimization opportunity
 */
export interface OptimizationOpportunity {
  /** Category of optimization */
  category: 'memory' | 'cpu' | 'network' | 'storage' | 'ui';
  /** Priority (0-1) */
  priority: number;
  /** Description */
  description: string;
  /** Potential impact */
  impact: string;
  /** Implementation complexity */
  complexity: 'low' | 'medium' | 'high';
}

/**
 * Adaptive performance parameters
 */
export interface AdaptiveParams {
  /** Recommended batch size for operations */
  batchSize: number;
  /** Recommended delay between operations (ms) */
  operationDelay: number;
  /** Maximum concurrent operations */
  maxConcurrentOps: number;
  /** Whether to enable background operations */
  enableBackgroundOps: boolean;
  /** Performance threshold for operation execution */
  performanceThreshold: number;
}

/**
 * Device performance tier
 */
export const DeviceTier = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  UNKNOWN: 'unknown'
} as const;

export type DeviceTier = typeof DeviceTier[keyof typeof DeviceTier];

/**
 * Resource monitoring configuration
 */
export interface ResourceMonitorConfig {
  /** Monitoring interval in milliseconds */
  monitoringInterval: number;
  /** History size for averaging */
  historySize: number;
  /** CPU threshold for performance alerts */
  cpuThreshold: number;
  /** Memory threshold for performance alerts */
  memoryThreshold: number;
  /** Performance score threshold */
  performanceThreshold: number;
  /** Device detection enabled */
  deviceDetectionEnabled: boolean;
  /** Adaptive scheduling enabled */
  adaptiveSchedulingEnabled: boolean;
  /** Debug logging enabled */
  debugMode: boolean;
}

/**
 * Performance event listener type
 */
export type PerformanceListener = (metrics: PerformanceMetrics) => void;

/**
 * Memory pressure levels
 */
export const MemoryPressureLevel = {
  LOW: 'low',
  MODERATE: 'moderate', 
  HIGH: 'high',
  CRITICAL: 'critical'
} as const;

export type MemoryPressureLevel = typeof MemoryPressureLevel[keyof typeof MemoryPressureLevel];

/**
 * Memory pressure event listener type
 */
export type MemoryPressureListener = (level: MemoryPressureLevel, metrics: PerformanceMetrics) => void;

/**
 * Default configuration
 */
const DEFAULT_CONFIG: ResourceMonitorConfig = {
  monitoringInterval: 5000, // 5 seconds
  historySize: 20, // Keep last 20 measurements
  cpuThreshold: 0.8, // 80% CPU usage
  memoryThreshold: 0.9, // 90% memory usage
  performanceThreshold: 0.5, // 50% performance score
  deviceDetectionEnabled: true,
  adaptiveSchedulingEnabled: true,
  debugMode: false
};

/**
 * Resource Monitor class
 */
export class ResourceMonitor {
  private static instance: ResourceMonitor;
  private config: ResourceMonitorConfig;
  private metricsHistory: PerformanceMetrics[] = [];
  private listeners: PerformanceListener[] = [];
  private memoryPressureListeners: MemoryPressureListener[] = [];
  private monitoringTimer: NodeJS.Timeout | null = null;
  private isMonitoring = false;
  private deviceTier: DeviceTier = DeviceTier.UNKNOWN;
  private lastMetrics: PerformanceMetrics | null = null;
  private performanceObserver: PerformanceObserver | null = null;
  private currentMemoryPressure: MemoryPressureLevel = MemoryPressureLevel.LOW;
  private memoryPressureHistory: { level: MemoryPressureLevel; timestamp: number }[] = [];
  // private gcObserver: PerformanceObserver | null = null;
  private gcCount = 0;
  private lastGcTime = 0;
  private memoryGrowthHistory: { usage: number; timestamp: number }[] = [];
  private performanceTimingHistory: TimingMetrics[] = [];
  private resourceUtilizationHistory: ResourceUtilizationMetrics[] = [];
  // private taskStartTimes: Map<string, number> = new Map();
  private networkRequestCount = 0;
  private storageOperationCount = 0;
  private cacheHits = 0;
  private cacheMisses = 0;

  private constructor(config: Partial<ResourceMonitorConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeDeviceDetection();
    this.setupPerformanceObserver();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<ResourceMonitorConfig>): ResourceMonitor {
    if (!ResourceMonitor.instance) {
      ResourceMonitor.instance = new ResourceMonitor(config);
    }
    return ResourceMonitor.instance;
  }

  /**
   * Start monitoring resources
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      return;
    }

    this.log('Starting resource monitoring...');
    this.isMonitoring = true;

    // Take initial measurement
    this.measurePerformance();

    // Set up periodic monitoring
    this.monitoringTimer = setInterval(() => {
      this.measurePerformance();
    }, this.config.monitoringInterval);

    this.log('Resource monitoring started');
  }

  /**
   * Stop monitoring resources
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.log('Stopping resource monitoring...');
    this.isMonitoring = false;

    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
      this.monitoringTimer = null;
    }

    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }

    this.log('Resource monitoring stopped');
  }

  /**
   * Add performance listener
   */
  public addListener(listener: PerformanceListener): void {
    this.listeners.push(listener);
  }

  /**
   * Remove performance listener
   */
  public removeListener(listener: PerformanceListener): void {
    const index = this.listeners.indexOf(listener);
    if (index !== -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Add memory pressure listener
   */
  public addMemoryPressureListener(listener: MemoryPressureListener): void {
    this.memoryPressureListeners.push(listener);
  }

  /**
   * Remove memory pressure listener
   */
  public removeMemoryPressureListener(listener: MemoryPressureListener): void {
    const index = this.memoryPressureListeners.indexOf(listener);
    if (index !== -1) {
      this.memoryPressureListeners.splice(index, 1);
    }
  }

  /**
   * Get current memory pressure level
   */
  public getCurrentMemoryPressure(): MemoryPressureLevel {
    return this.currentMemoryPressure;
  }

  /**
   * Get memory pressure history for the last period
   */
  public getMemoryPressureHistory(periodMs: number = 60000): { level: MemoryPressureLevel; timestamp: number }[] {
    const cutoff = Date.now() - periodMs;
    return this.memoryPressureHistory.filter(entry => entry.timestamp >= cutoff);
  }

  /**
   * Get current performance metrics
   */
  public getCurrentMetrics(): PerformanceMetrics | null {
    return this.lastMetrics;
  }

  /**
   * Get resource statistics
   */
  public getStats(): ResourceStats {
    if (this.metricsHistory.length === 0) {
      return {
        averageCpuUsage: 0,
        peakMemoryUsage: 0,
        performanceTrend: 0,
        measurementCount: 0,
        lastUpdated: 0,
        period: 0,
        memoryStats: this.getDefaultMemoryStats(),
        optimizationOpportunities: []
      };
    }

    const now = Date.now();
    const oldestMetric = this.metricsHistory[0];
    const period = now - oldestMetric.timestamp;

    const averageCpuUsage = this.metricsHistory.reduce((sum, m) => sum + m.cpuUsage, 0) / this.metricsHistory.length;
    const peakMemoryUsage = Math.max(...this.metricsHistory.map(m => m.memoryUsage));

    // Calculate performance trend (simple linear regression)
    const performanceTrend = this.calculatePerformanceTrend();

    return {
      averageCpuUsage,
      peakMemoryUsage,
      performanceTrend,
      measurementCount: this.metricsHistory.length,
      lastUpdated: now,
      period,
      memoryStats: this.calculateMemoryStats(),
      optimizationOpportunities: this.identifyOptimizationOpportunities()
    };
  }

  /**
   * Get adaptive performance parameters
   */
  public getAdaptiveParams(): AdaptiveParams {
    const currentMetrics = this.getCurrentMetrics();
    const stats = this.getStats();

    if (!currentMetrics) {
      return this.getDefaultAdaptiveParams();
    }

    const performanceScore = currentMetrics.performanceScore;
    const avgCpuUsage = stats.averageCpuUsage;

    // Adapt parameters based on performance
    let batchSize: number;
    let operationDelay: number;
    let maxConcurrentOps: number;

    if (performanceScore > 0.8 && avgCpuUsage < 0.3) {
      // High performance
      batchSize = 20;
      operationDelay = 10;
      maxConcurrentOps = 4;
    } else if (performanceScore > 0.5 && avgCpuUsage < 0.6) {
      // Medium performance
      batchSize = 10;
      operationDelay = 50;
      maxConcurrentOps = 2;
    } else {
      // Low performance
      batchSize = 5;
      operationDelay = 200;
      maxConcurrentOps = 1;
    }

    // Adjust for device tier
    switch (this.deviceTier) {
      case DeviceTier.LOW:
        batchSize = Math.min(batchSize, 5);
        operationDelay = Math.max(operationDelay, 100);
        maxConcurrentOps = 1;
        break;
      case DeviceTier.HIGH:
        batchSize = Math.max(batchSize, 10);
        operationDelay = Math.min(operationDelay, 20);
        break;
    }

    return {
      batchSize,
      operationDelay,
      maxConcurrentOps,
      enableBackgroundOps: performanceScore > this.config.performanceThreshold,
      performanceThreshold: this.config.performanceThreshold
    };
  }

  /**
   * Check if system is ready for operations
   */
  public isSystemReady(): boolean {
    const metrics = this.getCurrentMetrics();
    if (!metrics) {
      return false;
    }

    return (
      metrics.cpuUsage < this.config.cpuThreshold &&
      metrics.memoryUsage / metrics.memoryLimit < this.config.memoryThreshold &&
      metrics.performanceScore > this.config.performanceThreshold
    );
  }

  /**
   * Get device tier
   */
  public getDeviceTier(): DeviceTier {
    return this.deviceTier;
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<ResourceMonitorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart monitoring if interval changed
    if (newConfig.monitoringInterval && this.isMonitoring) {
      this.stopMonitoring();
      this.startMonitoring();
    }

    this.log('Configuration updated');
  }

  // Private methods

  /**
   * Measure current performance
   */
  private async measurePerformance(): Promise<void> {
    try {
      const metrics = await this.collectMetrics();
      
      // Add to history
      this.metricsHistory.push(metrics);
      
      // Maintain history size
      if (this.metricsHistory.length > this.config.historySize) {
        this.metricsHistory.shift();
      }
      
      this.lastMetrics = metrics;

      // Check and update memory pressure
      this.updateMemoryPressure(metrics);

      // Notify listeners
      this.notifyListeners(metrics);

      // Log if performance is poor
      if (metrics.performanceScore < this.config.performanceThreshold) {
        this.log('Performance degradation detected', {
          score: metrics.performanceScore,
          cpu: metrics.cpuUsage,
          memory: `${metrics.memoryUsage.toFixed(1)}/${metrics.memoryLimit.toFixed(1)}MB`
        });
      }

    } catch (error) {
      this.log('Failed to measure performance:', error);
    }
  }

  /**
   * Collect comprehensive performance metrics
   */
  private async collectMetrics(): Promise<PerformanceMetrics> {
    const timestamp = Date.now();

    // Get memory information
    const memoryInfo = await this.getMemoryInfo();
    const memoryUsage = memoryInfo.usedJSHeapSize / (1024 * 1024); // Convert to MB
    const memoryLimit = memoryInfo.jsHeapSizeLimit / (1024 * 1024); // Convert to MB
    const availableMemory = memoryLimit - memoryUsage;

    // Estimate CPU usage
    const cpuUsage = this.estimateCpuUsage();

    // Calculate performance score
    const performanceScore = this.calculatePerformanceScore(cpuUsage, memoryUsage, memoryLimit);

    // Collect detailed memory breakdown
    const memoryBreakdown = this.collectMemoryBreakdown(memoryInfo);

    // Collect timing metrics
    const timingMetrics = this.collectTimingMetrics();

    // Collect resource utilization metrics
    const resourceMetrics = this.collectResourceUtilizationMetrics();

    // Update growth tracking
    this.updateMemoryGrowthHistory(memoryUsage, timestamp);

    return {
      cpuUsage,
      memoryUsage,
      memoryLimit,
      performanceScore,
      availableMemory,
      timestamp,
      deviceTier: this.deviceTier,
      memoryBreakdown,
      timingMetrics,
      resourceMetrics
    };
  }

  /**
   * Get memory information
   */
  private async getMemoryInfo(): Promise<MemoryInfo> {
    if ('memory' in performance) {
      return (performance as any).memory;
    }

    // Fallback for browsers without memory API
    return {
      usedJSHeapSize: 50 * 1024 * 1024, // 50MB estimate
      totalJSHeapSize: 100 * 1024 * 1024, // 100MB estimate
      jsHeapSizeLimit: 2048 * 1024 * 1024 // 2GB estimate
    };
  }

  /**
   * Estimate CPU usage using multiple metrics and adaptive learning
   */
  private estimateCpuUsage(): number {
    // Use multiple estimation methods and combine them
    const frameTiming = this.estimateCpuFromFrameTiming();
    const taskTiming = this.estimateCpuFromTaskTiming();
    const resourceTiming = this.estimateCpuFromResourceTiming();
    const deviceProfile = this.estimateCpuFromDeviceProfile();

    // Weighted combination of different estimation methods
    const weights = {
      frameTiming: 0.3,
      taskTiming: 0.25,
      resourceTiming: 0.25,
      deviceProfile: 0.2
    };

    const estimatedCpu = 
      frameTiming * weights.frameTiming +
      taskTiming * weights.taskTiming +
      resourceTiming * weights.resourceTiming +
      deviceProfile * weights.deviceProfile;

    // Apply exponential smoothing for stability
    if (this.lastMetrics?.cpuUsage !== undefined) {
      const smoothingFactor = 0.3;
      return this.lastMetrics.cpuUsage * (1 - smoothingFactor) + estimatedCpu * smoothingFactor;
    }

    return Math.min(Math.max(estimatedCpu, 0.01), 1.0);
  }

  /**
   * Estimate CPU usage from frame timing (most accurate for UI thread)
   */
  private estimateCpuFromFrameTiming(): number {
    try {
      // Use requestAnimationFrame timing to detect CPU pressure
      // const now = performance.now();
      const idealFrameTime = 16.67; // 60 FPS
      
      // Get recent frame timing data
      const entries = performance.getEntriesByType('frame') as any[];
      if (entries.length === 0) {
        return this.fallbackCpuEstimate();
      }

      const recentEntries = entries.slice(-10); // Last 10 frames
      const avgFrameTime = recentEntries.reduce((sum, entry) => sum + entry.duration, 0) / recentEntries.length;
      
      // CPU usage correlates with frame time deviation
      const frameTimeRatio = avgFrameTime / idealFrameTime;
      
      // Normalize to 0-1 range with saturation at 4x normal frame time
      return Math.min((frameTimeRatio - 1) / 3, 1.0);
      
    } catch (error) {
      return this.fallbackCpuEstimate();
    }
  }

  /**
   * Estimate CPU usage from task timing
   */
  private estimateCpuFromTaskTiming(): number {
    try {
      const taskEntries = performance.getEntriesByType('measure');
      if (taskEntries.length === 0) {
        return this.fallbackCpuEstimate();
      }

      // Focus on recent entries (last 30 seconds)
      const cutoff = performance.now() - 30000;
      const recentEntries = taskEntries.filter(entry => entry.startTime > cutoff);
      
      if (recentEntries.length === 0) {
        return this.fallbackCpuEstimate();
      }

      // Calculate task density (tasks per second) and average duration
      const timeSpan = Math.max(1000, performance.now() - recentEntries[0].startTime);
      const taskDensity = recentEntries.length / (timeSpan / 1000);
      const avgDuration = recentEntries.reduce((sum, entry) => sum + entry.duration, 0) / recentEntries.length;

      // Combine task density and duration for CPU estimate
      const densityFactor = Math.min(taskDensity / 10, 1.0); // Normalize to 10 tasks/sec
      const durationFactor = Math.min(avgDuration / 50, 1.0); // Normalize to 50ms avg

      return (densityFactor * 0.6) + (durationFactor * 0.4);
      
    } catch (error) {
      return this.fallbackCpuEstimate();
    }
  }

  /**
   * Estimate CPU usage from resource timing
   */
  private estimateCpuFromResourceTiming(): number {
    try {
      const resourceEntries = performance.getEntriesByType('resource');
      if (resourceEntries.length === 0) {
        return this.fallbackCpuEstimate();
      }

      // Focus on recent resource loads
      const cutoff = performance.now() - 10000; // Last 10 seconds
      const recentEntries = resourceEntries.filter(entry => entry.startTime > cutoff);
      
      if (recentEntries.length === 0) {
        return this.fallbackCpuEstimate();
      }

      // Calculate average resource loading time as proxy for system responsiveness
      const avgLoadTime = recentEntries.reduce((sum, entry) => {
        // Check if entry has navigation timing properties
        if ('responseEnd' in entry && 'fetchStart' in entry) {
          const navEntry = entry as PerformanceNavigationTiming;
          const duration = navEntry.responseEnd - navEntry.fetchStart;
          return sum + duration;
        }
        // Fallback for other entry types
        return sum + (entry.duration || 100);
      }, 0) / recentEntries.length;

      // High load times indicate CPU pressure
      return Math.min(avgLoadTime / 1000, 1.0); // Normalize to 1 second
      
    } catch (error) {
      return this.fallbackCpuEstimate();
    }
  }

  /**
   * Estimate CPU usage based on device profile and history
   */
  private estimateCpuFromDeviceProfile(): number {
    // Use device tier and historical performance for baseline estimation
    let baselineLoad = 0.1; // Default low load

    switch (this.deviceTier) {
      case DeviceTier.LOW:
        baselineLoad = 0.3; // Low-end devices typically have higher baseline
        break;
      case DeviceTier.MEDIUM:
        baselineLoad = 0.2;
        break;
      case DeviceTier.HIGH:
        baselineLoad = 0.1;
        break;
    }

    // Adjust based on recent performance trends
    if (this.metricsHistory.length >= 5) {
      const recentPerformance = this.metricsHistory.slice(-5);
      const avgPerformanceScore = recentPerformance.reduce((sum, m) => sum + m.performanceScore, 0) / recentPerformance.length;
      
      // Lower performance score indicates higher CPU usage
      const performanceFactor = Math.max(0, 1 - avgPerformanceScore);
      baselineLoad += performanceFactor * 0.2;
    }

    return Math.min(baselineLoad, 1.0);
  }

  /**
   * Fallback CPU estimation when other methods fail
   */
  private fallbackCpuEstimate(): number {
    // Use device tier as fallback
    switch (this.deviceTier) {
      case DeviceTier.LOW:
        return 0.4;
      case DeviceTier.MEDIUM:
        return 0.2;
      case DeviceTier.HIGH:
        return 0.1;
      default:
        return 0.15;
    }
  }

  /**
   * Calculate overall performance score
   */
  private calculatePerformanceScore(cpuUsage: number, memoryUsage: number, memoryLimit: number): number {
    const cpuScore = Math.max(0, 1 - cpuUsage);
    const memoryScore = Math.max(0, 1 - (memoryUsage / memoryLimit));
    
    // Weighted average
    const performanceScore = (cpuScore * 0.6) + (memoryScore * 0.4);
    
    return Math.max(0, Math.min(1, performanceScore));
  }

  /**
   * Calculate performance trend
   */
  private calculatePerformanceTrend(): number {
    if (this.metricsHistory.length < 5) {
      return 0; // Not enough data
    }

    const recent = this.metricsHistory.slice(-5);
    const older = this.metricsHistory.slice(0, 5);

    const recentAvg = recent.reduce((sum, m) => sum + m.performanceScore, 0) / recent.length;
    const olderAvg = older.reduce((sum, m) => sum + m.performanceScore, 0) / older.length;

    return recentAvg - olderAvg; // Positive means improving, negative means degrading
  }

  /**
   * Initialize device detection
   */
  private initializeDeviceDetection(): void {
    if (!this.config.deviceDetectionEnabled) {
      return;
    }

    try {
      // Detect device tier based on hardware concurrency and memory
      const cores = navigator.hardwareConcurrency || 4;
      const connection = (navigator as any).connection;
      
      let tier: DeviceTier = DeviceTier.MEDIUM;

      if (cores >= 8) {
        tier = DeviceTier.HIGH;
      } else if (cores <= 2) {
        tier = DeviceTier.LOW;
      }

      // Adjust based on connection if available
      if (connection) {
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
          tier = DeviceTier.LOW;
        } else if (connection.effectiveType === '4g') {
          tier = tier === DeviceTier.LOW ? DeviceTier.MEDIUM : DeviceTier.HIGH;
        }
      }

      this.deviceTier = tier;
      this.log(`Device tier detected: ${tier} (cores: ${cores})`);

    } catch (error) {
      this.log('Device detection failed:', error);
      this.deviceTier = DeviceTier.UNKNOWN;
    }
  }

  /**
   * Setup performance observer
   */
  private setupPerformanceObserver(): void {
    try {
      if ('PerformanceObserver' in globalThis) {
        this.performanceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          // Process performance entries if needed
          this.log(`Performance entries received: ${entries.length}`);
        });

        this.performanceObserver.observe({ 
          entryTypes: ['measure', 'navigation', 'resource'] 
        });
      }
    } catch (error) {
      this.log('Failed to setup performance observer:', error);
    }
  }

  /**
   * Get default adaptive parameters
   */
  private getDefaultAdaptiveParams(): AdaptiveParams {
    return {
      batchSize: 10,
      operationDelay: 100,
      maxConcurrentOps: 2,
      enableBackgroundOps: true,
      performanceThreshold: this.config.performanceThreshold
    };
  }

  /**
   * Update memory pressure level and notify listeners if changed
   */
  private updateMemoryPressure(metrics: PerformanceMetrics): void {
    const newPressureLevel = this.calculateMemoryPressure(metrics);
    
    // Update history
    this.memoryPressureHistory.push({
      level: newPressureLevel,
      timestamp: metrics.timestamp
    });

    // Keep only last 100 entries (about 8 minutes at 5s intervals)
    if (this.memoryPressureHistory.length > 100) {
      this.memoryPressureHistory.shift();
    }

    // Check if pressure level changed
    if (newPressureLevel !== this.currentMemoryPressure) {
      const previousLevel = this.currentMemoryPressure;
      this.currentMemoryPressure = newPressureLevel;

      this.log(`Memory pressure changed: ${previousLevel} -> ${newPressureLevel}`, {
        memoryUsage: metrics.memoryUsage.toFixed(1),
        memoryLimit: metrics.memoryLimit.toFixed(1),
        memoryPercent: `${((metrics.memoryUsage / metrics.memoryLimit) * 100).toFixed(1)}%`
      });

      // Notify memory pressure listeners
      this.notifyMemoryPressureListeners(newPressureLevel, metrics);
    }
  }

  /**
   * Calculate memory pressure level based on current metrics
   */
  private calculateMemoryPressure(metrics: PerformanceMetrics): MemoryPressureLevel {
    const memoryPercent = metrics.memoryUsage / metrics.memoryLimit;
    const availableMemory = metrics.availableMemory;

    // Critical: Very high memory usage or very low available memory
    if (memoryPercent > 0.95 || availableMemory < 10) {
      return MemoryPressureLevel.CRITICAL;
    }

    // High: High memory usage or low available memory
    if (memoryPercent > 0.85 || availableMemory < 25) {
      return MemoryPressureLevel.HIGH;
    }

    // Moderate: Medium-high memory usage or moderate available memory
    if (memoryPercent > 0.70 || availableMemory < 50) {
      return MemoryPressureLevel.MODERATE;
    }

    // Low: Normal memory usage
    return MemoryPressureLevel.LOW;
  }

  /**
   * Notify all memory pressure listeners
   */
  private notifyMemoryPressureListeners(level: MemoryPressureLevel, metrics: PerformanceMetrics): void {
    for (const listener of this.memoryPressureListeners) {
      try {
        listener(level, metrics);
      } catch (error) {
        this.log('Memory pressure listener error:', error);
      }
    }
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(metrics: PerformanceMetrics): void {
    for (const listener of this.listeners) {
      try {
        listener(metrics);
      } catch (error) {
        this.log('Listener error:', error);
      }
    }
  }

  /**
   * Collect detailed memory breakdown
   */
  private collectMemoryBreakdown(memoryInfo: MemoryInfo): MemoryBreakdown {
    const usedJSHeapSize = memoryInfo.usedJSHeapSize / (1024 * 1024);
    const totalJSHeapSize = memoryInfo.totalJSHeapSize / (1024 * 1024);
    const jsHeapSizeLimit = memoryInfo.jsHeapSizeLimit / (1024 * 1024);

    // Calculate fragmentation ratio
    const fragmentationRatio = totalJSHeapSize > 0 ? 
      1 - (usedJSHeapSize / totalJSHeapSize) : 0;

    // Calculate GC frequency (collections per minute)
    const now = Date.now();
    const gcFrequency = this.lastGcTime > 0 ? 
      (this.gcCount / Math.max(1, (now - this.lastGcTime) / 60000)) : 0;

    // Calculate memory growth rate
    const memoryGrowthRate = this.calculateMemoryGrowthRate();

    return {
      usedJSHeapSize,
      totalJSHeapSize,
      jsHeapSizeLimit,
      fragmentationRatio,
      gcFrequency,
      memoryGrowthRate,
      pressureLevel: this.currentMemoryPressure
    };
  }

  /**
   * Collect timing metrics
   */
  private collectTimingMetrics(): TimingMetrics {
    // Get frame timing
    const frameTime = this.getAverageFrameTime();

    // Get task timing
    const taskTime = this.getAverageTaskTime();

    // Estimate event loop lag
    const eventLoopLag = this.estimateEventLoopLag();

    // Get resource loading time
    const resourceLoadTime = this.getAverageResourceLoadTime();

    // Get paint timing
    const paintTiming = this.getPaintTiming();

    // Get layout timing
    const layoutTiming = this.getLayoutTiming();

    const metrics: TimingMetrics = {
      frameTime,
      taskTime,
      eventLoopLag,
      resourceLoadTime,
      paintTiming,
      layoutTiming
    };

    // Store in history
    this.performanceTimingHistory.push(metrics);
    if (this.performanceTimingHistory.length > 50) {
      this.performanceTimingHistory.shift();
    }

    return metrics;
  }

  /**
   * Collect resource utilization metrics
   */
  private collectResourceUtilizationMetrics(): ResourceUtilizationMetrics {
    const threadUtilization = this.calculateThreadUtilization();
    const networkUtilization = this.calculateNetworkUtilization();
    const storageIORate = this.calculateStorageIORate();
    const cacheHitRatio = this.calculateCacheHitRatio();
    const backgroundTaskEfficiency = this.calculateBackgroundTaskEfficiency();
    const extensionOverhead = this.calculateExtensionOverhead();

    const metrics: ResourceUtilizationMetrics = {
      threadUtilization,
      networkUtilization,
      storageIORate,
      cacheHitRatio,
      backgroundTaskEfficiency,
      extensionOverhead
    };

    // Store in history
    this.resourceUtilizationHistory.push(metrics);
    if (this.resourceUtilizationHistory.length > 50) {
      this.resourceUtilizationHistory.shift();
    }

    return metrics;
  }

  /**
   * Update memory growth history
   */
  private updateMemoryGrowthHistory(memoryUsage: number, timestamp: number): void {
    this.memoryGrowthHistory.push({ usage: memoryUsage, timestamp });

    // Keep only last hour of data
    const cutoff = timestamp - 3600000;
    this.memoryGrowthHistory = this.memoryGrowthHistory.filter(
      entry => entry.timestamp > cutoff
    );
  }

  /**
   * Calculate memory growth rate (MB per minute)
   */
  private calculateMemoryGrowthRate(): number {
    if (this.memoryGrowthHistory.length < 2) {
      return 0;
    }

    const recent = this.memoryGrowthHistory.slice(-10); // Last 10 measurements
    const oldestEntry = recent[0];
    const newestEntry = recent[recent.length - 1];

    const timeDiff = newestEntry.timestamp - oldestEntry.timestamp;
    const memoryDiff = newestEntry.usage - oldestEntry.usage;

    if (timeDiff === 0) {
      return 0;
    }

    // Convert to MB per minute
    return (memoryDiff / timeDiff) * 60000;
  }

  /**
   * Get average frame time
   */
  private getAverageFrameTime(): number {
    try {
      const frameEntries = performance.getEntriesByType('frame') as any[];
      if (frameEntries.length === 0) {
        return 16.67; // Assume 60 FPS
      }

      const recentEntries = frameEntries.slice(-10);
      return recentEntries.reduce((sum, entry) => sum + entry.duration, 0) / recentEntries.length;
    } catch (error) {
      return 16.67;
    }
  }

  /**
   * Get average task time
   */
  private getAverageTaskTime(): number {
    try {
      const taskEntries = performance.getEntriesByType('measure');
      if (taskEntries.length === 0) {
        return 5; // Default estimate
      }

      const cutoff = performance.now() - 30000;
      const recentEntries = taskEntries.filter(entry => entry.startTime > cutoff);
      
      if (recentEntries.length === 0) {
        return 5;
      }

      return recentEntries.reduce((sum, entry) => sum + entry.duration, 0) / recentEntries.length;
    } catch (error) {
      return 5;
    }
  }

  /**
   * Estimate event loop lag
   */
  private estimateEventLoopLag(): number {
    // Use setTimeout precision as proxy for event loop lag
    const start = performance.now();
    setTimeout(() => {
      const lag = performance.now() - start;
      return Math.max(0, lag - 1); // Subtract expected minimum delay
    }, 0);
    
    return 1; // Default estimate
  }

  /**
   * Get average resource load time
   */
  private getAverageResourceLoadTime(): number {
    try {
      const resourceEntries = performance.getEntriesByType('resource');
      if (resourceEntries.length === 0) {
        return 100; // Default estimate
      }

      const cutoff = performance.now() - 30000;
      const recentEntries = resourceEntries.filter(entry => entry.startTime > cutoff);
      
      if (recentEntries.length === 0) {
        return 100;
      }

      const avgLoadTime = recentEntries.reduce((sum, entry) => {
        // Check if entry has resource timing properties
        if ('responseEnd' in entry && 'fetchStart' in entry) {
          const resourceEntry = entry as PerformanceResourceTiming;
          return sum + (resourceEntry.responseEnd - resourceEntry.fetchStart);
        }
        // Fallback for other entry types
        return sum + (entry.duration || 100);
      }, 0) / recentEntries.length;

      return avgLoadTime;
    } catch (error) {
      return 100;
    }
  }

  /**
   * Get paint timing
   */
  private getPaintTiming(): number {
    try {
      const paintEntries = performance.getEntriesByType('paint');
      if (paintEntries.length === 0) {
        return 10; // Default estimate
      }

      const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      return fcp ? fcp.startTime : 10;
    } catch (error) {
      return 10;
    }
  }

  /**
   * Get layout timing
   */
  private getLayoutTiming(): number {
    try {
      const layoutEntries = performance.getEntriesByType('layout-shift');
      if (layoutEntries.length === 0) {
        return 5; // Default estimate
      }

      return layoutEntries.reduce((sum, entry: any) => sum + entry.value, 0);
    } catch (error) {
      return 5;
    }
  }

  /**
   * Calculate thread utilization
   */
  private calculateThreadUtilization(): number {
    // Based on CPU usage and task density
    const cpuUsage = this.lastMetrics?.cpuUsage || 0.1;
    const taskEntries = performance.getEntriesByType('measure');
    const taskDensity = Math.min(taskEntries.length / 100, 1); // Normalize

    return Math.min((cpuUsage * 0.7) + (taskDensity * 0.3), 1);
  }

  /**
   * Calculate network utilization
   */
  private calculateNetworkUtilization(): number {
    const now = Date.now();
    const period = 60000; // 1 minute
    
    // Reset counter periodically
    if (now % period < 1000) {
      this.networkRequestCount = 0;
    }

    // Estimate based on request frequency
    const requestRate = this.networkRequestCount / (period / 1000);
    return Math.min(requestRate / 10, 1); // Normalize to 10 requests/second
  }

  /**
   * Calculate storage I/O rate
   */
  private calculateStorageIORate(): number {
    const now = Date.now();
    const period = 60000; // 1 minute
    
    // Reset counter periodically
    if (now % period < 1000) {
      this.storageOperationCount = 0;
    }

    return this.storageOperationCount / (period / 1000);
  }

  /**
   * Calculate cache hit ratio
   */
  private calculateCacheHitRatio(): number {
    const totalRequests = this.cacheHits + this.cacheMisses;
    if (totalRequests === 0) {
      return 1.0; // Assume good hit ratio if no data
    }

    return this.cacheHits / totalRequests;
  }

  /**
   * Calculate background task efficiency
   */
  private calculateBackgroundTaskEfficiency(): number {
    // Based on task completion rate vs resource usage
    const cpuUsage = this.lastMetrics?.cpuUsage || 0.1;
    const completedTasks = this.performanceTimingHistory.length;
    
    if (completedTasks === 0) {
      return 1.0;
    }

    // Higher efficiency = more tasks completed with less CPU
    const efficiency = Math.min(completedTasks / (cpuUsage * 100), 1);
    return efficiency;
  }

  /**
   * Calculate extension overhead
   */
  private calculateExtensionOverhead(): number {
    // Estimate overhead based on task timing
    const avgTaskTime = this.getAverageTaskTime();
    const baselineTaskTime = 5; // ms

    return Math.max(0, avgTaskTime - baselineTaskTime);
  }

  /**
   * Track network request
   */
  public trackNetworkRequest(): void {
    this.networkRequestCount++;
  }

  /**
   * Track storage operation
   */
  public trackStorageOperation(): void {
    this.storageOperationCount++;
  }

  /**
   * Track cache hit
   */
  public trackCacheHit(): void {
    this.cacheHits++;
  }

  /**
   * Track cache miss
   */
  public trackCacheMiss(): void {
    this.cacheMisses++;
  }

  /**
   * Track garbage collection
   */
  public trackGarbageCollection(): void {
    this.gcCount++;
    this.lastGcTime = Date.now();
  }

  /**
   * Get default memory stats when no data is available
   */
  private getDefaultMemoryStats(): MemoryStats {
    return {
      averageMemoryUsage: 0,
      peakMemoryUsage: 0,
      memoryVariance: 0,
      averageFragmentation: 0,
      gcFrequencyTrend: 0,
      leakIndicators: []
    };
  }

  /**
   * Calculate memory statistics from metrics history
   */
  private calculateMemoryStats(): MemoryStats {
    if (this.metricsHistory.length === 0) {
      return this.getDefaultMemoryStats();
    }

    const memoryUsages = this.metricsHistory.map(m => m.memoryUsage);
    const averageMemoryUsage = memoryUsages.reduce((sum, usage) => sum + usage, 0) / memoryUsages.length;
    const peakMemoryUsage = Math.max(...memoryUsages);
    
    // Calculate variance
    const variance = memoryUsages.reduce((sum, usage) => sum + Math.pow(usage - averageMemoryUsage, 2), 0) / memoryUsages.length;
    
    // Calculate average fragmentation from memory breakdown
    const fragmentationRatios = this.metricsHistory.map(m => m.memoryBreakdown.fragmentationRatio).filter(r => r > 0);
    const averageFragmentation = fragmentationRatios.length > 0 ? 
      fragmentationRatios.reduce((sum, ratio) => sum + ratio, 0) / fragmentationRatios.length : 0;
    
    // Calculate GC frequency trend
    const gcFrequencies = this.metricsHistory.map(m => m.memoryBreakdown.gcFrequency);
    const gcFrequencyTrend = gcFrequencies.length > 1 ? 
      (gcFrequencies[gcFrequencies.length - 1] - gcFrequencies[0]) / gcFrequencies.length : 0;

    return {
      averageMemoryUsage,
      peakMemoryUsage,
      memoryVariance: variance,
      averageFragmentation,
      gcFrequencyTrend,
      leakIndicators: this.identifyMemoryLeaks()
    };
  }

  /**
   * Identify memory leak indicators
   */
  private identifyMemoryLeaks(): MemoryLeakIndicator[] {
    const indicators: MemoryLeakIndicator[] = [];
    
    if (this.metricsHistory.length < 5) {
      return indicators;
    }

    // Check for growing heap
    const memoryGrowth = this.metricsHistory[this.metricsHistory.length - 1].memoryUsage - this.metricsHistory[0].memoryUsage;
    if (memoryGrowth > 50) { // 50MB growth threshold
      indicators.push({
        type: 'growing_heap',
        severity: Math.min(memoryGrowth / 100, 1),
        description: `Memory usage has grown by ${memoryGrowth.toFixed(1)}MB`,
        suggestion: 'Check for memory leaks in background tasks'
      });
    }

    // Check for high fragmentation
    const avgFragmentation = this.metricsHistory.reduce((sum, m) => sum + m.memoryBreakdown.fragmentationRatio, 0) / this.metricsHistory.length;
    if (avgFragmentation > 0.3) {
      indicators.push({
        type: 'high_fragmentation',
        severity: avgFragmentation,
        description: `High memory fragmentation detected (${(avgFragmentation * 100).toFixed(1)}%)`,
        suggestion: 'Consider memory cleanup or object pooling'
      });
    }

    return indicators;
  }

  /**
   * Identify performance optimization opportunities
   */
  private identifyOptimizationOpportunities(): OptimizationOpportunity[] {
    const opportunities: OptimizationOpportunity[] = [];
    
    if (this.metricsHistory.length === 0) {
      return opportunities;
    }

    const currentMetrics = this.metricsHistory[this.metricsHistory.length - 1];
    
    // Check for high CPU usage
    if (currentMetrics.cpuUsage > 0.7) {
      opportunities.push({
        category: 'cpu',
        priority: 0.8,
        description: 'High CPU usage detected',
        impact: 'Reduce background processing to improve user experience',
        complexity: 'medium'
      });
    }

    // Check for high memory usage
    if (currentMetrics.memoryUsage > 200) { // 200MB threshold
      opportunities.push({
        category: 'memory',
        priority: 0.7,
        description: 'High memory usage detected',
        impact: 'Implement memory cleanup to prevent browser slowdown',
        complexity: 'low'
      });
    }

    // Check for slow network operations
    if (currentMetrics.resourceMetrics.networkUtilization > 0.8) {
      opportunities.push({
        category: 'network',
        priority: 0.6,
        description: 'High network utilization detected',
        impact: 'Implement request batching or caching',
        complexity: 'medium'
      });
    }

    return opportunities;
  }

  /**
   * Log debug information
   */
  private log(message: string, data?: any): void {
    if (this.config.debugMode) {
      console.log(`[ResourceMonitor] ${message}`, data || '');
    }
  }
}

// Export singleton instance
export const resourceMonitor = ResourceMonitor.getInstance();

// Memory info interface for browsers that support it
interface MemoryInfo {
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}