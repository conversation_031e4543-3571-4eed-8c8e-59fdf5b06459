/**
 * Index Scheduler for Recall Background Service Worker
 * 
 * Main orchestrator that coordinates:
 * - Page content extraction and indexing
 * - Resource-aware task scheduling
 * - Chrome extension event handling
 * - Search index management
 * - Performance optimization
 */

/// <reference path="../types/chrome.d.ts" />

import { resourceMonitor, type ResourceMonitor } from './ResourceMonitor';
import { taskQueue, TaskQueue, TaskType, TaskPriority, TaskQueueUtils, type BaseTask, type TaskResult } from './TaskQueue';
import { dbService } from '../storage/IndexedDBService';
import type { Page } from '../models';

/**
 * Index scheduler configuration
 */
export interface IndexSchedulerConfig {
  /** Enable automatic indexing of visited pages */
  autoIndexing: boolean;
  /** Enable traditional search indexing */
  enableTraditionalSearch: boolean;
  /** Batch size for bulk operations */
  batchSize: number;
  /** Index rebuild threshold (number of changes) */
  rebuildThreshold: number;
  /** Performance monitoring enabled */
  enablePerformanceMonitoring: boolean;
  /** Debug mode */
  debugMode: boolean;
  /** Maximum pages to index per session */
  maxPagesPerSession: number;
  /** Minimum time between indexing operations (ms) */
  minIndexingInterval: number;
}

/**
 * Indexing statistics
 */
export interface IndexingStats {
  /** Total pages indexed in current session */
  pagesIndexed: number;
  /** Total indexing time in current session */
  totalIndexingTime: number;
  /** Average indexing time per page */
  averageIndexingTime: number;
  /** Failed indexing attempts */
  failedAttempts: number;
  /** Success rate */
  successRate: number;
  /** Current batch progress */
  currentBatchProgress?: {
    total: number;
    completed: number;
    failed: number;
  };
  /** Resource usage during indexing */
  resourceUsage: {
    averageCpu: number;
    peakMemory: number;
  };
}

/**
 * Page indexing event data
 */
export interface PageIndexEvent {
  /** Tab ID */
  tabId: number;
  /** Page URL */
  url: string;
  /** Page title */
  title: string;
  /** Navigation type */
  navigationType: 'link' | 'typed' | 'auto_bookmark' | 'auto_subframe' | 'manual_subframe' | 'generated' | 'start_page' | 'form_submit' | 'reload';
  /** Timestamp */
  timestamp: number;
}

/**
 * Main Index Scheduler class
 */
export class IndexScheduler {
  private static instance: IndexScheduler;
  private config: IndexSchedulerConfig;
  private resourceMonitor: ResourceMonitor;
  private taskQueue: TaskQueue;
  private isInitialized = false;
  private stats: IndexingStats;
  private navigationListeners = new Set<number>();
  private processingPages = new Set<string>();
  private lastIndexingTime = 0;

  // Store bound event handlers for proper cleanup
  private boundHandleNavigationCompleted: ((details: any) => Promise<void>) | null = null;
  private boundHandleHistoryStateUpdated: ((details: any) => Promise<void>) | null = null;
  private boundHandleTabUpdated: ((tabId: number, changeInfo: any, tab: any) => Promise<void>) | null = null;

  // Default configuration
  private static readonly DEFAULT_CONFIG: IndexSchedulerConfig = {
    autoIndexing: true,
    enableTraditionalSearch: true,
    batchSize: 10,
    rebuildThreshold: 1000,
    enablePerformanceMonitoring: true,
    debugMode: false,
    maxPagesPerSession: 500,
    minIndexingInterval: 1000, // 1 second
  };

  private constructor(config?: Partial<IndexSchedulerConfig>) {
    this.config = { ...IndexScheduler.DEFAULT_CONFIG, ...config };
    this.resourceMonitor = resourceMonitor;
    this.taskQueue = taskQueue;
    this.stats = this.initializeStats();

    // Bind event handlers once for proper cleanup
    this.boundHandleNavigationCompleted = this.handleNavigationCompleted.bind(this);
    this.boundHandleHistoryStateUpdated = this.handleHistoryStateUpdated.bind(this);
    this.boundHandleTabUpdated = this.handleTabUpdated.bind(this);
  }

  public static getInstance(config?: Partial<IndexSchedulerConfig>): IndexScheduler {
    if (!IndexScheduler.instance) {
      IndexScheduler.instance = new IndexScheduler(config);
    }
    return IndexScheduler.instance;
  }

  /**
   * Initialize the index scheduler
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log('[IndexScheduler] Initializing index scheduler...');

    try {
      // Initialize dependencies
      await this.taskQueue.initialize(this.resourceMonitor);
      
      if (this.config.enablePerformanceMonitoring) {
        this.resourceMonitor.startMonitoring();
      }

      // Note: Traditional search initialization handled by search services

      // Register task executors
      this.registerTaskExecutors();

      // Setup Chrome extension event listeners
      this.setupChromeEventListeners();

      // Setup performance monitoring
      this.setupPerformanceMonitoring();

      this.isInitialized = true;
      console.log('[IndexScheduler] Index scheduler initialized successfully');

    } catch (error) {
      console.error('[IndexScheduler] Failed to initialize:', error);
      throw error;
    }
  }

  /**
   * Shutdown the scheduler gracefully
   */
  public async shutdown(): Promise<void> {
    console.log('[IndexScheduler] Shutting down index scheduler...');

    this.isInitialized = false;
    
    // Remove Chrome event listeners
    this.removeChromeEventListeners();

    // Stop monitoring
    if (this.config.enablePerformanceMonitoring) {
      this.resourceMonitor.stopMonitoring();
    }

    // Shutdown task queue
    await this.taskQueue.shutdown();

    // Clear all references to prevent memory leaks
    this.navigationListeners.clear();
    this.processingPages.clear();
    this.stats = this.initializeStats();
    this.resourceMonitor = null as any;
    this.taskQueue = null as any;

    console.log('[IndexScheduler] Index scheduler shutdown complete');
  }

  /**
   * Manually trigger indexing of a specific page
   */
  public async indexPage(
    url: string, 
    title: string, 
    priority: TaskPriority = TaskPriority.HIGH
  ): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('IndexScheduler not initialized');
    }

    this.log(`Manual indexing requested for: ${url}`);

    // Create page object
    const page: Page = {
      id: this.generatePageId(url),
      url,
      title,
      content: '', // Will be extracted
      domain: new URL(url).hostname,
      visitTime: Date.now(),
      lastUpdated: Date.now(),
      accessCount: 1,
    };

    // Create and queue indexing task
    const task = TaskQueueUtils.createIndexPageTask(page, priority);
    const taskId = this.taskQueue.addTask(task);

    this.log(`Queued manual indexing task ${taskId} for ${url}`);
    return taskId;
  }

  /**
   * Trigger batch indexing of multiple pages
   */
  public async batchIndexPages(pages: Page[], priority: TaskPriority = TaskPriority.LOW): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('IndexScheduler not initialized');
    }

    this.log(`Batch indexing requested for ${pages.length} pages`);

    const task = TaskQueueUtils.createBatchIndexTask(pages, this.config.batchSize, priority);
    const taskId = this.taskQueue.addTask(task);

    this.log(`Queued batch indexing task ${taskId}`);
    return taskId;
  }

  /**
   * Rebuild the entire search index
   */
  public async rebuildIndex(force: boolean = false): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('IndexScheduler not initialized');
    }

    this.log(`Index rebuild requested (force: ${force})`);

    const task = TaskQueueUtils.createRebuildIndexTask(force);
    const taskId = this.taskQueue.addTask(task);

    this.log(`Queued index rebuild task ${taskId}`);
    return taskId;
  }

  /**
   * Get current indexing statistics
   */
  public getStats(): IndexingStats {
    
    // Calculate success rate
    const totalAttempts = this.stats.pagesIndexed + this.stats.failedAttempts;
    this.stats.successRate = totalAttempts > 0 ? this.stats.pagesIndexed / totalAttempts : 0;

    // Get current batch progress if any
    const batchTasks = Array.from(this.taskQueue['tasks'].values())
      .filter(task => task.type === TaskType.BATCH_INDEX);
    
    if (batchTasks.length > 0) {
      const completedBatches = batchTasks.filter(task => task.status === 'completed').length;
      const failedBatches = batchTasks.filter(task => task.status === 'failed').length;
      
      this.stats.currentBatchProgress = {
        total: batchTasks.length,
        completed: completedBatches,
        failed: failedBatches,
      };
    }

    return { ...this.stats };
  }

  /**
   * Update scheduler configuration
   */
  public updateConfig(newConfig: Partial<IndexSchedulerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.log('Configuration updated', this.config);
  }

  /**
   * Check if a URL should be indexed
   */
  public shouldIndexUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      
      // Skip non-http protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }

      // Skip Chrome extension pages
      if (urlObj.protocol === 'chrome-extension:') {
        return false;
      }

      // Skip data URLs
      if (urlObj.protocol === 'data:') {
        return false;
      }

      // Skip if already processing
      if (this.processingPages.has(url)) {
        return false;
      }

      // Check rate limiting
      const now = Date.now();
      if (now - this.lastIndexingTime < this.config.minIndexingInterval) {
        return false;
      }

      // Check session limits
      if (this.stats.pagesIndexed >= this.config.maxPagesPerSession) {
        this.log(`Session limit reached (${this.config.maxPagesPerSession} pages)`);
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  // Private methods

  /**
   * Initialize statistics
   */
  private initializeStats(): IndexingStats {
    return {
      pagesIndexed: 0,
      totalIndexingTime: 0,
      averageIndexingTime: 0,
      failedAttempts: 0,
      successRate: 0,
      resourceUsage: {
        averageCpu: 0,
        peakMemory: 0,
      },
    };
  }

  /**
   * Register task executors with the task queue
   */
  private registerTaskExecutors(): void {
    // Register page indexing executor
    this.taskQueue.registerExecutor(TaskType.INDEX_PAGE, async (task) => {
      return await this.executeIndexPageTask(task);
    });

    // Register batch indexing executor
    this.taskQueue.registerExecutor(TaskType.BATCH_INDEX, async (task) => {
      return await this.executeBatchIndexTask(task);
    });

    // Register index rebuild executor
    this.taskQueue.registerExecutor(TaskType.REBUILD_INDEX, async (task) => {
      return await this.executeRebuildIndexTask(task);
    });

    // Register page deletion executor
    this.taskQueue.registerExecutor(TaskType.DELETE_PAGE, async (task) => {
      return await this.executeDeletePageTask(task);
    });

    this.log('Task executors registered');
  }

  /**
   * Setup Chrome extension event listeners
   */
  private setupChromeEventListeners(): void {
    // Check if chrome API is available (not in test environment)
    const globalScope = (globalThis as any);
    if (typeof globalScope.chrome !== 'undefined' && globalScope.chrome?.webNavigation) {
      // Listen for navigation events
      if (this.boundHandleNavigationCompleted) {
        globalScope.chrome.webNavigation.onCompleted.addListener(this.boundHandleNavigationCompleted);
      }

      if (this.boundHandleHistoryStateUpdated) {
        globalScope.chrome.webNavigation.onHistoryStateUpdated.addListener(this.boundHandleHistoryStateUpdated);
      }

      // Listen for tab updates
      if (this.boundHandleTabUpdated) {
        globalScope.chrome.tabs.onUpdated.addListener(this.boundHandleTabUpdated);
      }

      this.log('Chrome event listeners setup complete');
    } else {
      this.log('Chrome API not available - skipping event listener setup');
    }
  }

  /**
   * Remove Chrome extension event listeners
   */
  private removeChromeEventListeners(): void {
    const globalScope = (globalThis as any);
    if (typeof globalScope.chrome !== 'undefined' && globalScope.chrome?.webNavigation) {
      // Remove navigation event listeners
      if (this.boundHandleNavigationCompleted) {
        globalScope.chrome.webNavigation.onCompleted.removeListener(this.boundHandleNavigationCompleted);
      }

      if (this.boundHandleHistoryStateUpdated) {
        globalScope.chrome.webNavigation.onHistoryStateUpdated.removeListener(this.boundHandleHistoryStateUpdated);
      }

      // Remove tab update listener
      if (this.boundHandleTabUpdated && globalScope.chrome.tabs) {
        globalScope.chrome.tabs.onUpdated.removeListener(this.boundHandleTabUpdated);
      }

      this.log('Chrome event listeners removed');
    }

    // Clear bound handler references
    this.boundHandleNavigationCompleted = null;
    this.boundHandleHistoryStateUpdated = null;
    this.boundHandleTabUpdated = null;
  }

  /**
   * Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    if (!this.config.enablePerformanceMonitoring) {
      return;
    }

    this.resourceMonitor.addListener((metrics) => {
      // Update resource usage statistics
      this.stats.resourceUsage.averageCpu = 
        (this.stats.resourceUsage.averageCpu + metrics.cpuUsage) / 2;
      
      if (metrics.memoryUsage > this.stats.resourceUsage.peakMemory) {
        this.stats.resourceUsage.peakMemory = metrics.memoryUsage;
      }
    });

    this.log('Performance monitoring setup complete');
  }

  /**
   * Handle navigation completed event
   */
  private async handleNavigationCompleted(details: any): Promise<void> {
    if (details.frameId !== 0) {
      return; // Only handle main frame navigation
    }

    if (!this.config.autoIndexing || !this.shouldIndexUrl(details.url)) {
      return;
    }

    this.log(`Navigation completed: ${details.url}`);

    // Add delay to allow page to load completely
    setTimeout(async () => {
      await this.schedulePageIndexing({
        tabId: details.tabId,
        url: details.url,
        title: '', // Will be extracted
        navigationType: details.transitionType as any,
        timestamp: Date.now(),
      });
    }, 2000); // 2 second delay
  }

  /**
   * Handle history state updated event (for SPAs)
   */
  private async handleHistoryStateUpdated(details: any): Promise<void> {
    if (details.frameId !== 0) {
      return; // Only handle main frame
    }

    if (!this.config.autoIndexing || !this.shouldIndexUrl(details.url)) {
      return;
    }

    this.log(`History state updated: ${details.url}`);

    // Add delay for SPA content to load
    setTimeout(async () => {
      await this.schedulePageIndexing({
        tabId: details.tabId,
        url: details.url,
        title: '', // Will be extracted
        navigationType: details.transitionType as any,
        timestamp: Date.now(),
      });
    }, 3000); // 3 second delay for SPAs
  }

  /**
   * Handle tab updated event
   */
  private async handleTabUpdated(tabId: number, tab: chrome.tabs.Tab): Promise<void> {
    if (!tab.url || !this.shouldIndexUrl(tab.url)) {
      return;
    }

    // Additional check: make sure the tab is not already being processed
    if (this.navigationListeners.has(tabId)) {
      return;
    }

    this.log(`Tab updated: ${tab.url}`);
    
    await this.schedulePageIndexing({
      tabId,
      url: tab.url,
      title: tab.title || '',
      navigationType: 'typed', // Default for tab updates
      timestamp: Date.now(),
    });
  }

  /**
   * Schedule page indexing
   */
  private async schedulePageIndexing(event: PageIndexEvent): Promise<void> {
    const { url, title, tabId } = event;
    
    if (this.processingPages.has(url)) {
      this.log(`Already processing page: ${url}`);
      return;
    }

    this.processingPages.add(url);
    this.navigationListeners.add(tabId);

    try {
      // Get page content via content script
      const extractedContent = await this.extractPageContent(tabId, url);
      
      if (!extractedContent || !extractedContent.success) {
        this.log(`Failed to extract content from ${url}: ${extractedContent?.error || 'Unknown error'}`);
        return;
      }

      // Create page object
      const page: Page = {
        id: this.generatePageId(url),
        url,
        title: extractedContent.title || title || 'Untitled',
        content: extractedContent.content,
        domain: new URL(url).hostname,
        visitTime: Date.now(),
        lastUpdated: Date.now(),
        accessCount: 1,
      };

      // Determine priority based on navigation type
      const priority = this.getIndexingPriority(event.navigationType);

      // Create and queue indexing task
      const task = TaskQueueUtils.createIndexPageTask(page, priority);
      task.metadata.source = 'navigation';
      
      const taskId = this.taskQueue.addTask(task);
      this.log(`Scheduled indexing task ${taskId} for ${url}`);

    } catch (error) {
      this.log(`Error scheduling indexing for ${url}: ${error}`);
    } finally {
      this.processingPages.delete(url);
      this.navigationListeners.delete(tabId);
    }
  }

  /**
   * Extract page content via content script
   */
  private async extractPageContent(tabId: number, url: string): Promise<any> {
    try {
      const globalScope = (globalThis as any);
      if (typeof globalScope.chrome !== 'undefined' && globalScope.chrome?.tabs) {
        return await globalScope.chrome.tabs.sendMessage(tabId, {
          type: 'EXTRACT_CONTENT',
          url,
        });
      }
      return null;
    } catch (error) {
      this.log(`Failed to send message to tab ${tabId}: ${error}`);
      return null;
    }
  }

  /**
   * Get indexing priority based on navigation type
   */
  private getIndexingPriority(navigationType: string): TaskPriority {
    switch (navigationType) {
      case 'typed':
        return TaskPriority.HIGH;
      case 'link':
        return TaskPriority.NORMAL;
      case 'auto_bookmark':
        return TaskPriority.HIGH;
      case 'form_submit':
        return TaskPriority.NORMAL;
      default:
        return TaskPriority.LOW;
    }
  }

  /**
   * Execute page indexing task
   */
  private async executeIndexPageTask(task: BaseTask): Promise<TaskResult> {
    const startTime = performance.now();
    
    try {
      const { page } = task.data;
      this.log(`Executing index page task for: ${page.url}`);

      // Store page in database
      const storedPage = await dbService.addPage(page);
      
      // Note: Search indexing handled by search services

      const executionTime = performance.now() - startTime;
      
      // Update statistics
      this.stats.pagesIndexed++;
      this.stats.totalIndexingTime += executionTime;
      this.stats.averageIndexingTime = this.stats.totalIndexingTime / this.stats.pagesIndexed;
      this.lastIndexingTime = Date.now();

      this.log(`Successfully indexed page ${page.url} in ${executionTime.toFixed(2)}ms`);

      return {
        success: true,
        data: { pageId: storedPage.id },
        executionTime,
      };

    } catch (error) {
      const executionTime = performance.now() - startTime;
      this.stats.failedAttempts++;
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      this.log(`Failed to index page: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
        executionTime,
      };
    }
  }

  /**
   * Execute batch indexing task
   */
  private async executeBatchIndexTask(task: BaseTask): Promise<TaskResult> {
    const startTime = performance.now();
    
    try {
      const { pages, batchSize } = task.data;
      this.log(`Executing batch index task for ${pages.length} pages`);

      let indexed = 0;
      let failed = 0;

      // Process in batches
      for (let i = 0; i < pages.length; i += batchSize) {
        const batch = pages.slice(i, i + batchSize);
        
        // Check if we should continue (task cancellation)
        if (task.status === 'cancelled') {
          break;
        }

        // Process batch
        for (const page of batch) {
          try {
            await dbService.addPage(page);
            
            // Note: Search indexing handled by search services
            
            indexed++;
          } catch (error) {
            failed++;
            this.log(`Failed to index page in batch: ${page.url}`);
          }
        }

        // Add delay between batches to prevent blocking
        if (i + batchSize < pages.length) {
          await this.sleep(100);
        }
      }

      const executionTime = performance.now() - startTime;
      
      // Update statistics
      this.stats.pagesIndexed += indexed;
      this.stats.failedAttempts += failed;
      this.stats.totalIndexingTime += executionTime;
      this.stats.averageIndexingTime = this.stats.totalIndexingTime / this.stats.pagesIndexed;

      this.log(`Batch indexing completed: ${indexed} success, ${failed} failed in ${executionTime.toFixed(2)}ms`);

      return {
        success: indexed > 0,
        data: { indexed, failed },
        executionTime,
      };

    } catch (error) {
      const executionTime = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      return {
        success: false,
        error: errorMessage,
        executionTime,
      };
    }
  }

  /**
   * Execute index rebuild task
   */
  private async executeRebuildIndexTask(task: BaseTask): Promise<TaskResult> {
    const startTime = performance.now();
    
    try {
      const { force } = task.data;
      this.log(`Executing index rebuild task (force: ${force})`);

      // Note: Index rebuild handled by search services
      this.log('Index rebuild task - handled by search services');

      // Get all pages from database for informational purposes
      let allPages: Page[] = [];
      try {
        allPages = await (dbService as any).getAllPages() || [];
      } catch (error) {
        this.log('Failed to get all pages for index rebuild:', error);
        allPages = [];
      }
      this.log(`Index rebuild requested for ${allPages.length} pages - will be handled by search services`);

      const executionTime = performance.now() - startTime;
      this.log(`Index rebuild completed in ${executionTime.toFixed(2)}ms`);

      return {
        success: true,
        data: { rebuiltPages: allPages.length },
        executionTime,
      };

    } catch (error) {
      const executionTime = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      return {
        success: false,
        error: errorMessage,
        executionTime,
      };
    }
  }

  /**
   * Execute page deletion task
   */
  private async executeDeletePageTask(task: BaseTask): Promise<TaskResult> {
    const startTime = performance.now();
    
    try {
      const { pageId } = task.data;
      this.log(`Executing delete page task for: ${pageId}`);

      // Remove from database
      const deleted = await dbService.deletePage(pageId);
      
      // Note: Search index removal handled by search services

      const executionTime = performance.now() - startTime;
      this.log(`Successfully deleted page ${pageId} in ${executionTime.toFixed(2)}ms`);

      return {
        success: deleted,
        data: { pageId },
        executionTime,
      };

    } catch (error) {
      const executionTime = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      return {
        success: false,
        error: errorMessage,
        executionTime,
      };
    }
  }

  /**
   * Generate page ID from URL
   */
  private generatePageId(url: string): string {
    // Create a consistent ID based on URL
    return btoa(url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Debug logging
   */
  private log(message: string, data?: any): void {
    if (this.config.debugMode) {
      console.log(`[IndexScheduler] ${message}`, data || '');
    }
  }
}

/**
 * Index scheduler utilities
 */
export class IndexSchedulerUtils {
  /**
   * Create optimized scheduler configuration for different environments
   */
  static createOptimizedConfig(environment: 'development' | 'production' | 'testing'): Partial<IndexSchedulerConfig> {
    switch (environment) {
      case 'development':
        return {
          debugMode: true,
          maxPagesPerSession: 100,
          batchSize: 5,
          enablePerformanceMonitoring: true,
        };
      
      case 'production':
        return {
          debugMode: false,
          maxPagesPerSession: 500,
          batchSize: 10,
          enablePerformanceMonitoring: true,
        };
      
      case 'testing':
        return {
          debugMode: true,
          maxPagesPerSession: 50,
          batchSize: 3,
          enablePerformanceMonitoring: false,
          autoIndexing: false,
        };
      
      default:
        return {};
    }
  }

  /**
   * Estimate indexing time for a batch of pages
   */
  static estimateBatchIndexingTime(pageCount: number, averagePageTime: number = 1000): number {
    return pageCount * averagePageTime;
  }

  /**
   * Check if indexing should be throttled based on system state
   */
  static shouldThrottleIndexing(resourceMonitor: ResourceMonitor): boolean {
    const metrics = resourceMonitor.getCurrentMetrics();
    if (!metrics) {
      return true; // Throttle if no metrics available
    }
    
    // Throttle if system is under load (high CPU or memory usage)
    const isUnderLoad = metrics.cpuUsage > 0.8 || (metrics.memoryUsage / metrics.memoryLimit) > 0.9;
    
    // Check if system is ready for operations
    const isSystemReady = resourceMonitor.isSystemReady();
    
    return isUnderLoad || !isSystemReady;
  }
}

// Export singleton instance
export const indexScheduler = IndexScheduler.getInstance();