/**
 * Intelligent Task Batching System for Recall V3.0
 * 
 * Automatically groups similar tasks together to reduce overhead and improve
 * performance by minimizing context switching and resource contention.
 */

import type { BaseTask, TaskType, TaskPriority } from './TaskQueue';

/**
 * Batch configuration for different task types
 */
export interface BatchConfig {
  /** Maximum number of tasks in a batch */
  maxBatchSize: number;
  /** Maximum time to wait for more tasks before executing batch (ms) */
  maxWaitTime: number;
  /** Whether this task type supports batching */
  batchable: boolean;
  /** Custom batch merger function */
  merger?: (tasks: BaseTask[]) => BaseTask;
}

/**
 * Task batch information
 */
export interface TaskBatch {
  /** Unique batch identifier */
  id: string;
  /** Task type for this batch */
  type: TaskType;
  /** Tasks in this batch */
  tasks: BaseTask[];
  /** Batch creation timestamp */
  createdAt: number;
  /** When batch should be executed (createdAt + maxWaitTime) */
  executeAt: number;
  /** Estimated batch execution time */
  estimatedDuration: number;
  /** Batch priority (highest priority of contained tasks) */
  priority: TaskPriority;
}

/**
 * Default batch configurations for different task types
 */
const DEFAULT_BATCH_CONFIGS: Record<TaskType, BatchConfig> = {
  'index_page': {
    maxBatchSize: 10,
    maxWaitTime: 2000, // 2 seconds
    batchable: true,
  },
  'update_page': {
    maxBatchSize: 15,
    maxWaitTime: 1500,
    batchable: true,
  },
  'delete_page': {
    maxBatchSize: 20,
    maxWaitTime: 1000,
    batchable: true,
  },
  'batch_index': {
    maxBatchSize: 1, // Already a batch
    maxWaitTime: 0,
    batchable: false,
  },
  'rebuild_index': {
    maxBatchSize: 1, // Single large operation
    maxWaitTime: 0,
    batchable: false,
  },
  'cleanup_index': {
    maxBatchSize: 1,
    maxWaitTime: 0,
    batchable: false,
  },
  'optimize_index': {
    maxBatchSize: 1,
    maxWaitTime: 0,
    batchable: false,
  },
  'health_check': {
    maxBatchSize: 1,
    maxWaitTime: 0,
    batchable: false,
  },
};

/**
 * Task similarity metrics
 */
interface TaskSimilarity {
  /** How similar tasks are (0-1, higher is more similar) */
  score: number;
  /** Factors contributing to similarity */
  factors: {
    type: boolean;
    priority: boolean;
    resourceRequirements: boolean;
    metadata: boolean;
  };
}

/**
 * Intelligent Task Batcher
 */
export class TaskBatcher {
  private static instance: TaskBatcher;
  private configs: Map<TaskType, BatchConfig> = new Map();
  private pendingBatches: Map<string, TaskBatch> = new Map();
  private batchTimers: Map<string, ReturnType<typeof setTimeout>> = new Map();
  private batchExecutionCallback?: (batch: TaskBatch) => Promise<void>;

  private constructor() {
    this.initializeDefaultConfigs();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): TaskBatcher {
    if (!TaskBatcher.instance) {
      TaskBatcher.instance = new TaskBatcher();
    }
    return TaskBatcher.instance;
  }

  /**
   * Initialize with batch execution callback
   */
  public initialize(batchExecutionCallback: (batch: TaskBatch) => Promise<void>): void {
    this.batchExecutionCallback = batchExecutionCallback;
    console.log('[TaskBatcher] Initialized with execution callback');
  }

  /**
   * Add a task for potential batching
   * Returns the batch ID if task was batched, null if task should be executed immediately
   */
  public addTask(task: BaseTask): string | null {
    const config = this.configs.get(task.type);
    
    if (!config || !config.batchable) {
      return null; // Task should be executed immediately
    }

    // Find or create appropriate batch
    const batch = this.findOrCreateBatch(task);
    
    // Add task to batch
    batch.tasks.push(task);
    batch.priority = Math.min(batch.priority, task.priority) as TaskPriority; // Higher priority (lower number)
    batch.estimatedDuration += task.metadata.estimatedDuration || 1000;

    console.log(`[TaskBatcher] Added task ${task.id} to batch ${batch.id} (${batch.tasks.length}/${config.maxBatchSize} tasks)`);

    // Check if batch is ready for execution
    if (this.shouldExecuteBatch(batch, config)) {
      this.executeBatch(batch.id);
    }

    return batch.id;
  }

  /**
   * Force execution of a specific batch
   */
  public executeBatch(batchId: string): void {
    const batch = this.pendingBatches.get(batchId);
    if (!batch) {
      console.warn(`[TaskBatcher] Batch ${batchId} not found`);
      return;
    }

    // Clear timeout if exists
    const timer = this.batchTimers.get(batchId);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(batchId);
    }

    // Remove from pending
    this.pendingBatches.delete(batchId);

    console.log(`[TaskBatcher] Executing batch ${batchId} with ${batch.tasks.length} tasks`);

    // Execute batch
    if (this.batchExecutionCallback) {
      this.batchExecutionCallback(batch).catch(error => {
        console.error(`[TaskBatcher] Batch execution failed for ${batchId}:`, error);
      });
    }
  }

  /**
   * Force execution of all pending batches
   */
  public flushAllBatches(): void {
    const batchIds = Array.from(this.pendingBatches.keys());
    console.log(`[TaskBatcher] Flushing ${batchIds.length} pending batches`);
    
    batchIds.forEach(batchId => {
      this.executeBatch(batchId);
    });
  }

  /**
   * Get statistics about current batching
   */
  public getStats(): {
    pendingBatches: number;
    totalPendingTasks: number;
    averageBatchSize: number;
    batchesByType: Record<TaskType, number>;
  } {
    const batches = Array.from(this.pendingBatches.values());
    const totalTasks = batches.reduce((sum, batch) => sum + batch.tasks.length, 0);
    const averageBatchSize = batches.length > 0 ? totalTasks / batches.length : 0;

    const batchesByType: Record<TaskType, number> = {} as Record<TaskType, number>;
    batches.forEach(batch => {
      batchesByType[batch.type] = (batchesByType[batch.type] || 0) + 1;
    });

    return {
      pendingBatches: batches.length,
      totalPendingTasks: totalTasks,
      averageBatchSize,
      batchesByType,
    };
  }

  /**
   * Update batch configuration for a task type
   */
  public updateBatchConfig(taskType: TaskType, config: Partial<BatchConfig>): void {
    const currentConfig = this.configs.get(taskType) || DEFAULT_BATCH_CONFIGS[taskType];
    this.configs.set(taskType, { ...currentConfig, ...config });
    console.log(`[TaskBatcher] Updated config for ${taskType}:`, config);
  }

  /**
   * Clean up resources
   */
  public shutdown(): void {
    // Clear all timers
    this.batchTimers.forEach(timer => clearTimeout(timer));
    this.batchTimers.clear();

    // Flush remaining batches
    this.flushAllBatches();

    console.log('[TaskBatcher] Shutdown complete');
  }

  // Private methods

  /**
   * Initialize default configurations
   */
  private initializeDefaultConfigs(): void {
    Object.entries(DEFAULT_BATCH_CONFIGS).forEach(([type, config]) => {
      this.configs.set(type as TaskType, config);
    });
  }

  /**
   * Find existing batch or create new one for the task
   */
  private findOrCreateBatch(task: BaseTask): TaskBatch {
    // Look for existing compatible batch
    for (const batch of this.pendingBatches.values()) {
      if (this.canAddTaskToBatch(task, batch)) {
        return batch;
      }
    }

    // Create new batch
    const batchId = this.generateBatchId();
    const config = this.configs.get(task.type)!;
    
    const batch: TaskBatch = {
      id: batchId,
      type: task.type,
      tasks: [],
      createdAt: Date.now(),
      executeAt: Date.now() + config.maxWaitTime,
      estimatedDuration: 0,
      priority: task.priority,
    };

    this.pendingBatches.set(batchId, batch);

    // Set up timer for batch execution
    if (config.maxWaitTime > 0) {
      const timer = setTimeout(() => {
        this.executeBatch(batchId);
      }, config.maxWaitTime);
      
      this.batchTimers.set(batchId, timer);
    }

    console.log(`[TaskBatcher] Created new batch ${batchId} for task type ${task.type}`);
    return batch;
  }

  /**
   * Check if a task can be added to an existing batch
   */
  private canAddTaskToBatch(task: BaseTask, batch: TaskBatch): boolean {
    const config = this.configs.get(task.type);
    if (!config) {
      return false;
    }

    // Check basic compatibility
    if (batch.type !== task.type) {
      return false;
    }

    // Check if batch is full
    if (batch.tasks.length >= config.maxBatchSize) {
      return false;
    }

    // Check if batch is too old (should have been executed already)
    if (Date.now() > batch.executeAt) {
      return false;
    }

    // Check task similarity for better batching
    const similarity = this.calculateTaskSimilarity(task, batch.tasks[0]);
    if (similarity.score < 0.8) { // 80% similarity threshold
      return false;
    }

    return true;
  }

  /**
   * Calculate similarity between two tasks
   */
  private calculateTaskSimilarity(task1: BaseTask, task2: BaseTask): TaskSimilarity {
    const factors = {
      type: task1.type === task2.type,
      priority: Math.abs(task1.priority - task2.priority) <= 1, // Adjacent priority levels
      resourceRequirements: this.compareResourceRequirements(task1, task2),
      metadata: this.compareMetadata(task1, task2),
    };

    // Calculate weighted score
    const weights = { type: 0.4, priority: 0.2, resourceRequirements: 0.2, metadata: 0.2 };
    const score = Object.entries(factors).reduce((sum, [key, value]) => {
      return sum + (value ? weights[key as keyof typeof weights] : 0);
    }, 0);

    return { score, factors };
  }

  /**
   * Compare resource requirements between tasks
   */
  private compareResourceRequirements(task1: BaseTask, task2: BaseTask): boolean {
    const req1 = task1.metadata.resourceRequirements;
    const req2 = task2.metadata.resourceRequirements;

    if (!req1 || !req2) {
      return !req1 && !req2; // Both undefined
    }

    // Similar CPU and memory requirements (within 50% difference)
    const cpuSimilar = Math.abs(req1.cpu - req2.cpu) / Math.max(req1.cpu, req2.cpu) < 0.5;
    const memorySimilar = Math.abs(req1.memory - req2.memory) / Math.max(req1.memory, req2.memory) < 0.5;

    return cpuSimilar && memorySimilar;
  }

  /**
   * Compare metadata between tasks
   */
  private compareMetadata(task1: BaseTask, task2: BaseTask): boolean {
    // Check if tasks have same source
    if (task1.metadata.source !== task2.metadata.source) {
      return false;
    }

    // Check if they're part of the same batch already
    if (task1.metadata.batchId && task2.metadata.batchId) {
      return task1.metadata.batchId === task2.metadata.batchId;
    }

    return true;
  }

  /**
   * Check if batch should be executed now
   */
  private shouldExecuteBatch(batch: TaskBatch, config: BatchConfig): boolean {
    // Execute if batch is full
    if (batch.tasks.length >= config.maxBatchSize) {
      return true;
    }

    // Execute if all tasks are critical priority
    if (batch.tasks.every(task => task.priority === 0)) { // TaskPriority.CRITICAL = 0
      return true;
    }

    // Execute if wait time has elapsed
    if (Date.now() >= batch.executeAt) {
      return true;
    }

    return false;
  }

  /**
   * Generate unique batch ID
   */
  private generateBatchId(): string {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Export singleton instance
 */
export const taskBatcher = TaskBatcher.getInstance();