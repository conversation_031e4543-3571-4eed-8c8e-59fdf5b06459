/**
 * Task Queue Management System for Recall Background Indexing
 * 
 * Provides intelligent task scheduling with:
 * - Priority-based queue management
 * - Persistent task storage
 * - Retry logic with exponential backoff
 * - Batch processing optimization
 * - Resource-aware execution
 * - Dead letter queue for failed tasks
 */

import type { ResourceMonitor } from './ResourceMonitor';
import type { Page } from '../models';
import { TaskBatcher, type TaskBatch } from './TaskBatcher';
import { AdaptiveThrottler } from './AdaptiveThrottler';
import { TaskPriorityManager } from './TaskPriorityManager';

/**
 * Task types supported by the queue system
 */
export const TaskType = {
  INDEX_PAGE: 'index_page',
  UPDATE_PAGE: 'update_page',
  DELETE_PAGE: 'delete_page',
  BATCH_INDEX: 'batch_index',
  REBUILD_INDEX: 'rebuild_index',
  CLEANUP_INDEX: 'cleanup_index',
  OPTIMIZE_INDEX: 'optimize_index',
  HEALTH_CHECK: 'health_check',
} as const;

export type TaskType = typeof TaskType[keyof typeof TaskType];

/**
 * Task priority levels
 */
export const TaskPriority = {
  CRITICAL: 0,   // Immediate execution required
  HIGH: 1,       // User-triggered actions
  NORMAL: 2,     // Regular background indexing  
  LOW: 3,        // Optimization and cleanup
  IDLE: 4,       // Only when system is completely idle
} as const;

export type TaskPriority = typeof TaskPriority[keyof typeof TaskPriority];

/**
 * Task status
 */
export const TaskStatus = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  RETRYING: 'retrying',
} as const;

export type TaskStatus = typeof TaskStatus[keyof typeof TaskStatus];

/**
 * Base task interface
 */
export interface BaseTask {
  /** Unique task identifier */
  id: string;
  /** Task type */
  type: TaskType;
  /** Task priority */
  priority: TaskPriority;
  /** Current status */
  status: TaskStatus;
  /** Task payload data */
  data: any;
  /** Creation timestamp */
  createdAt: number;
  /** Last execution attempt timestamp */
  lastAttempt?: number;
  /** Number of retry attempts */
  retryCount: number;
  /** Maximum retry attempts allowed */
  maxRetries: number;
  /** Task timeout in milliseconds */
  timeout: number;
  /** Task metadata */
  metadata: TaskMetadata;
}

/**
 * Task metadata
 */
export interface TaskMetadata {
  /** Source that created the task */
  source: string;
  /** Estimated execution time in ms */
  estimatedDuration?: number;
  /** Resource requirements */
  resourceRequirements?: {
    cpu: number;
    memory: number;
  };
  /** Dependencies on other tasks */
  dependencies?: string[];
  /** Batch group identifier */
  batchId?: string;
  /** Error information for failed tasks */
  error?: {
    message: string;
    stack?: string;
    code?: string;
  };
}

/**
 * Specific task types
 */
export interface IndexPageTask extends BaseTask {
  type: typeof TaskType.INDEX_PAGE;
  data: {
    page: Page;
    extractedContent?: {
      title: string;
      content: string;
    };
  };
}

export interface BatchIndexTask extends BaseTask {
  type: typeof TaskType.BATCH_INDEX;
  data: {
    pages: Page[];
    batchSize: number;
  };
}

export interface DeletePageTask extends BaseTask {
  type: typeof TaskType.DELETE_PAGE;
  data: {
    pageId: string;
  };
}

export interface RebuildIndexTask extends BaseTask {
  type: typeof TaskType.REBUILD_INDEX;
  data: {
    force?: boolean;
    onProgress?: (progress: number) => void;
  };
}

/**
 * Task execution result
 */
export interface TaskResult {
  success: boolean;
  data?: any;
  error?: string;
  executionTime: number;
  resourceUsage?: {
    cpu: number;
    memory: number;
  };
}

/**
 * Queue statistics
 */
export interface QueueStats {
  totalTasks: number;
  pendingTasks: number;
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageExecutionTime: number;
  tasksByType: Record<TaskType, number>;
  tasksByPriority: Record<TaskPriority, number>;
}

/**
 * Task queue configuration
 */
export interface TaskQueueConfig {
  /** Maximum number of concurrent tasks */
  maxConcurrentTasks: number;
  /** Default task timeout in milliseconds */
  defaultTimeout: number;
  /** Default maximum retries */
  defaultMaxRetries: number;
  /** Batch processing size */
  batchSize: number;
  /** Queue persistence enabled */
  persistQueue: boolean;
  /** Dead letter queue enabled */
  enableDeadLetterQueue: boolean;
  /** Automatic cleanup of completed tasks */
  autoCleanup: boolean;
  /** Cleanup interval in milliseconds */
  cleanupInterval: number;
}

/**
 * Task executor function type
 */
export type TaskExecutor<T extends BaseTask = BaseTask> = (task: T) => Promise<TaskResult>;

/**
 * Task queue event types
 */
export interface TaskQueueEvents {
  taskAdded: (task: BaseTask) => void;
  taskStarted: (task: BaseTask) => void;
  taskCompleted: (task: BaseTask, result: TaskResult) => void;
  taskFailed: (task: BaseTask, error: string) => void;
  taskRetrying: (task: BaseTask, attempt: number) => void;
  queueEmpty: () => void;
  queueFull: () => void;
}

/**
 * Intelligent Task Queue Manager
 */
export class TaskQueue {
  private static instance: TaskQueue;
  private config: TaskQueueConfig;
  private tasks = new Map<string, BaseTask>();
  private runningTasks = new Set<string>();
  private executors = new Map<TaskType, TaskExecutor>();
  private eventListeners = new Map<keyof TaskQueueEvents, Array<Function>>();
  private processingInterval?: ReturnType<typeof setInterval>;
  private cleanupInterval?: ReturnType<typeof setInterval>;
  private resourceMonitor?: ResourceMonitor;
  private isProcessing = false;
  private taskBatcher: TaskBatcher;
  private adaptiveThrottler: AdaptiveThrottler;
  private priorityManager: TaskPriorityManager;

  // Default configuration
  private static readonly DEFAULT_CONFIG: TaskQueueConfig = {
    maxConcurrentTasks: 3,
    defaultTimeout: 30000, // 30 seconds
    defaultMaxRetries: 3,
    batchSize: 10,
    persistQueue: true,
    enableDeadLetterQueue: true,
    autoCleanup: true,
    cleanupInterval: 300000, // 5 minutes
  };

  private constructor(config?: Partial<TaskQueueConfig>) {
    this.config = { ...TaskQueue.DEFAULT_CONFIG, ...config };
    this.taskBatcher = TaskBatcher.getInstance();
    this.adaptiveThrottler = AdaptiveThrottler.getInstance();
    this.priorityManager = TaskPriorityManager.getInstance();
    this.setupProcessing();
    this.setupCleanup();
  }

  public static getInstance(config?: Partial<TaskQueueConfig>): TaskQueue {
    if (!TaskQueue.instance) {
      TaskQueue.instance = new TaskQueue(config);
    }
    return TaskQueue.instance;
  }

  /**
   * Initialize the task queue
   */
  public async initialize(resourceMonitor?: ResourceMonitor): Promise<void> {
    console.log('[TaskQueue] Initializing task queue...');
    
    this.resourceMonitor = resourceMonitor;
    
    // Initialize task batcher with batch execution callback
    this.taskBatcher.initialize(this.executeBatchTasks.bind(this));
    
    // Initialize adaptive throttler with resource monitor
    if (resourceMonitor) {
      this.adaptiveThrottler.initialize(resourceMonitor);
    }
    
    // Load persisted tasks if enabled
    if (this.config.persistQueue) {
      await this.loadPersistedTasks();
    }

    // Start processing
    this.startProcessing();

    console.log('[TaskQueue] Task queue initialized successfully');
  }

  /**
   * Shutdown the task queue gracefully
   */
  public async shutdown(): Promise<void> {
    console.log('[TaskQueue] Shutting down task queue...');
    
    this.stopProcessing();
    
    // Wait for running tasks to complete (with timeout)
    const timeout = 10000; // 10 seconds
    const startTime = Date.now();
    
    while (this.runningTasks.size > 0 && (Date.now() - startTime) < timeout) {
      await this.sleep(100);
    }

    // Cancel remaining running tasks
    if (this.runningTasks.size > 0) {
      console.warn(`[TaskQueue] Force cancelling ${this.runningTasks.size} running tasks`);
      this.runningTasks.forEach(taskId => {
        const task = this.tasks.get(taskId);
        if (task) {
          task.status = TaskStatus.CANCELLED;
        }
      });
      this.runningTasks.clear();
    }

    // Persist remaining tasks
    if (this.config.persistQueue) {
      await this.persistTasks();
    }

    // Shutdown task batcher
    this.taskBatcher.shutdown();
    
    // Clear priority manager
    this.priorityManager.clear();

    // Clear all references to prevent memory leaks
    this.tasks.clear();
    this.executors.clear();
    this.eventListeners.clear();
    this.resourceMonitor = undefined;

    console.log('[TaskQueue] Task queue shutdown complete');
  }

  /**
   * Add a task to the queue
   */
  public addTask<T extends BaseTask>(taskData: Omit<T, 'id' | 'createdAt' | 'status' | 'retryCount'>): string {
    const task: BaseTask = {
      ...taskData,
      id: this.generateTaskId(),
      createdAt: Date.now(),
      status: TaskStatus.PENDING,
      retryCount: 0,
      timeout: taskData.timeout || this.config.defaultTimeout,
      maxRetries: taskData.maxRetries || this.config.defaultMaxRetries,
    };

    // Try to batch the task
    const batchId = this.taskBatcher.addTask(task);
    
    if (batchId) {
      // Task was successfully batched
      console.log(`[TaskQueue] Task ${task.id} (${task.type}) added to batch ${batchId}`);
      
      // Store task for tracking but mark as batched
      task.metadata.batchId = batchId;
      this.tasks.set(task.id, task);
      this.emit('taskAdded', task);
    } else {
      // Task should be executed immediately (not batchable or critical)
      this.tasks.set(task.id, task);
      
      // Add to priority manager for intelligent scheduling
      this.priorityManager.enqueue(task);
      
      this.emit('taskAdded', task);
      
      console.log(`[TaskQueue] Added task ${task.id} (${task.type}) with priority ${task.priority} (immediate execution)`);
    }
    
    // Persist immediately for critical tasks
    if (task.priority === TaskPriority.CRITICAL && this.config.persistQueue) {
      this.persistTasks().catch(console.error);
    }

    return task.id;
  }

  /**
   * Add multiple tasks as a batch
   */
  public addBatch(tasks: Array<Omit<BaseTask, 'id' | 'createdAt' | 'status' | 'retryCount'>>): string[] {
    const batchId = this.generateTaskId();
    const taskIds: string[] = [];

    tasks.forEach(taskData => {
      const taskId = this.addTask({
        ...taskData,
        metadata: {
          ...taskData.metadata,
          batchId,
        },
      });
      taskIds.push(taskId);
    });

    console.log(`[TaskQueue] Added batch ${batchId} with ${tasks.length} tasks`);
    return taskIds;
  }

  /**
   * Register a task executor
   */
  public registerExecutor<T extends BaseTask>(type: TaskType, executor: TaskExecutor<T>): void {
    this.executors.set(type, executor as TaskExecutor);
    console.log(`[TaskQueue] Registered executor for task type: ${type}`);
  }

  /**
   * Get task by ID
   */
  public getTask(taskId: string): BaseTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * Cancel a task
   */
  public cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task) {
      return false;
    }

    if (task.status === TaskStatus.RUNNING) {
      // Mark for cancellation - executor should check this
      task.status = TaskStatus.CANCELLED;
      return true;
    }

    if (task.status === TaskStatus.PENDING) {
      task.status = TaskStatus.CANCELLED;
      
      // Remove from priority manager if it's there
      this.priorityManager.removeTask(taskId);
      
      return true;
    }

    return false;
  }

  /**
   * Clear all tasks with optional status filter
   */
  public clearTasks(status?: TaskStatus): number {
    let cleared = 0;
    
    this.tasks.forEach((task, taskId) => {
      if (!status || task.status === status) {
        // Don't remove running tasks
        if (task.status !== TaskStatus.RUNNING) {
          this.tasks.delete(taskId);
          cleared++;
        }
      }
    });

    console.log(`[TaskQueue] Cleared ${cleared} tasks${status ? ` with status ${status}` : ''}`);
    return cleared;
  }

  /**
   * Get queue statistics
   */
  public getStats(): QueueStats & { batching: any } {
    const stats: QueueStats = {
      totalTasks: this.tasks.size,
      pendingTasks: 0,
      runningTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageExecutionTime: 0,
      tasksByType: {} as Record<TaskType, number>,
      tasksByPriority: {} as Record<TaskPriority, number>,
    };

    let totalExecutionTime = 0;
    let completedCount = 0;

    this.tasks.forEach(task => {
      // Count by status
      switch (task.status) {
        case TaskStatus.PENDING:
          stats.pendingTasks++;
          break;
        case TaskStatus.RUNNING:
          stats.runningTasks++;
          break;
        case TaskStatus.COMPLETED:
          stats.completedTasks++;
          if (task.lastAttempt) {
            totalExecutionTime += task.lastAttempt - task.createdAt;
            completedCount++;
          }
          break;
        case TaskStatus.FAILED:
          stats.failedTasks++;
          break;
      }

      // Count by type
      stats.tasksByType[task.type] = (stats.tasksByType[task.type] || 0) + 1;

      // Count by priority
      stats.tasksByPriority[task.priority] = (stats.tasksByPriority[task.priority] || 0) + 1;
    });

    stats.averageExecutionTime = completedCount > 0 ? totalExecutionTime / completedCount : 0;

    // Add batching, throttling, and priority management statistics
    const batchingStats = this.taskBatcher.getStats();
    // const throttlingStats = this.adaptiveThrottler.getStats();
    const priorityStats = this.priorityManager.getStats();

    return {
      ...stats,
      batching: batchingStats,
      priorityManagement: priorityStats,
    } as QueueStats & { 
      batching: any; 
      priorityManagement: any;
    };
  }

  /**
   * Add event listener
   */
  public on<K extends keyof TaskQueueEvents>(event: K, listener: TaskQueueEvents[K]): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  /**
   * Remove event listener
   */
  public off<K extends keyof TaskQueueEvents>(event: K, listener: TaskQueueEvents[K]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // Private methods

  /**
   * Setup task processing
   */
  private setupProcessing(): void {
    this.processingInterval = setInterval(() => {
      if (!this.isProcessing) {
        this.processPendingTasks().catch(console.error);
      }
    }, 100); // Check every 100ms
  }

  /**
   * Setup automatic cleanup
   */
  private setupCleanup(): void {
    if (this.config.autoCleanup) {
      this.cleanupInterval = setInterval(() => {
        this.performCleanup();
      }, this.config.cleanupInterval);
    }
  }

  /**
   * Start processing tasks
   */
  private startProcessing(): void {
    console.log('[TaskQueue] Started task processing');
  }

  /**
   * Stop processing tasks
   */
  private stopProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }

    console.log('[TaskQueue] Stopped task processing');
  }

  /**
   * Process pending tasks based on priority and resources
   */
  private async processPendingTasks(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      // Check adaptive throttling
      const throttlingDecision = this.adaptiveThrottler.getThrottlingDecision();
      
      if (throttlingDecision.shouldPause) {
        console.log(`[TaskQueue] Operations paused: ${throttlingDecision.reason}`);
        return;
      }

      // Apply throttling delay if needed
      if (throttlingDecision.delay > 0) {
        console.log(`[TaskQueue] Applying throttling delay: ${throttlingDecision.delay}ms (${throttlingDecision.reason})`);
        await this.adaptiveThrottler.applyThrottling(throttlingDecision);
      }

      // Check if we can run more tasks
      const canRunMore = this.runningTasks.size < this.config.maxConcurrentTasks;
      if (!canRunMore) {
        return;
      }

      // Get next eligible task
      const nextTask = this.getNextTask();
      if (!nextTask) {
        // No eligible tasks, emit queue empty if truly empty
        if (this.tasks.size === 0) {
          this.emit('queueEmpty');
        }
        return;
      }

      // Check resource availability
      if (this.resourceMonitor && !this.canExecuteTask(nextTask)) {
        return; // Wait for better resource conditions
      }

      // Execute the task
      await this.executeTask(nextTask);

    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get the next task to execute based on advanced priority management
   */
  private getNextTask(): BaseTask | null {
    // Update priority manager with current system context
    this.updatePriorityManagerContext();

    // Get next task from priority manager
    const nextTask = this.priorityManager.dequeue();
    
    if (!nextTask) {
      return null;
    }

    // Verify task is still eligible
    if (nextTask.status !== TaskStatus.PENDING || !this.areTaskDependenciesMet(nextTask)) {
      // Task is no longer eligible, try next one
      return this.getNextTask();
    }

    return nextTask;
  }

  /**
   * Check if task dependencies are met
   */
  private areTaskDependenciesMet(task: BaseTask): boolean {
    if (!task.metadata.dependencies || task.metadata.dependencies.length === 0) {
      return true;
    }

    return task.metadata.dependencies.every(depId => {
      const depTask = this.tasks.get(depId);
      return depTask && depTask.status === TaskStatus.COMPLETED;
    });
  }

  /**
   * Check if we can execute a task based on resource availability
   */
  private canExecuteTask(task: BaseTask): boolean {
    if (!this.resourceMonitor) {
      return true; // No resource monitoring, allow execution
    }

    const params = this.resourceMonitor.getAdaptiveParams();
    
    // For idle priority tasks, only execute when system allows background operations
    if (task.priority === TaskPriority.IDLE) {
      return params.enableBackgroundOps;
    }

    // For critical tasks, always allow
    if (task.priority === TaskPriority.CRITICAL) {
      return true;
    }

    // For other tasks, check if system allows background operations
    return params.enableBackgroundOps;
  }

  /**
   * Execute a task
   */
  private async executeTask(task: BaseTask): Promise<void> {
    const executor = this.executors.get(task.type);
    if (!executor) {
      const error = `No executor registered for task type: ${task.type}`;
      console.error(`[TaskQueue] ${error}`);
      this.markTaskFailed(task, error);
      return;
    }

    // Mark task as running
    task.status = TaskStatus.RUNNING;
    task.lastAttempt = Date.now();
    this.runningTasks.add(task.id);
    this.emit('taskStarted', task);

    console.log(`[TaskQueue] Executing task ${task.id} (${task.type})`);

    try {
      // Execute with timeout
      const result = await this.executeWithTimeout(executor, task);
      
      // Task completed successfully
      task.status = TaskStatus.COMPLETED;
      this.runningTasks.delete(task.id);
      this.emit('taskCompleted', task, result);

      console.log(`[TaskQueue] Completed task ${task.id} in ${result.executionTime}ms`);

    } catch (error) {
      console.error(`[TaskQueue] Task ${task.id} failed:`, error);
      
      this.runningTasks.delete(task.id);
      
      // Check if we should retry
      if (task.retryCount < task.maxRetries) {
        this.scheduleRetry(task, error as Error);
      } else {
        this.markTaskFailed(task, (error as Error).message);
      }
    }
  }

  /**
   * Execute task with timeout
   */
  private async executeWithTimeout(executor: TaskExecutor, task: BaseTask): Promise<TaskResult> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Task ${task.id} timed out after ${task.timeout}ms`));
      }, task.timeout);

      executor(task)
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
   * Schedule task retry with exponential backoff
   */
  private scheduleRetry(task: BaseTask, error: Error): void {
    task.retryCount++;
    task.status = TaskStatus.RETRYING;
    
    // Store error information
    task.metadata.error = {
      message: error.message,
      stack: error.stack,
    };

    // Calculate backoff delay (exponential backoff with jitter)
    const baseDelay = 1000; // 1 second
    const maxDelay = 30000; // 30 seconds
    const backoffDelay = Math.min(
      maxDelay,
      baseDelay * Math.pow(2, task.retryCount - 1)
    );
    const jitterDelay = backoffDelay + Math.random() * 1000; // Add jitter

    this.emit('taskRetrying', task, task.retryCount);

    console.log(`[TaskQueue] Scheduling retry ${task.retryCount}/${task.maxRetries} for task ${task.id} in ${jitterDelay}ms`);

    // Schedule retry
    setTimeout(() => {
      if (task.status === TaskStatus.RETRYING) {
        task.status = TaskStatus.PENDING;
      }
    }, jitterDelay);
  }

  /**
   * Mark task as failed
   */
  private markTaskFailed(task: BaseTask, error: string): void {
    task.status = TaskStatus.FAILED;
    task.metadata.error = { message: error };
    this.emit('taskFailed', task, error);

    // Move to dead letter queue if enabled
    if (this.config.enableDeadLetterQueue) {
      this.moveToDeadLetterQueue(task);
    }
  }

  /**
   * Move task to dead letter queue
   */
  private moveToDeadLetterQueue(task: BaseTask): void {
    // In a real implementation, this would move the task to a separate storage
    console.log(`[TaskQueue] Moving failed task ${task.id} to dead letter queue`);
  }

  /**
   * Perform periodic cleanup
   */
  private performCleanup(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago
    let cleaned = 0;

    this.tasks.forEach((task, taskId) => {
      // Remove old completed/failed tasks
      if ((task.status === TaskStatus.COMPLETED || task.status === TaskStatus.FAILED) &&
          task.createdAt < cutoffTime) {
        this.tasks.delete(taskId);
        cleaned++;
      }
    });

    if (cleaned > 0) {
      console.log(`[TaskQueue] Cleaned up ${cleaned} old tasks`);
    }
  }

  /**
   * Load persisted tasks
   */
  private async loadPersistedTasks(): Promise<void> {
    try {
      // Check if chrome storage is available (not in test environment)
      const globalScope = (globalThis as any);
      if (typeof globalScope.chrome !== 'undefined' && globalScope.chrome.storage) {
        const stored = await globalScope.chrome.storage.local.get('taskQueue');
        if (stored.taskQueue) {
          const tasks: BaseTask[] = JSON.parse(stored.taskQueue);
          tasks.forEach(task => {
            // Reset running tasks to pending
            if (task.status === TaskStatus.RUNNING) {
              task.status = TaskStatus.PENDING;
            }
            this.tasks.set(task.id, task);
          });
          console.log(`[TaskQueue] Loaded ${tasks.length} persisted tasks`);
        }
      }
    } catch (error) {
      console.error('[TaskQueue] Failed to load persisted tasks:', error);
    }
  }

  /**
   * Persist current tasks
   */
  private async persistTasks(): Promise<void> {
    try {
      // Check if chrome storage is available (not in test environment)
      const globalScope = (globalThis as any);
      if (typeof globalScope.chrome !== 'undefined' && globalScope.chrome.storage) {
        const tasksToStore = Array.from(this.tasks.values())
          .filter(task => task.status !== TaskStatus.COMPLETED);
        
        await globalScope.chrome.storage.local.set({
          taskQueue: JSON.stringify(tasksToStore)
        });
      }
    } catch (error) {
      console.error('[TaskQueue] Failed to persist tasks:', error);
    }
  }

  /**
   * Generate unique task ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Emit event to listeners
   */
  private emit<K extends keyof TaskQueueEvents>(event: K, ...args: Parameters<TaskQueueEvents[K]>): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          (listener as any)(...args);
        } catch (error) {
          console.error(`[TaskQueue] Event listener error for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Execute a batch of tasks
   */
  private async executeBatchTasks(batch: TaskBatch): Promise<void> {
    console.log(`[TaskQueue] Executing batch ${batch.id} with ${batch.tasks.length} tasks of type ${batch.type}`);

    const executor = this.executors.get(batch.type);
    if (!executor) {
      const error = `No executor registered for batch task type: ${batch.type}`;
      console.error(`[TaskQueue] ${error}`);
      
      // Mark all tasks in batch as failed
      batch.tasks.forEach(task => {
        this.markTaskFailed(task, error);
      });
      return;
    }

    // Execute all tasks in the batch
    const batchStartTime = Date.now();
    const results: TaskResult[] = [];

    for (const task of batch.tasks) {
      try {
        // Mark task as running
        task.status = TaskStatus.RUNNING;
        task.lastAttempt = Date.now();
        this.runningTasks.add(task.id);
        this.emit('taskStarted', task);

        // Execute task
        const result = await this.executeWithTimeout(executor, task);
        
        // Task completed successfully
        task.status = TaskStatus.COMPLETED;
        this.runningTasks.delete(task.id);
        this.emit('taskCompleted', task, result);
        results.push(result);

        console.log(`[TaskQueue] Batch task ${task.id} completed in ${result.executionTime}ms`);

      } catch (error) {
        console.error(`[TaskQueue] Batch task ${task.id} failed:`, error);
        
        this.runningTasks.delete(task.id);
        
        // Check if we should retry
        if (task.retryCount < task.maxRetries) {
          this.scheduleRetry(task, error as Error);
        } else {
          this.markTaskFailed(task, (error as Error).message);
        }
      }
    }

    const batchExecutionTime = Date.now() - batchStartTime;
    const successfulTasks = results.length;
    const failedTasks = batch.tasks.length - successfulTasks;

    console.log(
      `[TaskQueue] Batch ${batch.id} completed in ${batchExecutionTime}ms: ` +
      `${successfulTasks} successful, ${failedTasks} failed`
    );
  }

  /**
   * Update priority manager with current system context
   */
  private updatePriorityManagerContext(): void {
    const currentMetrics = this.resourceMonitor?.getCurrentMetrics();
    // const adaptiveParams = this.resourceMonitor?.getAdaptiveParams();

    // Calculate system load and available resources
    const systemLoad = currentMetrics?.cpuUsage || 0.1;
    const availableResources = currentMetrics?.availableMemory || 100;

    // Count pending tasks by type
    const pendingTasksByType: Record<TaskType, number> = {} as Record<TaskType, number>;
    this.tasks.forEach(task => {
      if (task.status === TaskStatus.PENDING) {
        pendingTasksByType[task.type] = (pendingTasksByType[task.type] || 0) + 1;
      }
    });

    // Update priority manager context
    this.priorityManager.updateSystemContext(
      systemLoad,
      availableResources / 1024, // Convert MB to normalized scale
      pendingTasksByType
    );
  }

  /**
   * Record user interaction for priority adjustment
   */
  public recordUserInteraction(type: string): void {
    this.priorityManager.recordUserInteraction(type);
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Task queue utilities
 */
export class TaskQueueUtils {
  /**
   * Create a task for indexing a page
   */
  static createIndexPageTask(page: Page, priority: TaskPriority = TaskPriority.NORMAL): Omit<IndexPageTask, 'id' | 'createdAt' | 'status' | 'retryCount'> {
    return {
      type: TaskType.INDEX_PAGE,
      priority,
      data: { page },
      timeout: 30000,
      maxRetries: 3,
      metadata: {
        source: 'content_extraction',
        estimatedDuration: 5000,
        resourceRequirements: {
          cpu: 0.1,
          memory: 10,
        },
      },
    };
  }

  /**
   * Create a batch indexing task
   */
  static createBatchIndexTask(
    pages: Page[], 
    batchSize: number = 10,
    priority: TaskPriority = TaskPriority.LOW
  ): Omit<BatchIndexTask, 'id' | 'createdAt' | 'status' | 'retryCount'> {
    return {
      type: TaskType.BATCH_INDEX,
      priority,
      data: { pages, batchSize },
      timeout: pages.length * 5000, // 5s per page
      maxRetries: 2,
      metadata: {
        source: 'batch_indexing',
        estimatedDuration: pages.length * 1000,
        resourceRequirements: {
          cpu: 0.2,
          memory: pages.length * 2,
        },
      },
    };
  }

  /**
   * Create a rebuild index task
   */
  static createRebuildIndexTask(force: boolean = false): Omit<RebuildIndexTask, 'id' | 'createdAt' | 'status' | 'retryCount'> {
    return {
      type: TaskType.REBUILD_INDEX,
      priority: force ? TaskPriority.HIGH : TaskPriority.LOW,
      data: { force },
      timeout: 300000, // 5 minutes
      maxRetries: 1,
      metadata: {
        source: 'index_maintenance',
        estimatedDuration: 60000,
        resourceRequirements: {
          cpu: 0.3,
          memory: 50,
        },
      },
    };
  }
}

// Export singleton instance
export const taskQueue = TaskQueue.getInstance();