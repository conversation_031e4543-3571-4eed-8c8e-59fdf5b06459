/**
 * Data Export Service
 * 
 * Handles exporting extension data in various formats
 */

import { dbService } from './db.service';
import { blacklistService } from './blacklist.service';
import type { Page, BlacklistEntry } from '../models';

/**
 * Export format types
 */
export type ExportFormat = 'json' | 'csv' | 'txt';

/**
 * Export options interface
 */
export interface ExportOptions {
  /** Export format */
  format: ExportFormat;
  
  /** Include pages data */
  includePages?: boolean;
  
  /** Include blacklist data */
  includeBlacklist?: boolean;
  
  /** Include settings data */
  includeSettings?: boolean;
  
  /** Date range filter for pages */
  dateRange?: {
    start: number;
    end: number;
  };
  
  /** Domain filter for pages */
  domains?: string[];
  
  /** Maximum number of pages to export */
  maxPages?: number;
}

/**
 * Export result interface
 */
export interface ExportResult {
  /** Export success status */
  success: boolean;
  
  /** Export data or error message */
  data?: string | Blob;
  
  /** Error message if failed */
  error?: string;
  
  /** Export statistics */
  stats?: {
    pagesExported: number;
    blacklistExported: number;
    settingsExported: number;
    totalSize: number;
  };
  
  /** Suggested filename */
  filename?: string;
}

/**
 * Export data structure for JSON format (V4.0)
 */
interface ExportData {
  format: string;
  exportTime: string;
  schema?: {
    version: string;
    timestampFields: string[];
  };
  backwardCompatible?: boolean;
  data?: Page[];
  pages?: Page[]; // Keep for backward compatibility
  blacklist?: BlacklistEntry[];
  settings?: Record<string, any>;
  metadata: {
    version?: string;
    totalPages: number;
    totalBlacklist: number;
    totalSettings: number;
    exportedBy: string;
    exportOptions: ExportOptions;
    timestampFields?: string[];
  };
}

/**
 * Export Service Class
 */
export class ExportService {
  private static instance: ExportService;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): ExportService {
    if (!ExportService.instance) {
      ExportService.instance = new ExportService();
    }
    return ExportService.instance;
  }

  /**
   * Export data with specified options
   */
  public async exportData(options: ExportOptions): Promise<ExportResult> {
    try {
      // Validate options
      if (!options.includePages && !options.includeBlacklist && !options.includeSettings) {
        return {
          success: false,
          error: 'At least one data type must be selected for export'
        };
      }

      // Collect data based on options (V4.0 format)
      const exportData: ExportData = {
        format: 'v4.0',
        exportTime: new Date().toISOString(),
        schema: {
          version: '4.0',
          timestampFields: ['visitTime', 'lastVisitTime', 'visitCount']
        },
        backwardCompatible: true,
        metadata: {
          version: '4.0',
          totalPages: 0,
          totalBlacklist: 0,
          totalSettings: 0,
          exportedBy: 'Recall v4.0',
          exportOptions: options,
          timestampFields: ['visitTime', 'lastVisitTime', 'visitCount']
        }
      };

      const stats = {
        pagesExported: 0,
        blacklistExported: 0,
        settingsExported: 0,
        totalSize: 0
      };

      // Export pages (V4.0 format with timestamp processing)
      if (options.includePages) {
        const pages = await this.getFilteredPages(options);
        const processedPages = pages.map(page => this.processPageTimestamps(page));
        exportData.data = processedPages; // V4.0 uses 'data' field
        exportData.pages = processedPages; // Keep for backward compatibility
        stats.pagesExported = processedPages.length;
        exportData.metadata.totalPages = processedPages.length;
      }

      // Export blacklist
      if (options.includeBlacklist) {
        const blacklist = await blacklistService.getAllDomains();
        exportData.blacklist = blacklist;
        stats.blacklistExported = blacklist.length;
        exportData.metadata.totalBlacklist = blacklist.length;
      }

      // Export settings (placeholder for future implementation)
      if (options.includeSettings) {
        const settings = await this.getAllSettings();
        exportData.settings = settings;
        stats.settingsExported = Object.keys(settings).length;
        exportData.metadata.totalSettings = Object.keys(settings).length;
      }

      // Generate export based on format
      const result = await this.generateExport(exportData, options.format);
      
      if (result.success && result.data) {
        stats.totalSize = typeof result.data === 'string' 
          ? new Blob([result.data]).size 
          : result.data.size;
      }

      return {
        ...result,
        stats,
        filename: this.generateFilename(options.format)
      };

    } catch (error) {
      console.error('Export failed:', error);
      return {
        success: false,
        error: `Export failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Process page timestamps for V4.0 export format
   * Preserves original timestamps and ensures data consistency
   */
  private processPageTimestamps(page: any): Page {
    // Normalize visitTime - preserve original if valid
    let visitTime = page.visitTime;
    if (typeof visitTime !== 'number' || isNaN(visitTime) || visitTime < 0) {
      visitTime = Date.now(); // Only use current time if original is invalid
    }
    
    // Use lastUpdated as lastVisitTime equivalent for export compatibility
    let lastVisitTime = page.lastUpdated || visitTime;
    if (typeof lastVisitTime !== 'number' || isNaN(lastVisitTime) || lastVisitTime < 0) {
      lastVisitTime = visitTime;
    }
    
    // Ensure lastVisitTime is not earlier than visitTime
    if (lastVisitTime < visitTime) {
      lastVisitTime = visitTime;
    }
    
    // Use accessCount as visitCount for export compatibility
    let visitCount = page.accessCount || 1;
    if (typeof visitCount !== 'number' || isNaN(visitCount) || visitCount <= 0) {
      visitCount = 1;
    }
    
    // Cap extreme values for visitCount
    if (visitCount > 1000000) {
      visitCount = 1000000;
    }
    
    // Return page with preserved original timestamps
    return {
      ...page,
      visitTime, // Preserve original visitTime
      lastVisitTime, // Use lastUpdated for compatibility
      visitCount // Use accessCount for compatibility
    };
  }

  /**
   * Get filtered pages based on export options
   */
  private async getFilteredPages(options: ExportOptions): Promise<Page[]> {
    let pages = await dbService.getAllPages();

    // Apply date range filter (use normalized timestamps)
    if (options.dateRange) {
      pages = pages.filter(page => {
        const visitTime = typeof page.visitTime === 'number' && page.visitTime > 0 
          ? page.visitTime 
          : Date.now();
        return visitTime >= options.dateRange!.start && 
               visitTime <= options.dateRange!.end;
      });
    }

    // Apply domain filter
    if (options.domains && options.domains.length > 0) {
      pages = pages.filter(page => 
        options.domains!.includes(page.domain)
      );
    }

    // Apply max pages limit
    if (options.maxPages && options.maxPages > 0) {
      // Sort by visit time (newest first) and take the limit
      pages = pages
        .sort((a, b) => {
          const aTime = typeof a.visitTime === 'number' && a.visitTime > 0 ? a.visitTime : 0;
          const bTime = typeof b.visitTime === 'number' && b.visitTime > 0 ? b.visitTime : 0;
          return bTime - aTime;
        })
        .slice(0, options.maxPages);
    }

    return pages;
  }

  /**
   * Get all settings (placeholder for future implementation)
   */
  private async getAllSettings(): Promise<Record<string, any>> {
    // This is a placeholder - in the future, we might have a settings service
    return {
      extensionEnabled: true,
      searchSettings: {
        maxResults: 50,
        highlightEnabled: true
      },
      exportedAt: Date.now()
    };
  }

  /**
   * Generate export data in specified format
   */
  private async generateExport(data: ExportData, format: ExportFormat): Promise<Omit<ExportResult, 'stats' | 'filename'>> {
    switch (format) {
      case 'json':
        return this.generateJSONExport(data);
      
      case 'csv':
        return this.generateCSVExport(data);
      
      case 'txt':
        return this.generateTXTExport(data);
      
      default:
        return {
          success: false,
          error: `Unsupported export format: ${format}`
        };
    }
  }

  /**
   * Generate JSON export
   */
  private generateJSONExport(data: ExportData): Omit<ExportResult, 'stats' | 'filename'> {
    try {
      const jsonString = JSON.stringify(data, null, 2);
      return {
        success: true,
        data: jsonString
      };
    } catch (error) {
      return {
        success: false,
        error: `JSON export failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Generate CSV export
   */
  private generateCSVExport(data: ExportData): Omit<ExportResult, 'stats' | 'filename'> {
    try {
      let csvContent = '';

      // Export pages as CSV (V4.0 format with timestamp fields)
      const pagesToExport = data.data || data.pages;
      if (pagesToExport && pagesToExport.length > 0) {
        csvContent += 'Pages\n';
        csvContent += 'ID,URL,Title,Domain,Visit Time,Last Visit Time,Visit Count,Access Count,Content Length\n';
        
        for (const page of pagesToExport) {
          const pageWithTimestamps = page as any; // V4.0 pages with timestamp fields
          const row = [
            this.escapeCsvField(page.id),
            this.escapeCsvField(page.url),
            this.escapeCsvField(page.title),
            this.escapeCsvField(page.domain),
            new Date(page.visitTime).toISOString(),
            new Date(pageWithTimestamps.lastVisitTime).toISOString(),
            pageWithTimestamps.visitCount.toString(),
            page.accessCount.toString(),
            page.content.length.toString()
          ].join(',');
          csvContent += row + '\n';
        }
        csvContent += '\n';
      }

      // Export blacklist as CSV
      if (data.blacklist && data.blacklist.length > 0) {
        csvContent += 'Blacklist\n';
        csvContent += 'Domain,Created At,Reason,Is Wildcard\n';
        
        for (const entry of data.blacklist) {
          const row = [
            this.escapeCsvField(entry.domain),
            new Date(entry.createdAt).toISOString(),
            this.escapeCsvField(entry.reason || ''),
            entry.isWildcard ? 'Yes' : 'No'
          ].join(',');
          csvContent += row + '\n';
        }
      }

      return {
        success: true,
        data: csvContent
      };
    } catch (error) {
      return {
        success: false,
        error: `CSV export failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Generate TXT export
   */
  private generateTXTExport(data: ExportData): Omit<ExportResult, 'stats' | 'filename'> {
    try {
      let txtContent = '';
      
      // Header (V4.0 format)
      txtContent += `Recall Export\n`;
      txtContent += `Generated: ${new Date(data.exportTime).toLocaleString()}\n`;
      txtContent += `Format: ${data.format}\n\n`;

      // Export pages (V4.0 format with timestamp fields)
      const pagesToExport = data.data || data.pages;
      if (pagesToExport && pagesToExport.length > 0) {
        txtContent += `=== PAGES (${pagesToExport.length}) ===\n\n`;
        
        for (const page of pagesToExport) {
          const pageWithTimestamps = page as any; // V4.0 pages with timestamp fields
          txtContent += `Title: ${page.title}\n`;
          txtContent += `URL: ${page.url}\n`;
          txtContent += `Domain: ${page.domain}\n`;
          txtContent += `Visit Time: ${new Date(page.visitTime).toLocaleString()}\n`;
          txtContent += `Last Visit Time: ${new Date(pageWithTimestamps.lastVisitTime).toLocaleString()}\n`;
          txtContent += `Visit Count: ${pageWithTimestamps.visitCount}\n`;
          txtContent += `Access Count: ${page.accessCount}\n`;
          txtContent += `Content Length: ${page.content.length} characters\n`;
          txtContent += `Content Preview: ${page.content.substring(0, 200)}...\n`;
          txtContent += `${'='.repeat(50)}\n\n`;
        }
      }

      // Export blacklist
      if (data.blacklist && data.blacklist.length > 0) {
        txtContent += `=== BLACKLIST (${data.blacklist.length}) ===\n\n`;
        
        for (const entry of data.blacklist) {
          txtContent += `Domain: ${entry.domain}\n`;
          txtContent += `Type: ${entry.isWildcard ? 'Wildcard' : 'Exact'}\n`;
          txtContent += `Added: ${new Date(entry.createdAt).toLocaleString()}\n`;
          if (entry.reason) {
            txtContent += `Reason: ${entry.reason}\n`;
          }
          txtContent += `${'-'.repeat(30)}\n\n`;
        }
      }

      return {
        success: true,
        data: txtContent
      };
    } catch (error) {
      return {
        success: false,
        error: `TXT export failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Escape CSV field
   */
  private escapeCsvField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  /**
   * Generate filename for export
   */
  private generateFilename(format: ExportFormat): string {
    const timestamp = new Date().toISOString().split('T')[0];
    return `recall-export-${timestamp}.${format}`;
  }
}

// Export singleton instance
export const exportService = ExportService.getInstance();
