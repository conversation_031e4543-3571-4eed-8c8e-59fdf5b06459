/**
 * StorageManager - Chrome Storage Adapter for i18n Resources
 * 
 * Features:
 * - Chrome storage.local integration
 * - Automatic compression
 * - Batch operations
 * - Error handling with fallbacks
 * - Storage quota management
 */

import type { StorageAdapter, CacheEntry } from './types';

export class StorageManager implements StorageAdapter {
  private keyPrefix: string;
  private compressionEnabled: boolean;
  private maxQuotaUsage: number = 0.8; // Use max 80% of available quota
  
  constructor(
    keyPrefix: string = 'i18n_',
    compressionEnabled: boolean = true
  ) {
    this.keyPrefix = keyPrefix;
    this.compressionEnabled = compressionEnabled;
  }
  
  /**
   * Get value from chrome storage
   */
  public async get(key: string): Promise<CacheEntry | null> {
    try {
      const storageKey = this.getStorageKey(key);
      const result = await chrome.storage.local.get(storageKey);
      
      if (!result[storageKey]) {
        return null;
      }
      
      const data = result[storageKey];
      
      // Decompress if needed
      if (this.compressionEnabled && data.compressed) {
        data.data = this.decompress(data.data);
        delete data.compressed;
      }
      
      return data as CacheEntry;
    } catch (error) {
      console.warn('Storage get failed:', error);
      return null;
    }
  }
  
  /**
   * Set value in chrome storage
   */
  public async set(key: string, value: CacheEntry): Promise<void> {
    try {
      const storageKey = this.getStorageKey(key);
      const dataToStore = { ...value };
      
      // Compress if enabled
      if (this.compressionEnabled) {
        const compressed = this.compress(value.data);
        if (compressed.length < JSON.stringify(value.data).length * 0.8) {
          (dataToStore as any).data = compressed;
          (dataToStore as any).compressed = true;
        }
      }
      
      // Check quota before storing
      await this.checkQuota(storageKey, dataToStore);
      
      await chrome.storage.local.set({
        [storageKey]: dataToStore
      });
    } catch (error) {
      console.warn('Storage set failed:', error);
      
      // If quota exceeded, try to free space
      if (error instanceof Error && error.message.includes('QUOTA_EXCEEDED')) {
        await this.cleanupOldEntries();
        // Retry once
        try {
          const storageKey = this.getStorageKey(key);
          await chrome.storage.local.set({
            [storageKey]: value
          });
        } catch (retryError) {
          throw new Error(`Storage quota exceeded even after cleanup: ${retryError}`);
        }
      } else {
        throw error;
      }
    }
  }
  
  /**
   * Remove value from chrome storage
   */
  public async remove(key: string): Promise<void> {
    try {
      const storageKey = this.getStorageKey(key);
      await chrome.storage.local.remove(storageKey);
    } catch (error) {
      console.warn('Storage remove failed:', error);
    }
  }
  
  /**
   * Clear all entries with our prefix
   */
  public async clear(): Promise<void> {
    try {
      const allKeys = await this.getAllKeys();
      if (allKeys.length > 0) {
        await chrome.storage.local.remove(allKeys);
      }
    } catch (error) {
      console.warn('Storage clear failed:', error);
    }
  }
  
  /**
   * Get total size of our storage usage
   */
  public async getSize(): Promise<number> {
    try {
      const allKeys = await this.getAllKeys();
      if (allKeys.length === 0) return 0;
      
      const data = await chrome.storage.local.get(allKeys);
      return this.calculateStorageSize(data);
    } catch (error) {
      console.warn('Storage size calculation failed:', error);
      return 0;
    }
  }
  
  /**
   * Get storage statistics
   */
  public async getStats() {
    try {
      const allKeys = await this.getAllKeys();
      const data = await chrome.storage.local.get(allKeys);
      const totalSize = this.calculateStorageSize(data);
      
      // Get available quota
      const quota = await this.getQuotaInfo();
      
      return {
        entriesCount: allKeys.length,
        totalSize,
        quotaUsed: quota.used,
        quotaTotal: quota.total,
        quotaAvailable: quota.total - quota.used,
        utilization: (totalSize / quota.total) * 100
      };
    } catch (error) {
      console.warn('Storage stats failed:', error);
      return {
        entriesCount: 0,
        totalSize: 0,
        quotaUsed: 0,
        quotaTotal: 0,
        quotaAvailable: 0,
        utilization: 0
      };
    }
  }
  
  /**
   * Batch set multiple entries
   */
  public async setBatch(entries: Map<string, CacheEntry>): Promise<void> {
    const storageData: Record<string, any> = {};
    
    for (const [key, value] of entries) {
      const storageKey = this.getStorageKey(key);
      const dataToStore = { ...value };
      
      if (this.compressionEnabled) {
        const compressed = this.compress(value.data);
        if (compressed.length < JSON.stringify(value.data).length * 0.8) {
          (dataToStore as any).data = compressed;
          (dataToStore as any).compressed = true;
        }
      }
      
      storageData[storageKey] = dataToStore;
    }
    
    try {
      await chrome.storage.local.set(storageData);
    } catch (error) {
      if (error instanceof Error && error.message.includes('QUOTA_EXCEEDED')) {
        await this.cleanupOldEntries();
        throw new Error('Storage quota exceeded during batch operation');
      }
      throw error;
    }
  }
  
  /**
   * Batch get multiple entries
   */
  public async getBatch(keys: string[]): Promise<Map<string, CacheEntry | null>> {
    const storageKeys = keys.map(key => this.getStorageKey(key));
    const result = new Map<string, CacheEntry | null>();
    
    try {
      const data = await chrome.storage.local.get(storageKeys);
      
      for (let i = 0; i < keys.length; i++) {
        const originalKey = keys[i];
        const storageKey = storageKeys[i];
        
        if (data[storageKey]) {
          const entry = data[storageKey] as CacheEntry;
          
          // Decompress if needed
          if (this.compressionEnabled && (entry as any).compressed) {
            entry.data = this.decompress(entry.data as any);
            delete (entry as any).compressed;
          }
          
          result.set(originalKey, entry);
        } else {
          result.set(originalKey, null);
        }
      }
    } catch (error) {
      console.warn('Batch get failed:', error);
      // Return null for all keys on error
      keys.forEach(key => result.set(key, null));
    }
    
    return result;
  }
  
  /**
   * Get all our storage keys
   */
  private async getAllKeys(): Promise<string[]> {
    const allData = await chrome.storage.local.get();
    return Object.keys(allData).filter(key => key.startsWith(this.keyPrefix));
  }
  
  /**
   * Get storage key with prefix
   */
  private getStorageKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }
  
  /**
   * Calculate storage size of data
   */
  private calculateStorageSize(data: Record<string, any>): number {
    return JSON.stringify(data).length * 2; // Rough estimate in bytes
  }
  
  /**
   * Check storage quota
   */
  private async checkQuota(key: string, value: any): Promise<void> {
    try {
      const quota = await this.getQuotaInfo();
      const valueSize = JSON.stringify({ [key]: value }).length * 2;
      
      if (quota.used + valueSize > quota.total * this.maxQuotaUsage) {
        throw new Error('Storage quota would be exceeded');
      }
    } catch (error) {
      // If we can't check quota, proceed anyway
      console.warn('Quota check failed:', error);
    }
  }
  
  /**
   * Get quota information
   */
  private async getQuotaInfo(): Promise<{ used: number; total: number }> {
    try {
      if ((chrome.storage.local as any).getBytesInUse) {
        const used = await (chrome.storage.local as any).getBytesInUse();
        return {
          used,
          total: (chrome.storage.local as any).QUOTA_BYTES || 5242880 // 5MB default
        };
      } else {
        // Fallback: estimate usage
        const allData = await chrome.storage.local.get();
        const used = this.calculateStorageSize(allData);
        return {
          used,
          total: 5242880 // 5MB
        };
      }
    } catch (error) {
      return {
        used: 0,
        total: 5242880 // 5MB
      };
    }
  }
  
  /**
   * Clean up old entries to free space
   */
  private async cleanupOldEntries(): Promise<void> {
    try {
      const allKeys = await this.getAllKeys();
      const data = await chrome.storage.local.get(allKeys);
      
      // Sort by timestamp (oldest first)
      const entries = Object.entries(data)
        .filter(([key]) => key.startsWith(this.keyPrefix))
        .map(([key, value]) => ({ key, timestamp: value.timestamp || 0 }))
        .sort((a, b) => a.timestamp - b.timestamp);
      
      // Remove oldest 25% of entries
      const toRemove = entries.slice(0, Math.ceil(entries.length * 0.25));
      const keysToRemove = toRemove.map(entry => entry.key);
      
      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(`Cleaned up ${keysToRemove.length} old i18n cache entries`);
      }
    } catch (error) {
      console.warn('Cleanup failed:', error);
    }
  }
  
  /**
   * Simple compression using JSON.stringify with replacer
   */
  private compress(data: any): string {
    try {
      // Simple compression by removing whitespace and using shorter keys
      return JSON.stringify(data, (_, value) => {
        // Could implement more sophisticated compression here
        return value;
      });
    } catch (error) {
      console.warn('Compression failed:', error);
      return JSON.stringify(data);
    }
  }
  
  /**
   * Simple decompression
   */
  private decompress(compressedData: string): any {
    try {
      return JSON.parse(compressedData);
    } catch (error) {
      console.warn('Decompression failed:', error);
      return compressedData;
    }
  }
  
  /**
   * Get entries older than specified age
   */
  public async getOldEntries(maxAge: number): Promise<string[]> {
    const cutoff = Date.now() - maxAge;
    const allKeys = await this.getAllKeys();
    const data = await chrome.storage.local.get(allKeys);
    
    return Object.entries(data)
      .filter(([, value]) => value.timestamp && value.timestamp < cutoff)
      .map(([key]) => key.replace(this.keyPrefix, ''));
  }
  
  /**
   * Remove entries older than specified age
   */
  public async removeOldEntries(maxAge: number): Promise<number> {
    const oldKeys = await this.getOldEntries(maxAge);
    const storageKeys = oldKeys.map(key => this.getStorageKey(key));
    
    if (storageKeys.length > 0) {
      await chrome.storage.local.remove(storageKeys);
    }
    
    return oldKeys.length;
  }
}