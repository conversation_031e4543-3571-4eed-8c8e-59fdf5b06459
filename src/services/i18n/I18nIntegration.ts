/**
 * I18n Integration Adapter
 * 
 * Provides seamless integration between the new ResourceManager
 * and the existing I18nManager system.
 */

import { I18nManager } from '../../i18n/I18nManager';
import type { SupportedLanguage, LanguageChangeEvent } from '../../i18n/I18nManager';
import { ResourceManager } from './ResourceManager';
import { extractNamespace } from './utils';
import type { ResourceConfig } from './types';

export class I18nIntegration {
  private static instance: I18nIntegration;
  private i18nManager: I18nManager;
  private resourceManager: ResourceManager;
  private initialized = false;
  
  private constructor() {
    this.i18nManager = I18nManager.getInstance();
    this.resourceManager = ResourceManager.getInstance();
  }
  
  public static getInstance(): I18nIntegration {
    if (!I18nIntegration.instance) {
      I18nIntegration.instance = new I18nIntegration();
    }
    return I18nIntegration.instance;
  }
  
  /**
   * Initialize the integration
   */
  public async initialize(config?: Partial<ResourceConfig>): Promise<void> {
    if (this.initialized) return;
    
    // Set up ResourceManager if config provided
    if (config) {
      this.resourceManager = ResourceManager.getInstance(config);
    }
    
    // Set up language change listener
    this.i18nManager.addLanguageChangeListener(this.handleLanguageChange.bind(this));
    
    // Preload resources for current language
    const currentLanguage = this.i18nManager.getCurrentLanguage();
    await this.preloadForLanguage(currentLanguage);
    
    this.initialized = true;
  }
  
  /**
   * Handle language change events
   */
  private async handleLanguageChange(event: LanguageChangeEvent): Promise<void> {
    try {
      await this.preloadForLanguage(event.newLanguage);
    } catch (error) {
      console.error('Failed to preload resources for language change:', error);
    }
  }
  
  /**
   * Preload resources for a language
   */
  private async preloadForLanguage(language: SupportedLanguage): Promise<void> {
    const namespaces = ['common', 'popup']; // Critical namespaces
    
    await Promise.all(
      namespaces.map(ns => 
        this.resourceManager.loadNamespace(ns, language).catch(error => {
          console.warn(`Failed to preload namespace ${ns} for ${language}:`, error);
        })
      )
    );
  }
  
  /**
   * Enhanced translation method with namespace support
   */
  public t(key: string, fallback?: string, language?: SupportedLanguage): string {
    const currentLanguage = language || this.i18nManager.getCurrentLanguage();
    
    // Extract namespace from key
    extractNamespace(key);
    
    // Try ResourceManager first (for namespaced resources)
    try {
      const translation = this.resourceManager.getTranslation(key, currentLanguage);
      if (translation) {
        return translation;
      }
    } catch (error) {
      console.warn('ResourceManager translation failed:', error);
    }
    
    // Fallback to existing I18nManager
    try {
      const legacyTranslation = this.i18nManager.getTranslation(key);
      if (legacyTranslation && legacyTranslation !== fallback) {
        return legacyTranslation;
      }
    } catch (error) {
      console.warn('I18nManager translation failed:', error);
    }
    
    // Return fallback or key
    return fallback || key;
  }
  
  /**
   * Load namespace on demand
   */
  public async loadNamespace(namespace: string, language?: SupportedLanguage): Promise<void> {
    const targetLanguage = language || this.i18nManager.getCurrentLanguage();
    await this.resourceManager.loadNamespace(namespace, targetLanguage);
  }
  
  /**
   * Check if namespace is loaded
   */
  public isNamespaceLoaded(namespace: string, language?: SupportedLanguage): boolean {
    const targetLanguage = language || this.i18nManager.getCurrentLanguage();
    return this.resourceManager.isNamespaceLoaded(namespace, targetLanguage);
  }
  
  /**
   * Get translation with interpolation support
   */
  public translate(
    key: string, 
    variables?: Record<string, string | number>,
    language?: SupportedLanguage
  ): string {
    let translation = this.t(key, undefined, language);
    
    // Simple variable interpolation
    if (variables && typeof translation === 'string') {
      Object.entries(variables).forEach(([varKey, value]) => {
        translation = translation.replace(
          new RegExp(`{{\\s*${varKey}\\s*}}`, 'g'),
          String(value)
        );
      });
    }
    
    return translation;
  }
  
  /**
   * Get all available namespaces
   */
  public getAvailableNamespaces(): string[] {
    return this.resourceManager.getLoadedNamespaces();
  }
  
  /**
   * Get resource statistics
   */
  public getResourceStats() {
    return this.resourceManager.getStats();
  }
  
  /**
   * Force reload a namespace
   */
  public async reloadNamespace(namespace: string, language?: SupportedLanguage): Promise<void> {
    const targetLanguage = language || this.i18nManager.getCurrentLanguage();
    await this.resourceManager.loadNamespace(namespace, targetLanguage, { forceReload: true });
  }
  
  /**
   * Clear all resource caches
   */
  public async clearCache(): Promise<void> {
    await this.resourceManager.invalidateCache();
  }
  
  /**
   * Get current language
   */
  public getCurrentLanguage(): SupportedLanguage {
    return this.i18nManager.getCurrentLanguage();
  }
  
  /**
   * Switch language (delegates to I18nManager)
   */
  public async switchLanguage(language: SupportedLanguage): Promise<void> {
    await this.i18nManager.setLanguage(language);
  }
  
  /**
   * Add language change listener
   */
  public addLanguageChangeListener(listener: (event: LanguageChangeEvent) => void): void {
    this.i18nManager.addLanguageChangeListener(listener);
  }
  
  /**
   * Remove language change listener
   */
  public removeLanguageChangeListener(listener: (event: LanguageChangeEvent) => void): void {
    this.i18nManager.removeLanguageChangeListener(listener);
  }
  
  /**
   * Batch load multiple namespaces
   */
  public async loadNamespaces(namespaces: string[], language?: SupportedLanguage): Promise<void> {
    const targetLanguage = language || this.i18nManager.getCurrentLanguage();
    
    await Promise.all(
      namespaces.map(namespace => 
        this.resourceManager.loadNamespace(namespace, targetLanguage)
      )
    );
  }
  
  /**
   * Check if integration is initialized
   */
  public isInitialized(): boolean {
    return this.initialized;
  }
  
  /**
   * Get both managers for advanced usage
   */
  public getManagers() {
    return {
      i18nManager: this.i18nManager,
      resourceManager: this.resourceManager
    };
  }
}

// Create and export singleton instance
export const i18nIntegration = I18nIntegration.getInstance();

// Export convenience functions
export const t = (key: string, fallback?: string, language?: SupportedLanguage) => 
  i18nIntegration.t(key, fallback, language);

export const translate = (
  key: string, 
  variables?: Record<string, string | number>,
  language?: SupportedLanguage
) => i18nIntegration.translate(key, variables, language);

export const loadNamespace = (namespace: string, language?: SupportedLanguage) =>
  i18nIntegration.loadNamespace(namespace, language);

export const switchLanguage = (language: SupportedLanguage) =>
  i18nIntegration.switchLanguage(language);