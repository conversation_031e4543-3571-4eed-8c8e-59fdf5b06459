/**
 * I18n Resource Management Type Definitions
 * Enhanced types for namespace-based resource management with lazy loading
 */

import type { SupportedLanguage, TranslationResource } from '../../i18n/types';

/**
 * Namespace configuration for organizing translations
 */
export interface NamespaceConfig {
  name: string;
  preload?: boolean; // Load on startup
  priority?: 'high' | 'medium' | 'low';
  dependencies?: string[]; // Other namespaces this depends on
}

/**
 * Loading state for resources
 */
export const LoadingState = {
  IDLE: 'idle',
  LOADING: 'loading',
  LOADED: 'loaded',
  ERROR: 'error'
} as const;

export type LoadingState = typeof LoadingState[keyof typeof LoadingState];

/**
 * Translation namespace with metadata
 */
export interface TranslationNamespace {
  name: string;
  translations: TranslationResource;
  version?: string;
  lastModified?: number;
  size?: number;
}

/**
 * Resource loading configuration
 */
export interface ResourceConfig {
  defaultNamespace: string;
  namespaces: NamespaceConfig[];
  cacheStrategy: 'memory' | 'storage' | 'hybrid';
  maxCacheSize: number;
  ttl?: number; // Time to live in milliseconds
  compressionEnabled?: boolean;
}

/**
 * Namespace loading result
 */
export interface NamespaceLoadResult {
  namespace: string;
  language: SupportedLanguage;
  state: LoadingState;
  data?: TranslationResource;
  error?: Error;
  loadTime?: number;
  fromCache?: boolean;
}

/**
 * Cache entry with metadata
 */
export interface CacheEntry {
  namespace: string;
  language: SupportedLanguage;
  data: TranslationResource;
  timestamp: number;
  size: number;
  accessCount: number;
  lastAccess: number;
}

/**
 * Resource manager statistics
 */
export interface ResourceStats {
  namespacesLoaded: number;
  totalCacheSize: number;
  cacheHitRate: number;
  averageLoadTime: number;
  memoryUsage: number;
  storageUsage: number;
}

/**
 * Type-safe translation key path
 * Supports dot notation: 'namespace.section.key'
 */
export type TranslationKey<T = string> = T extends `${infer NS}.${infer Rest}` 
  ? NS extends string 
    ? Rest extends string 
      ? `${NS}.${TranslationKey<Rest>}`
      : never
    : never
  : T;

/**
 * Namespace registry type
 */
export type NamespaceRegistry = Map<string, Map<SupportedLanguage, TranslationResource>>;

/**
 * Resource loading options with namespace support
 */
export interface NamespaceLoadOptions {
  language?: SupportedLanguage;
  forceReload?: boolean;
  timeout?: number;
  fallbackToDefault?: boolean;
  includeDependencies?: boolean;
}

/**
 * Chrome storage wrapper interface
 */
export interface StorageAdapter {
  get(key: string): Promise<any>;
  set(key: string, value: any): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  getSize(): Promise<number>;
}

/**
 * Resource manager error types
 */
export const ResourceErrorType = {
  NAMESPACE_NOT_FOUND: 'NAMESPACE_NOT_FOUND',
  LOADING_FAILED: 'LOADING_FAILED',
  CACHE_ERROR: 'CACHE_ERROR',
  STORAGE_LIMIT: 'STORAGE_LIMIT',
  INVALID_CONFIG: 'INVALID_CONFIG'
} as const;

export type ResourceErrorType = typeof ResourceErrorType[keyof typeof ResourceErrorType];

/**
 * Resource manager error
 */
export class ResourceError extends Error {
  public type: ResourceErrorType;
  public namespace?: string;
  public language?: SupportedLanguage;
  public cause?: Error;

  constructor(
    type: ResourceErrorType,
    message: string,
    namespace?: string,
    language?: SupportedLanguage,
    cause?: Error
  ) {
    super(message);
    this.name = 'ResourceError';
    this.type = type;
    this.namespace = namespace;
    this.language = language;
    this.cause = cause;
  }
}

/**
 * Batch loading result
 */
export interface BatchLoadResult {
  successful: NamespaceLoadResult[];
  failed: NamespaceLoadResult[];
  totalTime: number;
}

/**
 * Resource change event
 */
export interface ResourceChangeEvent {
  type: 'loaded' | 'unloaded' | 'updated' | 'error';
  namespace: string;
  language: SupportedLanguage;
  timestamp: number;
}

/**
 * LRU cache configuration
 */
export interface LRUConfig {
  maxSize: number; // in bytes
  maxAge?: number; // in milliseconds
  updateAgeOnGet?: boolean;
  stale?: boolean; // Allow stale cache on error
}

/**
 * Dynamic import handler type
 */
export type ResourceImporter = (
  namespace: string,
  language: SupportedLanguage
) => Promise<TranslationResource>;

/**
 * Translation key validation helper
 */
export type ValidTranslationKey<T extends TranslationResource> = T extends Record<string, any>
  ? {
      [K in keyof T]: T[K] extends TranslationResource
        ? `${string & K}` | `${string & K}.${ValidTranslationKey<T[K]>}`
        : `${string & K}`
    }[keyof T]
  : never;