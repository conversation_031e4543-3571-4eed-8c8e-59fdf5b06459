/**
 * Utility functions for i18n ResourceManager
 */

import type { SupportedLanguage, TranslationResource } from '../../i18n/types';
import type { NamespaceConfig, ResourceConfig } from './types';

/**
 * Default namespace configurations
 */
export const DEFAULT_NAMESPACES: NamespaceConfig[] = [
  {
    name: 'common',
    preload: true,
    priority: 'high'
  },
  {
    name: 'popup',
    preload: true,
    priority: 'high'
  },
  {
    name: 'options',
    preload: false,
    priority: 'medium'
  },
  {
    name: 'content',
    preload: false,
    priority: 'medium'
  },
  {
    name: 'background',
    preload: false,
    priority: 'low'
  }
];

/**
 * Validate namespace configuration
 */
export function validateNamespaceConfig(config: NamespaceConfig): boolean {
  if (!config.name || typeof config.name !== 'string') {
    return false;
  }
  
  if (config.priority && !['high', 'medium', 'low'].includes(config.priority)) {
    return false;
  }
  
  if (config.dependencies && !Array.isArray(config.dependencies)) {
    return false;
  }
  
  return true;
}

/**
 * Validate resource configuration
 */
export function validateResourceConfig(config: ResourceConfig): boolean {
  if (!config.defaultNamespace || typeof config.defaultNamespace !== 'string') {
    return false;
  }
  
  if (!Array.isArray(config.namespaces)) {
    return false;
  }
  
  for (const ns of config.namespaces) {
    if (!validateNamespaceConfig(ns)) {
      return false;
    }
  }
  
  if (config.cacheStrategy && !['memory', 'storage', 'hybrid'].includes(config.cacheStrategy)) {
    return false;
  }
  
  if (typeof config.maxCacheSize !== 'number' || config.maxCacheSize <= 0) {
    return false;
  }
  
  return true;
}

/**
 * Merge translation resources (for extending base translations)
 */
export function mergeTranslationResources(
  base: TranslationResource,
  override: TranslationResource
): TranslationResource {
  const result: TranslationResource = { ...base };
  
  for (const [key, value] of Object.entries(override)) {
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      if (typeof result[key] === 'object' && result[key] !== null && !Array.isArray(result[key])) {
        result[key] = mergeTranslationResources(
          result[key] as TranslationResource,
          value as TranslationResource
        );
      } else {
        result[key] = value;
      }
    } else {
      result[key] = value;
    }
  }
  
  return result;
}

/**
 * Flatten nested translation keys for type generation
 */
export function flattenTranslationKeys(
  resource: TranslationResource,
  prefix: string = ''
): string[] {
  const keys: string[] = [];
  
  for (const [key, value] of Object.entries(resource)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'string') {
      keys.push(fullKey);
    } else if (typeof value === 'object' && value !== null) {
      keys.push(...flattenTranslationKeys(value as TranslationResource, fullKey));
    }
  }
  
  return keys;
}

/**
 * Extract namespace from translation key
 */
export function extractNamespace(key: string): { namespace: string; key: string } {
  const parts = key.split('.');
  if (parts.length === 1) {
    return { namespace: 'common', key };
  }
  
  return {
    namespace: parts[0],
    key: parts.slice(1).join('.')
  };
}

/**
 * Create cache key for namespace and language
 */
export function createCacheKey(namespace: string, language: SupportedLanguage): string {
  return `${namespace}:${language}`;
}

/**
 * Parse cache key to get namespace and language
 */
export function parseCacheKey(cacheKey: string): { namespace: string; language: SupportedLanguage } | null {
  const parts = cacheKey.split(':');
  if (parts.length !== 2) {
    return null;
  }
  
  return {
    namespace: parts[0],
    language: parts[1] as SupportedLanguage
  };
}

/**
 * Calculate memory usage of an object
 */
export function calculateMemoryUsage(obj: any): number {
  const seen = new Set();
  
  function sizeOf(obj: any): number {
    if (obj === null || obj === undefined) return 0;
    if (seen.has(obj)) return 0;
    
    seen.add(obj);
    
    switch (typeof obj) {
      case 'boolean':
        return 4;
      case 'number':
        return 8;
      case 'string':
        return obj.length * 2;
      case 'object':
        if (Array.isArray(obj)) {
          return obj.reduce((sum, item) => sum + sizeOf(item), 8);
        } else {
          return Object.keys(obj).reduce((sum, key) => {
            return sum + sizeOf(key) + sizeOf(obj[key]);
          }, 8);
        }
      default:
        return 0;
    }
  }
  
  return sizeOf(obj);
}

/**
 * Debounce function for reducing API calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | undefined;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for limiting execution frequency
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle = false;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Create a promise that resolves after specified delay
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry function with exponential backoff
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 100
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      // Exponential backoff with jitter
      const delayMs = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 100;
      await delay(delayMs);
    }
  }
  
  throw lastError!;
}

/**
 * Check if code is running in development mode
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development' || 
         typeof chrome === 'undefined' ||
         !chrome.runtime?.id;
}

/**
 * Generate UUID for cache invalidation
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Safe JSON parse with fallback
 */
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return fallback;
  }
}

/**
 * Safe JSON stringify
 */
export function safeJsonStringify(obj: any): string {
  try {
    return JSON.stringify(obj);
  } catch {
    return '{}';
  }
}

/**
 * Get nested property value safely
 */
export function getNestedValue(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => {
    return current && typeof current === 'object' ? current[key] : undefined;
  }, obj);
}

/**
 * Set nested property value safely
 */
export function setNestedValue(obj: any, path: string, value: any): void {
  const keys = path.split('.');
  const lastKey = keys.pop();
  
  if (!lastKey) return;
  
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  
  target[lastKey] = value;
}

/**
 * Deep clone object
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  const cloned = {} as T;
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]);
    }
  }
  
  return cloned;
}

/**
 * Check if two objects are deeply equal
 */
export function deepEqual(a: any, b: any): boolean {
  if (a === b) return true;
  
  if (a === null || b === null) return false;
  if (typeof a !== typeof b) return false;
  
  if (typeof a !== 'object') return false;
  
  if (Array.isArray(a) !== Array.isArray(b)) return false;
  
  const keysA = Object.keys(a);
  const keysB = Object.keys(b);
  
  if (keysA.length !== keysB.length) return false;
  
  for (const key of keysA) {
    if (!keysB.includes(key)) return false;
    if (!deepEqual(a[key], b[key])) return false;
  }
  
  return true;
}