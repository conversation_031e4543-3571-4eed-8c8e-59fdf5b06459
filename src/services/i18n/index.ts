/**
 * i18n Resource Management System
 * 
 * Exports:
 * - ResourceManager: Main resource management class
 * - Types: TypeScript type definitions
 * - Utils: Utility functions
 * - Cache: LRU cache implementation
 * - StorageManager: Chrome storage adapter
 */

// Main exports
export { ResourceManager } from './ResourceManager';
import { ResourceManager } from './ResourceManager';
export { LRUCache } from './cache';
export { StorageManager } from './StorageManager';
export { I18nIntegration, i18nIntegration, t, translate, loadNamespace, switchLanguage } from './I18nIntegration';

// Type exports
export type {
  NamespaceConfig,
  LoadingState,
  TranslationNamespace,
  ResourceConfig,
  NamespaceLoadResult,
  CacheEntry,
  ResourceStats,
  NamespaceRegistry,
  NamespaceLoadOptions,
  BatchLoadResult,
  ResourceChangeEvent,
  ResourceImporter,
  StorageAdapter,
  LRUConfig,
  TranslationKey,
  ValidTranslationKey
} from './types';

import type { ResourceConfig } from './types';

export {
  ResourceError,
  ResourceErrorType
} from './types';

// Utility exports
export {
  DEFAULT_NAMESPACES,
  validateNamespaceConfig,
  validateResourceConfig,
  mergeTranslationResources,
  flattenTranslationKeys,
  extractNamespace,
  createCacheKey,
  parseCacheKey,
  calculateMemoryUsage,
  debounce,
  throttle,
  delay,
  retry,
  isDevelopment,
  generateUUID,
  safeJsonParse,
  safeJsonStringify,
  getNestedValue,
  setNestedValue,
  deepClone,
  deepEqual
} from './utils';

// Default configuration
export const DEFAULT_RESOURCE_CONFIG: ResourceConfig = {
  defaultNamespace: 'common',
  namespaces: [
    { name: 'common', preload: true, priority: 'high' },
    { name: 'popup', preload: true, priority: 'high' },
    { name: 'options', preload: false, priority: 'medium' },
    { name: 'content', preload: false, priority: 'medium' }
  ],
  cacheStrategy: 'hybrid',
  maxCacheSize: 5 * 1024 * 1024, // 5MB
  ttl: 24 * 60 * 60 * 1000, // 24 hours
  compressionEnabled: true
};

/**
 * Initialize ResourceManager with default configuration
 */
export function createResourceManager(config?: Partial<ResourceConfig>) {
  return ResourceManager.getInstance(config);
}

/**
 * Get default ResourceManager instance
 */
export function getResourceManager() {
  return ResourceManager.getInstance();
}