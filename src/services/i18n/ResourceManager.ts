/**
 * ResourceManager - Advanced i18n Resource Management System
 * 
 * Features:
 * - Namespace-based organization for better code splitting
 * - Two-tier caching (memory + chrome.storage.local)
 * - Dynamic imports with webpack code splitting
 * - Type-safe translation keys
 * - Automatic cache invalidation on extension updates
 * - Cross-context synchronization
 */

import type { SupportedLanguage, TranslationResource } from '../../i18n/types';
import type {
  ResourceConfig,
  NamespaceLoadResult,
  CacheEntry,
  ResourceStats,
  NamespaceRegistry,
  NamespaceLoadOptions,
  BatchLoadResult,
  ResourceChangeEvent,
  ResourceImporter
} from './types';
import { ResourceError, ResourceErrorType, LoadingState } from './types';
import { StorageManager } from './StorageManager';
import { LRUCache } from './cache';

/**
 * Default resource configuration
 */
const DEFAULT_CONFIG: ResourceConfig = {
  defaultNamespace: 'common',
  namespaces: [
    { name: 'common', preload: true, priority: 'high' },
    { name: 'popup', preload: true, priority: 'high' },
    { name: 'options', preload: false, priority: 'medium' },
    { name: 'content', preload: false, priority: 'medium' }
  ],
  cacheStrategy: 'hybrid',
  maxCacheSize: 5 * 1024 * 1024, // 5MB
  ttl: 24 * 60 * 60 * 1000, // 24 hours
  compressionEnabled: true
};

export class ResourceManager {
  private static instance: ResourceManager;
  
  // Core components
  private config: ResourceConfig;
  private memoryCache: LRUCache<CacheEntry>;
  private storageManager: StorageManager;
  private namespaceRegistry: NamespaceRegistry = new Map();
  private loadingStates: Map<string, LoadingState> = new Map();
  private loadingPromises: Map<string, Promise<TranslationResource>> = new Map();
  
  // Statistics tracking
  private stats: ResourceStats = {
    namespacesLoaded: 0,
    totalCacheSize: 0,
    cacheHitRate: 0,
    averageLoadTime: 0,
    memoryUsage: 0,
    storageUsage: 0
  };
  
  // Event listeners
  private eventListeners: Map<string, Set<(event: ResourceChangeEvent) => void>> = new Map();
  
  // Resource importers for dynamic loading
  private importers: Map<string, ResourceImporter> = new Map();
  
  private constructor(config: Partial<ResourceConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.memoryCache = new LRUCache<CacheEntry>({
      maxSize: this.config.maxCacheSize,
      maxAge: this.config.ttl,
      updateAgeOnGet: true
    });
    this.storageManager = new StorageManager('i18n_resources');
    
    this.initialize();
  }
  
  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<ResourceConfig>): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager(config);
    }
    return ResourceManager.instance;
  }
  
  /**
   * Initialize the resource manager
   */
  private async initialize(): Promise<void> {
    // Set up extension update listener for cache invalidation
    if (typeof chrome !== 'undefined' && chrome?.runtime) {
      try {
        // Note: onInstalled might not be available in all contexts
        if ((chrome.runtime as any).onInstalled) {
          (chrome.runtime as any).onInstalled.addListener(async (details: any) => {
            if (details.reason === 'update') {
              await this.invalidateCache();
            }
          });
        }
      } catch (error) {
        console.warn('Could not set up runtime listener:', error);
      }
    }
    
    // Set up storage change listener for cross-context sync
    if (typeof chrome !== 'undefined' && chrome?.storage) {
      try {
        if ((chrome.storage as any).onChanged) {
          (chrome.storage as any).onChanged.addListener((changes: any, areaName: string) => {
            if (areaName === 'local' && changes.i18n_resources) {
              this.handleStorageChange(changes.i18n_resources);
            }
          });
        }
      } catch (error) {
        console.warn('Could not set up storage listener:', error);
      }
    }
    
    // Register default importers
    this.registerDefaultImporters();
    
    // Preload critical namespaces
    await this.preloadNamespaces();
  }
  
  /**
   * Register default resource importers for dynamic loading
   */
  private registerDefaultImporters(): void {
    // Register importers for each namespace
    this.config.namespaces.forEach(nsConfig => {
      this.registerImporter(nsConfig.name, async (namespace, language) => {
        // Dynamic import with webpack magic comments for code splitting
        try {
          const module = await import(
            /* webpackChunkName: "i18n-[request]" */
            /* webpackMode: "lazy" */
            `../../i18n/locales/${language}/${namespace}.json`
          );
          return module.default || module;
        } catch (error) {
          // Fallback to combined locale file if namespace-specific file doesn't exist
          const fallbackModule = await import(
            /* webpackChunkName: "i18n-fallback-[request]" */
            `../../i18n/locales/${language}.ts`
          );
          return fallbackModule.default?.[namespace] || {};
        }
      });
    });
  }
  
  /**
   * Register a custom importer for a namespace
   */
  public registerImporter(namespace: string, importer: ResourceImporter): void {
    this.importers.set(namespace, importer);
  }
  
  /**
   * Load a namespace for a specific language
   */
  public async loadNamespace(
    namespace: string,
    language: SupportedLanguage,
    options: NamespaceLoadOptions = {}
  ): Promise<TranslationResource> {
    const cacheKey = `${namespace}:${language}`;
    const startTime = performance.now();
    
    try {
      // Check memory cache first
      const memoryCached = this.memoryCache.get(cacheKey);
      if (memoryCached && !options.forceReload) {
        this.updateStats('hit', performance.now() - startTime);
        return memoryCached.data;
      }
      
      // Check if already loading
      const existingPromise = this.loadingPromises.get(cacheKey);
      if (existingPromise) {
        return existingPromise;
      }
      
      // Set loading state
      this.setLoadingState(namespace, LoadingState.LOADING);
      
      // Create loading promise
      const loadPromise = this.performLoad(namespace, language, options);
      this.loadingPromises.set(cacheKey, loadPromise);
      
      const result = await loadPromise;
      
      this.setLoadingState(namespace, LoadingState.LOADED);
      this.loadingPromises.delete(cacheKey);
      this.updateStats('miss', performance.now() - startTime);
      
      return result;
    } catch (error) {
      this.setLoadingState(namespace, LoadingState.ERROR);
      this.loadingPromises.delete(cacheKey);
      throw new ResourceError(
        ResourceErrorType.LOADING_FAILED,
        `Failed to load namespace ${namespace} for ${language}`,
        namespace,
        language,
        error as Error
      );
    }
  }
  
  /**
   * Perform the actual resource loading
   */
  private async performLoad(
    namespace: string,
    language: SupportedLanguage,
    options: NamespaceLoadOptions
  ): Promise<TranslationResource> {
    const cacheKey = `${namespace}:${language}`;
    
    // Try storage cache if not force reload
    if (!options.forceReload && this.config.cacheStrategy !== 'memory') {
      try {
        const storageCached = await this.storageManager.get(cacheKey);
        if (storageCached && this.isValidCache(storageCached)) {
          // Populate memory cache
          this.memoryCache.set(cacheKey, storageCached);
          return storageCached.data;
        }
      } catch (error) {
        console.warn('Storage cache read failed:', error);
      }
    }
    
    // Load dependencies first if requested
    if (options.includeDependencies) {
      const nsConfig = this.config.namespaces.find(ns => ns.name === namespace);
      if (nsConfig?.dependencies) {
        await Promise.all(
          nsConfig.dependencies.map(dep => 
            this.loadNamespace(dep, language, { ...options, includeDependencies: false })
          )
        );
      }
    }
    
    // Load from importer
    const importer = this.importers.get(namespace);
    if (!importer) {
      throw new ResourceError(
        ResourceErrorType.NAMESPACE_NOT_FOUND,
        `No importer registered for namespace: ${namespace}`,
        namespace,
        language
      );
    }
    
    const data = await this.loadWithTimeout(
      () => importer(namespace, language),
      options.timeout || 5000
    );
    
    // Create cache entry
    const cacheEntry: CacheEntry = {
      namespace,
      language,
      data,
      timestamp: Date.now(),
      size: this.calculateSize(data),
      accessCount: 1,
      lastAccess: Date.now()
    };
    
    // Store in caches
    this.memoryCache.set(cacheKey, cacheEntry);
    
    if (this.config.cacheStrategy !== 'memory') {
      await this.storageManager.set(cacheKey, cacheEntry).catch(error => {
        console.warn('Storage cache write failed:', error);
      });
    }
    
    // Update registry
    if (!this.namespaceRegistry.has(namespace)) {
      this.namespaceRegistry.set(namespace, new Map());
    }
    this.namespaceRegistry.get(namespace)!.set(language, data);
    
    // Emit event
    this.emitEvent({
      type: 'loaded',
      namespace,
      language,
      timestamp: Date.now()
    });
    
    return data;
  }
  
  /**
   * Load with timeout
   */
  private async loadWithTimeout<T>(
    loader: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return Promise.race([
      loader(),
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error('Load timeout')), timeout)
      )
    ]);
  }
  
  /**
   * Get a translation value by key
   */
  public getTranslation(
    key: string,
    language: SupportedLanguage,
    defaultValue?: string
  ): string | undefined {
    const [namespace, ...keyParts] = key.split('.');
    const fullKey = keyParts.join('.');
    
    const namespaceData = this.namespaceRegistry.get(namespace)?.get(language);
    if (!namespaceData) {
      return defaultValue;
    }
    
    return this.getNestedValue(namespaceData, fullKey) || defaultValue;
  }
  
  /**
   * Get nested value from object
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
  
  /**
   * Batch load multiple namespaces
   */
  public async loadBatch(
    namespaces: string[],
    language: SupportedLanguage,
    options: NamespaceLoadOptions = {}
  ): Promise<BatchLoadResult> {
    const startTime = performance.now();
    const results = await Promise.allSettled(
      namespaces.map(ns => this.loadNamespace(ns, language, options))
    );
    
    const successful: NamespaceLoadResult[] = [];
    const failed: NamespaceLoadResult[] = [];
    
    results.forEach((result, index) => {
      const namespace = namespaces[index];
      if (result.status === 'fulfilled') {
        successful.push({
          namespace,
          language,
          state: LoadingState.LOADED,
          data: result.value
        });
      } else {
        failed.push({
          namespace,
          language,
          state: LoadingState.ERROR,
          error: result.reason
        });
      }
    });
    
    return {
      successful,
      failed,
      totalTime: performance.now() - startTime
    };
  }
  
  /**
   * Preload critical namespaces
   */
  private async preloadNamespaces(): Promise<void> {
    const preloadConfigs = this.config.namespaces.filter(ns => ns.preload);
    const language = await this.getCurrentLanguage();
    
    await Promise.all(
      preloadConfigs.map(config => 
        this.loadNamespace(config.name, language).catch(error => {
          console.error(`Failed to preload namespace ${config.name}:`, error);
        })
      )
    );
  }
  
  /**
   * Get current language from chrome storage or default
   */
  private async getCurrentLanguage(): Promise<SupportedLanguage> {
    try {
      const result = await chrome.storage.local.get('currentLanguage');
      return result.currentLanguage || 'en';
    } catch {
      return 'en';
    }
  }
  
  /**
   * Invalidate all caches (e.g., on extension update)
   */
  public async invalidateCache(): Promise<void> {
    this.memoryCache.clear();
    await this.storageManager.clear();
    this.namespaceRegistry.clear();
    this.loadingStates.clear();
    this.stats = {
      namespacesLoaded: 0,
      totalCacheSize: 0,
      cacheHitRate: 0,
      averageLoadTime: 0,
      memoryUsage: 0,
      storageUsage: 0
    };
  }
  
  /**
   * Handle storage changes for cross-context sync
   */
  private handleStorageChange(change: any): void {
    if (!change.newValue) return;
    
    // Sync memory cache with storage changes from other contexts
    Object.entries(change.newValue).forEach(([key, value]) => {
      if (this.isValidCache(value as CacheEntry)) {
        this.memoryCache.set(key, value as CacheEntry);
      }
    });
  }
  
  /**
   * Check if cache entry is valid
   */
  private isValidCache(entry: CacheEntry): boolean {
    if (!entry || !entry.timestamp) return false;
    
    if (this.config.ttl) {
      return Date.now() - entry.timestamp < this.config.ttl;
    }
    
    return true;
  }
  
  /**
   * Calculate size of data
   */
  private calculateSize(data: any): number {
    return JSON.stringify(data).length * 2; // Rough estimate in bytes
  }
  
  /**
   * Set loading state
   */
  private setLoadingState(namespace: string, state: LoadingState): void {
    this.loadingStates.set(namespace, state);
  }
  
  /**
   * Get loading state
   */
  public getLoadingState(namespace: string): LoadingState {
    return this.loadingStates.get(namespace) || LoadingState.IDLE;
  }
  
  /**
   * Update statistics
   */
  private updateStats(type: 'hit' | 'miss', loadTime: number): void {
    const { cacheHitRate, averageLoadTime, namespacesLoaded } = this.stats;
    
    if (type === 'hit') {
      this.stats.cacheHitRate = (cacheHitRate * namespacesLoaded + 1) / (namespacesLoaded + 1);
    } else {
      this.stats.cacheHitRate = (cacheHitRate * namespacesLoaded) / (namespacesLoaded + 1);
      this.stats.namespacesLoaded++;
    }
    
    this.stats.averageLoadTime = (averageLoadTime * namespacesLoaded + loadTime) / (namespacesLoaded + 1);
    this.stats.memoryUsage = this.memoryCache.getSize();
    this.stats.totalCacheSize = this.stats.memoryUsage + this.stats.storageUsage;
  }
  
  /**
   * Get resource statistics
   */
  public getStats(): ResourceStats {
    return { ...this.stats };
  }
  
  /**
   * Add event listener
   */
  public addEventListener(
    event: 'loaded' | 'unloaded' | 'updated' | 'error',
    listener: (event: ResourceChangeEvent) => void
  ): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(listener);
  }
  
  /**
   * Remove event listener
   */
  public removeEventListener(
    event: string,
    listener: (event: ResourceChangeEvent) => void
  ): void {
    this.eventListeners.get(event)?.delete(listener);
  }
  
  /**
   * Emit event
   */
  private emitEvent(event: ResourceChangeEvent): void {
    this.eventListeners.get(event.type)?.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Event listener error:', error);
      }
    });
  }
  
  /**
   * Get loaded namespaces
   */
  public getLoadedNamespaces(): string[] {
    return Array.from(this.namespaceRegistry.keys());
  }
  
  /**
   * Check if namespace is loaded
   */
  public isNamespaceLoaded(namespace: string, language: SupportedLanguage): boolean {
    return this.namespaceRegistry.get(namespace)?.has(language) || false;
  }
  
  /**
   * Release unused namespaces
   */
  public releaseNamespace(namespace: string, language?: SupportedLanguage): void {
    if (language) {
      const cacheKey = `${namespace}:${language}`;
      this.memoryCache.delete(cacheKey);
      this.namespaceRegistry.get(namespace)?.delete(language);
      
      this.emitEvent({
        type: 'unloaded',
        namespace,
        language,
        timestamp: Date.now()
      });
    } else {
      // Release all languages for the namespace
      const languages = this.namespaceRegistry.get(namespace)?.keys() || [];
      for (const lang of languages) {
        this.releaseNamespace(namespace, lang as SupportedLanguage);
      }
      this.namespaceRegistry.delete(namespace);
    }
  }
}