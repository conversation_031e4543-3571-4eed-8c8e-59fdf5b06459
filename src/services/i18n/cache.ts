/**
 * LRU Cache Implementation for i18n Resources
 * 
 * Features:
 * - Size-based eviction
 * - Time-based expiration
 * - Access count tracking
 * - Memory efficient
 */

import type { LRUConfig } from './types';

interface CacheNode<T> {
  key: string;
  value: T;
  size: number;
  timestamp: number;
  accessCount: number;
  prev: CacheNode<T> | null;
  next: CacheNode<T> | null;
}

export class LRUCache<T extends { size: number }> {
  private cache: Map<string, CacheNode<T>> = new Map();
  private head: CacheNode<T> | null = null;
  private tail: CacheNode<T> | null = null;
  private currentSize = 0;
  private config: Required<LRUConfig>;
  
  constructor(config: LRUConfig) {
    this.config = {
      maxSize: config.maxSize,
      maxAge: config.maxAge || Infinity,
      updateAgeOnGet: config.updateAgeOnGet ?? false,
      stale: config.stale ?? false
    };
  }
  
  /**
   * Get value from cache
   */
  public get(key: string): T | undefined {
    const node = this.cache.get(key);
    if (!node) return undefined;
    
    // Check if expired
    if (this.isExpired(node) && !this.config.stale) {
      this.remove(key);
      return undefined;
    }
    
    // Update access time if configured
    if (this.config.updateAgeOnGet) {
      node.timestamp = Date.now();
    }
    
    // Update access count and move to head
    node.accessCount++;
    this.moveToHead(node);
    
    return node.value;
  }
  
  /**
   * Set value in cache
   */
  public set(key: string, value: T): void {
    // Remove existing if present
    if (this.cache.has(key)) {
      this.remove(key);
    }
    
    // Check if we need to evict
    while (this.currentSize + value.size > this.config.maxSize && this.tail) {
      this.removeLRU();
    }
    
    // Create new node
    const node: CacheNode<T> = {
      key,
      value,
      size: value.size,
      timestamp: Date.now(),
      accessCount: 1,
      prev: null,
      next: this.head
    };
    
    // Add to cache
    this.cache.set(key, node);
    this.currentSize += value.size;
    
    // Update linked list
    if (this.head) {
      this.head.prev = node;
    }
    this.head = node;
    
    if (!this.tail) {
      this.tail = node;
    }
  }
  
  /**
   * Check if key exists in cache
   */
  public has(key: string): boolean {
    const node = this.cache.get(key);
    if (!node) return false;
    
    if (this.isExpired(node) && !this.config.stale) {
      this.remove(key);
      return false;
    }
    
    return true;
  }
  
  /**
   * Delete key from cache
   */
  public delete(key: string): boolean {
    return this.remove(key);
  }
  
  /**
   * Clear entire cache
   */
  public clear(): void {
    this.cache.clear();
    this.head = null;
    this.tail = null;
    this.currentSize = 0;
  }
  
  /**
   * Get current cache size
   */
  public getSize(): number {
    return this.currentSize;
  }
  
  /**
   * Get number of items in cache
   */
  public getCount(): number {
    return this.cache.size;
  }
  
  /**
   * Get all keys
   */
  public keys(): string[] {
    // Clean up expired entries first
    if (!this.config.stale) {
      this.cleanupExpired();
    }
    return Array.from(this.cache.keys());
  }
  
  /**
   * Get cache statistics
   */
  public getStats() {
    let totalAccessCount = 0;
    let expiredCount = 0;
    
    for (const node of this.cache.values()) {
      totalAccessCount += node.accessCount;
      if (this.isExpired(node)) {
        expiredCount++;
      }
    }
    
    return {
      size: this.currentSize,
      count: this.cache.size,
      maxSize: this.config.maxSize,
      utilization: (this.currentSize / this.config.maxSize) * 100,
      averageAccessCount: this.cache.size > 0 ? totalAccessCount / this.cache.size : 0,
      expiredCount
    };
  }
  
  /**
   * Remove item from cache
   */
  private remove(key: string): boolean {
    const node = this.cache.get(key);
    if (!node) return false;
    
    // Update size
    this.currentSize -= node.value.size;
    
    // Remove from map
    this.cache.delete(key);
    
    // Update linked list
    if (node.prev) {
      node.prev.next = node.next;
    } else {
      this.head = node.next;
    }
    
    if (node.next) {
      node.next.prev = node.prev;
    } else {
      this.tail = node.prev;
    }
    
    return true;
  }
  
  /**
   * Remove least recently used item
   */
  private removeLRU(): void {
    if (!this.tail) return;
    this.remove(this.tail.key);
  }
  
  /**
   * Move node to head of list
   */
  private moveToHead(node: CacheNode<T>): void {
    if (node === this.head) return;
    
    // Remove from current position
    if (node.prev) {
      node.prev.next = node.next;
    }
    
    if (node.next) {
      node.next.prev = node.prev;
    } else {
      this.tail = node.prev;
    }
    
    // Move to head
    node.prev = null;
    node.next = this.head;
    
    if (this.head) {
      this.head.prev = node;
    }
    
    this.head = node;
  }
  
  /**
   * Check if node is expired
   */
  private isExpired(node: CacheNode<T>): boolean {
    if (this.config.maxAge === Infinity) return false;
    return Date.now() - node.timestamp > this.config.maxAge;
  }
  
  /**
   * Clean up expired entries
   */
  private cleanupExpired(): void {
    const expiredKeys: string[] = [];
    
    for (const [key, node] of this.cache.entries()) {
      if (this.isExpired(node)) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.remove(key));
  }
  
  /**
   * Get least recently used items
   */
  public getLRUItems(count: number): Array<{ key: string; value: T }> {
    const items: Array<{ key: string; value: T }> = [];
    let current = this.tail;
    
    while (current && items.length < count) {
      items.push({ key: current.key, value: current.value });
      current = current.prev;
    }
    
    return items;
  }
  
  /**
   * Get most recently used items
   */
  public getMRUItems(count: number): Array<{ key: string; value: T }> {
    const items: Array<{ key: string; value: T }> = [];
    let current = this.head;
    
    while (current && items.length < count) {
      items.push({ key: current.key, value: current.value });
      current = current.next;
    }
    
    return items;
  }
  
  /**
   * Resize cache (may trigger evictions)
   */
  public resize(newMaxSize: number): void {
    this.config.maxSize = newMaxSize;
    
    // Evict if necessary
    while (this.currentSize > this.config.maxSize && this.tail) {
      this.removeLRU();
    }
  }
  
  /**
   * Update max age
   */
  public setMaxAge(maxAge: number): void {
    this.config.maxAge = maxAge;
    
    // Clean up expired entries if stricter
    if (!this.config.stale) {
      this.cleanupExpired();
    }
  }
}