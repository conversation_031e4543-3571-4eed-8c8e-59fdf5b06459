/**
 * Search Configuration Listener
 * 
 * Provides real-time configuration change notifications for search engines.
 * This ensures that configuration changes are immediately applied across
 * all search components without requiring page refresh.
 * 
 * @module SearchConfigListener
 * @version 1.0
 * @since 2025-06-29
 */

import { searchConfigService, type UnifiedSearchConfig } from './search-config.service';

export interface SearchConfigChangeEvent {
  type: 'engine-config' | 'behavior-config' | 'full-config';
  config: UnifiedSearchConfig;
  timestamp: number;
}

export type SearchConfigChangeListener = (event: SearchConfigChangeEvent) => void;

class SearchConfigListenerService {
  private static instance: SearchConfigListenerService;
  private listeners: Map<string, SearchConfigChangeListener> = new Map();
  private initialized = false;

  private constructor() {}

  static getInstance(): SearchConfigListenerService {
    if (!SearchConfigListenerService.instance) {
      SearchConfigListenerService.instance = new SearchConfigListenerService();
    }
    return SearchConfigListenerService.instance;
  }

  /**
   * Initialize the listener service
   */
  initialize(): void {
    if (this.initialized) return;

    // Listen for configuration changes from the service
    searchConfigService.addConfigChangeListener(this.handleConfigChange.bind(this));
    
    this.initialized = true;
    console.log('[SearchConfigListener] Initialized');
  }

  /**
   * Add a configuration change listener
   */
  addListener(id: string, listener: SearchConfigChangeListener): void {
    this.listeners.set(id, listener);
    console.log(`[SearchConfigListener] Added listener: ${id}`);
  }

  /**
   * Remove a configuration change listener
   */
  removeListener(id: string): void {
    this.listeners.delete(id);
    console.log(`[SearchConfigListener] Removed listener: ${id}`);
  }

  /**
   * Handle configuration changes from the service
   */
  private handleConfigChange(config: UnifiedSearchConfig): void {
    const event: SearchConfigChangeEvent = {
      type: 'full-config',
      config,
      timestamp: Date.now()
    };

    console.log('[SearchConfigListener] Broadcasting config change to', this.listeners.size, 'listeners');
    
    // Notify all listeners
    this.listeners.forEach((listener, id) => {
      try {
        listener(event);
      } catch (error) {
        console.error(`[SearchConfigListener] Error in listener ${id}:`, error);
      }
    });
  }

  /**
   * Trigger a manual configuration reload for all listeners
   */
  async triggerReload(): Promise<void> {
    try {
      const config = await searchConfigService.getUnifiedConfig();
      this.handleConfigChange(config);
    } catch (error) {
      console.error('[SearchConfigListener] Failed to trigger reload:', error);
    }
  }

  /**
   * Get current listener count (for debugging)
   */
  getListenerCount(): number {
    return this.listeners.size;
  }

  /**
   * Get listener IDs (for debugging)
   */
  getListenerIds(): string[] {
    return Array.from(this.listeners.keys());
  }
}

// Export singleton instance
export const searchConfigListener = SearchConfigListenerService.getInstance();

// Auto-initialize
searchConfigListener.initialize();