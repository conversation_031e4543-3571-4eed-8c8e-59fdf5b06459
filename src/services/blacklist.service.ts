/**
 * Blacklist Service
 * 
 * Manages domain blacklist for preventing page indexing
 */

import type { BlacklistEntry, DomainInfo } from '../models';
import { DB_CONFIG, DBError, DB_ERROR_CODES } from '../models';
import { IndexedDBService } from './db.service';
import { logger } from '../utils/logger';

/**
 * Blacklist Service Class
 * 
 * Provides CRUD operations for managing blacklisted domains
 */
export class BlacklistService {
  private static instance: BlacklistService;
  private dbService: IndexedDBService;

  private constructor() {
    this.dbService = IndexedDBService.getInstance();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): BlacklistService {
    if (!BlacklistService.instance) {
      BlacklistService.instance = new BlacklistService();
    }
    return BlacklistService.instance;
  }

  /**
   * Reset singleton instance (for testing)
   */
  public static reset(): void {
    BlacklistService.instance = undefined as any;
  }

  /**
   * Initialize the service
   */
  public async init(): Promise<void> {
    await this.dbService.init();
  }

  /**
   * Add a domain to the blacklist
   * @param domain - Domain to blacklist
   * @param reason - Optional reason for blacklisting
   * @param isWildcard - Whether to use wildcard matching
   */
  public async addDomain(domain: string, reason?: string, isWildcard: boolean = false): Promise<void> {
    if (!domain || typeof domain !== 'string') {
      throw new DBError(
        'Invalid domain provided',
        DB_ERROR_CODES.VALIDATION_FAILED
      );
    }

    // Normalize domain
    const normalizedDomain = this.normalizeDomain(domain);
    
    const entry: BlacklistEntry = {
      domain: normalizedDomain,
      createdAt: Date.now(),
      reason,
      isWildcard
    };

    await this.dbService.ensureReady();

    const transaction = this.dbService.createPublicTransaction(DB_CONFIG.stores.blacklist, 'readwrite');
    const store = transaction.objectStore(DB_CONFIG.stores.blacklist);

    try {
      await this.dbService.promiseRequestPublic(store.put(entry));
      logger.info(`Added domain to blacklist: ${normalizedDomain}`);
    } catch (error) {
      throw new DBError(
        `Failed to add domain to blacklist: ${normalizedDomain}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Remove a domain from the blacklist
   * @param domain - Domain to remove
   */
  public async removeDomain(domain: string): Promise<void> {
    if (!domain || typeof domain !== 'string') {
      throw new DBError(
        'Invalid domain provided',
        DB_ERROR_CODES.VALIDATION_FAILED
      );
    }

    const normalizedDomain = this.normalizeDomain(domain);

    await this.dbService.ensureReady();

    const transaction = this.dbService.createPublicTransaction(DB_CONFIG.stores.blacklist, 'readwrite');
    const store = transaction.objectStore(DB_CONFIG.stores.blacklist);

    try {
      await this.dbService.promiseRequestPublic(store.delete(normalizedDomain));
      logger.info(`Removed domain from blacklist: ${normalizedDomain}`);
    } catch (error) {
      throw new DBError(
        `Failed to remove domain from blacklist: ${normalizedDomain}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Check if a domain is blacklisted
   * @param domain - Domain to check
   * @returns True if domain is blacklisted
   */
  public async isDomainBlacklisted(domain: string): Promise<boolean> {
    if (!domain || typeof domain !== 'string') {
      return false;
    }

    const normalizedDomain = this.normalizeDomain(domain);

    await this.dbService.ensureReady();

    const transaction = this.dbService.createPublicTransaction(DB_CONFIG.stores.blacklist);
    const store = transaction.objectStore(DB_CONFIG.stores.blacklist);

    try {
      // Check for exact match first
      const exactMatch = await this.dbService.promiseRequestPublic(store.get(normalizedDomain));
      if (exactMatch) {
        return true;
      }

      // Check for wildcard matches
      const allEntries = await this.dbService.promiseRequestPublic(store.getAll());
      
      for (const entry of allEntries) {
        if (entry.isWildcard && this.matchesWildcard(normalizedDomain, entry.domain)) {
          return true;
        }
      }

      return false;
    } catch (error) {
      logger.error(error as Error, `Error checking domain: ${normalizedDomain}`);
      return false; // Fail open - don't block if there's an error
    }
  }

  /**
   * Get all blacklisted domains
   * @returns Array of blacklist entries
   */
  public async getAllDomains(): Promise<BlacklistEntry[]> {
    await this.dbService.ensureReady();

    const transaction = this.dbService.createPublicTransaction(DB_CONFIG.stores.blacklist);
    const store = transaction.objectStore(DB_CONFIG.stores.blacklist);

    try {
      const result = await this.dbService.promiseRequestPublic(store.getAll());
      return result.sort((a, b) => b.createdAt - a.createdAt); // Sort by newest first
    } catch (error) {
      throw new DBError(
        'Failed to get blacklisted domains',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get blacklist entry for a specific domain
   * @param domain - Domain to get entry for
   * @returns Blacklist entry or null if not found
   */
  public async getDomainEntry(domain: string): Promise<BlacklistEntry | null> {
    if (!domain || typeof domain !== 'string') {
      return null;
    }

    const normalizedDomain = this.normalizeDomain(domain);

    await this.dbService.ensureReady();

    const transaction = this.dbService.createPublicTransaction(DB_CONFIG.stores.blacklist);
    const store = transaction.objectStore(DB_CONFIG.stores.blacklist);

    try {
      const result = await this.dbService.promiseRequestPublic(store.get(normalizedDomain));
      return result || null;
    } catch (error) {
      throw new DBError(
        `Failed to get blacklist entry for domain: ${normalizedDomain}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Update a blacklist entry
   * @param domain - Domain to update
   * @param updates - Partial updates to apply
   */
  public async updateDomain(domain: string, updates: Partial<Omit<BlacklistEntry, 'domain'>>): Promise<void> {
    const existing = await this.getDomainEntry(domain);
    if (!existing) {
      throw new DBError(
        `Domain not found in blacklist: ${domain}`,
        DB_ERROR_CODES.NOT_FOUND
      );
    }

    const updated: BlacklistEntry = {
      ...existing,
      ...updates
    };

    await this.dbService.ensureReady();

    const transaction = this.dbService.createPublicTransaction(DB_CONFIG.stores.blacklist, 'readwrite');
    const store = transaction.objectStore(DB_CONFIG.stores.blacklist);

    try {
      await this.dbService.promiseRequestPublic(store.put(updated));
      logger.info(`Updated blacklist entry: ${domain}`);
    } catch (error) {
      throw new DBError(
        `Failed to update blacklist entry: ${domain}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Clear all blacklisted domains
   */
  public async clearAll(): Promise<void> {
    await this.dbService.ensureReady();

    const transaction = this.dbService.createPublicTransaction(DB_CONFIG.stores.blacklist, 'readwrite');
    const store = transaction.objectStore(DB_CONFIG.stores.blacklist);

    try {
      await this.dbService.promiseRequestPublic(store.clear());
      logger.info('Cleared all blacklisted domains');
    } catch (error) {
      throw new DBError(
        'Failed to clear blacklist',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get blacklist statistics
   */
  public async getStats(): Promise<{ totalDomains: number; wildcardDomains: number }> {
    const domains = await this.getAllDomains();
    
    return {
      totalDomains: domains.length,
      wildcardDomains: domains.filter(entry => entry.isWildcard).length
    };
  }

  /**
   * Get all unique domains from indexed pages with blacklist status
   * @returns Array of DomainInfo with blacklist status checked
   */
  public async getIndexedDomainsWithStatus(): Promise<DomainInfo[]> {
    try {
      // Get domains from database
      const domains = await this.dbService.getUniqueDomains();
      
      // Check blacklist status for each domain
      const domainsWithStatus = await Promise.all(
        domains.map(async (domain) => ({
          ...domain,
          isBlocked: await this.isDomainBlacklisted(domain.domain)
        }))
      );
      
      return domainsWithStatus;
    } catch (error) {
      logger.error(error as Error, 'Failed to get indexed domains with status');
      throw error;
    }
  }

  /**
   * Normalize domain for consistent storage and comparison
   * @param domain - Raw domain string
   * @returns Normalized domain
   */
  private normalizeDomain(domain: string): string {
    return domain
      .toLowerCase()
      .replace(/^https?:\/\//, '') // Remove protocol
      .replace(/^www\./, '')       // Remove www prefix
      .replace(/\/$/, '')          // Remove trailing slash
      .trim();
  }

  /**
   * Check if a domain matches a wildcard pattern
   * @param domain - Domain to check
   * @param pattern - Wildcard pattern
   * @returns True if domain matches pattern
   */
  private matchesWildcard(domain: string, pattern: string): boolean {
    // Simple wildcard matching - supports *.example.com patterns
    if (pattern.startsWith('*.')) {
      const baseDomain = pattern.substring(2);
      return domain === baseDomain || domain.endsWith('.' + baseDomain);
    }
    
    // Exact match for non-wildcard patterns
    return domain === pattern;
  }
}

// Export singleton instance
export const blacklistService = BlacklistService.getInstance();
