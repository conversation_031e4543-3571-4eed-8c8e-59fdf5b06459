# IndexedDB 服务模块

这个模块提供了 Recall 扩展的完整数据库操作功能。

## 功能特性

- ✅ **完整的 CRUD 操作**: 支持页面和设置数据的增删改查
- ✅ **类型安全**: 使用 TypeScript 确保类型安全
- ✅ **错误处理**: 完善的错误处理和异常管理
- ✅ **单例模式**: 确保数据库连接的唯一性
- ✅ **事务支持**: 保证数据操作的一致性
- ✅ **批量操作**: 支持高效的批量数据处理
- ✅ **数据验证**: 自动验证和清理输入数据
- ✅ **导入导出**: 支持数据备份和恢复

## 快速开始

### 基本使用

```typescript
import { dbService } from '../services';

// 初始化数据库
await dbService.init();

// 添加页面
const page = await dbService.addPage({
  url: 'https://example.com',
  title: 'Example Page',
  content: 'Page content here...'
});

// 查询页面
const allPages = await dbService.getAllPages();
const pageById = await dbService.getPage(page.id);
const pageByUrl = await dbService.getPageByUrl('https://example.com');

// 设置配置
await dbService.setSetting('theme', 'dark');
const theme = await dbService.getSetting('theme');
```

### 高级功能

```typescript
// 时间范围查询
const now = Date.now();
const oneWeekAgo = now - (7 * 24 * 60 * 60 * 1000);
const recentPages = await dbService.getPagesByTimeRange(oneWeekAgo, now);

// 域名过滤
const githubPages = await dbService.getPagesByDomain('github.com');

// 批量删除
const pageIds = ['id1', 'id2', 'id3'];
const deletedCount = await dbService.deletePages(pageIds);

// 获取存储信息
const storageInfo = await dbService.getStorageInfo();
console.log(`存储了 ${storageInfo.pagesCount} 个页面`);

// 数据导出
const exportData = await dbService.exportData();

// 数据导入
await dbService.importData(exportData);

// 健康检查
const health = await dbService.healthCheck();
if (!health.isHealthy) {
  console.warn('数据库健康检查失败:', health.issues);
}
```

## API 参考

### 页面操作

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `addPage(data)` | 添加或更新页面 | `{url, title, content}` | `Promise<Page>` |
| `getPage(id)` | 根据ID获取页面 | `string` | `Promise<Page \| null>` |
| `getPageByUrl(url)` | 根据URL获取页面 | `string` | `Promise<Page \| null>` |
| `updatePage(id, updates)` | 更新页面 | `string, Partial<Page>` | `Promise<Page>` |
| `deletePage(id)` | 删除页面 | `string` | `Promise<boolean>` |
| `getAllPages()` | 获取所有页面 | - | `Promise<Page[]>` |
| `getPagesByDomain(domain)` | 根据域名获取页面 | `string` | `Promise<Page[]>` |
| `getPagesByTimeRange(start, end)` | 根据时间范围获取页面 | `number, number` | `Promise<Page[]>` |
| `deletePages(ids)` | 批量删除页面 | `string[]` | `Promise<number>` |

### 设置操作

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `getSetting(key)` | 获取设置 | `string` | `Promise<any>` |
| `setSetting(key, value)` | 设置值 | `string, any` | `Promise<void>` |
| `deleteSetting(key)` | 删除设置 | `string` | `Promise<boolean>` |
| `getAllSettings()` | 获取所有设置 | - | `Promise<Record<string, any>>` |

### 工具方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `init()` | 初始化数据库 | - | `Promise<void>` |
| `getStorageInfo()` | 获取存储信息 | - | `Promise<StorageInfo>` |
| `clearAllData()` | 清空所有数据 | - | `Promise<void>` |
| `exportData()` | 导出数据 | - | `Promise<ExportData>` |
| `importData(data)` | 导入数据 | `ExportData` | `Promise<void>` |
| `healthCheck()` | 健康检查 | - | `Promise<HealthCheck>` |
| `close()` | 关闭数据库连接 | - | `void` |

## 数据结构

### Page 接口

```typescript
interface Page {
  id: string;           // 唯一标识符
  url: string;          // 页面URL
  title: string;        // 页面标题
  content: string;      // 页面内容
  domain: string;       // 网站域名
  visitTime: number;    // 访问时间戳
  accessCount: number;  // 访问次数
  lastUpdated: number;  // 最后更新时间
  language?: string;    // 页面语言（可选）
  summary?: string;     // 页面摘要（可选）
}
```

### Setting 接口

```typescript
interface Setting {
  key: string;          // 设置键
  value: any;           // 设置值
  createdAt: number;    // 创建时间
  updatedAt: number;    // 更新时间
}
```

## 错误处理

服务使用自定义的 `DBError` 类来处理错误：

```typescript
try {
  await dbService.addPage(invalidData);
} catch (error) {
  if (error instanceof DBError) {
    console.error('数据库错误:', error.message);
    console.error('错误代码:', error.code);
  }
}
```

常见错误代码：
- `DB_NOT_AVAILABLE`: 数据库不可用
- `STORE_NOT_FOUND`: 对象存储不存在
- `TRANSACTION_FAILED`: 事务失败
- `DUPLICATE_KEY`: 重复键
- `ITEM_NOT_FOUND`: 项目不存在
- `QUOTA_EXCEEDED`: 存储配额超限

## 测试

运行单元测试：

```bash
npm test src/services/db.service.test.ts
```

运行演示：

```typescript
import { runAllDemos } from '../services/db.demo';
await runAllDemos();
```

## 注意事项

1. **单例模式**: 使用 `dbService` 实例而不是直接创建 `IndexedDBService`
2. **初始化**: 在使用任何数据库操作前必须调用 `init()`
3. **错误处理**: 所有异步操作都应该包装在 try-catch 中
4. **性能**: 大量数据操作时考虑使用批量方法
5. **存储限制**: 注意浏览器的存储配额限制

## 最佳实践

1. **在应用启动时初始化数据库**
2. **使用类型安全的数据结构**
3. **实现适当的错误处理**
4. **定期进行健康检查**
5. **考虑数据备份和恢复策略**
