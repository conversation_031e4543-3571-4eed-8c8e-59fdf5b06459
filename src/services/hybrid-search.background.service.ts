/**
 * Background-only Hybrid Search Service
 * 
 * A lightweight version of hybrid search service specifically for background script.
 * Avoids importing UI-related dependencies that could cause createRoot errors.
 */

import type { Page } from '../models';
import { dbService } from './db.service';
import { backgroundLogger as logger } from '../utils';

/**
 * Background Hybrid Search Service Class
 * 
 * Provides minimal search functionality for background script operations.
 * Only handles index updates and basic search without UI dependencies.
 */
export class BackgroundHybridSearchService {
  private static instance: BackgroundHybridSearchService | null = null;
  private pages: Page[] = [];
  private isIndexing: boolean = false;
  private lastIndexUpdate: number = 0;

  private constructor() {
    // Private constructor for singleton
  }

  /**
   * Get service instance (singleton pattern)
   */
  public static getInstance(): BackgroundHybridSearchService {
    if (!BackgroundHybridSearchService.instance) {
      BackgroundHybridSearchService.instance = new BackgroundHybridSearchService();
    }
    return BackgroundHybridSearchService.instance;
  }

  /**
   * Initialize search service for background
   * Lightweight initialization without heavy dependencies
   */
  public async init(): Promise<void> {
    try {
      logger.info('Initializing background hybrid search service...');
      
      // Load a small subset of recent pages for basic functionality
      const result = await dbService.getPagesPaginated({
        limit: 50, // Much smaller than full service
        sortBy: 'visitTime',
        sortOrder: 'desc'
      });
      
      this.pages = result.items;
      this.lastIndexUpdate = Date.now();
      
      logger.info('Background hybrid search service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize background hybrid search service', error);
      // Don't throw - allow background script to continue
    }
  }

  /**
   * Add a page to the index (background operation)
   * Simplified version that just updates the pages array
   */
  public async addPageToIndex(page: Page): Promise<void> {
    try {
      if (!page || !page.id) {
        logger.warn('Invalid page data provided to addPageToIndex');
        return;
      }

      // Find existing page index
      const existingIndex = this.pages.findIndex(p => p.id === page.id);
      
      if (existingIndex !== -1) {
        // Update existing page
        this.pages[existingIndex] = page;
        logger.debug(`Updated page in background index: ${page.id}`);
      } else {
        // Add new page to beginning (most recent)
        this.pages.unshift(page);
        
        // Keep array size manageable
        if (this.pages.length > 100) {
          this.pages = this.pages.slice(0, 100);
        }
        
        logger.debug(`Added page to background index: ${page.id}`);
      }
      
      this.lastIndexUpdate = Date.now();
    } catch (error) {
      logger.error('Failed to add page to background index', error);
      // Don't throw - this shouldn't break page saving
    }
  }

  /**
   * Remove a page from the index
   */
  public async removePageFromIndex(pageId: string): Promise<void> {
    try {
      const initialLength = this.pages.length;
      this.pages = this.pages.filter(page => page.id !== pageId);
      
      if (this.pages.length !== initialLength) {
        this.lastIndexUpdate = Date.now();
        logger.debug(`Removed page from background index: ${pageId}`);
      }
    } catch (error) {
      logger.error('Failed to remove page from background index', error);
    }
  }

  /**
   * Get current index status
   */
  public getIndexStatus(): {
    pageCount: number;
    lastUpdate: number;
    isIndexing: boolean;
  } {
    return {
      pageCount: this.pages.length,
      lastUpdate: this.lastIndexUpdate,
      isIndexing: this.isIndexing
    };
  }

  /**
   * Clear the index
   */
  public clearIndex(): void {
    this.pages = [];
    this.lastIndexUpdate = Date.now();
    logger.info('Background search index cleared');
  }

  /**
   * Reset service instance (for testing)
   */
  public static reset(): void {
    if (BackgroundHybridSearchService.instance) {
      BackgroundHybridSearchService.instance.clearIndex();
      BackgroundHybridSearchService.instance = null;
    }
  }
}

// Export singleton instance
export const backgroundHybridSearchService = BackgroundHybridSearchService.getInstance();