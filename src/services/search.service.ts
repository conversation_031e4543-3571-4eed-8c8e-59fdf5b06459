/**
 * 搜索服务模块
 * 
 * 传统搜索引擎，整合 Fuse.js 模糊搜索和 Lunr.js 全文搜索功能
 */

import Fuse from 'fuse.js';
import type { Page, SearchOptions, SearchResultItem } from '../models';
import { dbService } from './db.service';
import {
  parseQuery,
  isValidQuery,
  buildFuseQuery,
  matchesSiteFilter,
  type ParsedQuery
} from './query-parser';

/**
 * Fuse.js 配置选项
 */
interface FuseConfig {
  /** 模糊匹配阈值 (0-1, 0为完全匹配) */
  threshold: number;
  /** 搜索的字段 */
  keys: Array<{
    name: string;
    weight: number;
  }>;
  /** 是否包含分数 */
  includeScore: boolean;
  /** 是否包含匹配信息 */
  includeMatches: boolean;
  /** 最小匹配字符长度 */
  minMatchCharLength: number;
  /** 搜索位置 */
  location: number;
  /** 搜索距离 */
  distance: number;
}

/**
 * 默认 Fuse.js 配置
 * 
 * 权重调整说明：
 * - title权重提升至0.5：标题通常最能代表页面核心内容
 * - content权重降至0.4：减少对正文内容的过度依赖
 * - url权重提升至0.15：URL路径信息对搜索很重要
 * - domain权重保持0.1：域名信息作为辅助参考
 */
const DEFAULT_FUSE_CONFIG: FuseConfig = {
  threshold: 0.3,
  keys: [
    { name: 'title', weight: 0.5 },    // 提升：0.4 → 0.5
    { name: 'content', weight: 0.4 },  // 降低：0.6 → 0.4
    { name: 'url', weight: 0.15 },     // 提升：0.1 → 0.15
    { name: 'domain', weight: 0.1 }    // 保持：0.1
  ],
  includeScore: true,
  includeMatches: true,
  minMatchCharLength: 2,
  location: 0,
  distance: 100
};

/**
 * 搜索结果
 */
export interface SearchResult {
  results: SearchResultItem[];
  totalResults: number;
  searchTime: number;
  searchMode: 'fuse' | 'fulltext';
  metadata?: {
    processingSteps: string[];
    fallbackUsed?: boolean;
  };
}

/**
 * 搜索服务类
 */
export class SearchService {
  private static instance: SearchService | null = null;
  private fuse: Fuse<Page> | null = null;
  private pages: Page[] = [];
  private lastIndexUpdate: number = 0;
  private isIndexing: boolean = false;
  private config: FuseConfig;
  
  // Suggestion caching and performance optimization
  private suggestionCache = new Map<string, { suggestions: string[]; timestamp: number }>();
  private readonly suggestionCacheExpiry = 5 * 60 * 1000; // 5 minutes
  private readonly maxCacheSize = 100; // Maximum cached queries

  private constructor(config: Partial<FuseConfig> = {}) {
    this.config = { ...DEFAULT_FUSE_CONFIG, ...config };
  }

  /**
   * 获取服务实例（单例模式）
   */
  public static getInstance(config?: Partial<FuseConfig>): SearchService {
    if (!SearchService.instance) {
      SearchService.instance = new SearchService(config);
    }
    return SearchService.instance;
  }

  /**
   * 初始化搜索服务
   */
  public async init(): Promise<void> {
    try {
      // 构建传统搜索索引
      await this.buildIndex();
      
      this.log('Search service initialized successfully', {
        fuseIndex: true
      });
    } catch (error) {
      this.log('Failed to initialize search service', error);
      throw error;
    }
  }

  /**
   * 构建搜索索引
   */
  public async buildIndex(): Promise<void> {
    if (this.isIndexing) {
      this.log('Index building already in progress');
      return;
    }

    this.isIndexing = true;

    try {
      this.log('Building search index with lazy loading...');
      
      // 首先加载最近的页面用于即时搜索（分页加载）
      const initialResult = await dbService.getPagesPaginated({
        limit: 200, // 加载最近200个页面
        sortBy: 'visitTime',
        sortOrder: 'desc'
      });
      
      this.pages = initialResult.items;
      
      // 创建 Fuse 实例
      this.fuse = new Fuse(this.pages, this.config);
      
      this.lastIndexUpdate = Date.now();
      
      // Clean expired suggestion cache when rebuilding index
      this.cleanExpiredSuggestionCache();
      
      this.log(`Initial search index built with ${this.pages.length} pages`);
      
      // 如果还有更多页面，在后台继续加载
      if (initialResult.hasMore) {
        this.loadRemainingPagesInBackground(initialResult.nextCursor);
      }
      
    } catch (error) {
      this.log('Failed to build search index', error);
      throw error;
    } finally {
      this.isIndexing = false;
    }
  }

  /**
   * 在后台加载剩余页面
   */
  private async loadRemainingPagesInBackground(cursor: string | null): Promise<void> {
    try {
      let currentCursor = cursor;
      let batchCount = 0;
      
      while (currentCursor) {
        // 使用 requestIdleCallback 避免阻塞主线程
        await new Promise(resolve => {
          if ('requestIdleCallback' in window) {
            requestIdleCallback(() => resolve(undefined), { timeout: 100 });
          } else {
            setTimeout(resolve, 10);
          }
        });
        
        const result = await dbService.getPagesPaginated({
          limit: 500, // 每批加载500个页面
          cursor: currentCursor,
          sortBy: 'visitTime',
          sortOrder: 'desc'
        });
        
        if (result.items.length > 0) {
          // 添加到现有页面数组
          this.pages = [...this.pages, ...result.items];
          
          // 重建 Fuse 索引
          this.fuse = new Fuse(this.pages, this.config);
          
          batchCount++;
          this.log(`Background index update: loaded batch ${batchCount}, total pages: ${this.pages.length}`);
        }
        
        currentCursor = result.hasMore ? result.nextCursor : null;
      }
      
      this.log(`Background index loading completed. Total pages: ${this.pages.length}`);
      
    } catch (error) {
      this.log('Error loading remaining pages in background', error);
      // 不抛出错误，因为初始索引已经可用
    }
  }

  /**
   * 执行搜索
   */
  public async search(query: string, options: Partial<SearchOptions> = {}): Promise<SearchResultItem[]> {
    if (!query || query.trim().length === 0) {
      return [];
    }

    // 执行基础 Fuse.js 搜索逻辑
    return this.performBasicFuseSearch(query, options);
  }

  /**
   * 执行增强搜索
   */
  public async searchEnhanced(query: string, options: Partial<SearchOptions> = {}): Promise<SearchResult> {
    const startTime = performance.now();
    const processingSteps: string[] = [];

    if (!query || query.trim().length === 0) {
      return {
        results: [],
        totalResults: 0,
        searchTime: 0,
        searchMode: 'fuse',
        metadata: { processingSteps: ['Empty query'] }
      };
    }

    // 确保索引已构建
    if (!this.fuse) {
      await this.buildIndex();
    }

    try {
      processingSteps.push('Query received and validated');
      processingSteps.push('Using Fuse.js search');
      
      const fuseResults = await this.performBasicFuseSearch(query, options);
      const searchTime = performance.now() - startTime;

      this.log(`Search completed in ${searchTime.toFixed(2)}ms`, {
        mode: 'fuse',
        results: fuseResults.length
      });

      return {
        results: fuseResults,
        totalResults: fuseResults.length,
        searchTime,
        searchMode: 'fuse',
        metadata: { processingSteps }
      };

    } catch (error) {
      this.log('Search failed', error);
      processingSteps.push(`Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      const searchTime = performance.now() - startTime;
      
      return {
        results: [],
        totalResults: 0,
        searchTime,
        searchMode: 'fuse',
        metadata: {
          processingSteps,
          fallbackUsed: true
        }
      };
    }
  }

  /**
   * 执行基础 Fuse 搜索（增强版：支持空内容页面优化）
   */
  private async performBasicFuseSearch(query: string, options: Partial<SearchOptions>): Promise<SearchResultItem[]> {
    if (!query || query.trim().length === 0) {
      return [];
    }

    // 确保索引已构建
    if (!this.fuse) {
      await this.buildIndex();
    }

    try {
      // Parse the advanced search query
      const parsedQuery = parseQuery(query);

      // Validate that the query has meaningful content
      if (!isValidQuery(parsedQuery)) {
        return [];
      }

      this.log('Parsed query', parsedQuery);

      const searchOptions = this.buildSearchOptions(options);

      // Apply site filter from parsed query if present
      let filteredPages = this.pages;
      if (parsedQuery.site) {
        filteredPages = filteredPages.filter(page =>
          matchesSiteFilter(page.domain, parsedQuery.site!)
        );
        this.log(`Site filter applied: ${parsedQuery.site}, ${filteredPages.length} pages remaining`);
      }

      // Apply existing filters from options
      filteredPages = this.applyFilters(filteredPages, searchOptions);

      // Build Fuse.js query from parsed components
      const fuseQuery = buildFuseQuery(parsedQuery);

      // If no searchable terms for Fuse.js, return filtered results (for site-only or exclude-only queries)
      if (!fuseQuery.trim()) {
        if (parsedQuery.site || parsedQuery.exclude.length > 0) {
          // Return all pages matching site filter or for exclude-only queries
          let results = filteredPages.map(page => ({
            page,
            score: 1.0,
            highlights: [page.content.substring(0, 150) + '...'],
            matchedFields: parsedQuery.site ? ['domain'] : ['content']
          }));

          // Apply exclude filter if present
          if (parsedQuery.exclude.length > 0) {
            results = this.applyExcludeFilter(results, parsedQuery.exclude);
            this.log(`Exclude filter applied: ${parsedQuery.exclude.join(', ')}, ${results.length} results remaining`);
          }

          return this.postProcessResults(results, searchOptions);
        }
        return [];
      }

      // 执行内容感知搜索
      const results = await this.performContentAwareSearch(filteredPages, fuseQuery, searchOptions, parsedQuery);

      // Apply exclude filter (post-processing)
      if (parsedQuery.exclude.length > 0) {
        const filteredResults = this.applyExcludeFilter(results, parsedQuery.exclude);
        this.log(`Exclude filter applied: ${parsedQuery.exclude.join(', ')}, ${filteredResults.length} results remaining`);
        return this.postProcessResults(filteredResults, searchOptions);
      }

      // Apply sorting and limits
      return this.postProcessResults(results, searchOptions);

    } catch (error) {
      this.log('Search failed', error);
      throw error;
    }
  }

  /**
   * 执行内容感知搜索：针对空内容页面进行优化
   */
  private async performContentAwareSearch(
    filteredPages: Page[], 
    fuseQuery: string, 
    searchOptions: Required<SearchOptions>,
    parsedQuery?: ParsedQuery
  ): Promise<SearchResultItem[]> {
    // 分离有内容和无内容的页面
    const pagesWithContent = filteredPages.filter(page => page.content && page.content.trim().length > 0);
    const pagesWithoutContent = filteredPages.filter(page => !page.content || page.content.trim().length === 0);

    const allResults: SearchResultItem[] = [];

    // 对有内容的页面使用标准搜索
    if (pagesWithContent.length > 0) {
      // If exact phrases are present, use stricter threshold
      const searchConfig = parsedQuery?.exact && parsedQuery.exact.length > 0 
        ? { ...this.config, threshold: 0.1 } // Very strict for exact phrases
        : this.config;
        
      const contentFuse = new Fuse(pagesWithContent, searchConfig);
      const contentResults = contentFuse.search(fuseQuery);
      let formattedContentResults = this.formatResults(contentResults, searchOptions, parsedQuery);
      
      // If exact phrases are specified, filter results to ensure they contain all exact phrases
      if (parsedQuery?.exact && parsedQuery.exact.length > 0) {
        formattedContentResults = formattedContentResults.filter(result => {
          const pageText = `${result.page.title || ''} ${result.page.content || ''}`.toLowerCase();
          return parsedQuery.exact.every(phrase => 
            pageText.includes(phrase.toLowerCase())
          );
        });
      }
      
      allResults.push(...formattedContentResults);
    }

    // 对无内容的页面使用仅标题+URL的优化搜索配置
    if (pagesWithoutContent.length > 0) {
      const baseConfig = {
        ...this.config,
        keys: [
          { name: 'title', weight: 0.7 },  // 提高标题权重
          { name: 'url', weight: 0.3 },    // 提高URL权重
          // 不包含content和domain字段
        ]
      };
      
      // Apply stricter threshold for exact phrases
      const titleUrlConfig = parsedQuery?.exact && parsedQuery.exact.length > 0
        ? { ...baseConfig, threshold: 0.1 }
        : baseConfig;
      
      const titleUrlFuse = new Fuse(pagesWithoutContent, titleUrlConfig);
      const titleUrlResults = titleUrlFuse.search(fuseQuery);
      let formattedTitleUrlResults = this.formatResults(titleUrlResults, searchOptions, parsedQuery);
      
      // Filter for exact phrases if specified
      if (parsedQuery?.exact && parsedQuery.exact.length > 0) {
        formattedTitleUrlResults = formattedTitleUrlResults.filter(result => {
          const pageText = `${result.page.title || ''} ${result.page.url || ''}`.toLowerCase();
          return parsedQuery.exact.every(phrase => 
            pageText.includes(phrase.toLowerCase())
          );
        });
      }
      
      // 为空内容页面的结果添加特殊标识和稍微降低得分
      const adjustedResults = formattedTitleUrlResults.map(result => ({
        ...result,
        score: result.score * 1.1, // 稍微降低排名优先级
        highlights: result.highlights.length > 0 ? result.highlights : [
          `标题: ${result.page.title}`, 
          `URL: ${result.page.url}`
        ],
        matchedFields: [...(result.matchedFields || []), 'no-content'] // 添加标识
      }));
      
      allResults.push(...adjustedResults);
    }

    this.log(`Content-aware search completed`, {
      withContent: pagesWithContent.length,
      withoutContent: pagesWithoutContent.length,
      totalResults: allResults.length
    });

    return allResults;
  }

  /**
   * 获取搜索建议（优化版：缓存、性能优化、内存控制）
   */
  public async getSuggestions(query: string, limit: number = 5): Promise<string[]> {
    if (!query || query.trim().length < 2) {
      return [];
    }

    const queryKey = `${query.toLowerCase().trim()}_${limit}`;
    
    // Check cache first
    const cached = this.suggestionCache.get(queryKey);
    if (cached && (Date.now() - cached.timestamp) < this.suggestionCacheExpiry) {
      return cached.suggestions;
    }

    try {
      // Check memory pressure before expensive operation
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        if (memInfo && memInfo.usedJSHeapSize > 100 * 1024 * 1024) { // 100MB threshold
          console.warn('[SearchService] High memory usage, returning simple suggestions');
          return this.getSimpleSuggestions(query, limit);
        }
      }

      const startTime = performance.now();
      const suggestions = new Map<string, number>(); // 建议词 -> 优先级分数
      const queryLower = query.toLowerCase();
      
      // Limit pages processed based on memory and performance constraints
      const maxPagesToProcess = Math.min(this.pages.length, 1000); // Max 1000 pages
      const pagesToProcess = this.pages.slice(0, maxPagesToProcess);
      
      for (const page of pagesToProcess) {
        // Check processing time limit (max 100ms for suggestions)
        if (performance.now() - startTime > 100) {
          console.warn('[SearchService] Suggestion processing timeout, returning partial results');
          break;
        }
        
        // 优先级1: 从标题提取建议（最高优先级）
        const titleWords = page.title.toLowerCase().split(/\s+/).slice(0, 20); // Limit title words
        for (const word of titleWords) {
          if (word.length >= 3 && word.startsWith(queryLower) && word.length > queryLower.length) {
            const priority = suggestions.get(word) || 0;
            suggestions.set(word, Math.max(priority, 100)); // 标题建议得分100
          }
        }
        
        // 优先级2: 从URL路径提取建议
        try {
          const urlParts = new URL(page.url).pathname.toLowerCase().split(/[/\-_.]/);
          for (const part of urlParts.slice(0, 10)) { // Limit URL parts
            if (part.length >= 3 && part.startsWith(queryLower) && part.length > queryLower.length) {
              const priority = suggestions.get(part) || 0;
              suggestions.set(part, Math.max(priority, 50)); // URL建议得分50
            }
          }
        } catch {
          // 忽略无效URL
        }
        
        // 优先级3: 从内容提取建议（更严格的限制）
        if (page.content && page.content.trim().length > 0 && suggestions.size < limit * 2) {
          const contentWords = page.content.toLowerCase().split(/\s+/).slice(0, 30); // 减少处理的词数
          for (const word of contentWords) {
            if (word.length >= 4 && word.startsWith(queryLower) && word.length > queryLower.length) {
              const priority = suggestions.get(word) || 0;
              if (priority < 25) { // 只有当前优先级较低时才更新
                suggestions.set(word, 25); // 内容建议得分25
              }
            }
          }
        }
        
        // 更严格的提前退出条件
        if (suggestions.size >= limit * 3) break;
      }
      
      // 按优先级分数排序并返回
      const sortedSuggestions = Array.from(suggestions.entries())
        .sort((a, b) => b[1] - a[1]) // 按分数降序排列
        .map(([word]) => word)
        .filter(word => word.length >= 3) // Filter short words
        .slice(0, limit);
      
      const processingTime = performance.now() - startTime;
      
      // Cache the results
      this.cacheSuggestion(queryKey, sortedSuggestions);
      
      this.log(`Generated ${sortedSuggestions.length} suggestions for query: ${query} in ${processingTime.toFixed(2)}ms`, {
        total: suggestions.size,
        returned: sortedSuggestions.length,
        processingTime,
        cached: false
      });
      
      return sortedSuggestions;
      
    } catch (error) {
      this.log('Failed to get suggestions', error);
      return [];
    }
  }

  /**
   * 获取简单建议（内存压力大时的降级版本）
   */
  private getSimpleSuggestions(query: string, limit: number): string[] {
    const queryLower = query.toLowerCase();
    const suggestions = new Set<string>();
    
    // Only process recent pages and titles
    const recentPages = this.pages.slice(0, 100);
    
    for (const page of recentPages) {
      const titleWords = page.title.toLowerCase().split(/\s+/).slice(0, 5);
      for (const word of titleWords) {
        if (word.startsWith(queryLower) && word.length > queryLower.length && word.length >= 3) {
          suggestions.add(word);
          if (suggestions.size >= limit) break;
        }
      }
      if (suggestions.size >= limit) break;
    }
    
    return Array.from(suggestions).slice(0, limit);
  }

  /**
   * 缓存建议结果
   */
  private cacheSuggestion(key: string, suggestions: string[]): void {
    // Clean old cache entries if needed
    if (this.suggestionCache.size >= this.maxCacheSize) {
      const oldestKey = this.suggestionCache.keys().next().value;
      if (oldestKey) {
        this.suggestionCache.delete(oldestKey);
      }
    }
    
    this.suggestionCache.set(key, {
      suggestions,
      timestamp: Date.now()
    });
  }

  /**
   * 清理过期的建议缓存
   */
  private cleanExpiredSuggestionCache(): void {
    const now = Date.now();
    for (const [key, value] of this.suggestionCache.entries()) {
      if (now - value.timestamp > this.suggestionCacheExpiry) {
        this.suggestionCache.delete(key);
      }
    }
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<FuseConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重建索引以应用新配置
    if (this.fuse) {
      this.fuse = new Fuse(this.pages, this.config);
      this.log('Search configuration updated');
    }
  }

  /**
   * 获取索引统计信息
   */
  public getIndexStats(): {
    totalPages: number;
    lastUpdate: number;
    isIndexing: boolean;
    config: FuseConfig;
  } {
    return {
      totalPages: this.pages.length,
      lastUpdate: this.lastIndexUpdate,
      isIndexing: this.isIndexing,
      config: { ...this.config }
    };
  }

  /**
   * 检查是否需要重建索引
   */
  public async shouldRebuildIndex(): Promise<boolean> {
    try {
      const storageInfo = await dbService.getStorageInfo();
      
      // 如果页面数量发生变化，需要重建
      if (storageInfo.pagesCount !== this.pages.length) {
        return true;
      }
      
      // 如果超过一定时间，需要重建（例如1小时）
      const oneHour = 60 * 60 * 1000;
      if (Date.now() - this.lastIndexUpdate > oneHour) {
        return true;
      }
      
      return false;
      
    } catch (error) {
      this.log('Failed to check if index rebuild is needed', error);
      return true; // 出错时重建索引
    }
  }

  /**
   * 构建搜索选项
   */
  private buildSearchOptions(options: Partial<SearchOptions>): Required<SearchOptions> {
    return {
      query: options.query || '',
      limit: options.limit || 50,
      timeRange: options.timeRange || { start: 0, end: Date.now() },
      domains: options.domains || [],
      excludeDomains: options.excludeDomains || [],
      sortBy: options.sortBy || 'relevance',
      sortOrder: options.sortOrder || 'desc'
    };
  }

  /**
   * 应用过滤器
   */
  private applyFilters(pages: Page[], options: Required<SearchOptions>): Page[] {
    let filtered = [...pages];

    // 时间范围过滤
    if (options.timeRange) {
      filtered = filtered.filter(page => 
        page.visitTime >= options.timeRange!.start && 
        page.visitTime <= options.timeRange!.end
      );
    }

    // 域名过滤
    if (options.domains && options.domains.length > 0) {
      filtered = filtered.filter(page => 
        options.domains!.includes(page.domain)
      );
    }

    // 排除域名
    if (options.excludeDomains && options.excludeDomains.length > 0) {
      filtered = filtered.filter(page => 
        !options.excludeDomains!.includes(page.domain)
      );
    }

    return filtered;
  }

  /**
   * 格式化搜索结果 (Enhanced with parsed query context)
   */
  private formatResults(
    fuseResults: any[],
    _options: Required<SearchOptions>,
    parsedQuery?: ParsedQuery
  ): SearchResultItem[] {
    // Debug: Check original Fuse.js scores
    if (fuseResults.length > 0) {
      console.log('[SearchService] Fuse.js Score Analysis:');
      fuseResults.slice(0, 3).forEach((result, index) => {
        console.log(`  Fuse[${index}]: score=${result.score?.toFixed(4)} | title=${result.item.title.substring(0, 50)}...`);
      });
    }
    
    return fuseResults.map((result, index) => {
      // Fuse.js scores are distance scores (0=perfect, 1=worst)
      // We need to convert them to relevance scores (1=perfect, 0=worst)
      const fuseScore = result.score || 0;
      const relevanceScore = 1 - fuseScore; // Convert distance to relevance
      
      if (index < 3) {
        console.log(`  Converted[${index}]: fuseScore=${fuseScore.toFixed(4)} -> relevanceScore=${relevanceScore.toFixed(4)}`);
      }
      
      return {
        page: result.item,
        score: relevanceScore,
        highlights: this.extractHighlights(result, parsedQuery),
        matchedFields: this.extractMatchedFields(result)
      };
    });
  }

  /**
   * Apply exclude filter to search results
   */
  private applyExcludeFilter(results: SearchResultItem[], excludeTerms: string[]): SearchResultItem[] {
    if (excludeTerms.length === 0) {
      return results;
    }

    return results.filter(result => {
      const searchText = `${result.page.title} ${result.page.content} ${result.page.url}`.toLowerCase();

      // Check if any exclude term is found in the page content
      return !excludeTerms.some(term =>
        searchText.includes(term.toLowerCase())
      );
    });
  }

  /**
   * 后处理结果（排序和限制）
   */
  private postProcessResults(
    results: SearchResultItem[],
    options: Required<SearchOptions>
  ): SearchResultItem[] {
    // 排序
    results.sort((a, b) => {
      switch (options.sortBy) {
        case 'time':
          const timeA = a.page.visitTime;
          const timeB = b.page.visitTime;
          return options.sortOrder === 'desc' ? timeB - timeA : timeA - timeB;

        case 'accessCount':
          const countA = a.page.accessCount;
          const countB = b.page.accessCount;
          return options.sortOrder === 'desc' ? countB - countA : countA - countB;

        case 'relevance':
        default:
          return options.sortOrder === 'desc' ? a.score - b.score : b.score - a.score;
      }
    });

    // 限制结果数量
    return results.slice(0, options.limit);
  }

  /**
   * 提取高亮片段 (Enhanced with exact phrase highlighting)
   */
  private extractHighlights(result: any, parsedQuery?: ParsedQuery): string[] {
    const highlights: string[] = [];

    if (result.matches) {
      for (const match of result.matches) {
        if (match.indices && match.value) {
          for (const [start, end] of match.indices) {
            // 提取匹配片段及其上下文
            const contextStart = Math.max(0, start - 50);
            const contextEnd = Math.min(match.value.length, end + 50);

            let snippet = match.value.substring(contextStart, contextEnd);

            // 添加省略号
            if (contextStart > 0) snippet = '...' + snippet;
            if (contextEnd < match.value.length) snippet = snippet + '...';

            // 高亮匹配的部分
            const matchText = match.value.substring(start, end + 1);
            snippet = snippet.replace(
              new RegExp(this.escapeRegExp(matchText), 'gi'),
              `<mark>$&</mark>`
            );

            highlights.push(snippet);
          }
        }
      }
    }

    // Enhanced highlighting for exact phrases
    if (parsedQuery && parsedQuery.exact.length > 0 && result.item) {
      const content = result.item.content || '';
      const title = result.item.title || '';

      for (const exactPhrase of parsedQuery.exact) {
        // Search for exact phrase in content
        const contentIndex = content.toLowerCase().indexOf(exactPhrase.toLowerCase());
        if (contentIndex !== -1) {
          const contextStart = Math.max(0, contentIndex - 50);
          const contextEnd = Math.min(content.length, contentIndex + exactPhrase.length + 50);

          let snippet = content.substring(contextStart, contextEnd);
          if (contextStart > 0) snippet = '...' + snippet;
          if (contextEnd < content.length) snippet = snippet + '...';

          // Highlight the exact phrase
          snippet = snippet.replace(
            new RegExp(this.escapeRegExp(exactPhrase), 'gi'),
            `<mark>$&</mark>`
          );

          highlights.push(snippet);
        }

        // Search for exact phrase in title
        const titleIndex = title.toLowerCase().indexOf(exactPhrase.toLowerCase());
        if (titleIndex !== -1) {
          const highlightedTitle = title.replace(
            new RegExp(this.escapeRegExp(exactPhrase), 'gi'),
            `<mark>$&</mark>`
          );
          highlights.push(`Title: ${highlightedTitle}`);
        }
      }
    }

    // 如果没有匹配信息，返回内容的开头部分
    if (highlights.length === 0 && result.item.content) {
      highlights.push(result.item.content.substring(0, 150) + '...');
    }

    return highlights.slice(0, 5); // 最多返回5个高亮片段 (increased for exact phrases)
  }

  /**
   * 提取匹配的字段
   */
  private extractMatchedFields(result: any): string[] {
    const fields: string[] = [];

    if (result.matches) {
      for (const match of result.matches) {
        if (match.key && !fields.includes(match.key)) {
          fields.push(match.key);
        }
      }
    }

    return fields;
  }

  /**
   * 转义正则表达式特殊字符
   */
  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 清理搜索索引
   */
  public clearIndex(): void {
    this.fuse = null;
    this.pages = [];
    this.lastIndexUpdate = 0;
    
    this.log('Search index cleared');
  }

  /**
   * 添加页面到索引
   */
  public async addPageToIndex(page: Page): Promise<void> {
    try {
      // 添加到页面列表
      const existingIndex = this.pages.findIndex(p => p.id === page.id);
      if (existingIndex >= 0) {
        this.pages[existingIndex] = page;
      } else {
        this.pages.push(page);
      }
      
      // 重建 Fuse 索引
      if (this.fuse) {
        this.fuse = new Fuse(this.pages, this.config);
      }
      
      this.log(`Page "${page.title}" added to search index`);
    } catch (error) {
      this.log('Failed to add page to index', error);
    }
  }

  /**
   * 从索引中移除页面
   */
  public async removePageFromIndex(pageId: string): Promise<void> {
    try {
      // 从页面列表中移除
      this.pages = this.pages.filter(p => p.id !== pageId);
      
      // 重建 Fuse 索引
      if (this.fuse) {
        this.fuse = new Fuse(this.pages, this.config);
      }
      
      this.log(`Page ${pageId} removed from search index`);
    } catch (error) {
      this.log('Failed to remove page from index', error);
    }
  }

  /**
   * 重置服务实例（主要用于测试）
   */
  public static reset(): void {
    if (SearchService.instance) {
      SearchService.instance.clearIndex();
      SearchService.instance = null;
    }
  }

  /**
   * 日志输出
   */
  private log(message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [Recall Search] ${message}`, data || '');
  }
}

// 导出单例实例
export const searchService = SearchService.getInstance();