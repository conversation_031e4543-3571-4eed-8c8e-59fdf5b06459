/**
 * Unit tests for IndexedDB Service domain functionality
 */

import type { Page, DomainInfo } from '../models';

// Mock services completely for testing
const mockGetUniqueDomains = jest.fn();
const mockIsDomainBlacklisted = jest.fn();
const mockCreateTransaction = jest.fn();

// Mock IndexedDBService
const MockIndexedDBService = {
  getInstance: jest.fn(() => ({
    getUniqueDomains: mockGetUniqueDomains,
    createTransaction: mockCreateTransaction,
    ensureInitialized: jest.fn().mockResolvedValue(undefined),
    init: jest.fn().mockResolvedValue(undefined),
  }))
};

// Mock BlacklistService  
const MockBlacklistService = {
  getInstance: jest.fn(() => ({
    getIndexedDomainsWithStatus: jest.fn(),
    isDomainBlacklisted: mockIsDomainBlacklisted,
    init: jest.fn().mockResolvedValue(undefined),
  }))
};

// Mock the actual modules
jest.mock('./db.service', () => ({
  IndexedDBService: MockIndexedDBService
}));

jest.mock('./blacklist.service', () => ({
  BlacklistService: MockBlacklistService
}));

describe('IndexedDBService Domain Functionality', () => {
  let dbService: IndexedDBService;
  let blacklistService: BlacklistService;
  let mockTransactionObject: any;
  let mockStoreObject: any;
  let mockIndexObject: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset singleton instances for testing
    (IndexedDBService as any).instance = null;
    (BlacklistService as any).instance = null;
    
    dbService = IndexedDBService.getInstance();
    blacklistService = BlacklistService.getInstance();

    // Setup mock transaction and store objects
    mockTransactionObject = {
      objectStore: jest.fn().mockReturnThis(),
      index: jest.fn().mockReturnThis(),
      openCursor: jest.fn(),
    };

    mockStoreObject = {
      index: jest.fn().mockReturnThis(),
      openCursor: jest.fn(),
      get: jest.fn(),
      put: jest.fn(),
      add: jest.fn(),
    };

    mockIndexObject = {
      openCursor: jest.fn(),
      get: jest.fn(),
    };

    // Mock the database initialization
    (dbService as any).db = { 
      transaction: jest.fn().mockReturnValue(mockTransactionObject) 
    };
    (dbService as any).initPromise = Promise.resolve();
  });

  describe('getUniqueDomains', () => {
    it('should return unique domains with statistics', async () => {
      // Mock test data
      const mockPages: Page[] = [
        {
          id: '1',
          url: 'https://example.com/page1',
          title: 'Example Page 1',
          content: 'Content 1',
          domain: 'example.com',
          visitTime: Date.now() - 86400000,
          lastUpdated: Date.now() - 86400000,
          accessCount: 5
        },
        {
          id: '2',
          url: 'https://example.com/page2',
          title: 'Example Page 2',
          content: 'Content 2',
          domain: 'example.com',
          visitTime: Date.now(),
          lastUpdated: Date.now(),
          accessCount: 3
        },
        {
          id: '3',
          url: 'https://github.com/repo',
          title: 'GitHub Repo',
          content: 'GitHub content',
          domain: 'github.com',
          visitTime: Date.now() - 3600000,
          lastUpdated: Date.now() - 3600000,
          accessCount: 10
        }
      ];

      // Mock cursor iteration
      let cursorIndex = 0;
      mockTransactionObject.objectStore.mockReturnValue({
        index: jest.fn().mockReturnValue({
          openCursor: jest.fn().mockImplementation(() => ({
            onsuccess: null,
            onerror: null
          }))
        })
      });

      // Setup cursor mock to iterate through test data
      const mockCursor = {
        value: null,
        continue: jest.fn()
      };

      // Mock the cursor iteration by calling onsuccess multiple times
      setTimeout(() => {
        const cursorRequest = mockTransactionObject.objectStore().index().openCursor();
        
        // Simulate cursor iteration
        const simulateCursor = () => {
          if (cursorIndex < mockPages.length) {
            mockCursor.value = mockPages[cursorIndex];
            cursorIndex++;
            cursorRequest.onsuccess({ target: { result: mockCursor } });
            mockCursor.continue.mockImplementation(simulateCursor);
          } else {
            cursorRequest.onsuccess({ target: { result: null } });
          }
        };

        simulateCursor();
      }, 0);

      const result = await dbService.getUniqueDomains();

      expect(result).toHaveLength(2);
      expect(result[0].domain).toBe('github.com'); // Should be first due to higher visit count
      expect(result[0].visitCount).toBe(10);
      expect(result[0].pageCount).toBe(1);
      expect(result[0].isBlocked).toBe(false);

      expect(result[1].domain).toBe('example.com');
      expect(result[1].visitCount).toBe(8); // 5 + 3
      expect(result[1].pageCount).toBe(2);
      expect(result[1].isBlocked).toBe(false);
    });

    it('should handle empty database', async () => {
      // Mock empty cursor
      mockTransactionObject.objectStore.mockReturnValue({
        index: jest.fn().mockReturnValue({
          openCursor: jest.fn().mockImplementation(() => ({
            onsuccess: null,
            onerror: null
          }))
        })
      });

      setTimeout(() => {
        const cursorRequest = mockTransactionObject.objectStore().index().openCursor();
        cursorRequest.onsuccess({ target: { result: null } });
      }, 0);

      const result = await dbService.getUniqueDomains();

      expect(result).toHaveLength(0);
    });

    it('should handle database errors', async () => {
      mockTransactionObject.objectStore.mockReturnValue({
        index: jest.fn().mockReturnValue({
          openCursor: jest.fn().mockImplementation(() => ({
            onsuccess: null,
            onerror: null
          }))
        })
      });

      setTimeout(() => {
        const cursorRequest = mockTransactionObject.objectStore().index().openCursor();
        cursorRequest.onerror();
      }, 0);

      await expect(dbService.getUniqueDomains()).rejects.toThrow();
    });
  });
});

describe('BlacklistService Domain Functionality', () => {
  let blacklistService: BlacklistService;
  let dbService: IndexedDBService;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset singleton instances
    (BlacklistService as any).instance = null;
    (IndexedDBService as any).instance = null;
    
    blacklistService = BlacklistService.getInstance();
    dbService = IndexedDBService.getInstance();

    // Mock database initialization
    (blacklistService as any).dbService = dbService;
  });

  describe('getIndexedDomainsWithStatus', () => {
    it('should return domains with blacklist status checked', async () => {
      const mockDomains: DomainInfo[] = [
        {
          domain: 'example.com',
          visitCount: 10,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 5
        },
        {
          domain: 'spam.com',
          visitCount: 2,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 1
        }
      ];

      // Mock getUniqueDomains
      jest.spyOn(dbService, 'getUniqueDomains').mockResolvedValue(mockDomains);
      
      // Mock isDomainBlacklisted
      jest.spyOn(blacklistService, 'isDomainBlacklisted')
        .mockImplementation(async (domain) => domain === 'spam.com');

      const result = await blacklistService.getIndexedDomainsWithStatus();

      expect(result).toHaveLength(2);
      expect(result[0].domain).toBe('example.com');
      expect(result[0].isBlocked).toBe(false);
      expect(result[1].domain).toBe('spam.com');
      expect(result[1].isBlocked).toBe(true);
    });

    it('should handle errors gracefully', async () => {
      jest.spyOn(dbService, 'getUniqueDomains').mockRejectedValue(new Error('Database error'));

      await expect(blacklistService.getIndexedDomainsWithStatus()).rejects.toThrow('Database error');
    });
  });
});