/**
 * Data Migration Utilities
 * 
 * Shared utility functions for import/export operations
 */

import type { Page } from '../models';

/**
 * Data format version types
 */
export type DataVersion = 'v2.0' | 'v3.0' | 'v4.0' | 'unknown';

/**
 * Common data operation result
 */
export interface DataOperationResult {
  success: boolean;
  error?: string;
  warnings?: string[];
}

/**
 * Progress tracking interface
 */
export interface ProgressInfo {
  total: number;
  processed: number;
  percentage: number;
  stage?: string;
}

/**
 * Common statistics interface
 */
export interface DataStatistics {
  pagesProcessed: number;
  blacklistProcessed: number;
  settingsProcessed: number;
  totalSize: number;
  errors: string[];
  warnings: string[];
}

/**
 * Timestamp fields used across different versions
 */
export const TIMESTAMP_FIELDS = {
  v3: ['visitTime'],
  v4: ['visitTime', 'accessCount', 'lastUpdated']
} as const;

/**
 * Data format schema definitions
 */
export const DATA_SCHEMAS = {
  v3: {
    version: '3.0',
    timestampFields: TIMESTAMP_FIELDS.v3,
    requiredFields: ['url', 'title'],
    optionalFields: ['content', 'domain', 'visitTime']
  },
  v4: {
    version: '4.0',
    timestampFields: TIMESTAMP_FIELDS.v4,
    requiredFields: ['url', 'title', 'content', 'domain'],
    optionalFields: ['language', 'summary', 'contentStatus']
  }
} as const;

/**
 * Detect data format version from import data
 */
export function detectDataVersion(data: any): DataVersion {
  if (!data || typeof data !== 'object') {
    return 'unknown';
  }

  // Check for V4.0 format
  if (data.format === 'v4.0' || data.format === '4.0') {
    return 'v4.0';
  }

  // Check for V3.0 format
  if (data.version === 'v3.0' || data.version === '3.0') {
    return 'v3.0';
  }

  // Check for legacy V2.0 format (basic structure)
  if (data.pages && Array.isArray(data.pages)) {
    const hasV3Fields = data.pages.some((page: any) => 
      page.hasOwnProperty('visitTime') || page.hasOwnProperty('domain')
    );
    return hasV3Fields ? 'v3.0' : 'v2.0';
  }

  // Check for V4.0 data array
  if (data.data && Array.isArray(data.data)) {
    return 'v4.0';
  }

  return 'unknown';
}

/**
 * Validate page data according to schema version
 */
export function validatePageData(page: any, version: DataVersion): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!page || typeof page !== 'object') {
    errors.push('Page data must be an object');
    return { valid: false, errors };
  }

  const schema = version === 'v4.0' ? DATA_SCHEMAS.v4 : DATA_SCHEMAS.v3;

  // Check required fields
  for (const field of schema.requiredFields) {
    if (!page.hasOwnProperty(field) || page[field] === null || page[field] === undefined) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Validate URL format
  if (page.url && typeof page.url === 'string') {
    try {
      new URL(page.url);
    } catch {
      errors.push('Invalid URL format');
    }
  }

  // Validate timestamp fields
  for (const field of schema.timestampFields) {
    if (page.hasOwnProperty(field) && page[field] !== null && page[field] !== undefined) {
      if (typeof page[field] !== 'number' || page[field] < 0) {
        errors.push(`Invalid timestamp format for field: ${field}`);
      }
    }
  }

  return { valid: errors.length === 0, errors };
}

/**
 * Normalize page data to V4.0 format
 */
export function normalizePageToV4(page: any, sourceVersion: DataVersion): Partial<Page> {
  const normalized: any = {
    url: page.url || '',
    title: page.title || '',
    content: page.content || '',
    domain: page.domain || extractDomainFromUrl(page.url || ''),
    visitTime: page.visitTime || Date.now(),
    accessCount: page.accessCount || page.visitCount || 1,
    lastUpdated: page.lastUpdated || page.visitTime || Date.now()
  };

  // Handle optional fields
  if (page.language) normalized.language = page.language;
  if (page.summary) normalized.summary = page.summary;
  if (page.contentStatus) normalized.contentStatus = page.contentStatus;
  if (page.extractionError) normalized.extractionError = page.extractionError;
  if (page.lastExtractionAttempt) normalized.lastExtractionAttempt = page.lastExtractionAttempt;

  // Handle legacy fields
  if (sourceVersion === 'v3.0') {
    // V3.0 might have lastVisitTime which maps to lastUpdated
    if (page.lastVisitTime) {
      normalized.lastUpdated = Math.max(normalized.lastUpdated, page.lastVisitTime);
    }
    
    // Ensure default values for new V4.0 fields
    if (!normalized.domain) {
      normalized.domain = extractDomainFromUrl(normalized.url);
    }
  }

  return normalized;
}

/**
 * Extract domain from URL
 */
export function extractDomainFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

/**
 * Merge conflicting page data using various strategies
 */
export function mergePageData(existing: Page, incoming: any, strategy: 'smart' | 'timestamp-priority' | 'preserve-existing' | 'overwrite'): Partial<Page> {
  switch (strategy) {
    case 'preserve-existing':
      return existing;

    case 'overwrite':
      return { ...existing, ...incoming };

    case 'timestamp-priority':
      // Use data from the page with more recent visitTime
      const useIncoming = incoming.visitTime > existing.visitTime;
      return useIncoming ? { ...existing, ...incoming } : existing;

    case 'smart':
    default:
      // Smart merge: combine access counts, use latest timestamps, prefer non-empty content
      return {
        ...existing,
        ...incoming,
        // Keep earlier visitTime (first visit)
        visitTime: Math.min(existing.visitTime, incoming.visitTime || existing.visitTime),
        // Use latest lastUpdated
        lastUpdated: Math.max(existing.lastUpdated, incoming.lastUpdated || existing.lastUpdated),
        // Combine access counts
        accessCount: existing.accessCount + (incoming.accessCount || incoming.visitCount || 1),
        // Prefer longer content
        content: incoming.content && incoming.content.length > existing.content.length 
          ? incoming.content 
          : existing.content,
        // Use newer title if provided
        title: incoming.title || existing.title
      };
  }
}

/**
 * Generate export filename with timestamp
 */
export function generateExportFilename(format: string, includeTimestamp: boolean = true): string {
  const timestamp = includeTimestamp ? new Date().toISOString().slice(0, 19).replace(/:/g, '-') : '';
  const extension = format === 'json' ? 'json' : format === 'csv' ? 'csv' : 'txt';
  const base = 'recall-export';
  
  return timestamp ? `${base}-${timestamp}.${extension}` : `${base}.${extension}`;
}

/**
 * Calculate data size estimate
 */
export function estimateDataSize(data: any): number {
  try {
    return new Blob([JSON.stringify(data)]).size;
  } catch {
    // Fallback estimation
    const str = JSON.stringify(data);
    return new TextEncoder().encode(str).length;
  }
}

/**
 * Validate storage quota requirements
 */
export function validateStorageQuota(estimatedSize: number, currentUsage: number, quota: number): { valid: boolean; error?: string } {
  const availableSpace = quota - currentUsage;
  const requiredSpace = estimatedSize * 1.2; // Add 20% buffer
  
  if (requiredSpace > availableSpace) {
    return {
      valid: false,
      error: `Insufficient storage space. Required: ${Math.round(requiredSpace / 1024 / 1024)}MB, Available: ${Math.round(availableSpace / 1024 / 1024)}MB`
    };
  }
  
  return { valid: true };
}

/**
 * Create progress tracker for data operations
 */
export class ProgressTracker {
  private total: number = 0;
  private processed: number = 0;
  private stage: string = '';
  private callback?: (progress: ProgressInfo) => void;

  constructor(total: number, callback?: (progress: ProgressInfo) => void) {
    this.total = total;
    this.callback = callback;
  }

  public setStage(stage: string): void {
    this.stage = stage;
    this.notifyProgress();
  }

  public increment(count: number = 1): void {
    this.processed = Math.min(this.processed + count, this.total);
    this.notifyProgress();
  }

  public setProgress(processed: number): void {
    this.processed = Math.min(processed, this.total);
    this.notifyProgress();
  }

  private notifyProgress(): void {
    if (this.callback) {
      this.callback({
        total: this.total,
        processed: this.processed,
        percentage: this.total > 0 ? Math.round((this.processed / this.total) * 100) : 0,
        stage: this.stage
      });
    }
  }
}

/**
 * Batch processor for large data operations
 */
export async function processBatch<T, R>(
  items: T[],
  batchSize: number,
  processor: (batch: T[]) => Promise<R[]>,
  progressTracker?: ProgressTracker
): Promise<R[]> {
  const results: R[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await processor(batch);
    results.push(...batchResults);
    
    if (progressTracker) {
      progressTracker.increment(batch.length);
    }
  }
  
  return results;
}

/**
 * Base service class for import/export operations
 */
export abstract class DataMigrationService {
  protected readonly serviceName: string;

  constructor(serviceName: string) {
    this.serviceName = serviceName;
  }

  /**
   * Create standardized error result
   */
  protected createErrorResult(message: string, error?: Error): DataOperationResult {
    return {
      success: false,
      error: `${this.serviceName}: ${message}${error ? ` - ${error.message}` : ''}`
    };
  }

  /**
   * Create standardized success result
   */
  protected createSuccessResult(warnings?: string[]): DataOperationResult {
    return {
      success: true,
      warnings
    };
  }

  /**
   * Validate operation prerequisites
   */
  protected validatePrerequisites(options: any): { valid: boolean; error?: string } {
    if (!options) {
      return { valid: false, error: 'Options are required' };
    }
    
    return { valid: true };
  }
}