/**
 * Search Configuration Service
 * 
 * Unified service for managing all search-related settings.
 * Combines traditional SearchSettings (maxResults, timeouts, etc.) 
 * with SearchConfigPanel (engine weights) into a single service.
 * 
 * @module SearchConfigService
 * @version 1.0
 * @since 2025-06-29
 */

import type { SearchEngineConfig } from '../models/settings';
import { 
  DEFAULT_SEARCH_ENGINE_CONFIG,
  normalizeSearchEngineWeights 
} from '../models/settings';

/**
 * Complete search configuration interface
 * Combines engine configuration with search behavior settings
 */
export interface UnifiedSearchConfig {
  // Engine configuration (from SearchConfigPanel)
  engines: SearchEngineConfig;
  
  // Search behavior settings (from SearchSettings)
  behavior: {
    maxResults: number;
    fuzzySearchThreshold: number;
    enableKeywordSearch: boolean;
    keywordSearchWeight: number;
    searchTimeoutMs: number;
    enableSearchHistory: boolean;
    enableAutoComplete: boolean;
    indexingEnabled: boolean;
    indexingDelay: number;
    enableDebugMode: boolean;
  };
}

/**
 * Default unified search configuration
 */
export const DEFAULT_UNIFIED_SEARCH_CONFIG: UnifiedSearchConfig = {
  engines: DEFAULT_SEARCH_ENGINE_CONFIG,
  behavior: {
    maxResults: 20,
    fuzzySearchThreshold: 0.3,
    enableKeywordSearch: true,
    keywordSearchWeight: 1.0,
    searchTimeoutMs: 5000,
    enableSearchHistory: true,
    enableAutoComplete: true,
    indexingEnabled: true,
    indexingDelay: 1000,
    enableDebugMode: false
  }
};

/**
 * Configuration storage keys
 */
const STORAGE_KEYS = {
  SEARCH_CONFIG: 'searchEngineConfig',  // Used by SearchConfigPanel
  SEARCH_SETTINGS: 'searchSettings'    // Used by SearchSettings
} as const;

class SearchConfigService {
  private static instance: SearchConfigService;
  private cachedConfig: UnifiedSearchConfig | null = null;
  private configChangeListeners: ((config: UnifiedSearchConfig) => void)[] = [];

  private constructor() {}

  static getInstance(): SearchConfigService {
    if (!SearchConfigService.instance) {
      SearchConfigService.instance = new SearchConfigService();
    }
    return SearchConfigService.instance;
  }

  /**
   * Load unified search configuration from storage
   */
  async getUnifiedConfig(): Promise<UnifiedSearchConfig> {
    if (this.cachedConfig) {
      return this.cachedConfig;
    }

    try {
      const [engineConfigResult, behaviorConfigResult] = await Promise.all([
        chrome.storage.local.get([STORAGE_KEYS.SEARCH_CONFIG]),
        chrome.storage.local.get([STORAGE_KEYS.SEARCH_SETTINGS])
      ]);

      const engineConfig: SearchEngineConfig = engineConfigResult[STORAGE_KEYS.SEARCH_CONFIG] 
        ? { ...DEFAULT_SEARCH_ENGINE_CONFIG, ...engineConfigResult[STORAGE_KEYS.SEARCH_CONFIG] }
        : DEFAULT_SEARCH_ENGINE_CONFIG;

      const behaviorConfig = behaviorConfigResult[STORAGE_KEYS.SEARCH_SETTINGS]
        ? { ...DEFAULT_UNIFIED_SEARCH_CONFIG.behavior, ...behaviorConfigResult[STORAGE_KEYS.SEARCH_SETTINGS] }
        : DEFAULT_UNIFIED_SEARCH_CONFIG.behavior;

      this.cachedConfig = {
        engines: normalizeSearchEngineWeights(engineConfig),
        behavior: behaviorConfig
      };

      return this.cachedConfig;
    } catch (error) {
      console.error('[SearchConfigService] Failed to load config:', error);
      return DEFAULT_UNIFIED_SEARCH_CONFIG;
    }
  }

  /**
   * Update engine configuration
   */
  async updateEngineConfig(engineConfig: SearchEngineConfig): Promise<void> {
    const normalizedConfig = normalizeSearchEngineWeights(engineConfig);
    
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.SEARCH_CONFIG]: normalizedConfig
      });

      // Update cached config
      const currentConfig = await this.getUnifiedConfig();
      this.cachedConfig = {
        ...currentConfig,
        engines: normalizedConfig
      };

      // Notify listeners
      this.notifyConfigChange(this.cachedConfig);
    } catch (error) {
      console.error('[SearchConfigService] Failed to update engine config:', error);
      throw error;
    }
  }

  /**
   * Update behavior configuration
   */
  async updateBehaviorConfig(behaviorConfig: UnifiedSearchConfig['behavior']): Promise<void> {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.SEARCH_SETTINGS]: behaviorConfig
      });

      // Update cached config
      const currentConfig = await this.getUnifiedConfig();
      this.cachedConfig = {
        ...currentConfig,
        behavior: behaviorConfig
      };

      // Notify listeners
      this.notifyConfigChange(this.cachedConfig);
    } catch (error) {
      console.error('[SearchConfigService] Failed to update behavior config:', error);
      throw error;
    }
  }

  /**
   * Update partial behavior configuration
   */
  async updateBehaviorSetting<K extends keyof UnifiedSearchConfig['behavior']>(
    key: K,
    value: UnifiedSearchConfig['behavior'][K]
  ): Promise<void> {
    const currentConfig = await this.getUnifiedConfig();
    const updatedBehavior = {
      ...currentConfig.behavior,
      [key]: value
    };

    await this.updateBehaviorConfig(updatedBehavior);
  }

  /**
   * Get engine configuration (for backward compatibility)
   */
  async getEngineConfig(): Promise<SearchEngineConfig> {
    const config = await this.getUnifiedConfig();
    return config.engines;
  }

  /**
   * Get behavior configuration (for backward compatibility)
   */
  async getBehaviorConfig(): Promise<UnifiedSearchConfig['behavior']> {
    const config = await this.getUnifiedConfig();
    return config.behavior;
  }

  /**
   * Reset to default configuration
   */
  async resetToDefaults(): Promise<void> {
    try {
      await chrome.storage.local.remove([STORAGE_KEYS.SEARCH_CONFIG, STORAGE_KEYS.SEARCH_SETTINGS]);
      this.cachedConfig = null;
      
      const defaultConfig = await this.getUnifiedConfig();
      this.notifyConfigChange(defaultConfig);
    } catch (error) {
      console.error('[SearchConfigService] Failed to reset config:', error);
      throw error;
    }
  }

  /**
   * Add configuration change listener
   */
  addConfigChangeListener(listener: (config: UnifiedSearchConfig) => void): void {
    this.configChangeListeners.push(listener);
  }

  /**
   * Remove configuration change listener
   */
  removeConfigChangeListener(listener: (config: UnifiedSearchConfig) => void): void {
    const index = this.configChangeListeners.indexOf(listener);
    if (index > -1) {
      this.configChangeListeners.splice(index, 1);
    }
  }

  /**
   * Clear cached configuration (force reload on next access)
   */
  clearCache(): void {
    this.cachedConfig = null;
  }

  /**
   * Notify all listeners of configuration changes
   */
  private notifyConfigChange(config: UnifiedSearchConfig): void {
    this.configChangeListeners.forEach(listener => {
      try {
        listener(config);
      } catch (error) {
        console.error('[SearchConfigService] Error in config change listener:', error);
      }
    });
  }

  /**
   * Listen for storage changes and update cache accordingly
   */
  setupStorageListener(): void {
    chrome.storage.onChanged.addListener((changes, area) => {
      if (area !== 'local') return;

      const hasConfigChanges = Object.keys(changes).some(key => 
        Object.values(STORAGE_KEYS).includes(key as any)
      );

      if (hasConfigChanges) {
        console.log('[SearchConfigService] Detected storage changes, clearing cache');
        this.clearCache();
      }
    });
  }
}

// Export singleton instance
export const searchConfigService = SearchConfigService.getInstance();

// Auto-setup storage listener
searchConfigService.setupStorageListener();