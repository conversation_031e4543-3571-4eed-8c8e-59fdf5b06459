/**
 * Advanced Search Query Parser
 * 
 * Parses user input strings to extract keywords, exact phrases, exclude terms, and site filters.
 * Supports advanced search syntax like:
 * - "exact phrase" for exact matching
 * - -exclude for excluding terms
 * - site:domain.com for site filtering
 * - Regular keywords for fuzzy matching
 */

/**
 * Parsed query result interface
 */
export interface ParsedQuery {
  /** Regular keywords for fuzzy matching */
  keywords: string[];
  
  /** Exact phrases that must match completely */
  exact: string[];
  
  /** Terms to exclude from results */
  exclude: string[];
  
  /** Site domain filter (only one site supported per query) */
  site: string | null;
}

/**
 * Parse a search query string into structured components
 * 
 * @param queryString - The raw search query string
 * @returns ParsedQuery object with separated components
 * 
 * @example
 * ```typescript
 * parseQuery('React "component lifecycle" -class site:reactjs.org')
 * // Returns:
 * // {
 * //   keywords: ['React'],
 * //   exact: ['component lifecycle'],
 * //   exclude: ['class'],
 * //   site: 'reactjs.org'
 * // }
 * ```
 */
export function parseQuery(queryString: string): ParsedQuery {
  const result: ParsedQuery = {
    keywords: [],
    exact: [],
    exclude: [],
    site: null
  };

  // Handle empty or whitespace-only queries
  if (!queryString || typeof queryString !== 'string') {
    return result;
  }

  // Normalize the query string
  let query = queryString.trim();
  if (!query) {
    return result;
  }

  // Regular expressions for different query components
  const patterns = {
    // Exact phrases: "phrase in quotes"
    exact: /"([^"]+)"/g,
    
    // Site filter: site:domain.com
    site: /\bsite:([^\s]+)/gi,
    
    // Exclude terms: -term
    exclude: /\s-([^\s]+)/g,
    
    // Leading exclude terms: -term at start
    leadingExclude: /^-([^\s]+)/g
  };

  // Extract exact phrases first
  let match;
  while ((match = patterns.exact.exec(query)) !== null) {
    const phrase = match[1].trim();
    if (phrase) {
      result.exact.push(phrase);
    }
  }
  // Remove extracted exact phrases from query (including empty quotes)
  query = query.replace(/"[^"]*"/g, ' ').trim();

  // Extract site filter
  patterns.site.lastIndex = 0; // Reset regex state
  match = patterns.site.exec(query);
  if (match) {
    const site = match[1].trim().toLowerCase();
    if (site) {
      result.site = site;
    }
    // Remove site filter from query
    query = query.replace(patterns.site, ' ').trim();
  }

  // Extract exclude terms (both leading and inline)
  // Use a more comprehensive pattern to catch all exclude terms
  const allExcludePattern = /(^|\s)-([^\s]+)/g;

  let excludeMatch;
  while ((excludeMatch = allExcludePattern.exec(query)) !== null) {
    const term = excludeMatch[2].trim();
    if (term) {
      result.exclude.push(term);
    }
  }

  // Remove all exclude terms from query
  query = query.replace(/(^|\s)-[^\s]+/g, ' ').trim();

  // Extract remaining keywords
  if (query) {
    // Split by whitespace and filter out empty strings
    const keywords = query
      .split(/\s+/)
      .map(word => word.trim())
      .filter(word => word.length > 0);
    
    result.keywords = keywords;
  }

  return result;
}

/**
 * Validate a parsed query to ensure it has meaningful search terms
 * 
 * @param parsedQuery - The parsed query object
 * @returns true if the query has searchable content, false otherwise
 */
export function isValidQuery(parsedQuery: ParsedQuery): boolean {
  return (
    parsedQuery.keywords.length > 0 ||
    parsedQuery.exact.length > 0 ||
    parsedQuery.exclude.length > 0 ||
    parsedQuery.site !== null
  );
}

/**
 * Convert a parsed query back to a search string for Fuse.js
 * This combines keywords and exact phrases for fuzzy matching
 * 
 * @param parsedQuery - The parsed query object
 * @returns A string suitable for Fuse.js search
 */
export function buildFuseQuery(parsedQuery: ParsedQuery): string {
  const searchTerms: string[] = [];
  
  // Add keywords
  searchTerms.push(...parsedQuery.keywords);
  
  // Add exact phrases (Fuse.js will handle them as regular terms)
  searchTerms.push(...parsedQuery.exact);
  
  return searchTerms.join(' ').trim();
}

/**
 * Get a human-readable description of the parsed query
 * Useful for debugging and user feedback
 * 
 * @param parsedQuery - The parsed query object
 * @returns A descriptive string
 */
export function describeQuery(parsedQuery: ParsedQuery): string {
  const parts: string[] = [];
  
  if (parsedQuery.keywords.length > 0) {
    parts.push(`Keywords: ${parsedQuery.keywords.join(', ')}`);
  }
  
  if (parsedQuery.exact.length > 0) {
    parts.push(`Exact: "${parsedQuery.exact.join('", "')}"`);
  }
  
  if (parsedQuery.exclude.length > 0) {
    parts.push(`Exclude: ${parsedQuery.exclude.join(', ')}`);
  }
  
  if (parsedQuery.site) {
    parts.push(`Site: ${parsedQuery.site}`);
  }
  
  return parts.length > 0 ? parts.join(' | ') : 'Empty query';
}

/**
 * Normalize a domain string for consistent comparison
 * 
 * @param domain - The domain string to normalize
 * @returns Normalized domain string
 */
export function normalizeDomain(domain: string): string {
  if (!domain || typeof domain !== 'string') {
    return '';
  }
  
  return domain
    .toLowerCase()
    .replace(/^https?:\/\//, '') // Remove protocol
    .replace(/^www\./, '')       // Remove www prefix
    .replace(/\/$/, '');         // Remove trailing slash
}

/**
 * Check if a page domain matches the site filter
 * 
 * @param pageDomain - The domain of the page
 * @param siteFilter - The site filter from parsed query
 * @returns true if the page matches the site filter
 */
export function matchesSiteFilter(pageDomain: string, siteFilter: string): boolean {
  if (!siteFilter) {
    return true; // No filter means all sites match
  }
  
  const normalizedPageDomain = normalizeDomain(pageDomain);
  const normalizedSiteFilter = normalizeDomain(siteFilter);
  
  if (!normalizedPageDomain || !normalizedSiteFilter) {
    return false;
  }
  
  // Exact match or subdomain match
  return normalizedPageDomain === normalizedSiteFilter ||
         normalizedPageDomain.endsWith('.' + normalizedSiteFilter);
}
