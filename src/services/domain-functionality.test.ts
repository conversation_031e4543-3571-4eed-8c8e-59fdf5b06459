/**
 * Unit tests for domain functionality
 * Tests the core logic of domain processing
 */

import type { Page, DomainInfo } from '../models';

describe('Domain Processing Logic', () => {
  
  describe('Domain aggregation logic', () => {
    it('should correctly aggregate domains from multiple pages', () => {
      // Test data mimicking what would come from IndexedDB
      const mockPages: Page[] = [
        {
          id: '1',
          url: 'https://example.com/page1',
          title: 'Example Page 1',
          content: 'Content 1',
          domain: 'example.com',
          visitTime: Date.now() - 86400000, // 1 day ago
          lastUpdated: Date.now() - 86400000,
          accessCount: 5
        },
        {
          id: '2',
          url: 'https://example.com/page2',
          title: 'Example Page 2',
          content: 'Content 2',
          domain: 'example.com',
          visitTime: Date.now(), // Most recent
          lastUpdated: Date.now(),
          accessCount: 3
        },
        {
          id: '3',
          url: 'https://github.com/repo',
          title: 'GitHub Repo',
          content: 'GitHub content',
          domain: 'github.com',
          visitTime: Date.now() - 3600000, // 1 hour ago
          lastUpdated: Date.now() - 3600000,
          accessCount: 10
        }
      ];

      // Simulate the aggregation logic from getUniqueDomains
      const domainMap = new Map<string, {
        domain: string;
        visitCount: number;
        lastVisit: Date | null;
        pageCount: number;
      }>();

      // Process pages like the real method would
      mockPages.forEach(page => {
        if (domainMap.has(page.domain)) {
          const existing = domainMap.get(page.domain)!;
          existing.visitCount += page.accessCount;
          existing.pageCount += 1;
          
          const pageLastVisit = new Date(page.visitTime);
          if (!existing.lastVisit || pageLastVisit > existing.lastVisit) {
            existing.lastVisit = pageLastVisit;
          }
        } else {
          domainMap.set(page.domain, {
            domain: page.domain,
            visitCount: page.accessCount,
            lastVisit: new Date(page.visitTime),
            pageCount: 1
          });
        }
      });

      // Convert to final format
      const result: DomainInfo[] = Array.from(domainMap.values()).map(domain => ({
        ...domain,
        isBlocked: false
      }));

      // Sort by visit count descending (like the real method)
      result.sort((a, b) => b.visitCount - a.visitCount);

      // Assertions
      expect(result).toHaveLength(2);
      
      // GitHub should be first (highest visit count)
      expect(result[0].domain).toBe('github.com');
      expect(result[0].visitCount).toBe(10);
      expect(result[0].pageCount).toBe(1);
      expect(result[0].isBlocked).toBe(false);

      // Example.com should be second
      expect(result[1].domain).toBe('example.com');
      expect(result[1].visitCount).toBe(8); // 5 + 3
      expect(result[1].pageCount).toBe(2);
      expect(result[1].lastVisit?.getTime()).toBe(mockPages[1].visitTime); // Most recent visit
    });

    it('should handle single domain with multiple pages', () => {
      const mockPages: Page[] = [
        {
          id: '1',
          url: 'https://docs.example.com/guide',
          title: 'Guide',
          content: 'Guide content',
          domain: 'docs.example.com',
          visitTime: Date.now() - 7200000, // 2 hours ago
          lastUpdated: Date.now() - 7200000,
          accessCount: 2
        },
        {
          id: '2',
          url: 'https://docs.example.com/api',
          title: 'API Docs',
          content: 'API content',
          domain: 'docs.example.com',
          visitTime: Date.now() - 3600000, // 1 hour ago (more recent)
          lastUpdated: Date.now() - 3600000,
          accessCount: 7
        }
      ];

      const domainMap = new Map();
      
      mockPages.forEach(page => {
        if (domainMap.has(page.domain)) {
          const existing = domainMap.get(page.domain);
          existing.visitCount += page.accessCount;
          existing.pageCount += 1;
          
          const pageLastVisit = new Date(page.visitTime);
          if (!existing.lastVisit || pageLastVisit > existing.lastVisit) {
            existing.lastVisit = pageLastVisit;
          }
        } else {
          domainMap.set(page.domain, {
            domain: page.domain,
            visitCount: page.accessCount,
            lastVisit: new Date(page.visitTime),
            pageCount: 1
          });
        }
      });

      const result = Array.from(domainMap.values());

      expect(result).toHaveLength(1);
      expect(result[0].domain).toBe('docs.example.com');
      expect(result[0].visitCount).toBe(9); // 2 + 7
      expect(result[0].pageCount).toBe(2);
      expect(result[0].lastVisit.getTime()).toBe(mockPages[1].visitTime); // More recent visit
    });

    it('should handle empty input', () => {
      const mockPages: Page[] = [];
      const domainMap = new Map();
      
      mockPages.forEach(page => {
        // Processing logic would not run
      });

      const result = Array.from(domainMap.values());
      expect(result).toHaveLength(0);
    });
  });

  describe('Domain blacklist status checking', () => {
    it('should correctly apply blacklist status to domains', async () => {
      const mockDomains: DomainInfo[] = [
        {
          domain: 'example.com',
          visitCount: 10,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 5
        },
        {
          domain: 'spam.com',
          visitCount: 2,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 1
        },
        {
          domain: 'github.com',
          visitCount: 15,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 3
        }
      ];

      // Mock blacklist check function
      const mockIsBlacklisted = async (domain: string): Promise<boolean> => {
        const blacklistedDomains = ['spam.com'];
        return blacklistedDomains.includes(domain);
      };

      // Simulate the status checking logic
      const domainsWithStatus = await Promise.all(
        mockDomains.map(async (domain) => ({
          ...domain,
          isBlocked: await mockIsBlacklisted(domain.domain)
        }))
      );

      expect(domainsWithStatus).toHaveLength(3);
      expect(domainsWithStatus[0].domain).toBe('example.com');
      expect(domainsWithStatus[0].isBlocked).toBe(false);
      expect(domainsWithStatus[1].domain).toBe('spam.com');
      expect(domainsWithStatus[1].isBlocked).toBe(true);
      expect(domainsWithStatus[2].domain).toBe('github.com');
      expect(domainsWithStatus[2].isBlocked).toBe(false);
    });
  });

  describe('Domain suggestion filtering', () => {
    it('should filter domains based on search query', () => {
      const indexedDomains: DomainInfo[] = [
        {
          domain: 'github.com',
          visitCount: 10,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 5
        },
        {
          domain: 'gitlab.com',
          visitCount: 5,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 2
        },
        {
          domain: 'stackoverflow.com',
          visitCount: 8,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 3
        },
        {
          domain: 'example.com',
          visitCount: 3,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 1
        }
      ];

      // Test filtering logic (from BlacklistManagement component)
      const query = 'git';
      const filteredDomains = indexedDomains
        .filter(domain => domain.domain.toLowerCase().includes(query.toLowerCase()))
        .slice(0, 10);

      expect(filteredDomains).toHaveLength(2);
      expect(filteredDomains[0].domain).toBe('github.com');
      expect(filteredDomains[1].domain).toBe('gitlab.com');
    });

    it('should return top domains when no query', () => {
      const indexedDomains: DomainInfo[] = [
        {
          domain: 'github.com',
          visitCount: 10,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 5
        },
        {
          domain: 'stackoverflow.com',
          visitCount: 8,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 3
        },
        {
          domain: 'gitlab.com',
          visitCount: 5,
          lastVisit: new Date(),
          isBlocked: false,
          pageCount: 2
        }
      ];

      // When no query (empty string), return top 10 domains
      const query = '';
      const suggestedDomains = query.trim() ? 
        indexedDomains.filter(domain => domain.domain.toLowerCase().includes(query.toLowerCase())) :
        indexedDomains.slice(0, 10);

      expect(suggestedDomains).toHaveLength(3);
      // Should maintain original order (sorted by visit count)
      expect(suggestedDomains[0].domain).toBe('github.com');
      expect(suggestedDomains[1].domain).toBe('stackoverflow.com');
      expect(suggestedDomains[2].domain).toBe('gitlab.com');
    });
  });
});