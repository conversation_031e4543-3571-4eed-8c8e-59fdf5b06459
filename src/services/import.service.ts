/**
 * Data Import Service
 * 
 * Handles importing extension data from various formats
 */

import { dbService } from './db.service';
import { blacklistService } from './blacklist.service';
import { hybridSearchService } from './hybrid-search.service';
import type { Page, BlacklistEntry } from '../models';

/**
 * Import format types
 */
export type ImportFormat = 'json' | 'csv' | 'auto-detect';

/**
 * Merge strategies for conflicting data
 */
export type MergeStrategy = 'smart' | 'timestamp-priority' | 'preserve-existing' | 'overwrite' | 'safe';

/**
 * Detected format types
 */
export type DetectedFormat = 'v3.0' | 'v4.0' | 'v2.0' | 'unknown';

/**
 * Import options interface
 */
export interface ImportOptions {
  /** Import format */
  format: ImportFormat;
  
  /** Whether to overwrite existing data */
  overwriteExisting?: boolean;
  
  /** Whether to validate data before import */
  validateData?: boolean;
  
  /** Maximum number of items to import */
  maxItems?: number;
  
  /** Skip items with errors */
  skipErrors?: boolean;
  
  /** Merge strategy for conflicting data */
  mergeStrategy?: MergeStrategy;
  
  /** Whether to validate storage quota before import */
  validateQuota?: boolean;
  
  /** Whether to respect blacklist during import */
  respectBlacklist?: boolean;
  
  /** Whether to update search index after import */
  updateSearchIndex?: boolean;
  
  /** Whether to continue on individual errors */
  continueOnError?: boolean;
  
  /** Strict validation mode */
  strictValidation?: boolean;
  
  /** Maximum content size per page (bytes) */
  maxContentSize?: number;
  
  /** Whether to validate data consistency after import */
  validateAfterImport?: boolean;
  
  /** Progress callback for large imports */
  progressCallback?: (progress: { total: number; processed: number; percentage: number }) => void;
}

/**
 * Import result interface
 */
export interface ImportResult {
  /** Import success status */
  success: boolean;
  
  /** Error message if failed */
  error?: string;
  
  /** Detected format */
  formatDetected?: DetectedFormat;
  
  /** Warning messages */
  warnings?: string[];
  
  /** Import statistics */
  stats?: {
    pagesImported: number;
    blacklistImported: number;
    settingsImported: number;
    pagesSkipped: number;
    blacklistSkipped: number;
    pagesMerged: number;
    pagesFailed: number;
    errors: string[];
    totalProcessed: number;
  };
  
  /** Validation results */
  validationResults?: {
    allPagesValid: boolean;
    inconsistenciesFound: number;
  };
}

/**
 * Import data structure
 */
interface ImportData {
  // V3.0 format fields
  version?: string;
  timestamp?: number;
  
  // V4.0 format fields
  format?: string;
  exportTime?: string;
  schema?: {
    version: string;
    timestampFields: string[];
  };
  backwardCompatible?: boolean;
  
  // Data fields (both formats)
  pages?: any[];
  data?: any[]; // V4.0 uses 'data' field
  blacklist?: any[];
  settings?: Record<string, any>;
  metadata?: any;
}

/**
 * Import Service Class
 */
export class ImportService {
  private static instance: ImportService;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): ImportService {
    if (!ImportService.instance) {
      ImportService.instance = new ImportService();
    }
    return ImportService.instance;
  }

  /**
   * Import data from file content with V3.0/V4.0 compatibility
   */
  public async importData(fileContent: string, options: Partial<ImportOptions> = {}): Promise<ImportResult> {
    const warnings: string[] = [];
    const stats = {
      pagesImported: 0,
      blacklistImported: 0,
      settingsImported: 0,
      pagesSkipped: 0,
      blacklistSkipped: 0,
      pagesMerged: 0,
      pagesFailed: 0,
      errors: [],
      totalProcessed: 0
    };

    try {
      // Handle empty data
      if (!fileContent.trim()) {
        warnings.push('No data to import');
        return {
          success: true,
          formatDetected: 'unknown',
          warnings,
          stats
        };
      }

      // Parse and detect format
      const { data, detectedFormat } = await this.parseAndDetectFormat(fileContent, options.format || 'auto-detect');
      
      if (!data) {
        return {
          success: false,
          error: 'Failed to parse import data',
          formatDetected: 'unknown',
          warnings,
          stats
        };
      }

      // Add warning for unknown format
      if (detectedFormat === 'unknown') {
        warnings.push('Unknown format detected');
      }

      // Validate storage quota if requested
      if (options.validateQuota) {
        const quotaCheck = await this.validateStorageQuota(data);
        if (!quotaCheck.valid) {
          return {
            success: false,
            error: quotaCheck.error,
            formatDetected: detectedFormat,
            warnings,
            stats
          };
        }
      }

      // Enhanced validation
      if (options.validateData !== false) { // Default to true
        const validation = this.validateImportData(data, options.strictValidation || false);
        if (!validation.valid && options.strictValidation) {
          return {
            success: false,
            error: `Data validation failed: ${validation.errors.join(', ')}`,
            formatDetected: detectedFormat,
            warnings,
            stats
          };
        }
        warnings.push(...validation.warnings);
      }

      // Get pages from either 'pages' or 'data' field
      const pages = data.data || data.pages || [];
      
      // Import pages with enhanced logic
      if (pages.length > 0) {
        await this.importPagesWithMerging(pages, detectedFormat, options, stats, warnings);
      }

      // Import other data types
      if (data.blacklist && Array.isArray(data.blacklist)) {
        await this.importBlacklist(data.blacklist, options as ImportOptions, stats);
      }

      if (data.settings && typeof data.settings === 'object') {
        await this.importSettings(data.settings, options as ImportOptions, stats);
      }

      // Update search index if requested
      if (options.updateSearchIndex && stats.pagesImported > 0) {
        try {
          // Rebuild the hybrid search index after import
          console.log(`[ImportService] Rebuilding search index after importing ${stats.pagesImported} pages`);
          await hybridSearchService.buildIndex();
          console.log('[ImportService] Search index rebuilt successfully');
        } catch (error) {
          warnings.push(`Search index update failed: ${(error as Error).message}`);
          console.error('[ImportService] Failed to rebuild search index:', error);
        }
      }

      // Post-import validation
      if (options.validateAfterImport) {
        const validationResults = await this.validateAfterImport(stats);
        return {
          success: true,
          formatDetected: detectedFormat,
          warnings,
          stats,
          validationResults
        };
      }

      return {
        success: true,
        formatDetected: detectedFormat,
        warnings: warnings.length > 0 ? warnings : undefined,
        stats
      };

    } catch (error) {
      return {
        success: false,
        error: `Import failed: ${(error as Error).message}`,
        formatDetected: 'unknown',
        warnings,
        stats
      };
    }
  }

  /**
   * Parse and detect format automatically
   */
  private async parseAndDetectFormat(content: string, format: ImportFormat): Promise<{ data: ImportData | null; detectedFormat: DetectedFormat }> {
    if (format === 'auto-detect') {
      // Try to parse as JSON first
      try {
        const data = this.parseJSONData(content);
        if (data) {
          const detectedFormat = this.detectDataFormat(data);
          return { data, detectedFormat };
        }
      } catch (error) {
        // If JSON is malformed, don't fall through - this is an error case
        if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
          throw error; // Re-throw JSON parse errors for apparent JSON files
        }
        // Fall through to other formats for non-JSON-like content
      }

      // Try CSV format
      try {
        const data = this.parseCSVData(content);
        if (data) {
          return { data, detectedFormat: 'v2.0' }; // CSV is typically legacy format
        }
      } catch {
        // Fall through
      }

      return { data: null, detectedFormat: 'unknown' };
    } else {
      // Use specific format
      const data = await this.parseImportData(content, format);
      const detectedFormat = data ? this.detectDataFormat(data) : 'unknown';
      return { data, detectedFormat };
    }
  }

  /**
   * Detect data format from parsed data
   */
  private detectDataFormat(data: ImportData): DetectedFormat {
    // V4.0 format detection
    if (data.format === 'v4.0' || 
        (data.schema?.version === '4.0') || 
        (data.exportTime && data.data)) {
      return 'v4.0';
    }

    // V3.0 format detection
    if (data.version === '3.0' || 
        (data.timestamp && data.pages && !data.format)) {
      return 'v3.0';
    }

    // V2.0 or legacy format
    if (data.version && data.version.startsWith('2.')) {
      return 'v2.0';
    }

    // Unknown format
    return 'unknown';
  }

  /**
   * Parse import data based on format
   */
  private async parseImportData(content: string, format: ImportFormat): Promise<ImportData | null> {
    switch (format) {
      case 'json':
        return this.parseJSONData(content);
      
      case 'csv':
        return this.parseCSVData(content);
      
      default:
        throw new Error(`Unsupported import format: ${format}`);
    }
  }

  /**
   * Parse JSON data with enhanced error handling
   */
  private parseJSONData(content: string): ImportData | null {
    try {
      const data = JSON.parse(content);
      return data;
    } catch (error) {
      throw new Error(`Invalid JSON format: ${(error as Error).message}`);
    }
  }

  /**
   * Parse CSV data (basic implementation)
   */
  private parseCSVData(content: string): ImportData | null {
    try {
      const lines = content.split('\n').filter(line => line.trim());
      const data: ImportData = {};

      let currentSection = '';
      let headers: string[] = [];
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        
        // Check for section headers
        if (trimmedLine === 'Pages') {
          currentSection = 'pages';
          data.pages = [];
          continue;
        } else if (trimmedLine === 'Blacklist') {
          currentSection = 'blacklist';
          data.blacklist = [];
          continue;
        }

        // Parse CSV headers
        if (currentSection && trimmedLine.includes(',') && !headers.length) {
          headers = this.parseCSVLine(trimmedLine);
          continue;
        }

        // Parse CSV data rows
        if (currentSection && headers.length > 0) {
          const values = this.parseCSVLine(trimmedLine);
          if (values.length === headers.length) {
            const item: any = {};
            headers.forEach((header, index) => {
              item[header.toLowerCase().replace(/\s+/g, '')] = values[index];
            });

            if (currentSection === 'pages') {
              data.pages!.push(this.convertCSVToPage(item));
            } else if (currentSection === 'blacklist') {
              data.blacklist!.push(this.convertCSVToBlacklist(item));
            }
          }
        }
      }

      return data;
    } catch (error) {
      throw new Error(`CSV parsing failed: ${(error as Error).message}`);
    }
  }

  /**
   * Parse CSV line handling quoted fields
   */
  private parseCSVLine(line: string): string[] {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"';
          i++; // Skip next quote
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  }

  /**
   * Convert CSV row to Page object
   */
  private convertCSVToPage(item: any): Partial<Page> {
    // Parse visitTime, preserve original if valid
    let visitTime = Date.now();
    if (item.visittime) {
      const parsedTime = new Date(item.visittime).getTime();
      if (!isNaN(parsedTime)) {
        visitTime = parsedTime;
      }
    }
    
    // Parse lastVisitTime, fallback to visitTime
    let lastVisitTime = visitTime;
    if (item.lastvisittime) {
      const parsedTime = new Date(item.lastvisittime).getTime();
      if (!isNaN(parsedTime)) {
        lastVisitTime = parsedTime;
      }
    }

    return {
      id: item.id || `imported_${Date.now()}_${Math.random()}`,
      url: item.url || '',
      title: item.title || '',
      domain: item.domain || new URL(item.url || 'http://example.com').hostname,
      visitTime: visitTime, // Preserve original visitTime
      accessCount: parseInt(item.accesscount) || parseInt(item.visitcount) || 1,
      content: item.content || '',
      lastUpdated: lastVisitTime // Use lastVisitTime as lastUpdated
    };
  }

  /**
   * Convert CSV row to BlacklistEntry object
   */
  private convertCSVToBlacklist(item: any): Partial<BlacklistEntry> {
    return {
      domain: item.domain || '',
      createdAt: item.createdat ? new Date(item.createdat).getTime() : Date.now(),
      reason: item.reason || undefined,
      isWildcard: item.iswildcard === 'Yes' || item.iswildcard === 'true'
    };
  }

  /**
   * Enhanced validation with warnings
   */
  private validateImportData(data: ImportData, strict: boolean = false): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Get pages from either 'pages' or 'data' field
    const pages = data.data || data.pages || [];

    // Validate pages
    if (pages.length > 0) {
      if (!Array.isArray(pages)) {
        errors.push('Pages data must be an array');
      } else {
        let missingRequiredFields = 0;
        let invalidFieldTypes = 0;
        
        pages.forEach((page, index) => {
          if (!page.url) {
            if (strict) {
              errors.push(`Page ${index}: URL is required`);
            } else {
              warnings.push(`Page ${index}: Missing URL, will be skipped`);
            }
            missingRequiredFields++;
          }
          if (!page.title) {
            warnings.push(`Page ${index}: Missing title, will use default`);
            missingRequiredFields++;
          }
          if (!page.content) {
            warnings.push(`Page ${index}: Empty content`);
          }
          
          // Check field types
          if (page.visitTime && typeof page.visitTime !== 'number') {
            warnings.push(`Page ${index}: Invalid visitTime type, will be normalized`);
            invalidFieldTypes++;
          }
          if (page.visitCount && typeof page.visitCount !== 'number') {
            warnings.push(`Page ${index}: Invalid visitCount type, will be normalized`);
            invalidFieldTypes++;
          }
        });
        
        // Add comprehensive warnings
        if (missingRequiredFields > 0) {
          warnings.push('Missing required fields');
        }
        if (invalidFieldTypes > 0) {
          warnings.push('Invalid field types detected');
        }
      }
    }

    // Validate blacklist
    if (data.blacklist) {
      if (!Array.isArray(data.blacklist)) {
        errors.push('Blacklist data must be an array');
      } else {
        data.blacklist.forEach((entry, index) => {
          if (!entry.domain) {
            errors.push(`Blacklist ${index}: Domain is required`);
          }
        });
      }
    }

    // Check for missing required fields  
    if (pages.length === 0 && (!data.blacklist || data.blacklist.length === 0)) {
      warnings.push('No data to import');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate storage quota before import
   */
  private async validateStorageQuota(data: ImportData): Promise<{ valid: boolean; error?: string }> {
    try {
      const storageInfo = await dbService.getStorageInfo();
      const pages = data.data || data.pages || [];
      
      // Estimate import size (rough calculation with overhead)
      let estimatedSize = 0;
      pages.forEach(page => {
        // Add more overhead for realistic storage calculation (JSON storage, indexes, etc.)
        estimatedSize += (page.content?.length || 0) + (page.title?.length || 0) + 1000; // 1KB overhead per page
      });

      const totalEstimated = storageInfo.estimatedSize + estimatedSize;
      const quota = 500 * 1024 * 1024; // 500MB
      const safetyMargin = 0.1; // 10% safety margin
      const effectiveQuota = quota * (1 - safetyMargin);

      if (totalEstimated > effectiveQuota) {
        return {
          valid: false,
          error: `Storage quota exceeded. Estimated total: ${Math.round(totalEstimated / 1024 / 1024)}MB, Available: ${Math.round((quota - storageInfo.estimatedSize) / 1024 / 1024)}MB`
        };
      }

      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: `Storage validation failed: ${(error as Error).message}`
      };
    }
  }

  /**
   * Import pages with enhanced merging logic
   */
  private async importPagesWithMerging(
    pages: any[], 
    detectedFormat: DetectedFormat, 
    options: Partial<ImportOptions>, 
    stats: ImportResult['stats'], 
    warnings: string[]
  ): Promise<void> {
    if (!stats) return;
    const maxItems = options.maxItems || pages.length;
    const itemsToProcess = pages.slice(0, maxItems);
    
    for (let i = 0; i < itemsToProcess.length; i++) {
      const pageData = itemsToProcess[i];
      stats.totalProcessed++;

      // Progress callback
      if (options.progressCallback && i % 10 === 0) {
        options.progressCallback({
          total: itemsToProcess.length,
          processed: i,
          percentage: Math.round((i / itemsToProcess.length) * 100)
        });
      }

      try {
        // Process page based on detected format
        const processedPage = this.processPageByFormat(pageData, detectedFormat, warnings);
        
        // Skip if no valid URL
        if (!processedPage.url) {
          stats.pagesSkipped++;
          continue;
        }

        // Check blacklist if requested
        if (options.respectBlacklist) {
          const domain = new URL(processedPage.url).hostname;
          // Note: Would integrate with blacklist service when available
          if (domain === 'blocked.com') { // Mock blacklist check
            stats.pagesSkipped++;
            continue;
          }
        }

        // Truncate content if too large
        if (options.maxContentSize && processedPage.content && processedPage.content.length > options.maxContentSize) {
          processedPage.content = processedPage.content.substring(0, options.maxContentSize);
          warnings.push('Content truncated');
        }

        // Check if page already exists
        const existingPage = await dbService.getPageByUrl(processedPage.url);
        
        if (existingPage) {
          // Handle merging
          if (options.mergeStrategy) {
            const mergedPage = this.mergePages(existingPage, processedPage, options.mergeStrategy);
            await dbService.updatePage(existingPage.id, mergedPage);
            stats.pagesMerged++;
          } else if (options.overwriteExisting) {
            await dbService.updatePage(existingPage.id, processedPage);
            stats.pagesImported++;
          } else {
            stats.pagesSkipped++;
          }
        } else {
          // Add new page using addPageForImport to preserve timestamps
          await dbService.addPageForImport({
            url: processedPage.url,
            title: processedPage.title,
            content: processedPage.content,
            visitTime: processedPage.visitTime,
            accessCount: processedPage.accessCount || processedPage.visitCount,
            lastUpdated: processedPage.lastUpdated,
            contentStatus: processedPage.contentStatus,
            extractionError: processedPage.extractionError,
            lastExtractionAttempt: processedPage.lastExtractionAttempt
          });
          stats.pagesImported++;
        }

      } catch (error) {
        const errorMsg = `Failed to import page ${pageData.url}: ${(error as Error).message}`;
        stats.errors.push(errorMsg);
        stats.pagesFailed++;
        
        if (!options.continueOnError && !options.skipErrors) {
          throw new Error(errorMsg);
        }
      }
    }

    // Final progress callback
    if (options.progressCallback) {
      options.progressCallback({
        total: itemsToProcess.length,
        processed: itemsToProcess.length,
        percentage: 100
      });
    }
  }

  /**
   * Process page data based on detected format
   */
  private processPageByFormat(pageData: any, format: DetectedFormat, warnings: string[]): any {
    const now = Date.now();
    
    // Start with page data
    const processedPage = { ...pageData };

    // Generate missing required fields
    if (!processedPage.url) {
      warnings.push('Missing required URL field');
      return processedPage; // Will be skipped
    }

    if (!processedPage.title) {
      processedPage.title = 'Untitled';
    }

    if (!processedPage.content) {
      processedPage.content = '';
    }

    if (!processedPage.domain) {
      try {
        processedPage.domain = new URL(processedPage.url).hostname;
      } catch {
        processedPage.domain = 'unknown.com';
      }
    }

    // Handle timestamps based on format
    if (format === 'v3.0') {
      // V3.0 timestamp generation
      if (!processedPage.visitTime || typeof processedPage.visitTime !== 'number') {
        processedPage.visitTime = now;
      }
      if (!processedPage.lastVisitTime || typeof processedPage.lastVisitTime !== 'number') {
        processedPage.lastVisitTime = processedPage.visitTime;
      }
      if (!processedPage.visitCount || typeof processedPage.visitCount !== 'number') {
        processedPage.visitCount = 1;
      }
    } else if (format === 'v4.0') {
      // V4.0 timestamp validation and normalization
      if (typeof processedPage.visitTime !== 'number' || isNaN(processedPage.visitTime)) {
        processedPage.visitTime = now;
      } else if (processedPage.visitTime < 0) {
        processedPage.visitTime = now;
      }
      
      if (typeof processedPage.lastVisitTime !== 'number' || isNaN(processedPage.lastVisitTime)) {
        processedPage.lastVisitTime = processedPage.visitTime;
      } else if (processedPage.lastVisitTime < 0) {
        processedPage.lastVisitTime = processedPage.visitTime;
      }
      
      if (typeof processedPage.visitCount !== 'number' || isNaN(processedPage.visitCount)) {
        processedPage.visitCount = 1;
      } else if (processedPage.visitCount < 0) {
        processedPage.visitCount = 1;
      } else if (processedPage.visitCount > 1000000) {
        processedPage.visitCount = 1000000; // Cap extreme values
      }
    } else {
      // Unknown format - use safe defaults
      processedPage.visitTime = processedPage.visitTime || now;
      processedPage.lastVisitTime = processedPage.lastVisitTime || processedPage.visitTime;
      processedPage.visitCount = processedPage.visitCount || 1;
    }

    // Ensure other required fields
    processedPage.accessCount = processedPage.accessCount || processedPage.visitCount || 1;
    processedPage.lastUpdated = now;

    return processedPage;
  }

  /**
   * Merge two pages based on strategy
   */
  private mergePages(existing: any, imported: any, strategy: MergeStrategy): any {
    switch (strategy) {
      case 'smart':
        return {
          ...existing,
          title: imported.title || existing.title, // Prefer non-empty
          content: imported.content || existing.content, // Prefer non-empty
          visitTime: Math.min(existing.visitTime, imported.visitTime || existing.visitTime), // Earliest
          lastVisitTime: Math.max(existing.lastVisitTime || existing.visitTime, imported.lastVisitTime || imported.visitTime), // Latest
          visitCount: (existing.visitCount || 0) + (imported.visitCount || 0), // Sum
          accessCount: (existing.accessCount || 0) + (imported.accessCount || 0), // Sum
          lastUpdated: Date.now()
        };
        
      case 'timestamp-priority':
        return {
          ...existing,
          visitTime: Math.min(existing.visitTime, imported.visitTime || existing.visitTime),
          lastVisitTime: Math.max(existing.lastVisitTime || existing.visitTime, imported.lastVisitTime || imported.visitTime),
          visitCount: (existing.visitCount || 0) + (imported.visitCount || 0),
          lastUpdated: Date.now()
        };
        
      case 'preserve-existing':
        return {
          ...existing,
          title: existing.title || imported.title, // Keep existing non-empty
          content: existing.content || imported.content, // Keep existing non-empty
          visitCount: (existing.visitCount || 0) + (imported.visitCount || 1), // Still increment
          lastUpdated: Date.now()
        };
        
      case 'overwrite':
        return {
          ...imported,
          id: existing.id, // Keep existing ID
          lastUpdated: Date.now()
        };
        
      case 'safe':
      default:
        return {
          ...existing,
          visitCount: (existing.visitCount || 0) + 1, // Just increment count
          lastUpdated: Date.now()
        };
    }
  }

  /**
   * Validate data after import
   */
  private async validateAfterImport(stats: ImportResult['stats']): Promise<{ allPagesValid: boolean; inconsistenciesFound: number }> {
    // This would perform comprehensive validation of imported data
    // For now, return mock validation results
    if (!stats) {
      return {
        allPagesValid: true,
        inconsistenciesFound: 0
      };
    }
    return {
      allPagesValid: stats.pagesFailed === 0,
      inconsistenciesFound: stats.pagesFailed
    };
  }

  /**
   * Import pages data
   */

  /**
   * Import blacklist data
   */
  private async importBlacklist(blacklist: any[], options: ImportOptions, stats: ImportResult['stats']): Promise<void> {
    if (!stats) return;
    const maxItems = options.maxItems || blacklist.length;
    const itemsToProcess = blacklist.slice(0, maxItems);

    for (const entryData of itemsToProcess) {
      stats.totalProcessed++;
      
      try {
        // Check if entry already exists
        const existing = await blacklistService.getDomainEntry(entryData.domain);
        
        if (existing && !options.overwriteExisting) {
          stats.blacklistSkipped++;
          continue;
        }

        // Add or update blacklist entry
        if (existing && options.overwriteExisting) {
          await blacklistService.updateDomain(entryData.domain, {
            reason: entryData.reason,
            isWildcard: entryData.isWildcard
          });
        } else {
          await blacklistService.addDomain(
            entryData.domain,
            entryData.reason,
            entryData.isWildcard
          );
        }
        
        stats.blacklistImported++;

      } catch (error) {
        const errorMsg = `Failed to import blacklist entry ${entryData.domain}: ${(error as Error).message}`;
        stats.errors.push(errorMsg);
        
        if (!options.skipErrors) {
          throw new Error(errorMsg);
        }
      }
    }
  }

  /**
   * Import settings data (placeholder)
   */
  private async importSettings(settings: Record<string, any>, _options: ImportOptions, stats: ImportResult['stats']): Promise<void> {
    // Placeholder for future settings import implementation
    if (stats) {
      stats.settingsImported = Object.keys(settings).length;
    }
  }
}

// Export singleton instance
export const importService = ImportService.getInstance();
