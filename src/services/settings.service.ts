/**
 * Settings service module
 * 
 * Provides high-level settings management functionality
 * Built on top of the database service
 */

import type { SearchEngineConfig } from '../models';
import {
  SETTING_KEYS,
  DEFAULT_SEARCH_ENGINE_CONFIG,
  validateSearchEngineConfig,
  normalizeSearchEngineWeights
} from '../models';
import { dbService } from './db.service';

/**
 * Settings service class
 * Handles application settings with type safety and validation
 */
export class SettingsService {
  private static instance: SettingsService | null = null;

  private constructor() {}

  /**
   * Get service instance (singleton pattern)
   */
  public static getInstance(): SettingsService {
    if (!SettingsService.instance) {
      SettingsService.instance = new SettingsService();
    }
    return SettingsService.instance;
  }

  /**
   * Get search engine configuration
   * Returns the stored config or default if not set
   */
  public async getSearchConfig(): Promise<SearchEngineConfig> {
    try {
      const config = await dbService.getSetting(SETTING_KEYS.SEARCH_ENGINE_CONFIG);
      
      if (!config) {
        // Return default config if not set
        return DEFAULT_SEARCH_ENGINE_CONFIG;
      }

      // Validate stored config
      const validation = validateSearchEngineConfig(config);
      if (!validation.valid) {
        console.warn('Invalid search engine config stored, returning default:', validation.errors);
        return DEFAULT_SEARCH_ENGINE_CONFIG;
      }

      return config;
    } catch (error) {
      console.error('Failed to get search engine config:', error);
      return DEFAULT_SEARCH_ENGINE_CONFIG;
    }
  }

  /**
   * Update search engine configuration
   * Validates and normalizes the config before storing
   */
  public async updateSearchConfig(config: SearchEngineConfig): Promise<SearchEngineConfig> {
    try {
      // Validate config
      const validation = validateSearchEngineConfig(config);
      if (!validation.valid) {
        // Try to normalize the config
        const normalized = normalizeSearchEngineWeights(config);
        
        // Re-validate normalized config
        const normalizedValidation = validateSearchEngineConfig(normalized);
        if (!normalizedValidation.valid) {
          throw new Error(`Invalid search engine config: ${normalizedValidation.errors.join(', ')}`);
        }
        
        config = normalized;
      }

      // Store the config
      await dbService.setSetting(SETTING_KEYS.SEARCH_ENGINE_CONFIG, config);
      
      return config;
    } catch (error) {
      console.error('Failed to update search engine config:', error);
      throw error;
    }
  }

  /**
   * Reset search engine configuration to default
   */
  public async resetSearchConfig(): Promise<SearchEngineConfig> {
    try {
      await dbService.setSetting(SETTING_KEYS.SEARCH_ENGINE_CONFIG, DEFAULT_SEARCH_ENGINE_CONFIG);
      return DEFAULT_SEARCH_ENGINE_CONFIG;
    } catch (error) {
      console.error('Failed to reset search engine config:', error);
      throw error;
    }
  }

  /**
   * Get a generic setting by key
   */
  public async getSetting<T = any>(key: string): Promise<T | null> {
    try {
      return await dbService.getSetting(key);
    } catch (error) {
      console.error(`Failed to get setting ${key}:`, error);
      return null;
    }
  }

  /**
   * Set a generic setting by key
   */
  public async setSetting<T = any>(key: string, value: T): Promise<void> {
    try {
      await dbService.setSetting(key, value);
    } catch (error) {
      console.error(`Failed to set setting ${key}:`, error);
      throw error;
    }
  }

  /**
   * Delete a setting by key
   */
  public async deleteSetting(key: string): Promise<boolean> {
    try {
      return await dbService.deleteSetting(key);
    } catch (error) {
      console.error(`Failed to delete setting ${key}:`, error);
      return false;
    }
  }

  /**
   * Get all settings
   */
  public async getAllSettings(): Promise<Record<string, any>> {
    try {
      return await dbService.getAllSettings();
    } catch (error) {
      console.error('Failed to get all settings:', error);
      return {};
    }
  }
}

// Export singleton instance
export const settingsService = SettingsService.getInstance();