/**
 * I18n Type Definitions
 * Defines all types used across the internationalization system
 */

export type SupportedLanguage = 'en' | 'zh-CN' | 'ja' | 'es' | 'fr';

export interface TranslationResource {
  [key: string]: string | TranslationResource;
}

export interface I18nConfig {
  defaultLanguage: SupportedLanguage;
  fallbackLanguage: SupportedLanguage;
  loadPath: string;
  cacheEnabled: boolean;
  cacheSize: number; // Maximum number of languages to cache
}

export interface LanguageMetadata {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
}

export const LANGUAGE_METADATA: Record<SupportedLanguage, LanguageMetadata> = {
  'en': {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    direction: 'ltr'
  },
  'zh-CN': {
    code: 'zh-CN',
    name: 'Chinese (Simplified)',
    nativeName: '中文简体',
    direction: 'ltr'
  },
  'ja': {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    direction: 'ltr'
  },
  'es': {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    direction: 'ltr'
  },
  'fr': {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    direction: 'ltr'
  }
};

export interface I18nChangeEvent {
  oldLanguage: SupportedLanguage;
  newLanguage: SupportedLanguage;
  timestamp: number;
}

export interface ResourceLoadMetrics {
  language: SupportedLanguage;
  loadTime: number;
  cacheHit: boolean;
  resourceSize: number;
  timestamp: number;
}

export interface I18nError extends Error {
  code: 'RESOURCE_LOAD_FAILED' | 'UNSUPPORTED_LANGUAGE' | 'STORAGE_ERROR' | 'PARSE_ERROR';
  language?: SupportedLanguage;
  details?: any;
}

export class I18nErrorImpl extends Error implements I18nError {
  code: I18nError['code'];
  language?: SupportedLanguage;
  details?: any;

  constructor(code: I18nError['code'], message: string, language?: SupportedLanguage, details?: any) {
    super(message);
    this.name = 'I18nError';
    this.code = code;
    this.language = language;
    this.details = details;
  }
}