/**
 * i18n Configuration for Recall Extension
 * 
 * Sets up internationalization with support for English, Chinese, and Japanese
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import language resources
import enResources from './locales/en';
import zhResources from './locales/zh-CN';
import jaResources from './locales/ja';

/**
 * Supported languages
 */
export const SUPPORTED_LANGUAGES = {
  en: 'English',
  'zh-CN': '中文',
  ja: '日本語'
} as const;

export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES;

/**
 * Default language
 */
export const DEFAULT_LANGUAGE: SupportedLanguage = 'en';

/**
 * Get language from Chrome storage
 */
const getStoredLanguage = async (): Promise<SupportedLanguage> => {
  try {
    const result = await chrome.storage.local.get(['language']);
    return result.language || DEFAULT_LANGUAGE;
  } catch (error) {
    console.warn('Failed to get language from storage:', error);
    return DEFAULT_LANGUAGE;
  }
};

/**
 * Save language to Chrome storage
 */
export const saveLanguage = async (language: SupportedLanguage): Promise<void> => {
  try {
    await chrome.storage.local.set({ language });
  } catch (error) {
    console.error('Failed to save language to storage:', error);
  }
};

/**
 * Initialize i18n
 */
export const initI18n = async (): Promise<void> => {
  const storedLanguage = await getStoredLanguage();

  await i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      // Language resources
      resources: {
        en: { translation: enResources },
        'zh-CN': { translation: zhResources },
        ja: { translation: jaResources }
      },

      // Language settings
      lng: storedLanguage,
      fallbackLng: DEFAULT_LANGUAGE,
      
      // Namespace settings
      defaultNS: 'translation',
      ns: ['translation'],

      // Detection settings
      detection: {
        // Don't use automatic detection, rely on stored preference
        order: [],
        caches: []
      },

      // Interpolation settings
      interpolation: {
        escapeValue: false // React already escapes values
      },

      // React settings
      react: {
        useSuspense: false // Disable suspense for Chrome extension compatibility
      },

      // Debug settings (disable in production)
      debug: process.env.NODE_ENV === 'development'
    });

  // Update HTML lang attribute
  document.documentElement.lang = storedLanguage;
};

/**
 * Change language and persist to storage
 */
export const changeLanguage = async (language: SupportedLanguage): Promise<void> => {
  await saveLanguage(language);
  await i18n.changeLanguage(language);
  
  // Update HTML lang attribute
  document.documentElement.lang = language;
};

export default i18n;