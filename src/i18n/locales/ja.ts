/**
 * Japanese language resources
 */

export default {
  // Common/General
  common: {
    loading: '読み込み中...',
    search: '検索',
    clear: 'クリア',
    close: '閉じる',
    save: '保存',
    cancel: 'キャンセル',
    delete: '削除',
    edit: '編集',
    add: '追加',
    remove: '削除',
    yes: 'はい',
    no: 'いいえ',
    ok: 'OK',
    retry: '再試行',
    refresh: '更新',
    settings: '設定',
    about: 'について',
    help: 'ヘルプ',
    language: '言語',
    all: 'すべて',
    relevance: '関連性',
    syntax: '構文'
  },

  // Main App
  app: {
    title: 'Recall',
    subtitle: 'インテリジェント履歴検索',
    searchPlaceholder: 'ブラウズ履歴を検索...',
    clearSearch: '検索をクリア',
    toggleDensity: '密度の切り替え',
    densityMode: {
      compact: 'コンパクト密度',
      comfortable: '快適密度'
    }
  },

  // Search
  search: {
    placeholder: '検索...',
    noResults: '結果が見つかりません',
    searching: '検索中...',
    results: '件の結果',
    result: '件の結果',
    searchTime: '検索完了: {{time}}ms',
    clearSearchTitle: '検索をクリア',
    syntaxHelp: '検索構文ヘルプ'
  },

  // Error handling
  errors: {
    // Error types
    serviceInit: {
      title: 'サービス初期化エラー',
      message: '検索サービスが正常に開始できません。ブラウザの互換性の問題の可能性があります',
      solutions: [
        'ページを更新して再試行',
        'ブラウザがIndexedDBをサポートしているか確認',
        'ブラウザのキャッシュとデータをクリア',
        'ブラウザを再起動'
      ]
    },
    search: {
      title: '検索サービスエラー',
      message: '検索機能が一時的に利用できません。データインデックスの問題の可能性があります',
      solutions: [
        '検索キーワードが有効か確認',
        '検索語を簡略化してみる',
        '数秒待ってから再試行',
        'ページを更新して最初からやり直す'
      ]
    },
    network: {
      title: 'ネットワーク接続の問題',
      message: '必要なサービスに接続できません。ネットワーク状態を確認してください',
      solutions: [
        'ネットワーク接続が正常か確認',
        'ページを更新して再試行',
        '後でもう一度試す'
      ]
    },
    database: {
      title: 'データストレージの問題',
      message: 'ローカルデータベースアクセスエラー。データのクリーンアップが必要かもしれません',
      solutions: [
        'ブラウザのストレージ容量を確認',
        '拡張機能のデータをクリアして最初からやり直す',
        '拡張機能設定でデータをリセット',
        'テクニカルサポートに連絡'
      ]
    },
    unknown: {
      title: '不明なエラー',
      message: '予期しないエラーが発生しました。ページを更新してみてください',
      solutions: [
        'ページを更新して再試行',
        'ブラウザを再起動',
        '拡張機能が正常に動作しているか確認',
        '問題が続く場合はサポートに連絡'
      ]
    },
    // Error actions
    reload: '🔄 再読み込み',
    closeError: '✕ エラーを閉じる',
    openSettings: '⚙️ 設定を開く',
    solutionsTitle: '💡 解決策'
  },

  // Empty state
  empty: {
    title: '履歴の検索を開始',
    description: '上記にキーワードを入力して、以前訪問したページを素早く見つけましょう',
    features: {
      fuzzyMatch: 'スマートファジーマッチング',
      multiFilter: '多次元フィルタリング',
      fastResponse: 'ミリ秒レスポンス'
    },
    searchTips: {
      title: '検索のコツ',
      items: [
        '多言語混合検索をサポート',
        'ページタイトル、URL、コンテンツを検索可能',
        'フィルターを使用して正確な結果を取得'
      ]
    },
    keyboardShortcuts: {
      title: '⌨️ キーボードショートカット',
      focusSearch: '検索にフォーカス',
      clearSearch: '検索をクリア',
      browseResults: '結果を閲覧',
      openPage: 'ページを開く',
      quickOpen: 'クイックオープン'
    },
    searchExamples: {
      title: 'これらの検索例を試してみてください',
      basic: {
        text: 'React JavaScript',
        description: '基本検索'
      },
      exact: {
        text: '"best practices"',
        description: '完全一致'
      },
      site: {
        text: 'site:github.com',
        description: 'サイト指定'
      },
      exclude: {
        text: 'Vue -tutorial',
        description: '除外検索'
      }
    }
  },

  // Options page
  options: {
    title: '設定センター',
    pageNotFound: 'ページが見つかりません',
    selectOption: '左のナビゲーションからオプションを選択してください。',
    loadingConfig: '設定センターを読み込み中...',
    
    // App settings
    app: {
      title: 'アプリ設定',
      description: 'アプリケーションの一般設定を構成します。',
      settings: {
        theme: {
          label: 'テーマ',
          description: 'ライトテーマとダークテーマを選択します。',
          options: {
            light: 'ライト',
            dark: 'ダーク',
            system: 'システム'
          }
        },
        density: {
          label: '密度',
          description: 'インターフェースの情報密度を調整します。',
          options: {
            compact: 'コンパクト',
            default: 'デフォルト',
            comfortable: '快適'
          }
        },
        language: {
          label: '言語',
          description: 'アプリケーションの表示言語を設定します。'
        }
      }
    },

    // Language settings
    languageSettings: {
      description: 'インターフェース言語とローカライゼーション設定を構成します。',
      interfaceLanguage: 'インターフェース言語',
      interfaceDescription: '拡張機能のインターフェース言語を選択します。',
      note: '言語の変更は再起動せずに即座に有効になります。'
    },

    // History Management
    historyManagement: {
      title: '📚 履歴管理',
      description: 'ブラウジング履歴の表示、検索、管理',
      loading: '履歴レコードを読み込み中...',
      stats: {
        totalPages: '総ページ数：{{count}}',
        totalDomains: 'ユニークドメイン：{{count}}',
        dateRange: '{{oldest}} から {{newest}} まで',
        totalSize: '総サイズ：{{size}} MB'
      },
      search: {
        placeholder: 'タイトル、URL、コンテンツを検索...',
        noResults: '一致する履歴が見つかりません',
        searching: '検索中...'
      },
      sorting: {
        label: 'ソート：',
        options: {
          visitTimeDesc: '最新訪問',
          visitTimeAsc: '最古訪問',
          lastUpdatedDesc: '最新更新',
          lastUpdatedAsc: '最古更新',
          domainAsc: 'ドメインソート (A-Z)',
          idDesc: 'ID順'
        }
      },
      actions: {
        selectAll: 'すべて選択',
        deselectAll: '選択解除',
        deleteSelected: '選択項目を削除',
        deleteConfirm: '"{{title}}" を削除しますか？',
        deleteSuccess: '削除成功',
        deleteError: '削除に失敗しました。再試行してください',
        exportSelected: '選択項目をエクスポート',
        refreshList: 'リストを更新'
      },
      item: {
        visitTime: '訪問時刻 {{time}}',
        readingTime: '読書時間 {{duration}}',
        accessCount: 'アクセス数 {{count}}',
        accessCountTooltip: 'アクセス数',
        lastVisitTooltip: '最終訪問時刻',
        contentSizeTooltip: 'コンテンツサイズ',
        delete: 'このレコードを削除',
        deleteAriaLabel: '{{title}} を削除',
        openInNewTab: '新しいタブで開く',
        sizeKB: '{{size}} KB'
      },
      emptyState: {
        noHistory: '履歴レコードが見つかりません',
        noSearchResults: '"{{query}}" を含むページが見つかりません',
        startBrowsing: 'ウェブの閲覧を開始して履歴を作成してください'
      }
    },

    // API Key Management
    apiKeys: {
      title: '🔑 APIキー管理',
      description: 'AIサービスプロバイダーのAPIキーを設定し、BYOK（Bring Your Own Key）モードを有効にします',
      loading: 'APIキー設定を読み込み中...',
      privacy: {
        title: 'プライバシー保護',
        points: [
          'すべてのAPIキーはAES-256で暗号化され、ローカルに保存されます',
          'キーは対応するAIサービスプロバイダーに直接接続し、弊社のサーバーを経由しません',
          'データとキーのセキュリティを完全にコントロールできます'
        ]
      },
      providers: {
        openai: {
          name: 'OpenAI',
          description: 'GPT-4、GPT-3.5などのモデル',
          setupGuide: 'OpenAIプラットフォームでAPIキーを作成',
          keyFormat: 'sk-...'
        },
        anthropic: {
          name: 'Anthropic',
          description: 'Claudeシリーズモデル',
          setupGuide: 'Anthropic ConsoleでAPIキーを作成',
          keyFormat: 'sk-ant-...'
        },
        deepseek: {
          name: 'DeepSeek',
          description: 'DeepSeek Chatモデル',
          setupGuide: 'DeepSeekプラットフォームでAPIキーを作成',
          keyFormat: 'sk-...'
        },
        google: {
          name: 'Google AI',
          description: 'Geminiシリーズモデル',
          setupGuide: 'Google AI StudioでAPIキーを作成',
          keyFormat: 'AI...'
        },
        azure: {
          name: 'Azure OpenAI',
          description: 'Azure上でホストされるOpenAIモデル',
          setupGuide: 'Azure PortalでOpenAIリソースを設定',
          keyFormat: 'エンドポイントとキーが必要'
        },
        litellm: {
          name: 'LiteLLM',
          description: '複数のモデルプロバイダーをサポートする統合インターフェース',
          setupGuide: 'LiteLLMプロキシエンドポイントを設定',
          keyFormat: '特定のプロバイダーに依存'
        }
      },
      modal: {
        title: '{{providerName}} APIキーの設定',
        apiKeyLabel: 'APIキー',
        apiKeyPlaceholder: '{{providerName}} APIキーを入力してください',
        endpointLabel: 'APIエンドポイント',
        endpointPlaceholder: 'https://your-resource.openai.azure.com/',
        securityNotice: '🔐 キーはAES-256暗号化アルゴリズムを使用してローカルに安全に保存され、拡張機能でのみ使用されます'
      },
      messages: {
        invalidKey: '有効なAPIキーを入力してください',
        saveSuccess: 'APIキーが安全に保存されました',
        saveError: '保存に失敗しました。再試行してください',
        removeConfirm: '{{providerName}}のAPIキーを削除してもよろしいですか？',
        removeSuccess: 'APIキーが削除されました',
        removeError: '削除に失敗しました。再試行してください',
        testSuccess: 'APIキーの検証が成功しました',
        testError: 'APIキーの検証に失敗しました',
        keyNotFound: 'APIキーが見つかりません'
      }
    },

    // Blacklist Management
    blacklistManagement: {
      title: 'ブラックリスト',
      description: 'インデックスしたくないウェブサイトドメインを管理',
      loading: '読み込み中...',
      
      // Header stats
      stats: {
        totalDomains: '総ドメイン数: {{count}}',
        wildcardDomains: 'ワイルドカード: {{count}}'
      },
      
      // Search functionality
      search: {
        placeholder: 'ドメインと理由を検索...'
      },
      
      // Add domain modal and form
      addDomain: {
        modalTitle: '新しいブロックドメインを追加',
        domainLabel: 'ドメイン',
        domainPlaceholder: 'example.com',
        domainPlaceholderWildcard: '*.example.com',
        reasonLabel: '理由（オプション）',
        reasonPlaceholder: '理由を説明（オプション）',
        wildcardLabel: 'ワイルドカード一致（すべてのサブドメインに一致）',
        submitButton: '追加確認',
        submitButtonLoading: '追加中...',
        cancelButton: 'キャンセル',
        
        // Validation errors
        errors: {
          domainRequired: 'ドメインを入力してください',
          domainInvalid: '有効なドメイン形式を入力してください',
          wildcardInvalid: 'ワイルドカードドメインは *. で始まる必要があります'
        },
        
        // Status messages
        addFailed: '追加に失敗しました。もう一度お試しください',
        
        // Domain suggestions
        suggestions: {
          title: 'インデックス済みサイトの提案',
          loading: '提案を読み込み中...',
          count: '({{count}})',
          noSuggestions: '提案なし',
          pageCount: '{{count}} ページ',
          visitCount: '{{count}} 回訪問',
          separator: '•'
        }
      },
      
      // Domain list and items
      domainList: {
        noReason: 'なし',
        exactMatch: '正確',
        wildcardMatch: 'ワイルドカード',
        exactMatchTooltip: '正確一致',
        wildcardMatchTooltip: 'ワイルドカード一致',
        addedTimeTooltip: '追加時刻: {{time}}',
        removeTooltip: 'ブラックリストから削除',
        removeAriaLabel: '{{domain}}を削除'
      },
      
      // Bulk actions
      bulkActions: {
        clearAll: 'すべてクリア',
        clearAllTooltip: 'すべてのブロックドメインをクリア',
        addDomain: 'ドメイン追加',
        addDomainTooltip: '新しいブロックドメインを追加'
      },
      
      // Confirmation dialogs
      confirmations: {
        removeDomain: '"{{domain}}"をブラックリストから削除してもよろしいですか？この操作は元に戻せません。',
        clearAll: 'ブラックリスト全体（{{count}}個のエントリ）をクリアしてもよろしいですか？この操作は元に戻せません。',
        deleteFailed: '削除に失敗しました。もう一度お試しください',
        clearFailed: 'クリアに失敗しました。もう一度お試しください'
      },
      
      // Empty states
      emptyState: {
        noResults: '結果が見つかりません',
        noResultsDescription: '"{{query}}"に関連するドメインが見つかりませんでした',
        empty: 'ブラックリストは空です',
        emptyDescription: 'ドメインをブラックリストに追加すると、関連ページがインデックスされなくなります',
        addFirstDomain: '最初のドメインを追加'
      },
      
      // Help tooltip
      help: {
        title: '使用方法',
        exactMatch: '正確一致：指定されたドメインのみに一致（例："example.com"）',
        wildcardMatch: 'ワイルドカード一致：ドメインとそのすべてのサブドメインに一致（例："*.example.com"）',
        autoEffect: '自動有効：ブラックリストに追加されたドメインは新しいページでインデックスされません',
        existingData: '既存データ：ブラックリストは既にインデックスされたページには影響しません。手動削除が必要です'
      }
    },
    
    // Navigation items
    navigation: {
      historyManagement: {
        label: '履歴記録',
        description: 'ブラウズ履歴の表示、検索、管理'
      },
      blacklistManagement: {
        label: 'ブラックリスト',
        description: 'インデックスしたくないウェブサイトドメインの管理'
      },
      dataBackup: {
        label: 'インポート・エクスポート',
        description: '履歴データのエクスポートとインポート'
      },
      searchSettings: {
        label: '検索設定',
        description: '検索動作と設定の構成'
      },
      apiKeyManagement: {
        label: 'APIキー管理',
        description: 'AIサービスプロバイダーのAPIキーの管理'
      },
      readingAssistant: {
        label: '読書アシスタント',
        description: 'スマート読書アシスタントとAI要約機能の設定'
      },
      languageSettings: {
        label: '言語',
        description: 'インターフェース言語とローカライゼーション設定の変更'
      },
      about: {
        label: 'について',
        description: 'バージョン情報と使用方法ヘルプ'
      }
    },

    // Common options translations
    common: {
      loading: '読み込み中...',
      saving: '保存中...',
      saved: '設定が保存されました',
      saveFailed: '保存に失敗しました。再試行してください',
      resetToDefaults: 'デフォルトにリセット',
      resetConfirm: 'デフォルト設定にリセットしてもよろしいですか？',
      configure: '設定',
      configured: '設定済み',
      unconfigured: '未設定',
      testConnection: '接続テスト',
      removeKey: 'キーを削除',
      getKey: 'キーを取得',
      saveKey: 'キーを保存',
      cancel: 'キャンセル',
      close: '×',
      enabled: '有効',
      disabled: '無効',
      version: 'バージョン {{version}}',
      units: {
        items: '項目',
        ms: 'ms',
        seconds: '秒',
        minutes: '分',
        mb: 'MB',
        percent: '%'
      }
    },

    // Search Settings
    search: {
      title: '⚙️ 検索設定',
      description: 'セマンティック検索とキーワード検索の動作パラメータを設定',
      loading: '検索設定を読み込み中...',
      sections: {
        results: {
          title: '📊 検索結果',
          description: '検索結果の数量と表示を制御'
        },
        traditional: {
          title: '🔍 伝統的な検索',
          description: 'キーワードマッチングに基づく伝統的な検索設定'
        },
        keyword: {
          title: '🔍 キーワード検索',
          description: '従来のキーワードマッチング検索設定'
        },
        indexing: {
          title: '📚 コンテンツインデックス化',
          description: 'ページコンテンツの自動インデックス化設定'
        },
        userExperience: {
          title: '🎯 ユーザーエクスペリエンス',
          description: '検索インターフェースとインタラクション体験設定'
        }
      },
      settings: {
        maxResults: {
          label: '最大検索結果数',
          description: '単一検索で返される結果の最大数'
        },
        searchTimeout: {
          label: '検索タイムアウト',
          description: '検索リクエストの最大待機時間'
        },
        traditionalSearchWeight: {
          label: '伝統的な検索の重み',
          description: 'ハイブリッド検索における伝統的な検索の重み比率'
        },
        enableKeywordSearch: {
          label: 'キーワード検索を有効にする',
          description: '従来のキーワードマッチング検索を使用'
        },
        fuzzySearchThreshold: {
          label: 'ファジー検索の閾値',
          description: 'ファジーマッチングの感度を制御（小さいほど厳密）'
        },
        keywordSearchWeight: {
          label: 'キーワード検索の重み',
          description: 'ハイブリッド検索におけるキーワード検索の重み比率'
        },
        enableIndexing: {
          label: '自動インデックス化を有効にする',
          description: '訪問したページのコンテンツを自動的にインデックス化'
        },
        indexingDelay: {
          label: 'インデックス化の遅延',
          description: 'ページ読み込み後、インデックス化を開始するまでの遅延'
        },
        enableSearchHistory: {
          label: '検索履歴を有効にする',
          description: '最近の検索クエリを記憶'
        },
        enableAutoComplete: {
          label: '自動補完を有効にする',
          description: '入力中に検索候補を表示'
        },
        enableDebugMode: {
          label: 'デバッグモードを有効にする',
          description: '詳細な検索デバッグ情報を表示'
        }
      }
    },

    // Data Backup
    dataBackup: {
      title: '💾 データバックアップ＆インポート',
      description: 'ブラウジング履歴データのエクスポートとインポート',
      loading: 'データバックアップインターフェースを読み込み中...',
      stats: {
        title: 'データ統計',
        totalPages: '総ページ数：{{count}}',
        totalBlacklist: 'ブラックリストドメイン：{{count}}',
        lastUpdate: '最終更新：{{time}}'
      },
      export: {
        title: 'データエクスポート',
        description: 'すべてのブラウジング履歴と設定をJSONファイルとしてエクスポート',
        button: 'すべてのデータをエクスポート',
        processing: 'エクスポート中...',
        progress: 'エクスポート中... {{progress}}%',
        success: 'データが正常にエクスポートされました',
        error: 'エクスポートに失敗しました：{{error}}'
      },
      import: {
        title: 'データインポート',
        description: '以前にエクスポートしたデータファイルをインポート',
        button: 'インポートファイルを選択',
        processing: 'インポートを処理中...',
        success: 'インポートが正常に完了しました',
        error: 'インポートに失敗しました：{{error}}',
        invalidFormat: '無効なバックアップファイル形式',
        stats: {
          pagesImported: 'インポートされたページ：{{count}}',
          blacklistImported: 'インポートされたブラックリスト：{{count}}',
          pagesSkipped: 'スキップされたページ：{{count}}',
          blacklistSkipped: 'スキップされたブラックリスト：{{count}}'
        }
      },
      warnings: {
        exportSize: 'エクスポートファイルのサイズは履歴のサイズによって大きくなる場合があります',
        importOverwrite: 'インポートは既存のデータとマージされ、重複は自動的にスキップされます',
        backupFirst: 'インポート前に現在のデータをバックアップすることをお勧めします'
      },
      dangerZone: {
        title: '危険ゾーン',
        description: 'すべてのデータをクリアすると、すべてのブラウジング履歴とブラックリストが永続的に削除されます。この操作は元に戻すことができません。続行する前に重要なデータをバックアップしていることを確認してください。',
        clearAllButton: '🗑️ すべてのデータをクリア',
        clearAllConfirm: 'すべてのデータをクリアしてもよろしいですか？これによりすべてのブラウジング履歴とブラックリストが永続的に削除されます。この操作は元に戻すことができません！',
        clearAllDoubleConfirm: '再度確認してください：これによりブラウジング履歴とブラックリストを含むすべてのデータが永続的に削除されます。続行してもよろしいですか？',
        clearAllSuccess: 'すべてのデータがクリアされました',
        clearAllError: 'クリアに失敗しました。再試行してください'
      },
      importResults: {
        title: 'インポート結果',
        pagesImported: 'インポートされた履歴：',
        blacklistImported: 'インポートされたブラックリスト：',
        pagesSkipped: 'スキップされた履歴：',
        blacklistSkipped: 'スキップされたブラックリスト：',
        entries: '{{count}}エントリ',
        importErrors: 'インポートエラー：',
        moreErrors: '... その他{{count}}のエラー'
      }
    },

    // Reading Assistant
    readingAssistant: {
      title: '📖 読書アシスタント',
      description: '読書体験を向上させるためのスマート読書アシスタントとAI要約機能を設定',
      loading: '読書アシスタント設定を読み込み中...',
      sections: {
        detection: {
          title: '🕐 読書検出',
          description: '読書行動と時間を自動的に検出'
        },
        notifications: {
          title: '🔔 読書リマインダー',
          description: '長時間の読書セッション中に控えめなリマインダーを表示'
        },
        aiSummary: {
          title: '🤖 AI要約',
          description: 'AIを使用して長い記事のインテリジェントな要約を生成'
        },
        general: {
          title: '⚙️ 一般設定',
          description: '読書アシスタントに関連するその他の設定'
        }
      },
      settings: {
        enableReadingDetection: {
          label: '読書検出を有効にする',
          description: 'ページ滞在時間とアクティブ状態を監視'
        },
        minimumReadingTime: {
          label: '最小読書時間',
          description: 'この時間より短い訪問は読書として記録されません'
        },
        enableToastNotifications: {
          label: '読書リマインダーを有効にする',
          description: '右下隅に読書時間のリマインダーを表示'
        },
        readingTimeThreshold: {
          label: 'リマインダー発動時間',
          description: 'この読書時間に達した後にリマインダーを表示'
        },
        enableAutoSummary: {
          label: 'AI要約を有効にする',
          description: '長い記事の要約生成を自動的に提案'
        },
        summaryTriggerThreshold: {
          label: '要約発動時間',
          description: 'この時間読書した後に要約の生成を提案'
        },
        summaryPosition: {
          label: '要約位置',
          description: 'ページ上の要約パネルの表示位置',
          options: {
            topRight: '右上',
            topLeft: '左上',
            bottomRight: '右下',
            bottomLeft: '左下'
          }
        },
        summaryLanguage: {
          label: '要約言語',
          description: '要約生成の言語設定',
          options: {
            auto: '自動検出',
            zh: '中国語',
            en: '英語'
          }
        },
        summaryLength: {
          label: '要約の長さ',
          description: '生成される要約の詳細レベル',
          options: {
            short: '簡潔（1-2文）',
            medium: '中程度（1段落）',
            long: '詳細（複数段落）'
          }
        },
        enableKeyboardShortcuts: {
          label: 'キーボードショートカットを有効にする',
          description: 'Ctrl+Shift+Sで素早く要約を生成'
        }
      },
      apiNotice: {
        title: '⚠️ APIキーが必要',
        description: 'AI要約機能にはLLMサービスプロバイダーのAPIキーの設定が必要です。{{apiKeyManagement}}ページで設定してください。',
        configureButton: 'APIキーを設定'
      }
    },

    // About
    about: {
      title: 'Recall',
      version: 'バージョン {{version}}',
      tagline: 'ユーザーの「見たけれど見つからない」問題を解決し、インテリジェントな全文検索でブラウザ履歴を本当に有用にします。',
      features: {
        title: 'コア機能',
        items: {
          fullTextSearch: {
            title: '全文検索',
            description: 'タイトルだけでなく、ページの完全なコンテンツ。'
          },
          fastResponse: {
            title: '高速レスポンス',
            description: 'リアルタイム検索結果、ミリ秒レベルの応答。'
          },
          privacyFirst: {
            title: 'プライバシー第一',
            description: 'すべてのデータはローカルに保存、クラウドにアップロードしません。'
          },
          smartMatching: {
            title: 'スマートマッチング',
            description: 'ファジー検索と高度なクエリ構文をサポート。'
          }
        }
      },
      privacy: {
        title: 'プライバシーの約束',
        description: 'お客様のデータはお客様のものであると確信しています。Recallは、すべてのブラウザ履歴インデックスをお客様のローカルブラウザに安全に保存します。お客様のデータを一切サーバーにアップロードしませんし、今後もしません。このプロジェクトは完全にオープンソースで、いつでもコードを確認して約束を検証できます。'
      },
      links: {
        title: '便利なリンク',
        items: {
          github: '⭐ GitHubでスターを付ける',
          guide: '❓ 使用ガイドを見る',
          review: '📝 Chrome ストアでレビューする'
        }
      },
      acknowledgements: '{{readability}} と {{fusejs}} の強力なサポートに感謝します。',
      copyright: '© 2025 Recall. Made with ❤️ by penwyp.'
    }
  },

  // Search Bar
  searchBar: {
    suggestions: '検索候補',
    noSuggestions: '検索候補がありません'
  },

  // Status Bar
  statusBar: {
    systemStatus: 'システム状態',
    normalOperation: '正常動作',
    needsAttention: '注意が必要',
    justNow: 'たった今',
    minutesAgo: '{{count}}分前',
    hoursAgo: '{{count}}時間前',
    daysAgo: '{{count}}日前',
    unknown: '不明',
    veryFast: '非常に高速',
    fast: '高速',
    normal: '通常',
    slow: '低速',
    systemDetails: 'システム詳細',
    lastUpdate: '最終更新',
    storageInfo: 'ストレージ情報',
    performanceMetrics: 'パフォーマンス指標',
    dataManagement: 'データ管理',
    backupData: 'データバックアップ',
    pages: 'ページ',
    showDetails: '詳細を表示',
    hideDetails: '詳細を非表示',
    refreshStats: '統計を更新',
    openSettings: '設定を開く',
    storageSize: 'ストレージサイズ',
    totalPages: '総ページ数',
    searchSpeed: '検索速度',
    responseTime: '応答時間'
  },

  // Filters
  filters: {
    all: 'すべて',
    today: '今日',
    thisWeek: '今週',
    thisMonth: '今月',
    relevance: '関連性',
    time: '時間',
    accessCount: 'アクセス回数'
  },

  // Syntax Help
  syntaxHelp: {
    title: '高度検索構文',
    close: 'ヘルプを閉じる',
    intro: '以下の構文を使用してブラウジング履歴を正確に検索：',
    examples: {
      basic: 'ReactとJavaScriptを含むページを検索',
      exact: '正確なフレーズマッチング（完全に一致する必要があります）',
      exclude: '特定の単語を含む結果を除外',
      site: '特定のウェブサイトのページのみを検索',
      complex: '複合クエリ：Reactを含み、"component lifecycle"と完全一致、classを除外、reactjs.orgに限定'
    },
    tips: {
      title: '使用のコツ',
      items: {
        '0': '複数の構文を組み合わせることができます',
        '1': '引用符内のコンテンツは正確にマッチされます',
        '2': 'マイナス記号（-）は除外する単語に付ける必要があります',
        '3': 'site: はサブドメインマッチングをサポート',
        '4': '検索は大文字小文字を区別しません'
      }
    },
    startSearch: '検索開始',
    viewHelp: '検索構文ヘルプを表示',
    syntax: '構文'
  },


  // Manifest localization
  manifest: {
    name: 'Recall',
    description: 'AI駆動のブラウザ履歴検索・知識管理拡張機能',
    actionTitle: 'Recall - スマート履歴検索'
  }
} as const;