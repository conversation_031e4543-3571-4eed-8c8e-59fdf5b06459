/**
 * Simplified Chinese language resources
 */

export default {
  // Common/General
  common: {
    loading: '加载中...',
    search: '搜索',
    clear: '清除',
    close: '关闭',
    save: '保存',
    cancel: '取消',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    remove: '移除',
    yes: '是',
    no: '否',
    ok: '确定',
    retry: '重试',
    refresh: '刷新',
    settings: '设置',
    about: '关于',
    help: '帮助',
    language: '语言',
    all: '全部',
    relevance: '相关性',
    syntax: '语法'
  },

  // Main App
  app: {
    title: 'Recall',
    subtitle: '智能历史记录搜索',
    searchPlaceholder: '搜索您的浏览历史记录...',
    clearSearch: '清除搜索',
    toggleDensity: '切换密度',
    densityMode: {
      compact: '紧凑密度',
      comfortable: '舒适密度'
    }
  },

  // Search
  search: {
    placeholder: '搜索...',
    noResults: '未找到结果',
    searching: '搜索中...',
    results: '个结果',
    result: '个结果',
    searchTime: '搜索完成，耗时 {{time}}ms',
    clearSearchTitle: '清除搜索',
    syntaxHelp: '搜索语法帮助'
  },

  // Error handling
  errors: {
    // Error types
    serviceInit: {
      title: '服务初始化失败',
      message: '搜索服务无法正常启动，可能是浏览器兼容性问题',
      solutions: [
        '刷新页面重试',
        '检查浏览器是否支持 IndexedDB',
        '清除浏览器缓存和数据',
        '重启浏览器'
      ]
    },
    search: {
      title: '搜索服务异常',
      message: '搜索功能暂时不可用，可能是数据索引问题',
      solutions: [
        '检查搜索关键词是否有效',
        '尝试简化搜索词',
        '等待几秒后重试',
        '刷新页面重新开始'
      ]
    },
    network: {
      title: '网络连接问题',
      message: '无法连接到必要的服务，请检查网络状态',
      solutions: [
        '检查网络连接是否正常',
        '刷新页面重试',
        '稍后再试'
      ]
    },
    database: {
      title: '数据存储问题',
      message: '本地数据库访问异常，可能需要清理数据',
      solutions: [
        '检查浏览器存储空间',
        '清除扩展数据重新开始',
        '在扩展设置中重置数据',
        '联系技术支持'
      ]
    },
    unknown: {
      title: '未知错误',
      message: '发生了意外错误，请尝试刷新页面',
      solutions: [
        '刷新页面重试',
        '重启浏览器',
        '检查扩展是否正常运行',
        '如问题持续请联系支持'
      ]
    },
    // Error actions
    reload: '🔄 重新加载',
    closeError: '✕ 关闭错误',
    openSettings: '⚙️ 打开设置',
    solutionsTitle: '💡 解决方案'
  },

  // Empty state
  empty: {
    title: '开始搜索您的历史记录',
    description: '在上方输入关键词，快速找到您之前访问过的网页',
    features: {
      fuzzyMatch: '智能模糊匹配',
      multiFilter: '多维度筛选',
      fastResponse: '毫秒级响应'
    },
    searchTips: {
      title: '搜索技巧',
      items: [
        '支持多语言混合搜索',
        '可搜索网页标题、URL 和内容',
        '使用筛选器精确定位结果'
      ]
    },
    keyboardShortcuts: {
      title: '⌨️ 键盘快捷键',
      focusSearch: '聚焦搜索',
      clearSearch: '清除搜索',
      browseResults: '浏览结果',
      openPage: '打开页面',
      quickOpen: '快速打开'
    },
    searchExamples: {
      title: '试试这些搜索示例',
      basic: {
        text: 'React JavaScript',
        description: '基础搜索'
      },
      exact: {
        text: '"best practices"',
        description: '精确匹配'
      },
      site: {
        text: 'site:github.com',
        description: '网站限定'
      },
      exclude: {
        text: 'Vue -tutorial',
        description: '排除搜索'
      }
    }
  },

  // Options page
  options: {
    title: '配置中心',
    pageNotFound: '页面未找到',
    selectOption: '请选择左侧导航中的一个选项。',
    loadingConfig: '正在加载配置中心...',
    
    // App settings
    app: {
      title: '应用设置',
      description: '配置通用应用程序设置。',
      settings: {
        theme: {
          label: '主题',
          description: '选择浅色或深色主题。',
          options: {
            light: '浅色',
            dark: '深色',
            system: '系统'
          }
        },
        density: {
          label: '密度',
          description: '调整界面的信息密度。',
          options: {
            compact: '紧凑',
            default: '默认',
            comfortable: '舒适'
          }
        },
        language: {
          label: '语言',
          description: '设置应用程序的显示语言。'
        }
      }
    },
    
    // Language settings
    languageSettings: {
      description: '配置界面语言和本地化偏好设置。',
      interfaceLanguage: '界面语言',
      interfaceDescription: '选择扩展程序界面的语言。',
      note: '语言更改将立即生效，无需重启。'
    },
    
    // Navigation items
    navigation: {
      historyManagement: {
        label: '历史记录',
        description: '查看、搜索和管理您的浏览历史记录'
      },
      blacklistManagement: {
        label: '屏蔽列表',
        description: '管理不希望被索引的网站域名'
      },
      dataBackup: {
        label: '导入与导出',
        description: '导出和导入您的历史数据'
      },
      searchSettings: {
        label: '搜索设置',
        description: '配置搜索行为和偏好设置'
      },
      apiKeyManagement: {
        label: 'API密钥管理',
        description: '管理AI服务提供商的API密钥'
      },
      readingAssistant: {
        label: '阅读助手',
        description: '配置智能阅读助手和AI摘要功能'
      },
      languageSettings: {
        label: '语言',
        description: '更改界面语言和本地化设置'
      },
      about: {
        label: '关于',
        description: '版本信息和使用帮助'
      }
    },

    // History Management
    historyManagement: {
      title: '📚 历史记录管理',
      description: '查看、搜索和管理您的浏览历史',
      loading: '正在加载历史记录...',
      stats: {
        totalPages: '总页面数：{{count}}',
        totalDomains: '独立域名：{{count}}',
        dateRange: '从 {{oldest}} 到 {{newest}}',
        totalSize: '总大小：{{size}} MB'
      },
      search: {
        placeholder: '搜索标题、URL和内容...',
        noResults: '未找到匹配的历史记录',
        searching: '搜索中...'
      },
      sorting: {
        label: '排序：',
        options: {
          visitTimeDesc: '最新访问',
          visitTimeAsc: '最早访问',
          lastUpdatedDesc: '最近更新',
          lastUpdatedAsc: '最早更新',
          domainAsc: '域名排序 (A-Z)',
          idDesc: 'ID 排序'
        }
      },
      actions: {
        selectAll: '全选',
        deselectAll: '取消全选',
        deleteSelected: '删除选中项',
        deleteConfirm: '确定要删除 "{{title}}" 吗？',
        deleteSuccess: '删除成功',
        deleteError: '删除失败，请重试',
        exportSelected: '导出选中项',
        refreshList: '刷新列表'
      },
      item: {
        visitTime: '访问时间 {{time}}',
        readingTime: '阅读时长 {{duration}}',
        accessCount: '访问次数 {{count}}',
        accessCountTooltip: '访问次数',
        lastVisitTooltip: '最后访问时间',
        contentSizeTooltip: '内容大小',
        delete: '删除此记录',
        deleteAriaLabel: '删除 {{title}}',
        openInNewTab: '在新标签页中打开',
        sizeKB: '{{size}} KB'
      },
      emptyState: {
        noHistory: '没有找到历史记录',
        noSearchResults: '没有找到包含 "{{query}}" 的页面',
        startBrowsing: '开始浏览网页来建立您的历史记录'
      },
      pagination: {
        showing: '显示第 {{start}} - {{end}} 项，共 {{total}} 项',
        itemsPerPage: '每页条数',
        previousPage: '上一页',
        nextPage: '下一页',
        previous: '上一页',
        next: '下一页',
        jumpToPage: '跳转到页数',
        go: '跳转',
        page: '第 {{page}} 页',
        firstPage: '首页',
        lastPage: '末页'
      }
    },

    // API Key Management
    apiKeys: {
      title: '🔑 API密钥管理',
      description: '配置您的AI服务提供商API密钥，启用BYOK（自带密钥）模式',
      loading: '正在加载API密钥配置...',
      privacy: {
        title: '隐私保护',
        points: [
          '所有API密钥使用AES-256加密存储在本地',
          '密钥直接连接到对应的AI服务商，不经过我们的服务器',
          '您完全控制自己的数据和密钥安全'
        ]
      },
      providers: {
        openai: {
          name: 'OpenAI',
          description: 'GPT-4, GPT-3.5等模型',
          setupGuide: '在OpenAI平台创建API密钥',
          keyFormat: 'sk-...'
        },
        anthropic: {
          name: 'Anthropic',
          description: 'Claude系列模型',
          setupGuide: '在Anthropic Console创建API密钥',
          keyFormat: 'sk-ant-...'
        },
        deepseek: {
          name: 'DeepSeek',
          description: 'DeepSeek Chat模型',
          setupGuide: '在DeepSeek平台创建API密钥',
          keyFormat: 'sk-...'
        },
        google: {
          name: 'Google AI',
          description: 'Gemini系列模型',
          setupGuide: '在Google AI Studio创建API密钥',
          keyFormat: 'AI...'
        },
        azure: {
          name: 'Azure OpenAI',
          description: 'Azure托管的OpenAI模型',
          setupGuide: '在Azure Portal配置OpenAI资源',
          keyFormat: '需要endpoint和密钥'
        },
        litellm: {
          name: 'LiteLLM',
          description: '支持多种模型提供商的统一接口',
          setupGuide: '配置LiteLLM代理端点',
          keyFormat: '依赖具体提供商'
        }
      },
      modal: {
        title: '配置 {{providerName}} API密钥',
        apiKeyLabel: 'API密钥',
        apiKeyPlaceholder: '输入您的 {{providerName}} API密钥',
        endpointLabel: 'API端点',
        endpointPlaceholder: 'https://your-resource.openai.azure.com/',
        securityNotice: '🔐 密钥将使用AES-256加密算法安全存储在本地，仅供扩展使用'
      },
      messages: {
        invalidKey: '请输入有效的API密钥',
        saveSuccess: 'API密钥已安全保存',
        saveError: '保存失败，请重试',
        removeConfirm: '确定要删除 {{providerName}} 的API密钥吗？',
        removeSuccess: 'API密钥已删除',
        removeError: '删除失败，请重试',
        testSuccess: 'API密钥验证成功',
        testError: 'API密钥验证失败',
        keyNotFound: 'API密钥未找到'
      }
    },

    // Search Settings
    search: {
      title: '⚙️ 搜索设置',
      description: '配置语义搜索和关键词搜索行为参数',
      loading: '正在加载搜索设置...',
      sections: {
        results: {
          title: '📊 搜索结果',
          description: '控制搜索结果数量和显示'
        },
        traditional: {
          title: '🔍 传统搜索',
          description: '基于关键词匹配的传统搜索设置'
        },
        keyword: {
          title: '🔍 关键词搜索',
          description: '传统关键词匹配搜索设置'
        },
        indexing: {
          title: '📚 内容索引',
          description: '自动页面内容索引设置'
        },
        userExperience: {
          title: '🎯 用户体验',
          description: '搜索界面和交互体验设置'
        }
      },
      settings: {
        maxResults: {
          label: '最大搜索结果数',
          description: '单次搜索返回的最大结果数量'
        },
        searchTimeout: {
          label: '搜索超时时间',
          description: '搜索请求的最大等待时间'
        },
        traditionalSearchWeight: {
          label: '传统搜索权重',
          description: '混合搜索中传统搜索的权重比例'
        },
        enableKeywordSearch: {
          label: '启用关键词搜索',
          description: '使用传统关键词匹配搜索'
        },
        fuzzySearchThreshold: {
          label: '模糊搜索阈值',
          description: '控制模糊匹配的灵敏度（数值越小越严格）'
        },
        keywordSearchWeight: {
          label: '关键词搜索权重',
          description: '混合搜索中关键词搜索的权重比例'
        },
        enableIndexing: {
          label: '启用自动索引',
          description: '自动索引访问过的页面内容'
        },
        indexingDelay: {
          label: '索引延迟',
          description: '页面加载后开始索引前的延迟时间'
        },
        enableSearchHistory: {
          label: '启用搜索历史',
          description: '记住最近的搜索查询'
        },
        enableAutoComplete: {
          label: '启用自动完成',
          description: '输入时显示搜索建议'
        },
        enableDebugMode: {
          label: '启用调试模式',
          description: '显示详细的搜索调试信息'
        }
      }
    },

    // Reading Assistant
    readingAssistant: {
      title: '📖 阅读助手',
      description: '配置智能阅读助手和AI摘要功能，提升您的阅读体验',
      loading: '正在加载阅读助手设置...',
      sections: {
        detection: {
          title: '🕐 阅读检测',
          description: '自动检测您的阅读行为和时长'
        },
        notifications: {
          title: '🔔 阅读提醒',
          description: '在长时间阅读期间显示非侵入式提醒'
        },
        aiSummary: {
          title: '🤖 AI摘要',
          description: '使用AI为长文章生成智能摘要'
        },
        general: {
          title: '⚙️ 通用设置',
          description: '其他阅读助手相关设置'
        }
      },
      settings: {
        enableReadingDetection: {
          label: '启用阅读检测',
          description: '监控页面停留时间和活跃状态'
        },
        minimumReadingTime: {
          label: '最小阅读时间',
          description: '短于此时间的访问不记录为阅读'
        },
        enableToastNotifications: {
          label: '启用阅读提醒',
          description: '在右下角显示阅读时长提醒'
        },
        readingTimeThreshold: {
          label: '提醒触发时间',
          description: '达到此阅读时长后显示提醒'
        },
        enableAutoSummary: {
          label: '启用AI摘要',
          description: '自动提示为长文章生成摘要'
        },
        summaryTriggerThreshold: {
          label: '摘要触发时间',
          description: '阅读此时长后提示生成摘要'
        },
        summaryPosition: {
          label: '摘要位置',
          description: '摘要面板在页面上的显示位置',
          options: {
            topRight: '右上角',
            topLeft: '左上角',
            bottomRight: '右下角',
            bottomLeft: '左下角'
          }
        },
        summaryLanguage: {
          label: '摘要语言',
          description: '摘要生成的语言偏好',
          options: {
            auto: '自动检测',
            zh: '中文',
            en: '英文'
          }
        },
        summaryLength: {
          label: '摘要长度',
          description: '生成摘要的详细程度',
          options: {
            short: '简短（1-2句话）',
            medium: '中等（1段落）',
            long: '详细（多段落）'
          }
        },
        enableKeyboardShortcuts: {
          label: '启用键盘快捷键',
          description: '使用Ctrl+Shift+S快速生成摘要'
        }
      },
      apiNotice: {
        title: '⚠️ 需要API密钥',
        description: 'AI摘要功能需要配置LLM服务提供商API密钥。请前往{{apiKeyManagement}}页面进行配置。',
        configureButton: '配置API密钥'
      }
    },

    // Data Backup
    dataBackup: {
      title: '💾 数据备份与导入',
      description: '导出和导入您的浏览历史数据',
      loading: '正在加载数据备份界面...',
      stats: {
        title: '数据统计',
        totalPages: '总页面数：{{count}}',
        totalBlacklist: '黑名单域名：{{count}}',
        lastUpdate: '最后更新：{{time}}'
      },
      export: {
        title: '导出数据',
        description: '将您的所有浏览历史和设置导出为JSON文件',
        button: '导出所有数据',
        processing: '导出中...',
        progress: '导出中... {{progress}}%',
        success: '数据导出成功',
        error: '导出失败：{{error}}'
      },
      import: {
        title: '导入数据',
        description: '导入之前导出的数据文件',
        button: '选择导入文件',
        processing: '正在处理导入...',
        success: '导入完成成功',
        error: '导入失败：{{error}}',
        invalidFormat: '备份文件格式无效',
        stats: {
          pagesImported: '已导入页面：{{count}}',
          blacklistImported: '已导入黑名单：{{count}}',
          pagesSkipped: '跳过页面：{{count}}',
          blacklistSkipped: '跳过黑名单：{{count}}'
        }
      },
      warnings: {
        exportSize: '导出文件大小可能取决于您的历史记录大小而较大',
        importOverwrite: '导入将与现有数据合并，重复项将被跳过',
        backupFirst: '建议在导入前备份当前数据'
      },
      dangerZone: {
        title: '危险区域',
        description: '清除所有数据将永久删除所有浏览历史和黑名单。此操作无法撤销。请确保在继续之前已备份重要数据。',
        clearAllButton: '🗑️ 清除所有数据',
        clearAllConfirm: '确定要清除所有数据吗？这将永久删除所有浏览历史和黑名单。此操作无法撤销！',
        clearAllDoubleConfirm: '请再次确认：这将永久删除所有数据，包括浏览历史和黑名单。您确定要继续吗？',
        clearAllSuccess: '所有数据已清除',
        clearAllError: '清除失败，请重试'
      },
      importResults: {
        title: '导入结果',
        pagesImported: '历史记录已导入：',
        blacklistImported: '黑名单已导入：',
        pagesSkipped: '历史记录已跳过：',
        blacklistSkipped: '黑名单已跳过：',
        entries: '{{count}} 个条目',
        importErrors: '导入错误：',
        moreErrors: '... 以及 {{count}} 个其他错误'
      }
    },

    common: {
      loading: '加载中...',
      saving: '保存中...',
      saved: '设置已保存',
      saveFailed: '保存失败，请重试',
      resetToDefaults: '重置为默认值',
      resetConfirm: '确定要重置为默认设置吗？',
      configure: '配置密钥',
      configured: '已配置',
      unconfigured: '未配置',
      testConnection: '测试连接',
      removeKey: '删除密钥',
      getKey: '获取密钥',
      saveKey: '保存密钥',
      cancel: '取消',
      close: '×',
      enabled: '已启用',
      disabled: '已禁用',
      version: '版本 {{version}}',
      units: {
        items: '项',
        ms: '毫秒',
        seconds: '秒',
        minutes: '分钟',
        mb: 'MB',
        percent: '%'
      }
    },

    // About
    about: {
      title: 'Recall',
      version: '版本 {{version}}',
      tagline: '解决了用户"看过但找不到"的痛点，通过智能全文搜索让您的浏览历史变得真正有用。',
      features: {
        title: '核心功能',
        items: {
          fullTextSearch: {
            title: '全文搜索',
            description: '不止是标题，更是页面完整内容。'
          },
          fastResponse: {
            title: '极速响应',
            description: '搜索结果实时呈现，毫秒级响应。'
          },
          privacyFirst: {
            title: '隐私至上',
            description: '所有数据本地存储，绝不上传云端。'
          },
          smartMatching: {
            title: '智能匹配',
            description: '支持模糊搜索与高级查询语法。'
          }
        }
      },
      privacy: {
        title: '隐私承诺',
        description: '我们坚信您的数据属于您自己。Recall 将所有浏览历史索引安全地存储在您本地的浏览器中。我们不会，也永远不会将您的数据上传到任何服务器。本项目完全开源，您可以随时审查代码以验证我们的承诺。'
      },
      links: {
        title: '实用链接',
        items: {
          github: '⭐ 在 GitHub 上给我们一颗星',
          guide: '❓ 查看使用指南',
          review: '📝 在 Chrome 商店评价我们'
        }
      },
      acknowledgements: '感谢 {{readability}} 与 {{fusejs}} 的强大支持。',
      copyright: '© 2025 Recall. Made with ❤️ by penwyp.'
    },

    // Blacklist Management
    blacklistManagement: {
      title: '屏蔽列表',
      description: '管理不希望被索引的网站域名',
      loading: '正在加载...',
      
      // Header stats
      stats: {
        totalDomains: '总域名: {{count}}',
        wildcardDomains: '通配符: {{count}}'
      },
      
      // Search functionality
      search: {
        placeholder: '搜索域名和原因...'
      },
      
      // Add domain modal and form
      addDomain: {
        modalTitle: '添加新的屏蔽域名',
        domainLabel: '域名',
        domainPlaceholder: 'example.com',
        domainPlaceholderWildcard: '*.example.com',
        reasonLabel: '原因（可选）',
        reasonPlaceholder: '说明原因（可选）',
        wildcardLabel: '通配符匹配 (匹配所有子域名)',
        submitButton: '确认添加',
        submitButtonLoading: '添加中...',
        cancelButton: '取消',
        
        // Validation errors
        errors: {
          domainRequired: '域名不能为空',
          domainInvalid: '请输入有效的域名格式',
          wildcardInvalid: '通配符域名必须以 *. 开头'
        },
        
        // Status messages
        addFailed: '添加失败，请重试',
        
        // Domain suggestions
        suggestions: {
          title: '已索引站点建议',
          loading: '加载建议...',
          count: '({{count}})',
          noSuggestions: '暂无建议',
          pageCount: '{{count}} 页面',
          visitCount: '{{count}} 次访问',
          separator: '•'
        }
      },
      
      // Domain list and items
      domainList: {
        noReason: '无',
        exactMatch: '精确',
        wildcardMatch: '通配符',
        exactMatchTooltip: '精确匹配',
        wildcardMatchTooltip: '通配符匹配',
        addedTimeTooltip: '添加时间: {{time}}',
        removeTooltip: '从黑名单中移除',
        removeAriaLabel: '移除 {{domain}}'
      },
      
      // Bulk actions
      bulkActions: {
        clearAll: '全部清除',
        clearAllTooltip: '清空所有屏蔽域名',
        addDomain: '添加域名',
        addDomainTooltip: '添加新的屏蔽域名'
      },
      
      // Confirmation dialogs
      confirmations: {
        removeDomain: '确定要从黑名单中移除 "{{domain}}" 吗？此操作不可撤销。',
        clearAll: '确定要清空整个黑名单 ({{count}} 个条目) 吗？此操作不可撤销。',
        deleteFailed: '删除失败，请重试',
        clearFailed: '清空失败，请重试'
      },
      
      // Empty states
      emptyState: {
        noResults: '未找到结果',
        noResultsDescription: '没有找到与 "{{query}}" 相关的域名',
        empty: '黑名单为空',
        emptyDescription: '添加域名到黑名单，相关页面将不会被索引',
        addFirstDomain: '添加第一个域名'
      },
      
      // Help tooltip
      help: {
        title: '使用说明',
        exactMatch: '精确匹配：只匹配指定的域名，如 "example.com"',
        wildcardMatch: '通配符匹配：匹配域名及其所有子域名，如 "*.example.com"',
        autoEffect: '自动生效：添加到黑名单的域名将不会被新的页面索引',
        existingData: '已有数据：黑名单不会影响已经索引的页面，需要手动删除'
      }
    }
  },

  // Search Bar
  searchBar: {
    suggestions: '搜索建议',
    noSuggestions: '无搜索建议'
  },

  // Status Bar
  statusBar: {
    systemStatus: '系统状态',
    normalOperation: '正常运行',
    needsAttention: '需要检查',
    justNow: '刚刚',
    minutesAgo: '{{count}}分钟前',
    hoursAgo: '{{count}}小时前',
    daysAgo: '{{count}}天前',
    unknown: '未知',
    veryFast: '极快',
    fast: '快速',
    normal: '正常',
    slow: '较慢',
    systemDetails: '系统详情',
    lastUpdate: '最后更新',
    storageInfo: '存储信息',
    performanceMetrics: '性能指标',
    dataManagement: '数据管理',
    backupData: '备份数据',
    pages: '页面',
    showDetails: '显示详情',
    hideDetails: '隐藏详情',
    refreshStats: '刷新统计信息',
    openSettings: '打开设置',
    storageSize: '存储大小',
    totalPages: '页面总数',
    searchSpeed: '搜索速度',
    responseTime: '响应时间'
  },

  // Filters
  filters: {
    all: '全部',
    today: '今天',
    thisWeek: '本周',
    thisMonth: '本月',
    relevance: '相关性',
    time: '时间',
    accessCount: '访问次数'
  },

  // Syntax Help
  syntaxHelp: {
    title: '高级搜索语法',
    close: '关闭帮助',
    intro: '使用以下语法来精确搜索您的浏览历史：',
    examples: {
      basic: '搜索包含 React 和 JavaScript 的页面',
      exact: '精确匹配短语（必须完全匹配）',
      exclude: '排除包含指定词汇的结果',
      site: '只搜索指定网站的页面',
      complex: '复合查询：包含 React，精确匹配 "component lifecycle"，排除 class，限制在 reactjs.org'
    },
    tips: {
      title: '使用技巧',
      items: {
        '0': '可以组合使用多种语法',
        '1': '引号内的内容会被精确匹配',
        '2': '减号（-）必须紧贴要排除的词汇',
        '3': 'site: 支持子域名匹配',
        '4': '搜索不区分大小写'
      }
    },
    startSearch: '开始搜索',
    viewHelp: '查看搜索语法帮助',
    syntax: '语法'
  },

  // Manifest localization
  manifest: {
    name: 'Recall',
    description: 'AI驱动的浏览器历史搜索和知识管理扩展',
    actionTitle: 'Recall - 智能历史搜索'
  }
} as const;