/**
 * French language resources
 */

export default {
  // Common/General
  common: {
    loading: 'Chargement...',
    search: 'Rechercher',
    clear: 'Effacer',
    close: '<PERSON><PERSON><PERSON>',
    save: 'Enregistre<PERSON>',
    cancel: 'Annuler',
    delete: 'Supprimer',
    edit: 'Modifier',
    add: 'Ajouter',
    remove: 'Retirer',
    yes: 'Oui',
    no: 'Non',
    ok: 'OK',
    retry: 'Réessay<PERSON>',
    refresh: 'Actualiser',
    settings: 'Paramètres',
    about: 'À propos',
    help: 'Aide',
    language: 'Langue',
    all: 'Tout',
    relevance: 'Pertinence',
    syntax: 'Syntaxe'
  },

  // Main App
  app: {
    title: 'Recall',
    subtitle: 'Recherche Intelligente d\'Historique',
    searchPlaceholder: 'Rechercher dans votre historique de navigation...',
    clearSearch: 'Effacer la recherche',
    toggleDensity: 'Basculer la densité',
    sizeMode: {
      compact: 'Vue compacte',
      expanded: 'Vue étendue'
    },
    densityMode: {
      compact: 'Densité compacte',
      comfortable: 'Densité confortable'
    }
  },

  // Search
  search: {
    placeholder: 'Rechercher...',
    noResults: 'Aucun résultat trouvé',
    searching: 'Recherche...',
    results: 'résultats',
    result: 'résultat',
    searchTime: 'Recherche complétée en {{time}}ms',
    clearSearchTitle: 'Effacer la recherche',
    syntaxHelp: 'Aide syntaxe de recherche'
  },

  // Error handling
  errors: {
    // Error types
    serviceInit: {
      title: 'Échec d\'Initialisation du Service',
      message: 'Le service de recherche n\'a pas pu démarrer correctement, cela pourrait être un problème de compatibilité du navigateur',
      solutions: [
        'Actualiser la page et réessayer',
        'Vérifier si votre navigateur supporte IndexedDB',
        'Vider le cache et les données du navigateur',
        'Redémarrer votre navigateur'
      ]
    },
    search: {
      title: 'Erreur du Service de Recherche',
      message: 'La fonctionnalité de recherche est temporairement indisponible, cela pourrait être un problème d\'indexation des données',
      solutions: [
        'Vérifier si les mots-clés de recherche sont valides',
        'Essayer de simplifier vos termes de recherche',
        'Attendre quelques secondes et réessayer',
        'Actualiser la page pour recommencer'
      ]
    },
    network: {
      title: 'Problème de Connexion Réseau',
      message: 'Impossible de se connecter aux services requis, veuillez vérifier l\'état de votre réseau',
      solutions: [
        'Vérifier si la connexion réseau fonctionne',
        'Actualiser la page et réessayer',
        'Réessayer plus tard'
      ]
    },
    database: {
      title: 'Problème de Stockage de Données',
      message: 'Erreur d\'accès à la base de données locale, un nettoyage des données pourrait être nécessaire',
      solutions: [
        'Vérifier l\'espace de stockage du navigateur',
        'Effacer les données de l\'extension et recommencer',
        'Réinitialiser les données dans les paramètres de l\'extension',
        'Contacter le support technique'
      ]
    },
    unknown: {
      title: 'Erreur Inconnue',
      message: 'Une erreur inattendue s\'est produite, veuillez essayer d\'actualiser la page',
      solutions: [
        'Actualiser la page et réessayer',
        'Redémarrer votre navigateur',
        'Vérifier si l\'extension fonctionne correctement',
        'Contacter le support si le problème persiste'
      ]
    },
    // Error actions
    reload: '🔄 Recharger',
    closeError: '✕ Fermer l\'Erreur',
    openSettings: '⚙️ Ouvrir les Paramètres',
    solutionsTitle: '💡 Solutions'
  },

  // Empty state
  empty: {
    title: 'Commencez à rechercher dans votre historique',
    description: 'Saisissez des mots-clés ci-dessus pour trouver rapidement les pages que vous avez visitées',
    features: {
      fuzzyMatch: 'Correspondance floue intelligente',
      multiFilter: 'Filtrage multidimensionnel',
      fastResponse: 'Réponse en millisecondes'
    },
    searchTips: {
      title: 'Conseils de Recherche',
      items: [
        'Supporte la recherche multilingue mixte',
        'Peut rechercher les titres de page, URLs et contenu',
        'Utiliser les filtres pour des résultats précis'
      ]
    },
    keyboardShortcuts: {
      title: '⌨️ Raccourcis Clavier',
      focusSearch: 'Focus recherche',
      clearSearch: 'Effacer recherche',
      browseResults: 'Parcourir résultats',
      openPage: 'Ouvrir page',
      quickOpen: 'Ouverture rapide'
    },
    searchExamples: {
      title: 'Essayez ces exemples de recherche',
      basic: {
        text: 'React JavaScript',
        description: 'Recherche de base'
      },
      exact: {
        text: '"meilleures pratiques"',
        description: 'Correspondance exacte'
      },
      site: {
        text: 'site:github.com',
        description: 'Spécifique au site'
      },
      exclude: {
        text: 'Vue -tutorial',
        description: 'Recherche d\'exclusion'
      }
    }
  },

  // Options page
  options: {
    title: 'Centre de Configuration',
    pageNotFound: 'Page non trouvée',
    selectOption: 'Veuillez sélectionner une option dans la navigation de gauche.',
    loadingConfig: 'Chargement du centre de configuration...',
    
    // App settings
    app: {
      title: 'Paramètres de l\'application',
      description: 'Configurer les paramètres généraux de l\'application.',
      settings: {
        theme: {
          label: 'Thème',
          description: 'Choisissez entre les thèmes clair et sombre.',
          options: {
            light: 'Clair',
            dark: 'Sombre',
            system: 'Système'
          }
        },
        density: {
          label: 'Densité',
          description: 'Ajustez la densité d\'information de l\'interface.',
          options: {
            compact: 'Compacte',
            default: 'Par défaut',
            comfortable: 'Confortable'
          }
        },
        language: {
          label: 'Langue',
          description: 'Définir la langue d\'affichage de l\'application.'
        }
      }
    },
    
    // Language settings
    languageSettings: {
      description: 'Configurer la langue de l\'interface et les préférences de localisation.',
      interfaceLanguage: 'Langue de l\'Interface',
      interfaceDescription: 'Choisir la langue pour l\'interface de l\'extension.',
      note: 'Les changements de langue prendront effet immédiatement sans nécessiter de redémarrage.'
    },

    // History Management
    historyManagement: {
      title: '📚 Gestion de l\'Historique',
      description: 'Voir, rechercher et gérer votre historique de navigation',
      loading: 'Chargement des enregistrements d\'historique...',
      stats: {
        totalPages: 'Pages totales : {{count}}',
        totalDomains: 'Domaines uniques : {{count}}',
        dateRange: 'De {{oldest}} à {{newest}}',
        totalSize: 'Taille totale : {{size}} MB'
      },
      search: {
        placeholder: 'Rechercher dans les titres, URLs et contenu...',
        noResults: 'Aucun historique correspondant trouvé',
        searching: 'Recherche en cours...'
      },
      sorting: {
        label: 'Trier :',
        options: {
          visitTimeDesc: 'Dernière visite',
          visitTimeAsc: 'Première visite',
          lastUpdatedDesc: 'Récemment mis à jour',
          lastUpdatedAsc: 'Mis à jour tôt',
          domainAsc: 'Trier par domaine (A-Z)',
          idDesc: 'Ordre par ID'
        }
      },
      actions: {
        selectAll: 'Tout sélectionner',
        deselectAll: 'Tout désélectionner',
        deleteSelected: 'Supprimer la sélection',
        deleteConfirm: 'Êtes-vous sûr de vouloir supprimer "{{title}}" ?',
        deleteSuccess: 'Suppression réussie',
        deleteError: 'Échec de la suppression, veuillez réessayer',
        exportSelected: 'Exporter la sélection',
        refreshList: 'Actualiser la liste'
      },
      item: {
        visitTime: 'Visité {{time}}',
        readingTime: 'Lu pendant {{duration}}',
        accessCount: 'Accédé {{count}} fois',
        accessCountTooltip: 'Nombre d\'accès',
        lastVisitTooltip: 'Heure de dernière visite',
        contentSizeTooltip: 'Taille du contenu',
        delete: 'Supprimer cet enregistrement',
        deleteAriaLabel: 'Supprimer {{title}}',
        openInNewTab: 'Ouvrir dans un nouvel onglet',
        sizeKB: '{{size}} KB'
      },
      emptyState: {
        noHistory: 'Aucun enregistrement d\'historique trouvé',
        noSearchResults: 'Aucune page contenant "{{query}}" trouvée',
        startBrowsing: 'Commencez à naviguer sur le web pour construire votre historique'
      }
    },

    // API Key Management
    apiKeys: {
      title: '🔑 Gestion des Clés API',
      description: 'Configurez vos clés API de fournisseur de services IA et activez le mode BYOK (Bring Your Own Key)',
      loading: 'Chargement de la configuration des clés API...',
      privacy: {
        title: 'Protection de la Vie Privée',
        points: [
          'Toutes les clés API sont chiffrées avec AES-256 et stockées localement',
          'Les clés se connectent directement aux fournisseurs de services IA correspondants, pas via nos serveurs',
          'Vous avez un contrôle total sur la sécurité de vos données et clés'
        ]
      },
      providers: {
        openai: {
          name: 'OpenAI',
          description: 'GPT-4, GPT-3.5 et autres modèles',
          setupGuide: 'Créer une clé API sur la plateforme OpenAI',
          keyFormat: 'sk-...'
        },
        anthropic: {
          name: 'Anthropic',
          description: 'Modèles de la série Claude',
          setupGuide: 'Créer une clé API dans Anthropic Console',
          keyFormat: 'sk-ant-...'
        },
        deepseek: {
          name: 'DeepSeek',
          description: 'Modèles DeepSeek Chat',
          setupGuide: 'Créer une clé API sur la plateforme DeepSeek',
          keyFormat: 'sk-...'
        },
        google: {
          name: 'Google AI',
          description: 'Modèles de la série Gemini',
          setupGuide: 'Créer une clé API dans Google AI Studio',
          keyFormat: 'AI...'
        },
        azure: {
          name: 'Azure OpenAI',
          description: 'Modèles OpenAI hébergés sur Azure',
          setupGuide: 'Configurer la ressource OpenAI dans Azure Portal',
          keyFormat: 'Nécessite endpoint et clé'
        },
        litellm: {
          name: 'LiteLLM',
          description: 'Interface unifiée supportant plusieurs fournisseurs de modèles',
          setupGuide: 'Configurer l\'endpoint proxy LiteLLM',
          keyFormat: 'Dépend du fournisseur spécifique'
        }
      },
      modal: {
        title: 'Configurer la Clé API {{providerName}}',
        apiKeyLabel: 'Clé API',
        apiKeyPlaceholder: 'Entrez votre clé API {{providerName}}',
        endpointLabel: 'Endpoint API',
        endpointPlaceholder: 'https://votre-ressource.openai.azure.com/',
        securityNotice: '🔐 Les clés seront stockées en sécurité localement en utilisant l\'algorithme de chiffrement AES-256, uniquement pour l\'usage de l\'extension'
      },
      messages: {
        invalidKey: 'Veuillez entrer une clé API valide',
        saveSuccess: 'Clé API sauvegardée en sécurité',
        saveError: 'Échec de la sauvegarde, veuillez réessayer',
        removeConfirm: 'Êtes-vous sûr de vouloir supprimer la clé API pour {{providerName}} ?',
        removeSuccess: 'Clé API supprimée',
        removeError: 'Échec de la suppression, veuillez réessayer',
        testSuccess: 'Vérification de la clé API réussie',
        testError: 'Échec de la vérification de la clé API',
        keyNotFound: 'Clé API non trouvée'
      }
    },

    // Blacklist Management
    blacklistManagement: {
      title: 'Liste Noire',
      description: 'Gérer les domaines de sites web que vous ne voulez pas indexer',
      loading: 'Chargement...',
      
      // Header stats
      stats: {
        totalDomains: 'Domaines totaux: {{count}}',
        wildcardDomains: 'Jokers: {{count}}'
      },
      
      // Search functionality
      search: {
        placeholder: 'Rechercher domaines et raisons...'
      },
      
      // Add domain modal and form
      addDomain: {
        modalTitle: 'Ajouter un Nouveau Domaine Bloqué',
        domainLabel: 'Domaine',
        domainPlaceholder: 'example.com',
        domainPlaceholderWildcard: '*.example.com',
        reasonLabel: 'Raison (optionnel)',
        reasonPlaceholder: 'Expliquer la raison (optionnel)',
        wildcardLabel: 'Correspondance joker (correspond à tous les sous-domaines)',
        submitButton: 'Confirmer l\'Ajout',
        submitButtonLoading: 'Ajout en cours...',
        cancelButton: 'Annuler',
        
        // Validation errors
        errors: {
          domainRequired: 'Le domaine ne peut pas être vide',
          domainInvalid: 'Veuillez entrer un format de domaine valide',
          wildcardInvalid: 'Les domaines joker doivent commencer par *.'
        },
        
        // Status messages
        addFailed: 'Échec de l\'ajout, veuillez réessayer',
        
        // Domain suggestions
        suggestions: {
          title: 'Suggestions de Sites Indexés',
          loading: 'Chargement des suggestions...',
          count: '({{count}})',
          noSuggestions: 'Aucune suggestion',
          pageCount: '{{count}} pages',
          visitCount: '{{count}} visites',
          separator: '•'
        }
      },
      
      // Domain list and items
      domainList: {
        noReason: 'Aucune',
        exactMatch: 'Exact',
        wildcardMatch: 'Joker',
        exactMatchTooltip: 'Correspondance exacte',
        wildcardMatchTooltip: 'Correspondance joker',
        addedTimeTooltip: 'Heure d\'ajout: {{time}}',
        removeTooltip: 'Supprimer de la liste noire',
        removeAriaLabel: 'Supprimer {{domain}}'
      },
      
      // Bulk actions
      bulkActions: {
        clearAll: 'Tout Effacer',
        clearAllTooltip: 'Effacer tous les domaines bloqués',
        addDomain: 'Ajouter Domaine',
        addDomainTooltip: 'Ajouter un nouveau domaine bloqué'
      },
      
      // Confirmation dialogs
      confirmations: {
        removeDomain: 'Êtes-vous sûr de vouloir supprimer "{{domain}}" de la liste noire? Cette opération ne peut pas être annulée.',
        clearAll: 'Êtes-vous sûr de vouloir effacer toute la liste noire ({{count}} entrées)? Cette opération ne peut pas être annulée.',
        deleteFailed: 'Échec de la suppression, veuillez réessayer',
        clearFailed: 'Échec de l\'effacement, veuillez réessayer'
      },
      
      // Empty states
      emptyState: {
        noResults: 'Aucun résultat trouvé',
        noResultsDescription: 'Aucun domaine trouvé lié à "{{query}}"',
        empty: 'La liste noire est vide',
        emptyDescription: 'Ajoutez des domaines à la liste noire, les pages associées ne seront pas indexées',
        addFirstDomain: 'Ajouter le premier domaine'
      },
      
      // Help tooltip
      help: {
        title: 'Instructions d\'Utilisation',
        exactMatch: 'Correspondance exacte: Ne correspond qu\'au domaine spécifié, comme "example.com"',
        wildcardMatch: 'Correspondance joker: Correspond au domaine et à tous ses sous-domaines, comme "*.example.com"',
        autoEffect: 'Effet automatique: Les domaines ajoutés à la liste noire ne seront pas indexés par les nouvelles pages',
        existingData: 'Données existantes: La liste noire n\'affecte pas les pages déjà indexées, suppression manuelle nécessaire'
      }
    },
    
    // Navigation items
    navigation: {
      historyManagement: {
        label: 'Enregistrements d\'Historique',
        description: 'Voir, rechercher et gérer votre historique de navigation'
      },
      blacklistManagement: {
        label: 'Liste Noire',
        description: 'Gérer les domaines de sites web que vous ne voulez pas indexer'
      },
      dataBackup: {
        label: 'Importer et Exporter',
        description: 'Exporter et importer vos données d\'historique'
      },
      searchSettings: {
        label: 'Paramètres de Recherche',
        description: 'Configurer le comportement et les préférences de recherche'
      },
      apiKeyManagement: {
        label: 'Gestion des Clés API',
        description: 'Gérer les clés API pour les fournisseurs de services IA'
      },
      readingAssistant: {
        label: 'Assistant de Lecture',
        description: 'Configurer l\'assistant de lecture intelligent et les fonctionnalités de résumé IA'
      },
      languageSettings: {
        label: 'Langue',
        description: 'Changer la langue de l\'interface et les paramètres de localisation'
      },
      about: {
        label: 'À propos',
        description: 'Informations de version et aide d\'utilisation'
      }
    },

    // Common options translations
    common: {
      loading: 'Chargement...',
      saving: 'Enregistrement...',
      saved: 'Paramètres sauvegardés',
      saveFailed: 'Échec de la sauvegarde, veuillez réessayer',
      resetToDefaults: 'Rétablir les valeurs par défaut',
      resetConfirm: 'Êtes-vous sûr de vouloir rétablir les paramètres par défaut ?',
      configure: 'Configurer',
      configured: 'Configuré',
      unconfigured: 'Non configuré',
      testConnection: 'Tester la connexion',
      removeKey: 'Supprimer la clé',
      getKey: 'Obtenir la clé',
      saveKey: 'Enregistrer la clé',
      cancel: 'Annuler',
      close: '×',
      enabled: 'Activé',
      disabled: 'Désactivé',
      version: 'Version {{version}}',
      units: {
        items: 'éléments',
        ms: 'ms',
        seconds: 'secondes',
        minutes: 'minutes',
        mb: 'MB',
        percent: '%'
      }
    },

    // Search Settings
    search: {
      title: '⚙️ Paramètres de Recherche',
      description: 'Configurer les paramètres de comportement de recherche sémantique et par mots-clés',
      loading: 'Chargement des paramètres de recherche...',
      sections: {
        results: {
          title: '📊 Résultats de Recherche',
          description: 'Contrôler la quantité et l\'affichage des résultats de recherche'
        },
        traditional: {
          title: '🔍 Recherche Traditionnelle',
          description: 'Paramètres de recherche traditionnelle basée sur la correspondance de mots-clés'
        },
        keyword: {
          title: '🔍 Recherche par Mots-Clés',
          description: 'Paramètres de recherche de correspondance traditionnelle par mots-clés'
        },
        indexing: {
          title: '📚 Indexation de Contenu',
          description: 'Paramètres d\'indexation automatique du contenu des pages'
        },
        userExperience: {
          title: '🎯 Expérience Utilisateur',
          description: 'Paramètres d\'interface de recherche et d\'expérience d\'interaction'
        }
      },
      settings: {
        maxResults: {
          label: 'Nombre maximum de résultats de recherche',
          description: 'Nombre maximum de résultats retournés dans une seule recherche'
        },
        searchTimeout: {
          label: 'Délai d\'attente de recherche',
          description: 'Temps d\'attente maximum pour les demandes de recherche'
        },
        traditionalSearchWeight: {
          label: 'Poids de la recherche traditionnelle',
          description: 'Rapport de poids de la recherche traditionnelle dans la recherche hybride'
        },
        enableKeywordSearch: {
          label: 'Activer la recherche par mots-clés',
          description: 'Utiliser la recherche de correspondance traditionnelle par mots-clés'
        },
        fuzzySearchThreshold: {
          label: 'Seuil de recherche floue',
          description: 'Contrôler la sensibilité de correspondance floue (plus petit est plus strict)'
        },
        keywordSearchWeight: {
          label: 'Poids de la recherche par mots-clés',
          description: 'Rapport de poids de la recherche par mots-clés dans la recherche hybride'
        },
        enableIndexing: {
          label: 'Activer l\'indexation automatique',
          description: 'Indexer automatiquement le contenu des pages visitées'
        },
        indexingDelay: {
          label: 'Délai d\'indexation',
          description: 'Délai après le chargement de la page avant de commencer l\'indexation'
        },
        enableSearchHistory: {
          label: 'Activer l\'historique de recherche',
          description: 'Se souvenir des requêtes de recherche récentes'
        },
        enableAutoComplete: {
          label: 'Activer l\'autocomplétion',
          description: 'Afficher des suggestions de recherche pendant la saisie'
        },
        enableDebugMode: {
          label: 'Activer le mode débogage',
          description: 'Afficher des informations détaillées de débogage de recherche'
        }
      }
    },

    // Data Backup
    dataBackup: {
      title: '💾 Sauvegarde et Importation de Données',
      description: 'Exporter et importer vos données d\'historique de navigation',
      loading: 'Chargement de l\'interface de sauvegarde de données...',
      stats: {
        title: 'Statistiques des Données',
        totalPages: 'Total des pages : {{count}}',
        totalBlacklist: 'Domaines en liste noire : {{count}}',
        lastUpdate: 'Dernière mise à jour : {{time}}'
      },
      export: {
        title: 'Exporter les Données',
        description: 'Exporter tout votre historique de navigation et paramètres en tant que fichier JSON',
        button: 'Exporter Toutes les Données',
        processing: 'Exportation...',
        progress: 'Exportation... {{progress}}%',
        success: 'Données exportées avec succès',
        error: 'Échec de l\'exportation : {{error}}'
      },
      import: {
        title: 'Importer les Données',
        description: 'Importer un fichier de données précédemment exporté',
        button: 'Sélectionner le Fichier d\'Importation',
        processing: 'Traitement de l\'importation...',
        success: 'Importation terminée avec succès',
        error: 'Échec de l\'importation : {{error}}',
        invalidFormat: 'Format de fichier de sauvegarde invalide',
        stats: {
          pagesImported: 'Pages importées : {{count}}',
          blacklistImported: 'Liste noire importée : {{count}}',
          pagesSkipped: 'Pages ignorées : {{count}}',
          blacklistSkipped: 'Liste noire ignorée : {{count}}'
        }
      },
      warnings: {
        exportSize: 'Le fichier d\'exportation peut être volumineux selon la taille de votre historique',
        importOverwrite: 'L\'importation fusionnera avec les données existantes, les doublons seront ignorés',
        backupFirst: 'Il est recommandé de sauvegarder les données actuelles avant l\'importation'
      },
      dangerZone: {
        title: 'Zone Dangereuse',
        description: 'Effacer toutes les données supprimera définitivement tout l\'historique de navigation et la liste noire. Cette opération ne peut pas être annulée. Veuillez vous assurer d\'avoir sauvegardé les données importantes avant de continuer.',
        clearAllButton: '🗑️ Effacer Toutes les Données',
        clearAllConfirm: 'Êtes-vous sûr de vouloir effacer toutes les données ? Cela supprimera définitivement tout l\'historique de navigation et la liste noire. Cette opération ne peut pas être annulée !',
        clearAllDoubleConfirm: 'Veuillez confirmer à nouveau : Cela supprimera définitivement toutes les données, y compris l\'historique de navigation et la liste noire. Êtes-vous sûr de vouloir continuer ?',
        clearAllSuccess: 'Toutes les données ont été effacées',
        clearAllError: 'Échec de l\'effacement, veuillez réessayer'
      },
      importResults: {
        title: 'Résultats de l\'Importation',
        pagesImported: 'Historique importé :',
        blacklistImported: 'Liste noire importée :',
        pagesSkipped: 'Historique ignoré :',
        blacklistSkipped: 'Liste noire ignorée :',
        entries: '{{count}} entrées',
        importErrors: 'Erreurs d\'importation :',
        moreErrors: '... et {{count}} erreurs supplémentaires'
      }
    },

    // Reading Assistant
    readingAssistant: {
      title: '📖 Assistant de Lecture',
      description: 'Configurer l\'assistant de lecture intelligent et les fonctionnalités de résumé IA pour améliorer votre expérience de lecture',
      loading: 'Chargement des paramètres de l\'assistant de lecture...',
      sections: {
        detection: {
          title: '🕐 Détection de Lecture',
          description: 'Détecter automatiquement votre comportement et durée de lecture'
        },
        notifications: {
          title: '🔔 Rappels de Lecture',
          description: 'Afficher des rappels non intrusifs pendant les sessions de lecture prolongées'
        },
        aiSummary: {
          title: '🤖 Résumé IA',
          description: 'Utiliser l\'IA pour générer des résumés intelligents pour les articles longs'
        },
        general: {
          title: '⚙️ Paramètres Généraux',
          description: 'Autres paramètres liés à l\'assistant de lecture'
        }
      },
      settings: {
        enableReadingDetection: {
          label: 'Activer la détection de lecture',
          description: 'Surveiller le temps de séjour sur la page et le statut actif'
        },
        minimumReadingTime: {
          label: 'Temps de lecture minimum',
          description: 'Les visites plus courtes que ce temps ne sont pas enregistrées comme lecture'
        },
        enableToastNotifications: {
          label: 'Activer les rappels de lecture',
          description: 'Afficher des rappels de durée de lecture dans le coin inférieur droit'
        },
        readingTimeThreshold: {
          label: 'Temps de déclenchement du rappel',
          description: 'Afficher le rappel après avoir atteint cette durée de lecture'
        },
        enableAutoSummary: {
          label: 'Activer le résumé IA',
          description: 'Proposer automatiquement de générer des résumés pour les articles longs'
        },
        summaryTriggerThreshold: {
          label: 'Temps de déclenchement du résumé',
          description: 'Proposer de générer un résumé après avoir lu pendant cette durée'
        },
        summaryPosition: {
          label: 'Position du résumé',
          description: 'Position d\'affichage du panneau de résumé sur la page',
          options: {
            topRight: 'En haut à droite',
            topLeft: 'En haut à gauche',
            bottomRight: 'En bas à droite',
            bottomLeft: 'En bas à gauche'
          }
        },
        summaryLanguage: {
          label: 'Langue du résumé',
          description: 'Préférence de langue pour la génération de résumés',
          options: {
            auto: 'Détection automatique',
            zh: 'Chinois',
            en: 'Anglais'
          }
        },
        summaryLength: {
          label: 'Longueur du résumé',
          description: 'Niveau de détail du résumé généré',
          options: {
            short: 'Bref (1-2 phrases)',
            medium: 'Moyen (1 paragraphe)',
            long: 'Détaillé (plusieurs paragraphes)'
          }
        },
        enableKeyboardShortcuts: {
          label: 'Activer les raccourcis clavier',
          description: 'Utiliser Ctrl+Shift+S pour générer rapidement un résumé'
        }
      },
      apiNotice: {
        title: '⚠️ Clé API Requise',
        description: 'Les fonctionnalités de résumé IA nécessitent la configuration de clés API de fournisseurs de services LLM. Veuillez vous rendre sur la page {{apiKeyManagement}} pour configurer.',
        configureButton: 'Configurer les Clés API'
      }
    },

    // About
    about: {
      title: 'Recall',
      version: 'Version {{version}}',
      tagline: 'Résout le problème des utilisateurs « j\'ai vu mais je ne trouve pas », rendant votre historique de navigation vraiment utile grâce à une recherche intelligente en texte intégral.',
      features: {
        title: 'Fonctionnalités Principales',
        items: {
          fullTextSearch: {
            title: 'Recherche en Texte Intégral',
            description: 'Pas seulement les titres, mais le contenu complet de la page.'
          },
          fastResponse: {
            title: 'Réponse Ultra-Rapide',
            description: 'Résultats de recherche en temps réel, réponse en millisecondes.'
          },
          privacyFirst: {
            title: 'Respect de la Vie Privée',
            description: 'Toutes les données stockées localement, jamais téléchargées dans le cloud.'
          },
          smartMatching: {
            title: 'Correspondance Intelligente',
            description: 'Prend en charge la recherche floue et la syntaxe de requête avancée.'
          }
        }
      },
      privacy: {
        title: 'Engagement de Confidentialité',
        description: 'Nous croyons fermement que vos données vous appartiennent. Recall stocke en toute sécurité tous les index d\'historique de navigation localement dans votre navigateur. Nous ne téléchargerons jamais vos données vers un serveur. Ce projet est entièrement open source, et vous pouvez examiner le code à tout moment pour vérifier notre engagement.'
      },
      links: {
        title: 'Liens Utiles',
        items: {
          github: '⭐ Donnez-nous une étoile sur GitHub',
          guide: '❓ Voir le guide d\'utilisation',
          review: '📝ÉvaluEz-nous sur le Chrome Store'
        }
      },
      acknowledgements: 'Merci à {{readability}} et {{fusejs}} pour leur puissant soutien.',
      copyright: '© 2025 Recall. Made with ❤️ by penwyp.'
    }
  },

  // Search Bar
  searchBar: {
    suggestions: 'Suggestions de recherche',
    noSuggestions: 'Aucune suggestion'
  },

  // Status Bar
  statusBar: {
    systemStatus: 'État du Système',
    normalOperation: 'Fonctionnement Normal',
    needsAttention: 'Nécessite une Attention',
    justNow: 'À l\'instant',
    minutesAgo: 'il y a {{count}} minutes',
    hoursAgo: 'il y a {{count}} heures',
    daysAgo: 'il y a {{count}} jours',
    unknown: 'Inconnu',
    veryFast: 'Très Rapide',
    fast: 'Rapide',
    normal: 'Normal',
    slow: 'Lent',
    systemDetails: 'Détails du Système',
    lastUpdate: 'Dernière Mise à Jour',
    storageInfo: 'Informations de Stockage',
    performanceMetrics: 'Métriques de Performance',
    dataManagement: 'Gestion des Données',
    backupData: 'Sauvegarde des Données',
    pages: 'Pages',
    showDetails: 'Afficher les Détails',
    hideDetails: 'Masquer les Détails',
    refreshStats: 'Actualiser les Statistiques',
    openSettings: 'Ouvrir les Paramètres',
    storageSize: 'Taille de Stockage',
    totalPages: 'Total des Pages',
    searchSpeed: 'Vitesse de Recherche',
    responseTime: 'Temps de Réponse'
  },

  // Filters
  filters: {
    all: 'Tout',
    today: 'Aujourd\'hui',
    thisWeek: 'Cette Semaine',
    thisMonth: 'Ce Mois',
    relevance: 'Pertinence',
    time: 'Temps',
    accessCount: 'Nombre d\'Accès'
  },

  // Syntax Help
  syntaxHelp: {
    title: 'Syntaxe de Recherche Avancée',
    close: 'Fermer l\'aide',
    intro: 'Utilisez la syntaxe suivante pour rechercher précisément dans votre historique de navigation :',
    examples: {
      basic: 'Rechercher des pages contenant React et JavaScript',
      exact: 'Correspondance exacte de phrases (doit correspondre complètement)',
      exclude: 'Exclure les résultats contenant des mots spécifiques',
      site: 'Rechercher seulement les pages de sites web spécifiques',
      complex: 'Requête composée : contient React, correspondance exacte "component lifecycle", exclut class, limité à reactjs.org'
    },
    tips: {
      title: 'Conseils d\'Utilisation',
      items: {
        '0': 'Peut combiner plusieurs syntaxes',
        '1': 'Le contenu entre guillemets sera correspondu exactement',
        '2': 'Le signe moins (-) doit être collé au mot à exclure',
        '3': 'site: supporte la correspondance de sous-domaines',
        '4': 'La recherche ne fait pas la distinction entre majuscules et minuscules'
      }
    },
    startSearch: 'Commencer la Recherche',
    viewHelp: 'Voir l\'aide de syntaxe de recherche',
    syntax: 'Syntaxe'
  },


  // Manifest localization
  manifest: {
    name: 'Recall',
    description: 'Extension de gestion de connaissances et de recherche d\'historique de navigateur alimentée par IA',
    actionTitle: 'Recall - Recherche Intelligente d\'Historique'
  }
} as const;