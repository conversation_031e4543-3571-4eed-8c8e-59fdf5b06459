/**
 * IMPL-404-001: I18nManager - Internationalization Management System
 *
 * This module provides centralized management for multi-language support including:
 * - Language resource loading and caching
 * - Real-time language switching
 * - Translation key resolution with fallbacks
 * - Performance optimization for <50ms switching
 */

export type SupportedLanguage = 'en' | 'zh-CN' | 'ja' | 'es' | 'fr';

export interface TranslationResource {
  [key: string]: string | TranslationResource;
}

export interface LanguageChangeEvent {
  newLanguage: SupportedLanguage;
  oldLanguage: SupportedLanguage;
}

export type LanguageChangeListener = (event: LanguageChangeEvent) => void;

export class I18nManager {
  private static instance: I18nManager;
  private currentLanguage: SupportedLanguage = 'en';
  private loadedResources: Map<SupportedLanguage, TranslationResource> = new Map();
  private listeners: Set<LanguageChangeListener> = new Set();
  private loadingPromises: Map<SupportedLanguage, Promise<TranslationResource>> = new Map();
  private initializationPromise: Promise<void> | null = null;
  
  // Static cache for imported modules to avoid repeated dynamic imports
  private static moduleCache: Map<SupportedLanguage, any> = new Map();

  private constructor() {
    // Initialize storage and load default resources
    this.initializationPromise = this.initializeWithDefaultLanguage();
  }

  /**
   * Initialize storage and load default language resources
   */
  private async initializeWithDefaultLanguage(): Promise<void> {
    await this.initializeStorage();
    // Only load the current language to speed up initialization
    try {
      await this.loadLanguageResources(this.currentLanguage);
      // Preload English as fallback in background if not already loaded
      if (this.currentLanguage !== 'en') {
        // Load English asynchronously without blocking
        this.loadLanguageResources('en').catch(error => {
          console.warn('Failed to preload English fallback:', error);
        });
      }
    } catch (error) {
      console.error('Failed to load default language resources:', error);
      // Try to load English as emergency fallback
      if (this.currentLanguage !== 'en') {
        try {
          await this.loadLanguageResources('en');
          this.currentLanguage = 'en';
        } catch (fallbackError) {
          console.error('Failed to load English fallback:', fallbackError);
        }
      }
    }
  }

  public static getInstance(): I18nManager {
    if (!I18nManager.instance) {
      I18nManager.instance = new I18nManager();
    }
    return I18nManager.instance;
  }

  /**
   * Wait for the I18nManager to be fully initialized
   * This ensures resources are loaded before using translations
   */
  public async waitForInitialization(): Promise<void> {
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  /**
   * Initialize storage by:
   * 1. Checking for a user-saved language preference.
   * 2. If not found, detecting language from timezone.
   * 3. If detection fails, defaulting to English.
   */
  private async initializeStorage(): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['language']);
        if (result.language && this.isLanguageSupported(result.language)) {
          this.currentLanguage = result.language;
          console.log(`Loaded saved language preference: ${result.language}`);
        } else {
          // No saved preference, try to detect from timezone
          const detectedLanguage = this.getLanguageFromTimezone();
          if (detectedLanguage) {
            this.currentLanguage = detectedLanguage;
            console.log(`Detected language from timezone: ${detectedLanguage}`);
          } else {
            console.log('Could not detect language from timezone, using default: en');
            this.currentLanguage = 'en'; // Default to English
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load language preference from storage:', error);
      // Fallback to English in case of storage error
      this.currentLanguage = 'en';
    }
  }

  /**
   * Detects the most appropriate supported language based on the user's timezone.
   * @returns {SupportedLanguage | null} The detected language or null if no mapping is found.
   */
  private getLanguageFromTimezone(): SupportedLanguage | null {
    try {
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      console.log(`Detecting language for timezone: ${timezone}`);

      if (timezone.startsWith('Asia/Shanghai') || timezone.startsWith('Asia/Chongqing') || timezone.startsWith('Asia/Harbin') || timezone.startsWith('Asia/Urumqi') || timezone.startsWith('Asia/Hong_Kong') || timezone.startsWith('Asia/Taipei') || timezone.startsWith('Asia/Macau')) {
        return 'zh-CN';
      }
      if (timezone.startsWith('Asia/Tokyo')) {
        return 'ja';
      }
      if (timezone.startsWith('America/') || timezone.startsWith('Australia/') || timezone.startsWith('Europe/London') || timezone.startsWith('Europe/Dublin')) {
        return 'en';
      }
       if (timezone.startsWith('Europe/Madrid') || timezone.startsWith('Europe/Paris')) { // Includes Spain and France
        const userLocale = navigator.language.toLowerCase();
        if (userLocale.startsWith('fr')) return 'fr';
        if (userLocale.startsWith('es')) return 'es';
        return 'en'; // Default for this timezone region if locale is ambiguous
      }
      if (timezone.startsWith('Europe/Berlin') || timezone.startsWith('Europe/Rome') || timezone.startsWith('Europe/Amsterdam')) {
         const userLocale = navigator.language.toLowerCase();
         if (userLocale.startsWith('fr')) return 'fr';
         if (userLocale.startsWith('es')) return 'es';
         return 'en';
      }

      console.log('No specific language mapping found for timezone.');
      return null;
    } catch (error) {
      console.warn('Could not determine timezone for language detection:', error);
      return null;
    }
  }

  /**
   * Set current language and trigger updates
   */
  public async setLanguage(language: SupportedLanguage): Promise<void> {
    if (!this.isLanguageSupported(language)) {
      throw new Error(`Unsupported language: ${language}`);
    }

    const oldLanguage = this.currentLanguage;

    if (oldLanguage === language) {
      return; // No change needed
    }

    // Load language resources if not already loaded
    await this.loadLanguageResources(language);

    // Update current language
    this.currentLanguage = language;

    // Save to storage
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.set({ language });
      }
    } catch (error) {
      console.warn('Failed to save language preference to storage:', error);
    }

    // Notify listeners
    const event: LanguageChangeEvent = { newLanguage: language, oldLanguage };
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in language change listener:', error);
      }
    });
  }

  /**
   * Get current language
   */
  public getCurrentLanguage(): SupportedLanguage {
    return this.currentLanguage;
  }

  /**
   * Check if language is supported
   */
  public isLanguageSupported(language: string): language is SupportedLanguage {
    return ['en', 'zh-CN', 'ja', 'es', 'fr'].includes(language);
  }

  /**
   * Load language resources with caching and performance optimization
   */
  public async loadLanguageResources(language: SupportedLanguage): Promise<TranslationResource> {
    // Return cached resource if available
    if (this.loadedResources.has(language)) {
      return this.loadedResources.get(language)!;
    }

    // Return existing loading promise if in progress
    if (this.loadingPromises.has(language)) {
      return this.loadingPromises.get(language)!;
    }

    // Start loading
    const loadingPromise = this.loadResourceFromFile(language);
    this.loadingPromises.set(language, loadingPromise);

    try {
      const resource = await loadingPromise;
      this.loadedResources.set(language, resource);
      this.loadingPromises.delete(language);
      return resource;
    } catch (error) {
      this.loadingPromises.delete(language);
      throw error;
    }
  }

  /**
   * Load resource from file system
   */
  private async loadResourceFromFile(language: SupportedLanguage): Promise<TranslationResource> {
    try {
      // Check static module cache first
      if (I18nManager.moduleCache.has(language)) {
        const cachedModule = I18nManager.moduleCache.get(language);
        console.log(`Using cached locale module for ${language}`);
        return cachedModule.default || cachedModule;
      }

      // Try to load actual locale files first
      let localeModule: any;

      try {
        switch (language) {
          case 'en':
            localeModule = await import('./locales/en');
            break;
          case 'zh-CN':
            localeModule = await import('./locales/zh-CN');
            break;
          case 'ja':
            localeModule = await import('./locales/ja');
            break;
          case 'es':
            localeModule = await import('./locales/es');
            break;
          case 'fr':
            localeModule = await import('./locales/fr');
            break;
          default:
            // For languages without dedicated files, fall back to English
            console.warn(`No locale file found for ${language}, falling back to English`);
            localeModule = await import('./locales/en');
            break;
        }

        // Cache the module statically
        I18nManager.moduleCache.set(language, localeModule);

        // Return the default export from the locale module
        const resource = localeModule.default || localeModule;
        console.log(`Loaded and cached locale resource for ${language}`);
        return resource;
      } catch (importError) {
        console.warn(`Failed to import locale file for ${language}, trying test fixtures:`, importError);
        // Fallback to test fixtures if locale files can't be imported
        return await this.loadTestFixture(language);
      }
    } catch (error) {
      console.error(`Failed to load language resource for ${language}:`, error);

      // Fallback to English if available
      if (language !== 'en' && this.loadedResources.has('en')) {
        console.warn(`Falling back to English for ${language}`);
        return this.loadedResources.get('en')!;
      }

      throw error;
    }
  }

  /**
   * Load test fixture (for testing purposes and fallback)
   */
  private async loadTestFixture(language: SupportedLanguage): Promise<TranslationResource> {
    // This would be replaced with actual file loading in production
    const fixtures: Record<SupportedLanguage, TranslationResource> = {
      'en': {
        app: { subtitle: 'Intelligent History Search' },
        search: { placeholder: 'Search your browsing history...', searching: 'Searching...', results: 'results' },
        empty: {
          title: 'Start searching your history',
          description: 'Enter keywords above to quickly find web pages you visited before'
        },
        statusBar: {
          systemStatus: 'System Status',
          normalOperation: 'Running Normally',
          needsAttention: 'Needs Attention',
          justNow: 'Just now',
          minutesAgo: '{{count}} minutes ago',
          hoursAgo: '{{count}} hours ago',
          daysAgo: '{{count}} days ago',
          unknown: 'Unknown',
          veryFast: 'Very Fast',
          fast: 'Fast',
          normal: 'Normal',
          slow: 'Slow',
          systemDetails: 'System Details',
          lastUpdate: 'Last Update',
          storageInfo: 'Storage Info',
          performanceMetrics: 'Performance Metrics',
          dataManagement: 'Data Management',
          backupData: 'Backup Data',
          pages: 'Pages',
          showDetails: 'Show Details',
          hideDetails: 'Hide Details',
          refreshStats: 'Refresh Statistics',
          openSettings: 'Open Settings',
          indexSize: 'Index Size',
          totalPages: 'Total Pages',
          searchSpeed: 'Search Speed',
          responseTime: 'Response Time'
        },
        common: {
          loading: 'Loading...',
          search: 'Search',
          clear: 'Clear',
          close: 'Close',
          save: 'Save',
          cancel: 'Cancel',
          delete: 'Delete',
          edit: 'Edit',
          add: 'Add',
          remove: 'Remove',
          yes: 'Yes',
          no: 'No',
          ok: 'OK',
          retry: 'Retry',
          refresh: 'Refresh',
          settings: 'Settings',
          about: 'About',
          help: 'Help',
          language: 'Language'
        },
        errors: {
          serviceInit: {
            title: 'Service Initialization Failed',
            message: 'Search service could not start properly'
          }
        }
      },
      'zh-CN': {
        app: { subtitle: '智能历史记录搜索' },
        search: { placeholder: '搜索您的浏览历史记录...', searching: '搜索中...', results: '结果' },
        empty: {
          title: '开始搜索您的历史记录',
          description: '在上方输入关键词，快速找到您之前访问过的网页'
        },
        statusBar: {
          systemStatus: '系统状态',
          normalOperation: '正常运行',
          needsAttention: '需要注意',
          justNow: '刚刚',
          minutesAgo: '{{count}}分钟前',
          hoursAgo: '{{count}}小时前',
          daysAgo: '{{count}}天前',
          unknown: '未知',
          veryFast: '极快',
          fast: '快速',
          normal: '正常',
          slow: '缓慢',
          systemDetails: '系统详情',
          lastUpdate: '最后更新',
          storageInfo: '存储信息',
          performanceMetrics: '性能指标',
          dataManagement: '数据管理',
          backupData: '备份数据',
          pages: '页面',
          showDetails: '显示详情',
          hideDetails: '隐藏详情',
          refreshStats: '刷新统计',
          openSettings: '打开设置',
          indexSize: '索引大小',
          totalPages: '总页面数',
          searchSpeed: '搜索速度',
          responseTime: '响应时间'
        },
        common: {
          loading: '加载中...',
          search: '搜索',
          clear: '清除',
          close: '关闭',
          save: '保存',
          cancel: '取消',
          delete: '删除',
          edit: '编辑',
          add: '添加',
          remove: '移除',
          yes: '是',
          no: '否',
          ok: '确定',
          retry: '重试',
          refresh: '刷新',
          settings: '设置',
          about: '关于',
          help: '帮助',
          language: '语言'
        },
        errors: {
          serviceInit: {
            title: '服务初始化失败',
            message: '搜索服务无法正常启动'
          }
        }
      },
      'ja': {
        app: { subtitle: 'インテリジェント履歴検索' },
        search: { placeholder: 'ブラウザ履歴を検索...', searching: '検索中...', results: '結果' },
        empty: {
          title: '履歴の検索を開始',
          description: '上にキーワードを入力して、以前に訪問したウェブページを素早く見つけます'
        },
        statusBar: {
          systemStatus: 'システム状態',
          normalOperation: '正常動作',
          needsAttention: '注意が必要',
          justNow: 'たった今',
          minutesAgo: '{{count}}分前',
          hoursAgo: '{{count}}時間前',
          daysAgo: '{{count}}日前',
          unknown: '不明',
          veryFast: '非常に高速',
          fast: '高速',
          normal: '通常',
          slow: '低速',
          systemDetails: 'システム詳細',
          lastUpdate: '最終更新',
          storageInfo: 'ストレージ情報',
          performanceMetrics: 'パフォーマンス指標',
          dataManagement: 'データ管理',
          backupData: 'データバックアップ',
          pages: 'ページ',
          showDetails: '詳細を表示',
          hideDetails: '詳細を非表示',
          refreshStats: '統計を更新',
          openSettings: '設定を開く',
          indexSize: 'インデックスサイズ',
          totalPages: '総ページ数',
          searchSpeed: '検索速度',
          responseTime: '応答時間'
        },
        common: {
          loading: '読み込み中...',
          search: '検索',
          clear: 'クリア',
          close: '閉じる',
          save: '保存',
          cancel: 'キャンセル',
          delete: '削除',
          edit: '編集',
          add: '追加',
          remove: '削除',
          yes: 'はい',
          no: 'いいえ',
          ok: 'OK',
          retry: '再試行',
          refresh: '更新',
          settings: '設定',
          about: 'について',
          help: 'ヘルプ',
          language: '言語'
        },
        errors: {
          serviceInit: {
            title: 'サービス初期化に失敗',
            message: '検索サービスが正常に開始できません'
          }
        }
      },
      'es': {
        app: { subtitle: 'Búsqueda Inteligente de Historial' },
        search: { placeholder: 'Buscar en tu historial de navegación...', searching: 'Buscando...', results: 'resultados' },
        empty: {
          title: 'Comienza a buscar en tu historial',
          description: 'Ingresa palabras clave arriba para encontrar rápidamente páginas web que visitaste antes'
        },
        statusBar: {
          systemStatus: 'Estado del Sistema',
          normalOperation: 'Funcionando Normalmente',
          needsAttention: 'Necesita Atención',
          justNow: 'Ahora mismo',
          minutesAgo: 'hace {{count}} minutos',
          hoursAgo: 'hace {{count}} horas',
          daysAgo: 'hace {{count}} días',
          unknown: 'Desconocido',
          veryFast: 'Muy Rápido',
          fast: 'Rápido',
          normal: 'Normal',
          slow: 'Lento',
          systemDetails: 'Detalles del Sistema',
          lastUpdate: 'Última Actualización',
          storageInfo: 'Información de Almacenamiento',
          performanceMetrics: 'Métricas de Rendimiento',
          dataManagement: 'Gestión de Datos',
          backupData: 'Respaldar Datos',
          pages: 'Páginas',
          showDetails: 'Mostrar Detalles',
          hideDetails: 'Ocultar Detalles',
          refreshStats: 'Actualizar Estadísticas',
          openSettings: 'Abrir Configuración',
          indexSize: 'Tamaño del Índice',
          totalPages: 'Total de Páginas',
          searchSpeed: 'Velocidad de Búsqueda',
          responseTime: 'Tiempo de Respuesta'
        },
        common: {
          loading: 'Cargando...',
          search: 'Buscar',
          clear: 'Limpiar',
          close: 'Cerrar',
          save: 'Guardar',
          cancel: 'Cancelar',
          delete: 'Eliminar',
          edit: 'Editar',
          add: 'Agregar',
          remove: 'Quitar',
          yes: 'Sí',
          no: 'No',
          ok: 'OK',
          retry: 'Reintentar',
          refresh: 'Actualizar',
          settings: 'Configuración',
          about: 'Acerca de',
          help: 'Ayuda',
          language: 'Idioma'
        },
        errors: {
          serviceInit: {
            title: 'Error de Inicialización del Servicio',
            message: 'El servicio de búsqueda no pudo iniciarse correctamente'
          }
        }
      },
      'fr': {
        app: { subtitle: 'Recherche Intelligente d\'Historique' },
        search: { placeholder: 'Rechercher dans votre historique de navigation...', searching: 'Recherche...', results: 'résultats' },
        empty: {
          title: 'Commencez à rechercher dans votre historique',
          description: 'Saisissez des mots-clés ci-dessus pour trouver rapidement les pages web que vous avez visitées'
        },
        statusBar: {
          systemStatus: 'État du Système',
          normalOperation: 'Fonctionnement Normal',
          needsAttention: 'Nécessite une Attention',
          justNow: 'À l\'instant',
          minutesAgo: 'il y a {{count}} minutes',
          hoursAgo: 'il y a {{count}} heures',
          daysAgo: 'il y a {{count}} jours',
          unknown: 'Inconnu',
          veryFast: 'Très Rapide',
          fast: 'Rapide',
          normal: 'Normal',
          slow: 'Lent',
          systemDetails: 'Détails du Système',
          lastUpdate: 'Dernière Mise à Jour',
          storageInfo: 'Informations de Stockage',
          performanceMetrics: 'Métriques de Performance',
          dataManagement: 'Gestion des Données',
          backupData: 'Sauvegarde des Données',
          pages: 'Pages',
          showDetails: 'Afficher les Détails',
          hideDetails: 'Masquer les Détails',
          refreshStats: 'Actualiser les Statistiques',
          openSettings: 'Ouvrir les Paramètres',
          indexSize: 'Taille de l\'Index',
          totalPages: 'Total des Pages',
          searchSpeed: 'Vitesse de Recherche',
          responseTime: 'Temps de Réponse'
        },
        common: {
          loading: 'Chargement...',
          search: 'Rechercher',
          clear: 'Effacer',
          close: 'Fermer',
          save: 'Enregistrer',
          cancel: 'Annuler',
          delete: 'Supprimer',
          edit: 'Modifier',
          add: 'Ajouter',
          remove: 'Retirer',
          yes: 'Oui',
          no: 'Non',
          ok: 'OK',
          retry: 'Réessayer',
          refresh: 'Actualiser',
          settings: 'Paramètres',
          about: 'À propos',
          help: 'Aide',
          language: 'Langue'
        },
        errors: {
          serviceInit: {
            title: 'Échec d\'Initialisation du Service',
            message: 'Le service de recherche n\'a pas pu démarrer correctement'
          }
        }
      }
    };

    return fixtures[language];
  }


  /**
   * Get translation for a key with fallback support
   */
  public getTranslation(key: string, interpolations?: Record<string, any>): string {
    const resource = this.loadedResources.get(this.currentLanguage);

    if (!resource) {
      // Try to load current language resource
      this.loadLanguageResources(this.currentLanguage).catch(console.error);
      console.warn(`No resource loaded for language: ${this.currentLanguage}, key: ${key}`);
      return key; // Return key as fallback
    }

    const translation = this.resolveTranslationKey(resource, key);

    if (translation === null) {
      // Try fallback to English
      const englishResource = this.loadedResources.get('en');
      if (englishResource && this.currentLanguage !== 'en') {
        const fallbackTranslation = this.resolveTranslationKey(englishResource, key);
        if (fallbackTranslation !== null) {
          return this.interpolateTranslation(fallbackTranslation, interpolations);
        }
      }

      console.warn(`Translation not found for key: ${key}`, {
        currentLanguage: this.currentLanguage,
        hasResource: !!resource,
        resourceKeys: resource ? Object.keys(resource) : [],
        keyParts: key.split('.')
      });
      return key; // Return key as final fallback
    }

    return this.interpolateTranslation(translation, interpolations);
  }

  /**
   * Resolve nested translation key
   */
  private resolveTranslationKey(resource: TranslationResource, key: string): string | null {
    const keys = key.split('.');
    let current: any = resource;

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return null;
      }
    }

    return typeof current === 'string' ? current : null;
  }

  /**
   * Interpolate variables in translation
   */
  private interpolateTranslation(translation: string, interpolations?: Record<string, any>): string {
    if (!interpolations) {
      return translation;
    }

    return translation.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      const value = interpolations[key];
      if (value === undefined || value === null) {
        return match;
      }

      // Format numbers with locale-specific formatting
      if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
        const numValue = typeof value === 'number' ? value : Number(value);
        return this.formatNumber(numValue);
      }

      return String(value);
    });
  }

  /**
   * Format number according to current locale
   */
  private formatNumber(num: number): string {
    try {
      // Get browser locale that matches our current language
      let locale = 'en-US'; // Default fallback
      
      switch (this.currentLanguage) {
        case 'zh-CN':
          locale = 'zh-CN';
          break;
        case 'ja':
          locale = 'ja-JP';
          break;
        case 'es':
          locale = 'es-ES';
          break;
        case 'fr':
          locale = 'fr-FR';
          break;
        case 'en':
        default:
          locale = 'en-US';
          break;
      }

      // Use Intl.NumberFormat for proper locale formatting
      return new Intl.NumberFormat(locale).format(num);
    } catch (error) {
      // Fallback to simple comma formatting if Intl is not available
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  }

  /**
   * Add language change listener
   */
  public addLanguageChangeListener(listener: LanguageChangeListener): void {
    this.listeners.add(listener);
  }

  /**
   * Remove language change listener
   */
  public removeLanguageChangeListener(listener: LanguageChangeListener): void {
    this.listeners.delete(listener);
  }

  /**
   * Get supported languages list
   */
  public getSupportedLanguages(): SupportedLanguage[] {
    return ['zh-CN', 'en', 'ja', 'es', 'fr'];
  }

  /**
   * Get language display name
   */
  public getLanguageDisplayName(language: SupportedLanguage): string {
    const names: Record<SupportedLanguage, string> = {
      'en': 'English',
      'zh-CN': '中文简体',
      'ja': '日本語',
      'es': 'Español',
      'fr': 'Français'
    };
    return names[language];
  }

  /**
   * Preload resources for better performance
   */
  public async preloadLanguages(languages: SupportedLanguage[]): Promise<void> {
    const promises = languages.map(lang => this.loadLanguageResources(lang));
    await Promise.all(promises);
  }

  /**
   * Clear cached resources (for memory management)
   */
  public clearCache(): void {
    this.loadedResources.clear();
    this.loadingPromises.clear();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): { loaded: number; loading: number } {
    return {
      loaded: this.loadedResources.size,
      loading: this.loadingPromises.size
    };
  }
}