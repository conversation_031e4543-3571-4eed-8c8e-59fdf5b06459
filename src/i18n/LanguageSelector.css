/**
 * LanguageSelector Component Styles
 * 
 * Provides top-right positioned language selector with:
 * - Clean, accessible design
 * - Smooth animations (<50ms)
 * - Mobile-responsive layout
 * - High contrast for accessibility
 */

.language-selector {
  position: relative;
  display: inline-block;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  z-index: 1000;
}

/* Top-right positioning for popup/options pages */
.language-selector--top-right {
  position: absolute;
  top: 16px;
  right: 16px;
}

/* Trigger <PERSON> */
.language-selector__trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: var(--bg-primary, #ffffff);
  border: 1px solid var(--border-color, #e1e5e9);
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #1a1a1a);
  transition: all 0.15s ease;
  min-width: 120px;
  justify-content: space-between;
}

.language-selector__trigger:hover {
  background: var(--bg-hover, #f5f5f5);
  border-color: var(--border-hover, #c1c8cd);
}

.language-selector__trigger:focus {
  outline: none;
  border-color: var(--accent-color, #0066cc);
  box-shadow: 0 0 0 3px var(--focus-ring, rgba(0, 102, 204, 0.1));
}

.language-selector__trigger--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Current Language Display */
.language-selector__current {
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Dropdown Arrow */
.language-selector__arrow {
  display: inline-block;
  transition: transform 0.15s ease;
  font-size: 12px;
  color: var(--text-secondary, #666);
}

.language-selector__arrow--up {
  transform: rotate(180deg);
}

/* Dropdown Container */
.language-selector__dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary, #ffffff);
  border: 1px solid var(--border-color, #e1e5e9);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
  animation: slideDown 0.15s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Language Options */
.language-selector__option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: var(--text-primary, #1a1a1a);
  transition: background-color 0.1s ease;
  border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.language-selector__option:last-child {
  border-bottom: none;
}

.language-selector__option:hover {
  background: var(--bg-hover, #f5f5f5);
}

.language-selector__option:focus {
  outline: none;
  background: var(--bg-focus, #e6f3ff);
}

.language-selector__option--selected {
  background: var(--bg-selected, #e6f3ff);
  color: var(--accent-color, #0066cc);
  font-weight: 600;
}

.language-selector__option--selected:hover {
  background: var(--bg-selected-hover, #d1e9ff);
}

/* Option Text */
.language-selector__option-text {
  flex: 1;
}

/* Check Mark */
.language-selector__check {
  color: var(--accent-color, #0066cc);
  font-weight: bold;
  margin-left: 8px;
}

/* Loading State */
.language-selector__loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
}

.language-selector__spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-light, #f0f0f0);
  border-top: 2px solid var(--accent-color, #0066cc);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Screen Reader Only Text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Dark Theme Support */
[data-theme="dark"] .language-selector__trigger {
  background: var(--bg-primary-dark, #2a2a2a);
  border-color: var(--border-color-dark, #404040);
  color: var(--text-primary-dark, #ffffff);
}

[data-theme="dark"] .language-selector__trigger:hover {
  background: var(--bg-hover-dark, #363636);
  border-color: var(--border-hover-dark, #505050);
}

[data-theme="dark"] .language-selector__dropdown {
  background: var(--bg-primary-dark, #2a2a2a);
  border-color: var(--border-color-dark, #404040);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .language-selector__option {
  color: var(--text-primary-dark, #ffffff);
  border-bottom-color: var(--border-light-dark, #404040);
}

[data-theme="dark"] .language-selector__option:hover {
  background: var(--bg-hover-dark, #363636);
}

[data-theme="dark"] .language-selector__option--selected {
  background: var(--bg-selected-dark, #1a3d5c);
  color: var(--accent-color-dark, #4d9eff);
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .language-selector__trigger {
    border-width: 2px;
  }
  
  .language-selector__option {
    border-bottom-width: 1px;
    border-bottom-style: solid;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .language-selector__trigger,
  .language-selector__arrow,
  .language-selector__option {
    transition: none;
  }
  
  .language-selector__dropdown {
    animation: none;
  }
  
  .language-selector__spinner {
    animation: none;
  }
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .language-selector {
    width: 100%;
  }
  
  .language-selector__trigger {
    width: 100%;
    min-width: auto;
  }
  
  .language-selector__dropdown {
    max-height: 200px;
  }
}

/* Focus Visible Support */
.language-selector__trigger:focus-visible {
  outline: 2px solid var(--accent-color, #0066cc);
  outline-offset: 2px;
}

.language-selector__option:focus-visible {
  outline: 2px solid var(--accent-color, #0066cc);
  outline-offset: -2px;
}