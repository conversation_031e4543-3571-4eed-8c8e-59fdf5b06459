/**
 * IMPL-404-001: LanguageSelector Component - Multi-language Support UI
 * 
 * React component that provides:
 * - Language selection dropdown in top-right position
 * - Real-time language switching without page refresh
 * - Performance optimized switching (<50ms)
 * - Accessibility support with ARIA labels
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { I18nManager } from './I18nManager';
import type { SupportedLanguage, LanguageChangeEvent } from './I18nManager';
import './LanguageSelector.css';

export interface LanguageSelectorProps {
  onLanguageChange?: (language: SupportedLanguage) => void;
  className?: string;
  disabled?: boolean;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  onLanguageChange,
  className = '',
  disabled = false
}) => {
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>('en');
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const i18nManager = useRef(I18nManager.getInstance());
  const selectorRef = useRef<HTMLDivElement>(null);

  // Initialize and wait for resources
  useEffect(() => {
    const initialize = async () => {
      const manager = i18nManager.current;
      await manager.waitForInitialization();
      setCurrentLanguage(manager.getCurrentLanguage());
      setIsInitialized(true);
    };
    initialize();
  }, []);

  // Language change handler
  const handleLanguageChange = useCallback(async (event: LanguageChangeEvent) => {
    setCurrentLanguage(event.newLanguage);
    if (onLanguageChange) {
      await onLanguageChange(event.newLanguage);
    }
  }, [onLanguageChange]);

  // Set up language change listener
  useEffect(() => {
    const manager = i18nManager.current;
    manager.addLanguageChangeListener(handleLanguageChange);

    return () => {
      manager.removeLanguageChangeListener(handleLanguageChange);
    };
  }, [handleLanguageChange]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle language selection
  const selectLanguage = async (language: SupportedLanguage) => {
    if (disabled || language === currentLanguage) {
      setIsOpen(false);
      return;
    }

    setIsLoading(true);
    const startTime = performance.now();

    try {
      await i18nManager.current.setLanguage(language);
      
      const endTime = performance.now();
      const switchTime = endTime - startTime;
      
      // Log performance for monitoring
      if (switchTime > 50) {
        console.warn(`Language switch took ${switchTime.toFixed(2)}ms, exceeding 50ms target`);
      }
    } catch (error) {
      console.error('Failed to switch language:', error);
    } finally {
      setIsLoading(false);
      setIsOpen(false);
    }
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent) => {
    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        setIsOpen(!isOpen);
        break;
      case 'Escape':
        setIsOpen(false);
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          setIsOpen(false);
        }
        break;
    }
  };

  const supportedLanguages = i18nManager.current.getSupportedLanguages();
  const currentLanguageDisplay = i18nManager.current.getLanguageDisplayName(currentLanguage);

  return (
    <div 
      ref={selectorRef}
      className={`language-selector ${className} ${isOpen ? 'language-selector--open' : ''}`}
      data-testid="language-selector"
    >
      <button
        className={`language-selector__trigger ${disabled ? 'language-selector__trigger--disabled' : ''}`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        disabled={disabled || isLoading || !isInitialized}
        aria-label={isInitialized ? i18nManager.current.getTranslation('common.language') : 'Loading languages'}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        role="combobox"
        data-testid="language-trigger"
      >
        <span className="language-selector__current">
          {isInitialized ? currentLanguageDisplay : '...'}
        </span>
        <span 
          className={`language-selector__arrow ${isOpen ? 'language-selector__arrow--up' : ''}`}
          aria-hidden="true"
        >
          ▼
        </span>
      </button>

      {isOpen && isInitialized && (
        <div 
          className="language-selector__dropdown"
          role="listbox"
          aria-label="Language options"
        >
          {supportedLanguages.map((language) => {
            const displayName = i18nManager.current.getLanguageDisplayName(language);
            const isSelected = language === currentLanguage;
            
            return (
              <button
                key={language}
                className={`language-selector__option ${isSelected ? 'language-selector__option--selected' : ''}`}
                onClick={() => selectLanguage(language)}
                role="option"
                aria-selected={isSelected}
                data-testid={`language-${language}`}
                disabled={isLoading}
              >
                <span className="language-selector__option-text">
                  {displayName}
                </span>
                {isSelected && (
                  <span className="language-selector__check" aria-hidden="true">
                    ✓
                  </span>
                )}
              </button>
            );
          })}
        </div>
      )}

      {(isLoading || !isInitialized) && (
        <div className="language-selector__loading" role="status" aria-live="polite">
          <span className="language-selector__spinner" aria-hidden="true"></span>
          <span className="sr-only">
            {isInitialized ? 'Switching language...' : 'Loading languages...'}
          </span>
        </div>
      )}
    </div>
  );
};