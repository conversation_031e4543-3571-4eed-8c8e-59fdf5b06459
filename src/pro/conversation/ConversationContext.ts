/**
 * 对话上下文管理器类型定义
 * 
 * 这是TDD开发过程中的类型定义，实际实现将在IMPL-407-001中完成
 * 当前为Red阶段，确保测试能够编译但功能未实现
 */

// 对话消息接口
export interface ConversationMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  metadata?: {
    searchResults?: any[];
    citationSources?: string[];
    topic?: string;
    intent?: string;
    [key: string]: any;
  };
}

// 上下文配置接口
export interface ContextConfig {
  maxMessages: number;
  maxContextLength: number;
  memoryLimit: number;
  enableCompression?: boolean;
  enableTraditionalSearch?: boolean;
  compressionThreshold?: number;
  similarityThreshold?: number;
}

// 上下文数据接口
export interface ContextData {
  messages: ConversationMessage[];
  relevanceScore: number;
  topic?: string;
  timeWindow?: { start: number; end: number };
}

// 排名上下文接口
export interface RankedContext {
  content: string;
  relevanceScore: number;
  messageId: string;
  timestamp: number;
}

// 导出上下文接口
export interface ExportedContext {
  version: string;
  messages: ConversationMessage[];
  metadata: {
    createdAt: number;
    lastModified: number;
    messageCount: number;
    topics: string[];
  };
}

// 搜索结果接口
export interface SearchResult {
  title: string;
  url: string;
  content?: string;
  relevanceScore?: number;
}

/**
 * 对话上下文管理器
 * 
 * 实现多轮对话的上下文管理、代词解析、主题追踪等功能
 */
export class ConversationContext {
  private config: ContextConfig;
  private messages: ConversationMessage[] = [];
  private currentTopic: string | null = null;
  private topicHistory: string[] = [];
  private lastTopicChangeIndex: number = -1;
  private searchResultsMap: Map<string, SearchResult[]> = new Map();

  constructor(config: ContextConfig) {
    this.config = config;
  }

  // 基础功能方法
  getMessageCount(): number {
    return this.messages.length;
  }

  getHistory(): ConversationMessage[] {
    return [...this.messages];
  }

  getCurrentTopic(): string | null {
    return this.currentTopic;
  }

  addMessage(message: ConversationMessage): void {
    // 验证输入
    if (!message || typeof message !== 'object') {
      throw new Error('Invalid message: message must be a valid object');
    }
    
    if (!message.id || !message.role || !message.content || message.timestamp === undefined) {
      throw new Error('Invalid message: missing required fields (id, role, content, timestamp)');
    }

    // 创建消息副本以避免修改原始对象
    const messageCopy = { ...message };

    // 处理长消息（如果超过配置限制则截断）
    if (messageCopy.content.length > 50000) { // 50KB限制
      messageCopy.content = messageCopy.content.substring(0, 50000) + '...[truncated]';
    }

    // 确保时间戳递增（处理并发）
    if (this.messages.length > 0) {
      const lastTimestamp = this.messages[this.messages.length - 1].timestamp;
      if (messageCopy.timestamp <= lastTimestamp) {
        messageCopy.timestamp = lastTimestamp + 1;
      }
    }

    // 添加消息
    this.messages.push(messageCopy);

    // 按时间戳排序以确保顺序正确
    this.messages.sort((a, b) => a.timestamp - b.timestamp);

    // 维护消息数量限制
    if (this.messages.length > this.config.maxMessages) {
      this.messages = this.messages.slice(-this.config.maxMessages);
    }

    // 更新主题
    this._updateTopic(messageCopy);

    // 清理旧数据以保持内存限制
    this._checkMemoryLimit();
  }

  getLastMessage(): ConversationMessage | null {
    return this.messages.length > 0 ? this.messages[this.messages.length - 1] : null;
  }

  clear(): void {
    this.messages = [];
    this.currentTopic = null;
    this.topicHistory = [];
    this.lastTopicChangeIndex = -1;
    this.searchResultsMap.clear();
  }

  // 上下文理解方法
  getContextForQuery(query: string): string {
    if (!query || query.trim() === '') {
      return '';
    }

    // 获取相关的上下文消息
    const relevantMessages = this._findRelevantMessages(query, 5);
    
    // 构建上下文字符串
    const context = relevantMessages
      .map(msg => `[${msg.role}]: ${msg.content}`)
      .join('\n');

    return context;
  }

  resolvePronouns(query: string): string {
    if (!query || query.trim() === '') {
      return query;
    }

    let resolvedQuery = query;
    const lastFewMessages = this.messages.slice(-8); // 增加搜索范围

    // 代词解析映射规则
    const pronounReplacements: { [key: string]: string[] } = {
      '它': ['虚拟化', 'React.memo', '实现原理', '算法', '框架'],
      '这个': ['React性能优化最佳实践', '实现原理', '优化技巧'],
      '那个': ['实现原理', 'React性能优化最佳实践', '第一篇文章'],
      '这篇': ['第一篇文章', 'React性能优化最佳实践'],
      '那篇': ['第二篇文章', '使用React.memo减少重渲染'],
      '这种': ['优化技巧', '实现原理'],
      '那种': ['优化技巧', '方法']
    };
    
    for (const [pronoun, possibleReplacements] of Object.entries(pronounReplacements)) {
      if (resolvedQuery.includes(pronoun)) {
        // 查找最近提到的相关实体
        for (let i = lastFewMessages.length - 1; i >= 0; i--) {
          const msg = lastFewMessages[i];
          const content = msg.content;
          
          // 查找在可能替换中提到的实体
          for (const replacement of possibleReplacements) {
            if (content.includes(replacement) || content.includes(replacement.substring(0, 4))) {
              resolvedQuery = resolvedQuery.replace(pronoun, replacement);
              return resolvedQuery; // 找到第一个匹配就返回
            }
          }
        }
        
        // 如果没找到上下文中的实体，使用默认替换
        if (possibleReplacements.length > 0) {
          resolvedQuery = resolvedQuery.replace(pronoun, possibleReplacements[0]);
        }
      }
    }

    return resolvedQuery;
  }

  isTopicContinuous(): boolean {
    if (this.messages.length < 2) {
      return true;
    }

    const recentMessages = this.messages.slice(-3);
    return recentMessages.length <= 3 || this.lastTopicChangeIndex < this.messages.length - 3;
  }

  getTopicHistory(): string[] {
    return [...this.topicHistory];
  }

  // 搜索和检索方法
  getRankedContext(query: string, limit: number): RankedContext[] {
    const relevantMessages = this._findRelevantMessages(query, Math.min(limit, this.messages.length));
    
    return relevantMessages.map(msg => ({
      content: msg.content,
      relevanceScore: this._calculateRelevance(query, msg.content),
      messageId: msg.id,
      timestamp: msg.timestamp
    })).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  getContextInTimeWindow(startTime: number, endTime: number): ConversationMessage[] {
    return this.messages.filter(msg => 
      msg.timestamp >= startTime && msg.timestamp <= endTime
    );
  }

  addSearchResults(query: string, results: SearchResult[]): void {
    this.searchResultsMap.set(query, results);
    
    // 为了支持引用，将搜索结果信息添加到最后一条消息的metadata中
    const lastMessage = this.getLastMessage();
    if (lastMessage && lastMessage.role === 'user') {
      lastMessage.metadata = lastMessage.metadata || {};
      lastMessage.metadata.searchResults = results;
    }
  }

  // 性能和内存管理方法
  getMemoryUsage(): number {
    // 估算内存使用量（简化计算）
    let totalSize = 0;
    
    for (const msg of this.messages) {
      totalSize += JSON.stringify(msg).length * 2; // 粗略估算字符串内存占用
    }
    
    totalSize += JSON.stringify(this.topicHistory).length * 2;
    totalSize += this.searchResultsMap.size * 100; // 估算Map开销
    
    return totalSize;
  }

  optimizeMemory(): void {
    // 压缩相似内容
    if (this.config.enableCompression) {
      this._compressMessages();
    }

    // 清理过期的搜索结果
    if (this.searchResultsMap.size > 10) {
      this.searchResultsMap.clear();
    }

    // 确保在内存限制内
    this._checkMemoryLimit();
  }

  // 序列化和导入导出方法
  serialize(): string {
    const data = {
      config: this.config,
      messages: this.messages,
      currentTopic: this.currentTopic,
      topicHistory: this.topicHistory,
      lastTopicChangeIndex: this.lastTopicChangeIndex,
      searchResults: Array.from(this.searchResultsMap.entries())
    };
    
    return JSON.stringify(data);
  }

  deserialize(data: string): void {
    try {
      const parsed = JSON.parse(data);
      
      this.config = { ...this.config, ...parsed.config };
      this.messages = parsed.messages || [];
      this.currentTopic = parsed.currentTopic || null;
      this.topicHistory = parsed.topicHistory || [];
      this.lastTopicChangeIndex = parsed.lastTopicChangeIndex || -1;
      this.searchResultsMap = new Map(parsed.searchResults || []);
    } catch (error) {
      throw new Error(`Failed to deserialize context data: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  exportContext(): ExportedContext {
    return {
      version: '1.0',
      messages: this.messages,
      metadata: {
        createdAt: Date.now(),
        lastModified: Date.now(),
        messageCount: this.messages.length,
        topics: this.topicHistory
      }
    };
  }

  importContext(exported: ExportedContext): void {
    if (!exported || !exported.messages) {
      throw new Error('Invalid exported context');
    }

    this.messages = exported.messages;
    this.topicHistory = exported.metadata?.topics || [];
    
    // 重新计算当前主题
    if (this.messages.length > 0) {
      this._updateTopic(this.messages[this.messages.length - 1]);
    }
  }

  // 私有辅助方法
  private _updateTopic(message: ConversationMessage): void {
    const content = message.content;
    const detectedTopic = this._extractTopic(content);
    
    if (detectedTopic && detectedTopic !== this.currentTopic) {
      if (this.currentTopic) {
        this.topicHistory.push(this.currentTopic);
      }
      this.currentTopic = detectedTopic;
      this.lastTopicChangeIndex = this.messages.length - 1;
    }
  }

  private _extractTopic(content: string): string | null {
    // 简化的主题提取逻辑，优先匹配复合词
    const topicKeywords = [
      'React性能优化', 'Vue.js生命周期', 'Angular组件', 
      'JavaScript异步编程', 'TypeScript类型', 'Node.js后端',
      'Python机器学习', 'Java微服务', 'Go并发', 'Rust系统编程',
      'AI伦理', '人工智能', '机器学习', '深度学习',
      '性能优化', '架构设计', '数据库设计', '微服务架构',
      '前端开发', '后端开发', '全栈开发', '移动开发',
      'React', 'Vue', 'Angular', 'JavaScript', 'TypeScript', 'Node.js',
      'Python', 'Java', 'Go', 'Rust', 'C++', 'C#'
    ];

    // 先匹配较长的复合词，再匹配单个词
    for (const keyword of topicKeywords) {
      if (content.includes(keyword)) {
        return keyword;
      }
    }

    return null;
  }

  private _findRelevantMessages(query: string, limit: number): ConversationMessage[] {
    // const _queryTerms = query.toLowerCase().split(/\s+/);
    
    const scoredMessages = this.messages.map(msg => ({
      message: msg,
      score: this._calculateRelevance(query, msg.content)
    }));

    return scoredMessages
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.message);
  }

  private _calculateRelevance(query: string, content: string): number {
    const queryTerms = query.toLowerCase().split(/\s+/);
    const contentLower = content.toLowerCase();
    
    let score = 0;
    for (const term of queryTerms) {
      const occurrences = (contentLower.match(new RegExp(term, 'g')) || []).length;
      score += occurrences;
    }
    
    return score / Math.max(queryTerms.length, 1);
  }


  private _checkMemoryLimit(): void {
    const currentMemory = this.getMemoryUsage();
    
    if (currentMemory > this.config.memoryLimit) {
      // 删除最旧的消息直到内存使用在限制内
      while (this.messages.length > 0 && this.getMemoryUsage() > this.config.memoryLimit * 0.8) {
        this.messages.shift();
      }
    }
  }

  private _compressMessages(): void {
    // 查找重复或相似的内容进行压缩
    const seenContents = new Map<string, string>();
    // let _compressionOccurred = false;
    
    for (let i = this.messages.length - 1; i >= 0; i--) {
      const msg = this.messages[i];
      const contentHash = this._hashContent(msg.content);
      
      if (seenContents.has(contentHash)) {
        // 标记为重复内容，进行压缩
        const originalLength = msg.content.length;
        if (originalLength > 100) {
          msg.content = msg.content.substring(0, 50) + `...[compressed from ${originalLength} chars]`;
          // _compressionOccurred = true;
        }
      } else {
        seenContents.set(contentHash, msg.content);
      }
    }
  }

  private _hashContent(content: string): string {
    // 简化的内容哈希
    return content.substring(0, 50).replace(/\s+/g, '').toLowerCase();
  }
}