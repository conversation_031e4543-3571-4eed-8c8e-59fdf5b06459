/**
 * Test utilities for streaming functionality
 * Provides helper functions and mock data for testing
 */

import { MockEventSource } from './EventSource';

export interface StreamTestData {
  id: string;
  content: string;
  timestamp: number;
  type: 'text' | 'code' | 'markdown';
  isComplete?: boolean;
}

export interface StreamTestScenario {
  name: string;
  events: StreamTestEvent[];
  expectedResult: any;
  timeout?: number;
}

export interface StreamTestEvent {
  delay: number;
  type: 'message' | 'error' | 'close';
  data?: string;
  eventType?: string;
}

/**
 * Create test streaming data
 */
export function createTestStreamData(chunks: string[]): StreamTestData[] {
  return chunks.map((chunk, index) => ({
    id: `chunk-${index}`,
    content: chunk,
    timestamp: Date.now() + index * 100,
    type: 'text' as const,
    isComplete: index === chunks.length - 1
  }));
}

/**
 * Generate realistic streaming response chunks
 */
export function generateStreamingChunks(fullText: string, chunkSize: number = 10): string[] {
  const chunks: string[] = [];
  for (let i = 0; i < fullText.length; i += chunkSize) {
    chunks.push(fullText.slice(0, i + chunkSize));
  }
  return chunks;
}

/**
 * Create mock conversation response
 */
export function createMockConversationResponse(): string {
  return `这是一个模拟的AI对话响应。它包含了多个段落来测试流式传输功能。

第一段：这里测试基本的文本流式传输功能，确保每个字符都能正确地实时显示给用户。

第二段：这里包含一些**粗体文本**和*斜体文本*来测试Markdown格式的处理能力。

第三段：\`代码片段\`的处理也很重要，因为开发者用户经常需要查看代码相关的回答。

最后一段：这里测试流式传输的完成检测和最终状态处理。`;
}

/**
 * Create performance test data
 */
export function createPerformanceTestData(size: 'small' | 'medium' | 'large'): string {
  const baseSizes = {
    small: 1000,    // 1KB
    medium: 10000,  // 10KB  
    large: 100000   // 100KB
  };
  
  const targetSize = baseSizes[size];
  const pattern = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ';
  const repetitions = Math.ceil(targetSize / pattern.length);
  
  return pattern.repeat(repetitions).slice(0, targetSize);
}

/**
 * Create test scenarios for different streaming conditions
 */
export function createStreamingTestScenarios(): StreamTestScenario[] {
  return [
    {
      name: 'Normal streaming',
      events: [
        { delay: 50, type: 'message', data: 'Hello' },
        { delay: 100, type: 'message', data: 'Hello world' },
        { delay: 150, type: 'message', data: 'Hello world!' },
        { delay: 200, type: 'close' }
      ],
      expectedResult: 'Hello world!'
    },
    {
      name: 'Streaming with errors',
      events: [
        { delay: 50, type: 'message', data: 'Start' },
        { delay: 100, type: 'error' },
        { delay: 200, type: 'message', data: 'Recovered' }
      ],
      expectedResult: 'error_recovery_test',
      timeout: 500
    },
    {
      name: 'Large data streaming',
      events: [
        { delay: 10, type: 'message', data: createPerformanceTestData('medium') }
      ],
      expectedResult: 'performance_test'
    },
    {
      name: 'Rapid streaming',
      events: Array.from({ length: 50 }, (_, i) => ({
        delay: i * 10,
        type: 'message' as const,
        data: `Chunk ${i + 1}`
      })),
      expectedResult: 'rapid_stream_test'
    }
  ];
}

/**
 * Wait for async operations in tests
 */
export function waitFor(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Create a controlled mock EventSource
 */
export function createControlledMockEventSource(url: string): {
  eventSource: MockEventSource;
  trigger: {
    open: () => void;
    message: (data: string, type?: string) => void;
    error: (error?: Error) => void;
    close: () => void;
  };
} {
  const eventSource = new MockEventSource(url);
  
  return {
    eventSource,
    trigger: {
      open: () => eventSource.simulateConnection(),
      message: (data: string, type?: string) => eventSource.simulateMessage(data, type),
      error: (error?: Error) => eventSource.simulateError(error),
      close: () => eventSource.simulateClose()
    }
  };
}

/**
 * Memory usage testing utilities
 */
export class MemoryTestUtils {
  private initialMemory: number = 0;
  
  startMonitoring(): void {
    // In browser environment, we'd use performance.memory
    // In Node.js test environment, we simulate
    this.initialMemory = this.getCurrentMemoryUsage();
  }
  
  getCurrentMemoryUsage(): number {
    // Simulate memory usage measurement
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    
    // Fallback for Node.js testing
    if (typeof process !== 'undefined' && (process as any).memoryUsage) {
      return (process as any).memoryUsage().heapUsed;
    }
    
    return 0;
  }
  
  getMemoryDelta(): number {
    return this.getCurrentMemoryUsage() - this.initialMemory;
  }
  
  checkMemoryLeak(threshold: number = 1024 * 1024): boolean { // 1MB default
    return this.getMemoryDelta() > threshold;
  }
}

/**
 * Performance benchmarking utilities
 */
export class PerformanceBenchmark {
  private startTime: number = 0;
  private measurements: number[] = [];
  
  start(): void {
    this.startTime = performance.now();
  }
  
  end(): number {
    const endTime = performance.now();
    const duration = endTime - this.startTime;
    this.measurements.push(duration);
    return duration;
  }
  
  getAverageTime(): number {
    if (this.measurements.length === 0) return 0;
    return this.measurements.reduce((sum, time) => sum + time, 0) / this.measurements.length;
  }
  
  getMedianTime(): number {
    if (this.measurements.length === 0) return 0;
    const sorted = [...this.measurements].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  }
  
  reset(): void {
    this.measurements = [];
    this.startTime = 0;
  }
}