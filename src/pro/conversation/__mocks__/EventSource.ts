/**
 * Mock EventSource for testing streaming functionality
 * Simulates Server-Sent Events behavior for unit tests
 */

export interface MockEventSourceEvent {
  type: string;
  data: string;
  id?: string;
  retry?: number;
}

export class MockEventSource {
  public url: string;
  public readyState: number;
  public withCredentials: boolean;
  
  // Event handlers
  public onopen: ((event: Event) => void) | null = null;
  public onmessage: ((event: MessageEvent) => void) | null = null;
  public onerror: ((event: Event) => void) | null = null;
  
  // Mock control properties
  private listeners: Map<string, ((event: any) => void)[]> = new Map();
  private isConnected: boolean = false;
  private simulatedDelay: number = 0;
  
  // Static properties to match EventSource
  static readonly CONNECTING = 0;
  static readonly OPEN = 1;
  static readonly CLOSED = 2;
  
  constructor(url: string, eventSourceInitDict?: EventSourceInit) {
    this.url = url;
    this.withCredentials = eventSourceInitDict?.withCredentials || false;
    this.readyState = MockEventSource.CONNECTING;
    
    // Simulate connection after next tick
    setTimeout(() => this.simulateConnection(), 10);
  }
  
  addEventListener(type: string, listener: (event: any) => void): void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, []);
    }
    this.listeners.get(type)!.push(listener);
  }
  
  removeEventListener(type: string, listener: (event: any) => void): void {
    const listeners = this.listeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }
  
  close(): void {
    this.readyState = MockEventSource.CLOSED;
    this.isConnected = false;
  }
  
  // Mock control methods for testing
  simulateConnection(): void {
    this.readyState = MockEventSource.OPEN;
    this.isConnected = true;
    this.dispatchEvent(new Event('open'));
  }
  
  simulateMessage(data: string, type: string = 'message', id?: string): void {
    if (!this.isConnected) return;
    
    const event = new MessageEvent(type, {
      data,
      lastEventId: id || '',
      origin: this.url
    });
    
    setTimeout(() => {
      this.dispatchEvent(event);
    }, this.simulatedDelay);
  }
  
  simulateError(error?: Error): void {
    const errorEvent = new Event('error');
    if (error) {
      (errorEvent as any).error = error;
    }
    this.dispatchEvent(errorEvent);
  }
  
  simulateClose(): void {
    this.readyState = MockEventSource.CLOSED;
    this.isConnected = false;
    this.dispatchEvent(new Event('close'));
  }
  
  // Simulate network delay
  setSimulatedDelay(ms: number): void {
    this.simulatedDelay = ms;
  }
  
  // Simulate reconnection
  simulateReconnection(): void {
    this.readyState = MockEventSource.CONNECTING;
    this.isConnected = false;
    setTimeout(() => this.simulateConnection(), 100);
  }
  
  private dispatchEvent(event: Event): void {
    // Handle built-in event handlers
    if (event.type === 'open' && this.onopen) {
      this.onopen(event);
    } else if (event.type === 'message' && this.onmessage) {
      this.onmessage(event as MessageEvent);
    } else if (event.type === 'error' && this.onerror) {
      this.onerror(event);
    }
    
    // Handle addEventListener listeners
    const listeners = this.listeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => listener(event));
    }
  }
}

// Global mock setup
const MockEventSourceClass = MockEventSource;

// Export for Jest mocking
export const createMockEventSource = (url: string, init?: EventSourceInit) => {
  return new MockEventSourceClass(url, init);
};

// Jest manual mock
const mockEventSource = MockEventSource;
export default mockEventSource;