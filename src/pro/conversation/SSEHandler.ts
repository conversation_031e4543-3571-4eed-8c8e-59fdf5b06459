/**
 * SSEHandler.ts
 * Server-Sent Events处理器实现
 * 
 * 提供稳定的SSE连接管理，支持自动重连、错误处理和事件分发
 */

export interface SSEOptions {
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  timeout?: number;
  headers?: Record<string, string>;
  withCredentials?: boolean;
}

export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';

export interface SSEEvent {
  type: string;
  data: string;
  id?: string;
  retry?: number;
  timestamp: number;
}

/**
 * SSE处理器 - 封装EventSource并提供增强功能
 */
export class SSEHandler {
  private eventSource: EventSource | null = null;
  private url: string = '';
  private options: SSEOptions = {};
  private listeners: Map<string, Function[]> = new Map();
  private connectionStatus: ConnectionStatus = 'disconnected';
  private reconnectAttempts: number = 0;
  private lastError: Error | null = null;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private connectionTimeout: NodeJS.Timeout | null = null;
  private autoReconnectEnabled: boolean = true;

  /**
   * 连接到SSE端点
   */
  async connect(url: string, options: SSEOptions = {}): Promise<void> {
    // 验证URL
    if (!url || url.trim() === '') {
      throw new Error('URL cannot be empty');
    }

    this.url = url;
    this.options = {
      autoReconnect: true,
      maxReconnectAttempts: 3,
      reconnectDelay: 1000,
      timeout: 30000,
      withCredentials: false,
      ...options
    };

    this.autoReconnectEnabled = this.options.autoReconnect ?? true;
    
    return new Promise((resolve, reject) => {
      try {
        this.connectionStatus = 'connecting';
        this.clearTimers();

        // 创建EventSource连接
        const eventSourceInit: EventSourceInit = {
          withCredentials: this.options.withCredentials
        };

        this.eventSource = new EventSource(url, eventSourceInit);

        // 设置连接超时
        if (this.options.timeout) {
          this.connectionTimeout = setTimeout(() => {
            this.handleConnectionTimeout();
            reject(new Error('Connection timeout'));
          }, this.options.timeout);
        }

        // 监听连接事件
        this.eventSource.onopen = (event) => {
          this.clearTimers();
          this.connectionStatus = 'connected';
          this.reconnectAttempts = 0;
          this.lastError = null;
          this.emit('open', event);
          resolve();
        };

        this.eventSource.onmessage = (event) => {
          const sseEvent: SSEEvent = {
            type: 'message',
            data: event.data,
            id: event.lastEventId,
            timestamp: Date.now()
          };
          this.emit('message', sseEvent);
        };

        this.eventSource.onerror = (_event) => {
          this.handleConnectionError(new Error('EventSource error'));
          if (this.connectionStatus === 'connecting') {
            reject(new Error('Failed to establish connection'));
          }
        };

      } catch (error) {
        this.connectionStatus = 'error';
        this.lastError = error as Error;
        reject(error);
      }
    });
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.clearTimers();
    this.autoReconnectEnabled = false;
    
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    
    this.connectionStatus = 'disconnected';
    this.reconnectAttempts = 0;
    this.emit('disconnect', {});
  }

  /**
   * 手动重连
   */
  async reconnect(): Promise<void> {
    this.disconnect();
    await this.connect(this.url, this.options);
  }

  /**
   * 添加事件监听器
   */
  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, listener?: Function): void {
    if (!this.listeners.has(event)) {
      return;
    }

    if (!listener) {
      // 移除所有监听器
      this.listeners.delete(event);
      return;
    }

    const listeners = this.listeners.get(event)!;
    const index = listeners.indexOf(listener);
    if (index !== -1) {
      listeners.splice(index, 1);
    }

    if (listeners.length === 0) {
      this.listeners.delete(event);
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * 获取重连尝试次数
   */
  getReconnectAttempts(): number {
    return this.reconnectAttempts;
  }

  /**
   * 获取最后一次错误
   */
  getLastError(): Error | null {
    return this.lastError;
  }

  /**
   * 检查是否启用自动重连
   */
  isAutoReconnectEnabled(): boolean {
    return this.autoReconnectEnabled;
  }

  /**
   * 设置自动重连开关
   */
  setAutoReconnect(enabled: boolean): void {
    this.autoReconnectEnabled = enabled;
    if (!enabled) {
      this.clearTimers();
    }
  }

  /**
   * 处理连接错误
   */
  private handleConnectionError(error: Error): void {
    this.lastError = error;
    this.connectionStatus = 'error';
    this.emit('error', error);

    if (this.autoReconnectEnabled && 
        this.reconnectAttempts < (this.options.maxReconnectAttempts ?? 3)) {
      this.attemptReconnection();
    }
  }

  /**
   * 处理连接超时
   */
  private handleConnectionTimeout(): void {
    const error = new Error('Connection timeout');
    this.handleConnectionError(error);
  }

  /**
   * 尝试重连
   */
  private attemptReconnection(): void {
    this.reconnectAttempts++;
    this.connectionStatus = 'reconnecting';
    
    const delay = this.calculateReconnectDelay(this.reconnectAttempts);
    
    this.reconnectTimer = setTimeout(async () => {
      try {
        if (this.eventSource) {
          this.eventSource.close();
        }
        await this.connect(this.url, this.options);
      } catch (error) {
        this.handleConnectionError(error as Error);
      }
    }, delay);
  }

  /**
   * 计算重连延迟（指数退避）
   */
  private calculateReconnectDelay(attempt: number): number {
    const baseDelay = this.options.reconnectDelay ?? 1000;
    return Math.min(baseDelay * Math.pow(2, attempt - 1), 30000); // 最大30秒
  }

  /**
   * 清理定时器
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }

  /**
   * 发送事件
   */
  private emit(event: string, data: any): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      // 异步执行监听器，避免阻塞
      listeners.forEach(listener => {
        try {
          setTimeout(() => listener(data), 0);
        } catch (error) {
          console.error(`Error in SSE listener for event ${event}:`, error);
        }
      });
    }
  }
}

export default SSEHandler;