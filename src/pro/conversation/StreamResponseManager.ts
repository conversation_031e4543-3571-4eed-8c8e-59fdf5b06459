/**
 * StreamResponseManager.ts
 * 流式响应管理器实现
 * 
 * 统一管理多个SSE流连接，提供会话管理和事件分发功能
 */

import { SSEHandler, type SSEOptions, type SSEEvent } from './SSEHandler';

export interface StreamOptions {
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface StreamSession {
  id: string;
  url: string;
  status: 'connecting' | 'active' | 'paused' | 'completed' | 'error';
  startTime: number;
  lastUpdateTime: number;
  dataReceived: number;
  onData: (data: StreamData) => void;
  onError: (error: Error) => void;
  onComplete: () => void;
}

export interface StreamData {
  content: string;
  type: string;
  timestamp: number;
  id?: string;
  isComplete?: boolean;
}

/**
 * 流式响应管理器 - 管理多个SSE连接和会话
 */
export class StreamResponseManager {
  private sessions: Map<string, StreamSession> = new Map();
  private handlers: Map<string, SSEHandler> = new Map();
  private listeners: Map<string, Function[]> = new Map();
  private sessionCounter: number = 0;

  /**
   * 启动新的流会话
   */
  async startStream(url: string, options: StreamOptions = {}): Promise<StreamSession> {
    const sessionId = this.generateSessionId();
    const startTime = Date.now();

    // 创建会话对象
    const session: StreamSession = {
      id: sessionId,
      url,
      status: 'connecting',
      startTime,
      lastUpdateTime: startTime,
      dataReceived: 0,
      onData: () => {},
      onError: () => {},
      onComplete: () => {}
    };

    // 创建SSE处理器
    const handler = new SSEHandler();
    
    // 设置事件监听
    handler.on('open', () => {
      session.status = 'active';
      session.lastUpdateTime = Date.now();
      this.emit('sessionStarted', { sessionId, session });
    });

    handler.on('message', (event: SSEEvent) => {
      this.handleStreamData(sessionId, event);
    });

    handler.on('error', (error: Error) => {
      session.status = 'error';
      session.lastUpdateTime = Date.now();
      session.onError(error);
      this.emit('sessionError', { sessionId, error });
    });

    handler.on('disconnect', () => {
      session.status = 'completed';
      session.lastUpdateTime = Date.now();
      session.onComplete();
      this.emit('sessionCompleted', { sessionId });
    });

    try {
      // 存储会话和处理器
      this.sessions.set(sessionId, session);
      this.handlers.set(sessionId, handler);

      // 连接到SSE端点
      const sseOptions: SSEOptions = {
        autoReconnect: options.autoReconnect,
        maxReconnectAttempts: options.maxReconnectAttempts,
        reconnectDelay: options.reconnectDelay,
        timeout: options.timeout,
        headers: options.headers
      };

      await handler.connect(url, sseOptions);
      return session;

    } catch (error) {
      // 清理失败的会话
      this.sessions.delete(sessionId);
      this.handlers.delete(sessionId);
      throw error;
    }
  }

  /**
   * 停止流会话
   */
  async stopStream(sessionId: string): Promise<void> {
    const handler = this.handlers.get(sessionId);
    const session = this.sessions.get(sessionId);

    if (handler) {
      handler.disconnect();
      this.handlers.delete(sessionId);
    }

    if (session) {
      session.status = 'completed';
      session.lastUpdateTime = Date.now();
      this.sessions.delete(sessionId);
    }

    this.emit('sessionStopped', { sessionId });
  }

  /**
   * 暂停流会话
   */
  async pauseStream(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session && session.status === 'active') {
      session.status = 'paused';
      session.lastUpdateTime = Date.now();
      this.emit('sessionPaused', { sessionId });
    }
  }

  /**
   * 恢复流会话
   */
  async resumeStream(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session && session.status === 'paused') {
      session.status = 'active';
      session.lastUpdateTime = Date.now();
      this.emit('sessionResumed', { sessionId });
    }
  }

  /**
   * 获取活跃的流会话
   */
  getActiveStreams(): StreamSession[] {
    return Array.from(this.sessions.values())
      .filter(session => session.status === 'active' || session.status === 'connecting');
  }

  /**
   * 添加事件监听器
   */
  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, listener: Function): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
      if (listeners.length === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /**
   * 处理流数据
   */
  private handleStreamData(sessionId: string, event: SSEEvent): void {
    const session = this.sessions.get(sessionId);
    if (!session || session.status !== 'active') {
      return;
    }

    try {
      // 解析流数据
      const streamData: StreamData = this.parseStreamData(event);
      
      // 更新会话统计
      session.dataReceived += streamData.content.length;
      session.lastUpdateTime = Date.now();

      // 检查是否完成
      if (streamData.isComplete) {
        session.status = 'completed';
        session.onComplete();
        this.emit('sessionCompleted', { sessionId });
      } else {
        // 调用数据处理回调
        session.onData(streamData);
        this.emit('data', { sessionId, data: streamData });
      }

    } catch (error) {
      console.error(`Error processing stream data for session ${sessionId}:`, error);
      session.onError(error as Error);
      this.emit('sessionError', { sessionId, error });
    }
  }

  /**
   * 解析流数据
   */
  private parseStreamData(event: SSEEvent): StreamData {
    let content = event.data;
    let type = 'text';
    let isComplete = false;

    try {
      // 尝试解析JSON格式的数据
      const parsed = JSON.parse(content);
      if (parsed && typeof parsed === 'object') {
        content = parsed.content || parsed.data || content;
        type = parsed.type || type;
        isComplete = parsed.isComplete || parsed.done || false;
      }
    } catch {
      // 不是JSON格式，直接使用原始数据
    }

    return {
      content,
      type,
      timestamp: event.timestamp,
      id: event.id,
      isComplete
    };
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    this.sessionCounter++;
    return `stream-${Date.now()}-${this.sessionCounter}`;
  }

  /**
   * 发送事件
   */
  private emit(event: string, data: any): void {
    const listeners = this.listeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          setTimeout(() => listener(data), 0);
        } catch (error) {
          console.error(`Error in StreamResponseManager listener for event ${event}:`, error);
        }
      });
    }
  }

  /**
   * 清理所有会话
   */
  async cleanup(): Promise<void> {
    const sessionIds = Array.from(this.sessions.keys());
    await Promise.all(sessionIds.map(id => this.stopStream(id)));
    this.listeners.clear();
  }
}

export default StreamResponseManager;