/**
 * StreamUIUpdater.ts
 * 流式UI更新器实现
 * 
 * 负责将流式数据实时更新到用户界面，支持自动滚动和性能优化
 */

import type { StreamData } from './StreamResponseManager';

export interface UpdateStats {
  updatesCount: number;
  lastUpdate: number;
}

/**
 * 流式UI更新器 - 管理流式内容的界面更新
 */
export class StreamUIUpdater {
  private container: HTMLElement | null = null;
  private autoScrollEnabled: boolean = true;
  private userInteractionPreserved: boolean = true;
  private updateStats: UpdateStats = {
    updatesCount: 0,
    lastUpdate: 0
  };
  private pendingUpdates: StreamData[] = [];
  private updateTimer: NodeJS.Timeout | null = null;
  private readonly UPDATE_INTERVAL = 16; // 60fps
  private scrollListener: (() => void) | null = null;
  private userScrollDetected: boolean = false;

  /**
   * 设置容器元素
   */
  setContainer(element: HTMLElement): void {
    if (this.container && this.scrollListener) {
      this.container.removeEventListener('scroll', this.scrollListener);
    }

    this.container = element;
    this.setupScrollListener();
  }

  /**
   * 更新内容
   */
  updateContent(data: StreamData): void {
    if (!this.container) {
      throw new Error('Container not set. Call setContainer() first.');
    }

    // 添加到待处理队列
    this.pendingUpdates.push(data);

    // 立即更新统计计数器
    this.updateStats.updatesCount++;
    this.updateStats.lastUpdate = Date.now();

    // 如果没有正在进行的更新，立即处理
    if (!this.updateTimer) {
      this.scheduleUpdate();
    }
  }

  /**
   * 清理内容
   */
  clearContent(): void {
    if (!this.container) {
      throw new Error('Container not set. Call setContainer() first.');
    }

    this.container.innerHTML = '';
    this.pendingUpdates = [];
    this.updateStats.updatesCount = 0;
    this.userScrollDetected = false;
  }

  /**
   * 启用/禁用自动滚动
   */
  enableAutoScroll(enabled: boolean): void {
    this.autoScrollEnabled = enabled;
    if (enabled) {
      this.userScrollDetected = false;
    }
  }

  /**
   * 启用/禁用用户交互保护
   */
  preserveUserInteraction(enabled: boolean): void {
    this.userInteractionPreserved = enabled;
  }

  /**
   * 获取更新统计
   */
  getUpdateStats(): UpdateStats {
    return { ...this.updateStats };
  }

  /**
   * 安排更新
   */
  private scheduleUpdate(): void {
    this.updateTimer = setTimeout(() => {
      this.processPendingUpdates();
      this.updateTimer = null;

      // 如果还有待处理的更新，继续调度
      if (this.pendingUpdates.length > 0) {
        this.scheduleUpdate();
      }
    }, this.UPDATE_INTERVAL);
  }

  /**
   * 处理待处理的更新
   */
  private processPendingUpdates(): void {
    if (!this.container || this.pendingUpdates.length === 0) {
      return;
    }

    const updates = this.pendingUpdates.splice(0); // 取出所有待处理更新
    let contentChanged = false;

    try {
      // 保存用户交互状态
      const activeElement = document.activeElement;
      const selection = this.userInteractionPreserved ? this.saveSelection() : null;
      const scrollInfo = this.saveScrollInfo();

      // 批量更新内容
      for (const data of updates) {
        this.applyContentUpdate(data);
        contentChanged = true;
      }

      // 恢复用户交互状态
      if (this.userInteractionPreserved) {
        this.restoreSelection(selection);
        if (activeElement && document.contains(activeElement)) {
          (activeElement as HTMLElement).focus();
        }
      }

      // 处理滚动
      if (contentChanged) {
        this.handleAutoScroll(scrollInfo);
      }

      // 统计已在updateContent中更新，这里不需要重复计算

    } catch (error) {
      console.error('Error processing pending updates:', error);
    }
  }

  /**
   * 应用内容更新
   */
  private applyContentUpdate(data: StreamData): void {
    if (!this.container) return;

    // 根据数据类型选择不同的更新策略
    switch (data.type) {
      case 'text':
        this.appendTextContent(data.content);
        break;
      case 'markdown':
        this.appendMarkdownContent(data.content);
        break;
      case 'code':
        this.appendCodeContent(data.content);
        break;
      default:
        this.appendTextContent(data.content);
    }
  }

  /**
   * 追加文本内容
   */
  private appendTextContent(content: string): void {
    if (!this.container) return;

    const textNode = document.createTextNode(content);
    this.container.appendChild(textNode);
  }

  /**
   * 追加Markdown内容
   */
  private appendMarkdownContent(content: string): void {
    if (!this.container) return;

    // 简单的Markdown渲染（实际项目中可能需要更完整的Markdown解析器）
    const div = document.createElement('div');
    div.className = 'markdown-content';
    
    // 基础Markdown处理
    const html = content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
      .replace(/\n/g, '<br>');
    
    div.innerHTML = html;
    this.container.appendChild(div);
  }

  /**
   * 追加代码内容
   */
  private appendCodeContent(content: string): void {
    if (!this.container) return;

    const pre = document.createElement('pre');
    const code = document.createElement('code');
    code.textContent = content;
    pre.appendChild(code);
    pre.className = 'code-content';
    this.container.appendChild(pre);
  }

  /**
   * 设置滚动监听器
   */
  private setupScrollListener(): void {
    if (!this.container) return;

    this.scrollListener = () => {
      if (this.autoScrollEnabled && !this.userScrollDetected) {
        // 检测用户是否手动滚动
        const { scrollTop, scrollHeight, clientHeight } = this.container!;
        const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
        
        if (!isAtBottom) {
          this.userScrollDetected = true;
        }
      }
    };

    this.container.addEventListener('scroll', this.scrollListener, { passive: true });
  }

  /**
   * 处理自动滚动
   */
  private handleAutoScroll(previousScrollInfo: { scrollTop: number; scrollHeight: number }): void {
    if (!this.container || !this.autoScrollEnabled || this.userScrollDetected) {
      return;
    }

    // 只有在内容增加时才自动滚动
    if (this.container.scrollHeight > previousScrollInfo.scrollHeight) {
      this.container.scrollTop = this.container.scrollHeight;
    }
  }

  /**
   * 保存滚动信息
   */
  private saveScrollInfo(): { scrollTop: number; scrollHeight: number } {
    if (!this.container) {
      return { scrollTop: 0, scrollHeight: 0 };
    }

    return {
      scrollTop: this.container.scrollTop,
      scrollHeight: this.container.scrollHeight
    };
  }

  /**
   * 保存文本选择状态
   */
  private saveSelection(): Range | null {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      return selection.getRangeAt(0).cloneRange();
    }
    return null;
  }

  /**
   * 恢复文本选择状态
   */
  private restoreSelection(range: Range | null): void {
    if (!range) return;

    try {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } catch (error) {
      // 选择恢复失败，忽略错误
      console.debug('Failed to restore selection:', error);
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.updateTimer) {
      clearTimeout(this.updateTimer);
      this.updateTimer = null;
    }

    if (this.container && this.scrollListener) {
      this.container.removeEventListener('scroll', this.scrollListener);
    }

    this.pendingUpdates = [];
    this.container = null;
    this.scrollListener = null;
  }
}

export default StreamUIUpdater;