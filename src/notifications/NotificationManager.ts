/**
 * @fileoverview Chrome Notification Manager for Configuration Feedback
 * Provides user feedback through Chrome notifications for configuration actions
 * 
 * <AUTHOR> 3 - Configuration UX Team
 * @version 1.0.0
 */

/**
 * Enum for notification types
 */
export const NotificationType = {
  SUCCESS: 'success' as const,
  ERROR: 'error' as const,
  INFO: 'info' as const
} as const;

export type NotificationType = typeof NotificationType[keyof typeof NotificationType];

/**
 * Interface for notification options
 */
interface NotificationOptions {
  title: string;
  message: string;
  type?: NotificationType;
  autoDismiss?: boolean;
  dismissAfter?: number;
}

/**
 * Interface for Chrome notification create options
 */
interface ChromeNotificationOptions {
  type: 'basic' | 'image' | 'list' | 'progress';
  iconUrl: string;
  title: string;
  message: string;
  priority?: number;
  requireInteraction?: boolean;
}

/**
 * NotificationManager class for handling Chrome notifications
 * 
 * Provides a unified interface for showing configuration-related notifications
 * with proper positioning, auto-dismiss behavior, and error handling.
 * 
 * Features:
 * - Success notifications (auto-dismiss after 3s)
 * - Error notifications (manual dismiss required)
 * - Info notifications (auto-dismiss after 4s)
 * - Right-side positioning (Chrome default)
 * - Proper cleanup and memory management
 * 
 * @example
 * ```typescript
 * const notificationManager = NotificationManager.getInstance();
 * 
 * // Show success notification
 * await notificationManager.showSuccess(
 *   'Configuration Saved',
 *   'Your API settings have been saved successfully'
 * );
 * 
 * // Show error notification
 * await notificationManager.showError(
 *   'Save Failed',
 *   'Unable to save configuration. Please try again.'
 * );
 * ```
 */
export class NotificationManager {
  private static instance: NotificationManager;
  private activeNotifications: Set<string> = new Set();
  private autoTimers: Map<string, NodeJS.Timeout> = new Map();
  private isInitialized: boolean = false;

  /**
   * Private constructor for singleton pattern
   */
  private constructor() {
    this.initialize();
  }

  /**
   * Get singleton instance of NotificationManager
   * 
   * @returns The singleton NotificationManager instance
   */
  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  /**
   * Initialize the notification manager
   * Sets up Chrome API listeners and event handlers
   */
  private initialize(): void {
    if (this.isInitialized) {
      return;
    }

    try {
      // Check if Chrome API is available
      if (typeof chrome !== 'undefined' && chrome.notifications) {
        // Listen for notification clicks
        chrome.notifications.onClicked.addListener((notificationId) => {
          this.handleNotificationClick(notificationId);
        });

        // Listen for notification close events
        chrome.notifications.onClosed.addListener((notificationId, byUser) => {
          this.handleNotificationClose(notificationId, byUser);
        });

        this.isInitialized = true;
      }
    } catch (error) {
      console.warn('Chrome notifications API not available:', error);
    }
  }

  /**
   * Show a success notification
   * Auto-dismisses after 3 seconds
   * 
   * @param title - Notification title
   * @param message - Notification message
   * @returns Promise that resolves when notification is created
   * 
   * @example
   * ```typescript
   * await notificationManager.showSuccess(
   *   'Settings Saved',
   *   'Your configuration has been saved successfully'
   * );
   * ```
   */
  public async showSuccess(title: string, message: string): Promise<void> {
    await this.createNotification({
      title,
      message,
      type: NotificationType.SUCCESS,
      autoDismiss: true,
      dismissAfter: 3000
    });
  }

  /**
   * Show an error notification
   * Requires manual dismissal (does not auto-dismiss)
   * 
   * @param title - Notification title
   * @param message - Notification message
   * @returns Promise that resolves when notification is created
   * 
   * @example
   * ```typescript
   * await notificationManager.showError(
   *   'Validation Failed',
   *   'The API key format is invalid. Please check and try again.'
   * );
   * ```
   */
  public async showError(title: string, message: string): Promise<void> {
    await this.createNotification({
      title,
      message,
      type: NotificationType.ERROR,
      autoDismiss: false
    });
  }

  /**
   * Show an info notification
   * Auto-dismisses after 4 seconds
   * 
   * @param title - Notification title
   * @param message - Notification message
   * @returns Promise that resolves when notification is created
   * 
   * @example
   * ```typescript
   * await notificationManager.showInfo(
   *   'Import Started',
   *   'Configuration import is in progress...'
   * );
   * ```
   */
  public async showInfo(title: string, message: string): Promise<void> {
    await this.createNotification({
      title,
      message,
      type: NotificationType.INFO,
      autoDismiss: true,
      dismissAfter: 4000
    });
  }

  /**
   * Manually clear a specific notification
   * 
   * @param notificationId - ID of the notification to clear
   * @returns Promise that resolves when notification is cleared
   */
  public async clearNotification(notificationId: string): Promise<void> {
    try {
      if (typeof chrome !== 'undefined' && chrome.notifications) {
        await chrome.notifications.clear(notificationId);
        this.cleanupNotification(notificationId);
      }
    } catch (error) {
      console.warn(`Failed to clear notification ${notificationId}:`, error);
    }
  }

  /**
   * Clear all active notifications
   * 
   * @returns Promise that resolves when all notifications are cleared
   */
  public async clearAllNotifications(): Promise<void> {
    const clearPromises = Array.from(this.activeNotifications).map(id => 
      this.clearNotification(id)
    );
    
    await Promise.allSettled(clearPromises);
  }

  /**
   * Create a Chrome notification with specified options
   * 
   * @param options - Notification configuration options
   * @returns Promise that resolves when notification is created
   */
  private async createNotification(options: NotificationOptions): Promise<void> {
    try {
      if (typeof chrome === 'undefined' || !chrome.notifications) {
        console.warn('Chrome notifications API not available');
        return;
      }

      const notificationId = this.generateNotificationId(options.type || NotificationType.INFO);
      const chromeOptions = this.buildChromeNotificationOptions(options);

      await new Promise<void>((resolve, reject) => {
        chrome.notifications.create(notificationId, chromeOptions, (_id) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve();
        });
      });

      // Track active notification
      this.activeNotifications.add(notificationId);

      // Set up auto-dismiss if enabled
      if (options.autoDismiss && options.dismissAfter) {
        this.setupAutoDismiss(notificationId, options.dismissAfter);
      }

    } catch (error) {
      console.warn('Failed to create notification:', error);
    }
  }

  /**
   * Build Chrome notification options from our options
   * 
   * @param options - Our notification options
   * @returns Chrome-compatible notification options
   */
  private buildChromeNotificationOptions(options: NotificationOptions): ChromeNotificationOptions {
    const baseOptions: ChromeNotificationOptions = {
      type: 'basic',
      iconUrl: this.getIconUrl(options.type || NotificationType.INFO),
      title: options.title,
      message: options.message
    };

    // Set priority and interaction requirements based on type
    switch (options.type) {
      case NotificationType.SUCCESS:
        baseOptions.priority = 1;
        break;
      case NotificationType.ERROR:
        baseOptions.priority = 2;
        baseOptions.requireInteraction = true; // Requires manual dismissal
        break;
      case NotificationType.INFO:
      default:
        baseOptions.priority = 0;
        break;
    }

    return baseOptions;
  }

  /**
   * Generate a unique notification ID
   * 
   * @param type - Notification type
   * @returns Unique notification ID
   */
  private generateNotificationId(type: NotificationType): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `recall-${type}-${timestamp}-${random}`;
  }

  /**
   * Get appropriate icon URL for notification type
   * 
   * @param type - Notification type
   * @returns Icon URL path
   */
  private getIconUrl(type: NotificationType): string {
    const iconPaths = {
      [NotificationType.SUCCESS]: '/icons/notification-success.png',
      [NotificationType.ERROR]: '/icons/notification-error.png',
      [NotificationType.INFO]: '/icons/notification-info.png'
    };

    return iconPaths[type] || '/icons/icon-48.png'; // Fallback to main icon
  }

  /**
   * Set up auto-dismiss timer for notification
   * 
   * @param notificationId - ID of the notification
   * @param delay - Delay in milliseconds before auto-dismiss
   */
  private setupAutoDismiss(notificationId: string, delay: number): void {
    const timer = setTimeout(async () => {
      await this.clearNotification(notificationId);
    }, delay);

    this.autoTimers.set(notificationId, timer);
  }

  /**
   * Handle notification click events
   * 
   * @param notificationId - ID of the clicked notification
   */
  private handleNotificationClick(notificationId: string): void {
    // Auto-clear notification when clicked
    this.clearNotification(notificationId);
  }

  /**
   * Handle notification close events
   * 
   * @param notificationId - ID of the closed notification
   * @param byUser - Whether the notification was closed by user action
   */
  private handleNotificationClose(notificationId: string, _byUser: boolean): void {
    this.cleanupNotification(notificationId);
  }

  /**
   * Clean up notification tracking and timers
   * 
   * @param notificationId - ID of the notification to clean up
   */
  private cleanupNotification(notificationId: string): void {
    // Remove from active notifications
    this.activeNotifications.delete(notificationId);

    // Clear auto-dismiss timer if exists
    const timer = this.autoTimers.get(notificationId);
    if (timer) {
      clearTimeout(timer);
      this.autoTimers.delete(notificationId);
    }
  }

  /**
   * Get count of active notifications
   * 
   * @returns Number of active notifications
   */
  public getActiveNotificationCount(): number {
    return this.activeNotifications.size;
  }

  /**
   * Check if notification manager is properly initialized
   * 
   * @returns Whether the manager is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && typeof chrome !== 'undefined' && !!chrome.notifications;
  }
}

/**
 * Default export for convenience
 */
export default NotificationManager;