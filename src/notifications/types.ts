/**
 * Notification System Types
 * 
 * Type definitions for the Chrome Extension notification system:
 * - Notification configuration and options
 * - Queue management types
 * - Event handler types
 * - API validation notification types
 * 
 * @module notification-types
 * @version 4.0
 * @since 2025-06-24
 */

export type NotificationType = 'info' | 'success' | 'warning' | 'error';
export type NotificationPriority = 'low' | 'medium' | 'high';

export interface NotificationConfig {
  enabled?: boolean;
  maxConcurrent?: number;
  defaultTimeout?: number;
  soundEnabled?: boolean;
  persistentNotifications?: boolean;
  showInQuietHours?: boolean;
  quietHoursStart?: string;
  quietHoursEnd?: string;
}

export interface NotificationAction {
  id: string;
  title: string;
  iconUrl?: string;
}

export interface NotificationData {
  title: string;
  message: string;
  type: NotificationType;
  iconUrl?: string;
  timeout?: number;
  persistent?: boolean;
  priority?: NotificationPriority;
  groupId?: string;
  batchId?: string;
  actions?: NotificationAction[];
  onClick?: (notificationId: string) => void;
  onAction?: (actionId: string, notificationId: string) => void;
  onClose?: (notificationId: string, byUser: boolean) => void;
  retryOnFailure?: boolean;
}

export interface NotificationResult {
  success: boolean;
  notificationId?: string;
  error?: string;
  queued?: boolean;
  fallbackUsed?: boolean;
}

export interface QueueConfig {
  maxSize?: number;
  priorityLevels?: NotificationPriority[];
  processingDelay?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

export interface QueuedNotification extends NotificationData {
  id: string;
  timestamp: number;
  retryCount: number;
}

export interface QueueResult {
  success: boolean;
  error?: string;
  position?: number;
}

export interface NotificationStatistics {
  totalShown: number;
  totalQueued: number;
  totalFailed: number;
  currentActive: number;
  byType: Record<NotificationType, number>;
  byPriority: Record<NotificationPriority, number>;
  averageDisplayTime: number;
}

// API Validation specific types
export interface ApiKeyValidationNotification {
  provider: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  batchId?: string;
  retry?: boolean;
  details?: {
    keyFormat?: boolean;
    connectivity?: boolean;
    permissions?: string[];
    usage?: {
      used: number;
      limit: number;
    };
  };
}

export interface ConfigurationSavedNotification {
  fields: string[];
  provider?: string;
  errors?: string[];
  warnings?: string[];
}

// Search operation specific types
export interface SearchDegradedNotification {
  reason: string;
  fallbackMode: string;
  duration?: number;
  affectedFeatures?: string[];
  retryAction?: () => void;
}

export interface SearchPerformanceWarning {
  searchTime: number;
  threshold: number;
  suggestion: string;
  details?: {
    indexSize?: number;
    queryComplexity?: string;
    resourceUsage?: {
      cpu: number;
      memory: number;
    };
  };
}

export interface IndexingProgressNotification {
  processed: number;
  total: number;
  currentPage?: string;
  estimatedRemaining?: number;
  errors?: number;
  rate?: number; // pages per second
}

// Permission management types
export interface PermissionResult {
  granted: boolean;
  fallbackMode?: boolean;
  error?: string;
}

// Event handler types
export type NotificationClickHandler = (notificationId: string) => void;
export type NotificationActionHandler = (actionId: string, notificationId: string) => void;
export type NotificationCloseHandler = (notificationId: string, byUser: boolean) => void;
export type NotificationErrorHandler = (error: Error, notification: NotificationData) => void;

// Chrome API extension types
export interface ChromeNotificationOptions {
  type: 'basic' | 'image' | 'list' | 'progress';
  iconUrl: string;
  title: string;
  message: string;
  contextMessage?: string;
  priority?: 0 | 1 | 2;
  eventTime?: number;
  buttons?: Array<{ title: string; iconUrl?: string }>;
  imageUrl?: string;
  items?: Array<{ title: string; message: string }>;
  progress?: number;
  isClickable?: boolean;
  requireInteraction?: boolean;
  silent?: boolean;
}

export interface NotificationGroupConfig {
  id: string;
  title: string;
  maxNotifications: number;
  mergeStrategy: 'replace' | 'append' | 'count';
  displayTemplate?: string;
}

export interface NotificationBatch {
  id: string;
  notifications: NotificationData[];
  startTime: number;
  timeout: number;
  onComplete?: (results: NotificationResult[]) => void;
}

// Fallback notification types (for when Chrome API is unavailable)
export interface FallbackNotificationConfig {
  enabled: boolean;
  useConsole: boolean;
  useToast: boolean;
  toastDuration: number;
  toastPosition: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
}

export interface ToastNotification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  duration: number;
  position: string;
  actions?: NotificationAction[];
}