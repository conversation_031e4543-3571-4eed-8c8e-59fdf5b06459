/**
 * Chrome Extension Notification Queue
 * 
 * Manages a priority queue for notifications with:
 * - Priority-based ordering (high, medium, low)
 * - Automatic processing with configurable delays
 * - Duplicate detection and merging
 * - Error handling and retry logic
 * - Size limits and overflow protection
 * 
 * @module NotificationQueue
 * @version 4.0
 * @since 2025-06-24
 */

import type {
  NotificationData,
  NotificationResult,
  QueueConfig,
  QueuedNotification,
  QueueResult,
  NotificationPriority,
  NotificationErrorHandler
} from './types';

interface QueueProcessor {
  (notification: NotificationData): Promise<NotificationResult>;
}

/**
 * Priority-based notification queue
 */
export class NotificationQueue {
  private config: Required<QueueConfig>;
  private queue = new Map<NotificationPriority, QueuedNotification[]>();
  private processing = false;
  private processor: QueueProcessor | null = null;
  private errorHandler: NotificationErrorHandler | null = null;
  private processingTimer: NodeJS.Timeout | null = null;
  private idCounter = 0;
  private duplicateMap = new Map<string, string>(); // groupId -> notificationId mapping

  constructor(config: QueueConfig = {}) {
    this.config = {
      maxSize: 50,
      priorityLevels: ['high', 'medium', 'low'],
      processingDelay: 100,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    };

    // Initialize priority queues
    this.config.priorityLevels.forEach(priority => {
      this.queue.set(priority as NotificationPriority, []);
    });
  }

  /**
   * Add notification to queue
   */
  enqueue(notification: NotificationData): QueueResult {
    // Check size limit
    if (this.getTotalSize() >= this.config.maxSize) {
      return {
        success: false,
        error: 'Queue is full'
      };
    }

    // Handle duplicates
    if (notification.groupId) {
      const existingId = this.duplicateMap.get(notification.groupId);
      if (existingId) {
        // Remove existing notification
        this.removeById(existingId);
      }
    }

    // Create queued notification
    const queuedNotification: QueuedNotification = {
      ...notification,
      id: this.generateId(),
      timestamp: Date.now(),
      retryCount: 0,
      priority: notification.priority || 'medium'
    };

    // Add to appropriate priority queue
    const priority = queuedNotification.priority || 'medium';
    const priorityQueue = this.queue.get(priority);
    if (!priorityQueue) {
      return {
        success: false,
        error: `Invalid priority: ${priority}`
      };
    }

    priorityQueue.push(queuedNotification);

    // Track duplicates
    if (notification.groupId) {
      this.duplicateMap.set(notification.groupId, queuedNotification.id);
    }

    // Sort by timestamp (older first within same priority)
    priorityQueue.sort((a, b) => a.timestamp - b.timestamp);

    // Start processing if not already running
    if (!this.processing && this.processor) {
      this.startProcessing();
    }

    return {
      success: true,
      position: this.getPositionInQueue(queuedNotification.id)
    };
  }

  /**
   * Remove and return next notification from queue
   */
  dequeue(): NotificationData | null {
    // Check high priority first, then medium, then low
    for (const priority of this.config.priorityLevels) {
      const priorityQueue = this.queue.get(priority as NotificationPriority);
      if (priorityQueue && priorityQueue.length > 0) {
        const notification = priorityQueue.shift()!;
        
        // Clean up duplicate tracking
        if (notification.groupId) {
          this.duplicateMap.delete(notification.groupId);
        }

        return notification;
      }
    }

    return null;
  }

  /**
   * Set notification processor function
   */
  setProcessor(processor: QueueProcessor): void {
    this.processor = processor;
  }

  /**
   * Set error handler
   */
  onError(handler: NotificationErrorHandler): void {
    this.errorHandler = handler;
  }

  /**
   * Start automatic queue processing
   */
  startProcessing(): void {
    if (this.processing || !this.processor) {
      return;
    }

    this.processing = true;
    this.processNext();
  }

  /**
   * Stop automatic queue processing
   */
  stopProcessing(): void {
    this.processing = false;
    if (this.processingTimer) {
      clearTimeout(this.processingTimer);
      this.processingTimer = null;
    }
  }

  /**
   * Get queue size
   */
  size(): number {
    return this.getTotalSize();
  }

  /**
   * Check if queue is empty
   */
  isEmpty(): boolean {
    return this.getTotalSize() === 0;
  }

  /**
   * Clear all notifications from queue
   */
  clear(): void {
    for (const priorityQueue of this.queue.values()) {
      priorityQueue.length = 0;
    }
    this.duplicateMap.clear();
    this.stopProcessing();
  }

  /**
   * Get queue statistics
   */
  getStatistics(): {
    total: number;
    byPriority: Record<NotificationPriority, number>;
    oldestTimestamp: number | null;
    duplicatesTracked: number;
  } {
    const stats = {
      total: 0,
      byPriority: {} as Record<NotificationPriority, number>,
      oldestTimestamp: null as number | null,
      duplicatesTracked: this.duplicateMap.size
    };

    let oldestTime = Infinity;

    for (const [priority, priorityQueue] of this.queue) {
      const count = priorityQueue.length;
      stats.byPriority[priority] = count;
      stats.total += count;

      // Find oldest notification
      if (count > 0) {
        const oldest = Math.min(...priorityQueue.map(n => n.timestamp));
        if (oldest < oldestTime) {
          oldestTime = oldest;
        }
      }
    }

    stats.oldestTimestamp = oldestTime === Infinity ? null : oldestTime;

    return stats;
  }

  /**
   * Remove notifications older than specified age
   */
  removeExpired(maxAge: number): number {
    const cutoff = Date.now() - maxAge;
    let removedCount = 0;

    for (const priorityQueue of this.queue.values()) {
      const originalLength = priorityQueue.length;
      
      // Remove expired notifications
      for (let i = priorityQueue.length - 1; i >= 0; i--) {
        if (priorityQueue[i].timestamp < cutoff) {
          const removed = priorityQueue.splice(i, 1)[0];
          
          // Clean up duplicate tracking
          if (removed.groupId) {
            this.duplicateMap.delete(removed.groupId);
          }
        }
      }

      removedCount += originalLength - priorityQueue.length;
    }

    return removedCount;
  }

  /**
   * Get notifications by priority
   */
  getByPriority(priority: NotificationPriority): QueuedNotification[] {
    const priorityQueue = this.queue.get(priority);
    return priorityQueue ? [...priorityQueue] : [];
  }

  /**
   * Find notification by ID
   */
  findById(id: string): QueuedNotification | null {
    for (const priorityQueue of this.queue.values()) {
      const found = priorityQueue.find(n => n.id === id);
      if (found) {
        return found;
      }
    }
    return null;
  }

  /**
   * Remove notification by ID
   */
  removeById(id: string): boolean {
    for (const priorityQueue of this.queue.values()) {
      const index = priorityQueue.findIndex(n => n.id === id);
      if (index >= 0) {
        const removed = priorityQueue.splice(index, 1)[0];
        
        // Clean up duplicate tracking
        if (removed.groupId) {
          this.duplicateMap.delete(removed.groupId);
        }
        
        return true;
      }
    }
    return false;
  }

  /**
   * Process next notification in queue
   */
  private async processNext(): Promise<void> {
    if (!this.processing || !this.processor) {
      return;
    }

    const notification = this.dequeue();
    if (!notification) {
      // No notifications to process, schedule next check
      this.scheduleNextProcess();
      return;
    }

    try {
      await this.processor(notification);
      // Success, process next immediately
      this.scheduleNextProcess(0);
    } catch (error) {
      // Convert NotificationData back to QueuedNotification for retry logic
      const queuedNotification = notification as QueuedNotification;
      if (!queuedNotification.id) queuedNotification.id = this.generateId();
      if (!queuedNotification.timestamp) queuedNotification.timestamp = Date.now();
      if (!queuedNotification.retryCount) queuedNotification.retryCount = 0;
      
      await this.handleProcessingError(error as Error, queuedNotification);
    }
  }

  /**
   * Handle processing error with retry logic
   */
  private async handleProcessingError(error: Error, notification: QueuedNotification): Promise<void> {
    // Increment retry count
    notification.retryCount++;

    if (notification.retryCount < this.config.retryAttempts) {
      // Re-queue for retry
      const priority = notification.priority || 'medium';
      const priorityQueue = this.queue.get(priority);
      if (priorityQueue) {
        // Add back to front of queue for priority
        priorityQueue.unshift(notification);
      }

      // Schedule retry with delay
      this.scheduleNextProcess(this.config.retryDelay);
    } else {
      // Max retries reached, call error handler
      if (this.errorHandler) {
        this.errorHandler(error, notification);
      }

      // Continue processing
      this.scheduleNextProcess();
    }
  }

  /**
   * Schedule next processing cycle
   */
  private scheduleNextProcess(delay?: number): void {
    if (!this.processing) {
      return;
    }

    const actualDelay = delay !== undefined ? delay : this.config.processingDelay;

    this.processingTimer = setTimeout(() => {
      this.processNext();
    }, actualDelay);
  }

  /**
   * Get total size across all priority queues
   */
  private getTotalSize(): number {
    let total = 0;
    for (const priorityQueue of this.queue.values()) {
      total += priorityQueue.length;
    }
    return total;
  }

  /**
   * Get position of notification in queue
   */
  private getPositionInQueue(id: string): number {
    let position = 1;

    // Check high priority notifications first
    for (const priority of this.config.priorityLevels) {
      const priorityQueue = this.queue.get(priority as NotificationPriority);
      if (!priorityQueue) continue;

      for (const notification of priorityQueue) {
        if (notification.id === id) {
          return position;
        }
        position++;
      }
    }

    return -1; // Not found
  }

  /**
   * Generate unique notification ID
   */
  private generateId(): string {
    return `queue_${++this.idCounter}_${Date.now()}`;
  }
}