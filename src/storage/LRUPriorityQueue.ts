/**
 * Priority Queue for LRU Cache Management
 * 
 * Efficiently maintains pages sorted by last access time for fast LRU cleanup
 */

import type { LRUCacheEntry } from './types';

/**
 * Min heap priority queue for LRU entries
 * Entries with older lastAccessed times have higher priority
 */
export class LRUPriorityQueue {
  private heap: LRUCacheEntry[] = [];
  private indexMap = new Map<string, number>(); // pageId -> heap index
  
  /**
   * Get the size of the queue
   */
  get size(): number {
    return this.heap.length;
  }

  /**
   * Check if queue is empty
   */
  isEmpty(): boolean {
    return this.heap.length === 0;
  }

  /**
   * Add or update an entry in the queue
   */
  upsert(entry: LRUCacheEntry): void {
    const existingIndex = this.indexMap.get(entry.pageId);
    
    if (existingIndex !== undefined) {
      // Update existing entry
      this.heap[existingIndex] = entry;
      // Re-heapify from this position
      this.bubbleUp(existingIndex);
      this.bubbleDown(existingIndex);
    } else {
      // Add new entry
      const index = this.heap.length;
      this.heap.push(entry);
      this.indexMap.set(entry.pageId, index);
      this.bubbleUp(index);
    }
  }

  /**
   * Get the oldest entry without removing it
   */
  peek(): LRUCacheEntry | undefined {
    return this.heap[0];
  }

  /**
   * Remove and return the oldest entry
   */
  extractMin(): LRUCacheEntry | undefined {
    if (this.heap.length === 0) {
      return undefined;
    }

    const min = this.heap[0];
    const last = this.heap.pop()!;
    
    if (this.heap.length > 0) {
      this.heap[0] = last;
      this.indexMap.set(last.pageId, 0);
      this.bubbleDown(0);
    }
    
    this.indexMap.delete(min.pageId);
    return min;
  }

  /**
   * Remove a specific entry by pageId
   */
  remove(pageId: string): boolean {
    const index = this.indexMap.get(pageId);
    if (index === undefined) {
      return false;
    }

    const last = this.heap.pop()!;
    
    if (index < this.heap.length) {
      this.heap[index] = last;
      this.indexMap.set(last.pageId, index);
      this.bubbleUp(index);
      this.bubbleDown(index);
    }
    
    this.indexMap.delete(pageId);
    return true;
  }

  /**
   * Get unprotected candidates up to target size
   */
  getUnprotectedCandidates(targetSize: number): LRUCacheEntry[] {
    const candidates: LRUCacheEntry[] = [];
    let accumulatedSize = 0;
    
    // Create a copy to avoid modifying the original heap
    const tempHeap = [...this.heap];
    tempHeap.sort((a, b) => a.lastAccessed - b.lastAccessed);
    
    for (const entry of tempHeap) {
      if (entry.isProtected) {
        continue;
      }
      
      candidates.push(entry);
      accumulatedSize += entry.storageSize;
      
      if (accumulatedSize >= targetSize) {
        break;
      }
    }
    
    return candidates;
  }

  /**
   * Clear the queue
   */
  clear(): void {
    this.heap = [];
    this.indexMap.clear();
  }

  /**
   * Build heap from array of entries
   */
  buildHeap(entries: LRUCacheEntry[]): void {
    this.clear();
    this.heap = [...entries];
    
    // Build index map
    for (let i = 0; i < this.heap.length; i++) {
      this.indexMap.set(this.heap[i].pageId, i);
    }
    
    // Heapify from bottom up
    for (let i = Math.floor(this.heap.length / 2) - 1; i >= 0; i--) {
      this.bubbleDown(i);
    }
  }

  /**
   * Get all entries as array (for debugging/testing)
   */
  toArray(): LRUCacheEntry[] {
    return [...this.heap];
  }

  // Private helper methods

  private bubbleUp(index: number): void {
    while (index > 0) {
      const parentIndex = Math.floor((index - 1) / 2);
      
      if (this.compare(this.heap[index], this.heap[parentIndex]) >= 0) {
        break;
      }
      
      this.swap(index, parentIndex);
      index = parentIndex;
    }
  }

  private bubbleDown(index: number): void {
    while (true) {
      let minIndex = index;
      const leftChild = 2 * index + 1;
      const rightChild = 2 * index + 2;
      
      if (leftChild < this.heap.length && 
          this.compare(this.heap[leftChild], this.heap[minIndex]) < 0) {
        minIndex = leftChild;
      }
      
      if (rightChild < this.heap.length && 
          this.compare(this.heap[rightChild], this.heap[minIndex]) < 0) {
        minIndex = rightChild;
      }
      
      if (minIndex === index) {
        break;
      }
      
      this.swap(index, minIndex);
      index = minIndex;
    }
  }

  private swap(i: number, j: number): void {
    const temp = this.heap[i];
    this.heap[i] = this.heap[j];
    this.heap[j] = temp;
    
    // Update index map
    this.indexMap.set(this.heap[i].pageId, i);
    this.indexMap.set(this.heap[j].pageId, j);
  }

  private compare(a: LRUCacheEntry, b: LRUCacheEntry): number {
    // Protected entries should be considered "newer" (lower priority for deletion)
    if (a.isProtected !== b.isProtected) {
      return a.isProtected ? 1 : -1;
    }
    
    // Otherwise compare by last accessed time (older = higher priority)
    return a.lastAccessed - b.lastAccessed;
  }
}

/**
 * Incremental LRU Cleanup Manager
 * 
 * Performs cleanup in small increments to avoid blocking the main thread
 */
export class IncrementalLRUCleanup {
  private queue: LRUPriorityQueue;
  private cleanupInProgress = false;
  private cleanupBudgetMs = 5; // Max time per cleanup iteration
  
  constructor(queue: LRUPriorityQueue) {
    this.queue = queue;
  }

  /**
   * Perform incremental cleanup
   */
  async performIncrementalCleanup(
    targetSize: number,
    deleteCallback: (pageId: string) => Promise<number>
  ): Promise<{
    completed: boolean;
    freedSize: number;
    deletedCount: number;
    remainingSize: number;
  }> {
    if (this.cleanupInProgress) {
      return {
        completed: false,
        freedSize: 0,
        deletedCount: 0,
        remainingSize: targetSize
      };
    }

    this.cleanupInProgress = true;
    const startTime = performance.now();
    let freedSize = 0;
    let deletedCount = 0;

    try {
      // Get candidates efficiently from priority queue
      const candidates = this.queue.getUnprotectedCandidates(targetSize);
      
      for (const candidate of candidates) {
        // Check time budget
        if (performance.now() - startTime > this.cleanupBudgetMs) {
          // Schedule continuation
          if (typeof requestIdleCallback !== 'undefined') {
            requestIdleCallback(() => {
              this.performIncrementalCleanup(
                targetSize - freedSize, 
                deleteCallback
              );
            });
          } else {
            setTimeout(() => {
              this.performIncrementalCleanup(
                targetSize - freedSize, 
                deleteCallback
              );
            }, 10);
          }
          
          return {
            completed: false,
            freedSize,
            deletedCount,
            remainingSize: targetSize - freedSize
          };
        }

        try {
          const deletedSize = await deleteCallback(candidate.pageId);
          freedSize += deletedSize;
          deletedCount++;
          
          // Remove from queue
          this.queue.remove(candidate.pageId);
          
          if (freedSize >= targetSize) {
            break;
          }
        } catch (error) {
          console.warn(`Failed to delete page ${candidate.pageId}:`, error);
        }
      }

      return {
        completed: true,
        freedSize,
        deletedCount,
        remainingSize: Math.max(0, targetSize - freedSize)
      };
    } finally {
      this.cleanupInProgress = false;
    }
  }

  /**
   * Check if cleanup is in progress
   */
  isCleanupInProgress(): boolean {
    return this.cleanupInProgress;
  }

  /**
   * Set cleanup time budget
   */
  setCleanupBudget(budgetMs: number): void {
    this.cleanupBudgetMs = Math.max(1, budgetMs);
  }
}