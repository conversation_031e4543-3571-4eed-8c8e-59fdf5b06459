/**
 * LRU Cache Manager for Recall V3.0
 * 
 * Manages storage quota by implementing Least Recently Used (LRU) cache strategy
 * - Tracks page access patterns
 * - Automatically cleans up old data when approaching storage limits
 * - Protects important/favorite pages from cleanup
 * - Provides storage analytics and monitoring
 */

import { <PERSON><PERSON><PERSON><PERSON>, DB_ERROR_CODES } from '../models';
import { 
  DB_CONFIG
} from './types';
import type {
  StorageQuota,
  LRUCacheEntry,
  PageV3
} from './types';
import { LRUPriorityQueue, IncrementalLRUCleanup } from './LRUPriorityQueue';

/**
 * Storage monitoring service
 */
export class StorageMonitor {
  private db: IDBDatabase;
  private listeners: ((quota: StorageQuota) => void)[] = [];

  constructor(db: IDBDatabase) {
    this.db = db;
  }

  /**
   * Get current storage usage
   */
  async getCurrentUsage(): Promise<StorageQuota> {
    const breakdown = {
      pages: 0,
      settings: 0,
      cache: 0
    };

    try {
      const transaction = this.db.transaction(Object.values(DB_CONFIG.stores), 'readonly');
      
      // Calculate pages storage
      const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
      breakdown.pages = await this.estimatePagesSize(pagesStore);

      // Calculate other stores (simplified estimates)
      const settingsStore = transaction.objectStore(DB_CONFIG.stores.settings);
      const settingsCount = await this.promiseRequest(settingsStore.count());
      breakdown.settings = settingsCount * 100; // ~100 bytes per setting

      const lruStore = transaction.objectStore(DB_CONFIG.stores.lruCache);
      const cacheCount = await this.promiseRequest(lruStore.count());
      breakdown.cache = cacheCount * 200; // Small estimate for cache entries

      const currentUsage = Object.values(breakdown).reduce((sum, size) => sum + size, 0);

      return {
        maxStorage: DB_CONFIG.maxStorageBytes,
        currentUsage,
        breakdown,
        warningThreshold: DB_CONFIG.maxStorageBytes * 0.9
      };
    } catch (error) {
      throw new DBError(
        'Failed to calculate storage usage',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Check if storage is approaching limits
   */
  async needsCleanup(): Promise<boolean> {
    const quota = await this.getCurrentUsage();
    return quota.currentUsage > quota.warningThreshold;
  }

  /**
   * Add storage change listener
   */
  addListener(listener: (quota: StorageQuota) => void): void {
    this.listeners.push(listener);
  }

  /**
   * Remove storage change listener
   */
  removeListener(listener: (quota: StorageQuota) => void): void {
    this.listeners = this.listeners.filter(l => l !== listener);
  }


  /**
   * Estimate pages storage size
   */
  private async estimatePagesSize(pagesStore: IDBObjectStore): Promise<number> {
    return new Promise((resolve, reject) => {
      let totalSize = 0;
      const request = pagesStore.openCursor();

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        
        if (!cursor) {
          resolve(totalSize);
          return;
        }

        const page = cursor.value as PageV3;
        totalSize += this.estimatePageSize(page);
        cursor.continue();
      };

      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Estimate individual page size
   */
  private estimatePageSize(page: PageV3): number {
    // Base JSON size (AI properties removed)
    const size = JSON.stringify(page).length * 2; // UTF-16 encoding
    return size;
  }

  /**
   * Promise wrapper for IndexedDB requests
   */
  private promiseRequest<T>(request: IDBRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}

/**
 * LRU Cache Manager
 */
export class LRUCacheManager {
  private db: IDBDatabase;
  private monitor: StorageMonitor;
  private priorityQueue: LRUPriorityQueue;
  private incrementalCleanup: IncrementalLRUCleanup;
  private isInitialized = false;

  constructor(db: IDBDatabase) {
    this.db = db;
    this.monitor = new StorageMonitor(db);
    this.priorityQueue = new LRUPriorityQueue();
    this.incrementalCleanup = new IncrementalLRUCleanup(this.priorityQueue);
    
    // Initialize the priority queue with existing entries
    this.initializePriorityQueue();
  }

  /**
   * Initialize priority queue with existing LRU entries
   */
  private async initializePriorityQueue(): Promise<void> {
    try {
      const transaction = this.db.transaction([DB_CONFIG.stores.lruCache], 'readonly');
      const store = transaction.objectStore(DB_CONFIG.stores.lruCache);
      const entries: LRUCacheEntry[] = [];
      
      await new Promise<void>((resolve, reject) => {
        const request = store.openCursor();
        
        request.onsuccess = (event) => {
          const cursor = (event.target as IDBRequest).result;
          
          if (!cursor) {
            resolve();
            return;
          }
          
          entries.push(cursor.value as LRUCacheEntry);
          cursor.continue();
        };
        
        request.onerror = () => reject(request.error);
      });
      
      // Build the priority queue with all entries
      this.priorityQueue.buildHeap(entries);
      this.isInitialized = true;
    } catch (error) {
      console.warn('Failed to initialize priority queue:', error);
      this.isInitialized = true; // Continue anyway
    }
  }

  /**
   * Track page access for LRU management
   */
  async trackAccess(pageId: string, page?: PageV3): Promise<void> {
    const transaction = this.db.transaction([DB_CONFIG.stores.lruCache], 'readwrite');
    const store = transaction.objectStore(DB_CONFIG.stores.lruCache);

    try {
      const existing = await this.promiseRequest(store.get(pageId));
      
      if (existing) {
        // Update existing entry
        existing.lastAccessed = Date.now();
        existing.accessCount += 1;
        await this.promiseRequest(store.put(existing));
        
        // Update priority queue
        this.priorityQueue.upsert(existing);
      } else if (page) {
        // Create new entry
        const entry: LRUCacheEntry = {
          pageId,
          lastAccessed: Date.now(),
          accessCount: 1,
          storageSize: this.estimatePageSize(page),
          isProtected: page.isFavorite || page.importance! > 4
        };
        await this.promiseRequest(store.add(entry));
        
        // Add to priority queue
        this.priorityQueue.upsert(entry);
      }
    } catch (error) {
      throw new DBError(
        'Failed to track page access',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Update protection status for a page
   */
  async updateProtection(pageId: string, isProtected: boolean): Promise<void> {
    const transaction = this.db.transaction([DB_CONFIG.stores.lruCache], 'readwrite');
    const store = transaction.objectStore(DB_CONFIG.stores.lruCache);

    try {
      const entry = await this.promiseRequest(store.get(pageId));
      if (entry) {
        entry.isProtected = isProtected;
        await this.promiseRequest(store.put(entry));
        
        // Update priority queue
        this.priorityQueue.upsert(entry);
      }
    } catch (error) {
      throw new DBError(
        'Failed to update page protection',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get LRU candidates for cleanup
   */
  async getLRUCandidates(targetSize: number): Promise<LRUCacheEntry[]> {
    // Ensure priority queue is initialized
    if (!this.isInitialized) {
      await this.initializePriorityQueue();
    }
    
    // Use priority queue for O(log n) performance instead of O(n) linear scan
    return this.priorityQueue.getUnprotectedCandidates(targetSize);
  }

  /**
   * Perform cleanup based on LRU strategy
   */
  async performCleanup(targetCleanupSize: number): Promise<{
    deletedCount: number;
    freedSize: number;
    deletedPageIds: string[];
  }> {
    try {
      // Ensure priority queue is initialized
      if (!this.isInitialized) {
        await this.initializePriorityQueue();
      }

      // Use incremental cleanup to avoid blocking the main thread
      const result = await this.incrementalCleanup.performIncrementalCleanup(
        targetCleanupSize,
        async (pageId: string) => {
          // Delete callback
          const transaction = this.db.transaction([
            DB_CONFIG.stores.pages,
            DB_CONFIG.stores.lruCache
          ], 'readwrite');

          const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
          const lruStore = transaction.objectStore(DB_CONFIG.stores.lruCache);

          // Get the entry to determine its size
          const entry = await this.promiseRequest(lruStore.get(pageId));
          if (!entry) {
            return 0;
          }

          // Delete page
          await this.promiseRequest(pagesStore.delete(pageId));
          
          // Delete LRU entry
          await this.promiseRequest(lruStore.delete(pageId));
          
          return entry.storageSize;
        }
      );

      // Keep calling incremental cleanup until completed
      const totalFreedSize = result.freedSize;
      const totalDeletedCount = result.deletedCount;
      const deletedPageIds: string[] = [];

      if (!result.completed && result.remainingSize > 0) {
        // Schedule continuation
        console.log(`Incremental cleanup in progress: freed ${totalFreedSize} bytes, ${result.remainingSize} bytes remaining`);
      }

      return {
        deletedCount: totalDeletedCount,
        freedSize: totalFreedSize,
        deletedPageIds
      };
    } catch (error) {
      throw new DBError(
        'Failed to perform LRU cleanup',
        DB_ERROR_CODES.LRU_CLEANUP_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    totalPages: number;
    protectedPages: number;
    avgAccessCount: number;
    oldestAccess: number | null;
    newestAccess: number | null;
  }> {
    const transaction = this.db.transaction([DB_CONFIG.stores.lruCache], 'readonly');
    const store = transaction.objectStore(DB_CONFIG.stores.lruCache);

    return new Promise((resolve, reject) => {
      let totalPages = 0;
      let protectedPages = 0;
      let totalAccessCount = 0;
      let oldestAccess: number | null = null;
      let newestAccess: number | null = null;

      const request = store.openCursor();

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;

        if (!cursor) {
          resolve({
            totalPages,
            protectedPages,
            avgAccessCount: totalPages > 0 ? totalAccessCount / totalPages : 0,
            oldestAccess,
            newestAccess
          });
          return;
        }

        const entry = cursor.value as LRUCacheEntry;
        totalPages++;
        totalAccessCount += entry.accessCount;

        if (entry.isProtected) {
          protectedPages++;
        }

        if (oldestAccess === null || entry.lastAccessed < oldestAccess) {
          oldestAccess = entry.lastAccessed;
        }

        if (newestAccess === null || entry.lastAccessed > newestAccess) {
          newestAccess = entry.lastAccessed;
        }

        cursor.continue();
      };

      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Force cleanup to target storage percentage
   */
  async cleanupToTarget(targetPercentage: number = 0.8): Promise<{
    success: boolean;
    freedSize: number;
    deletedCount: number;
  }> {
    const quota = await this.monitor.getCurrentUsage();
    const targetSize = DB_CONFIG.maxStorageBytes * targetPercentage;
    
    if (quota.currentUsage <= targetSize) {
      return {
        success: true,
        freedSize: 0,
        deletedCount: 0
      };
    }

    const cleanupSize = quota.currentUsage - targetSize;
    const result = await this.performCleanup(cleanupSize);

    return {
      success: result.freedSize >= cleanupSize * 0.8, // Consider 80% success as good enough
      freedSize: result.freedSize,
      deletedCount: result.deletedCount
    };
  }

  /**
   * Get storage monitor instance
   */
  getMonitor(): StorageMonitor {
    return this.monitor;
  }

  // AI-related helper methods removed

  /**
   * Estimate page size
   */
  private estimatePageSize(page: PageV3): number {
    // AI properties removed
    const size = JSON.stringify(page).length * 2;
    return size;
  }

  /**
   * Promise wrapper for IndexedDB requests
   */
  private promiseRequest<T>(request: IDBRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Schedule automatic cleanup check
   */
  scheduleAutoCleanup(intervalMs: number = 60000): NodeJS.Timeout {
    return setInterval(async () => {
      try {
        const needsCleanup = await this.monitor.needsCleanup();
        if (needsCleanup) {
          console.log('Auto cleanup triggered');
          await this.cleanupToTarget(0.8);
        }
      } catch (error) {
        console.error('Auto cleanup failed:', error);
      }
    }, intervalMs);
  }

  /**
   * Clear cleanup interval
   */
  clearAutoCleanup(intervalId: NodeJS.Timeout): void {
    clearInterval(intervalId);
  }
}

/**
 * Create LRU Cache Manager instance
 */
export function createLRUCacheManager(db: IDBDatabase): LRUCacheManager {
  return new LRUCacheManager(db);
}

/**
 * Storage cleanup utilities
 */
export class StorageCleanupUtils {
  /**
   * Calculate recommended cleanup size
   */
  static calculateCleanupSize(currentUsage: number, maxStorage: number, targetPercentage: number = 0.8): number {
    const targetSize = maxStorage * targetPercentage;
    return Math.max(0, currentUsage - targetSize);
  }

  /**
   * Get storage health status
   */
  static getStorageHealth(quota: StorageQuota): 'healthy' | 'warning' | 'critical' {
    const usagePercentage = quota.currentUsage / quota.maxStorage;
    
    if (usagePercentage < 0.8) {
      return 'healthy';
    } else if (usagePercentage < 0.95) {
      return 'warning';
    } else {
      return 'critical';
    }
  }

  /**
   * Format storage size for display
   */
  static formatStorageSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}