/**
 * Enhanced Storage Service with Content Compression
 * 
 * Extends the existing IndexedDB service with transparent content compression
 * capabilities. Provides automatic compression/decompression of page content
 * to optimize storage usage while maintaining API compatibility.
 */

import { IndexedDBService } from '../services/db.service';
import { contentCompressionService, ContentCompressionService } from './ContentCompression';
import type { Page } from '../models';
import type { PageV3 } from './types';
import { DBError, DB_ERROR_CODES } from '../models';

export interface StorageOptimizationMetrics {
  /** Total pages stored */
  totalPages: number;
  
  /** Number of compressed pages */
  compressedPages: number;
  
  /** Total storage used (bytes) */
  totalStorage: number;
  
  /** Storage saved through compression (bytes) */
  storageSaved: number;
  
  /** Average compression ratio */
  avgCompressionRatio: number;
  
  /** Compression rate (percentage of pages compressed) */
  compressionRate: number;
}

export class EnhancedStorageService {
  private static instance: EnhancedStorageService | null = null;
  private dbService: IndexedDBService;
  
  private constructor() {
    this.dbService = IndexedDBService.getInstance();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): EnhancedStorageService {
    if (!EnhancedStorageService.instance) {
      EnhancedStorageService.instance = new EnhancedStorageService();
    }
    return EnhancedStorageService.instance;
  }

  /**
   * Initialize the enhanced storage service
   */
  public async init(): Promise<void> {
    await this.dbService.init();
  }

  /**
   * Add page with automatic content compression
   */
  public async addPageV3(pageData: {
    url: string;
    title: string;
    content: string;
    contentStatus?: 'extracted' | 'failed' | 'pending' | 'empty';
    extractionError?: string;
    lastExtractionAttempt?: number;
  }): Promise<PageV3> {
    try {
      // Compress content if beneficial
      const compressionResult = await contentCompressionService.compressContent(pageData.content);
      
      // Create enhanced page data
      const enhancedPageData: Partial<PageV3> = {
        ...pageData,
        isContentCompressed: compressionResult.compressed,
        originalContentSize: compressionResult.originalSize
      };

      if (compressionResult.compressed) {
        // Store compressed content and clear original
        enhancedPageData.compressedContent = compressionResult.data as Uint8Array;
        enhancedPageData.compressionRatio = compressionResult.compressionRatio;
        enhancedPageData.content = ''; // Clear original content to save space
      } else {
        // Keep original content
        enhancedPageData.content = pageData.content;
      }

      // Use existing database service to store
      const storedPage = await this.dbService.addPageWithStatus(pageData);
      
      // Return enhanced page with proper content
      return this.enhancePageData(storedPage);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DBError(
        `Failed to add page with compression: ${errorMessage}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get page with automatic content decompression
   */
  public async getPageV3(id: string): Promise<PageV3 | null> {
    try {
      const page = await this.dbService.getPage(id);
      if (!page) {
        return null;
      }

      return this.enhancePageData(page);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DBError(
        `Failed to get page with decompression: ${errorMessage}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get page by URL with automatic content decompression
   */
  public async getPageByUrlV3(url: string): Promise<PageV3 | null> {
    try {
      const page = await this.dbService.getPageByUrl(url);
      if (!page) {
        return null;
      }

      return this.enhancePageData(page);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DBError(
        `Failed to get page by URL with decompression: ${errorMessage}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Update page with automatic compression handling
   */
  public async updatePageV3(id: string, updates: Partial<PageV3>): Promise<PageV3> {
    try {
      // If content is being updated, handle compression
      if (updates.content !== undefined) {
        const compressionResult = await contentCompressionService.compressContent(updates.content);
        
        const enhancedUpdates: Partial<PageV3> = {
          ...updates,
          isContentCompressed: compressionResult.compressed,
          originalContentSize: compressionResult.originalSize
        };

        if (compressionResult.compressed) {
          enhancedUpdates.compressedContent = compressionResult.data as Uint8Array;
          enhancedUpdates.compressionRatio = compressionResult.compressionRatio;
          enhancedUpdates.content = ''; // Clear original content
        } else {
          enhancedUpdates.content = updates.content;
          // Clear compression fields if not compressed
          enhancedUpdates.compressedContent = undefined;
          enhancedUpdates.compressionRatio = undefined;
        }

        const updatedPage = await this.dbService.updatePage(id, enhancedUpdates);
        return this.enhancePageData(updatedPage);
      } else {
        // No content update, just pass through
        const updatedPage = await this.dbService.updatePage(id, updates);
        return this.enhancePageData(updatedPage);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DBError(
        `Failed to update page with compression: ${errorMessage}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get all pages with automatic decompression
   */
  public async getAllPagesV3(): Promise<PageV3[]> {
    try {
      const pages = await this.dbService.getAllPages();
      return Promise.all(pages.map(page => this.enhancePageData(page)));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DBError(
        `Failed to get all pages with decompression: ${errorMessage}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Convert legacy pages to use compression
   */
  public async compressExistingPages(batchSize: number = 50): Promise<{
    processed: number;
    compressed: number;
    errors: number;
    storageSaved: number;
  }> {
    let processed = 0;
    let compressed = 0;
    let errors = 0;
    let storageSaved = 0;

    try {
      const allPages = await this.dbService.getAllPages();
      
      for (let i = 0; i < allPages.length; i += batchSize) {
        const batch = allPages.slice(i, i + batchSize);
        
        for (const page of batch) {
          try {
            // Skip if already compressed
            const pageV3 = page as PageV3;
            if (pageV3.isContentCompressed) {
              processed++;
              continue;
            }

            // Try to compress
            const compressionResult = await contentCompressionService.compressContent(page.content);
            
            if (compressionResult.compressed) {
              const updates: Partial<PageV3> = {
                compressedContent: compressionResult.data as Uint8Array,
                isContentCompressed: true,
                originalContentSize: compressionResult.originalSize,
                compressionRatio: compressionResult.compressionRatio,
                content: '' // Clear original content
              };

              await this.dbService.updatePage(page.id, updates);
              compressed++;
              storageSaved += (compressionResult.originalSize - compressionResult.compressedSize);
            }

            processed++;

          } catch (error) {
            console.warn(`Failed to compress page ${page.id}:`, error);
            errors++;
            processed++;
          }
        }

        // Small delay between batches to avoid blocking
        if (i + batchSize < allPages.length) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      return { processed, compressed, errors, storageSaved };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DBError(
        `Failed to compress existing pages: ${errorMessage}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get storage optimization metrics
   */
  public async getOptimizationMetrics(): Promise<StorageOptimizationMetrics> {
    try {
      const allPages = await this.dbService.getAllPages();
      let totalStorage = 0;
      let storageSaved = 0;
      let compressedPages = 0;
      let totalCompressionRatio = 0;

      for (const page of allPages) {
        const pageV3 = page as PageV3;
        
        if (pageV3.isContentCompressed && pageV3.compressedContent) {
          compressedPages++;
          const originalSize = pageV3.originalContentSize || 0;
          const compressedSize = pageV3.compressedContent.length;
          
          totalStorage += compressedSize;
          storageSaved += (originalSize - compressedSize);
          totalCompressionRatio += (pageV3.compressionRatio || 0);
        } else {
          // Uncompressed content
          totalStorage += new TextEncoder().encode(page.content).length;
        }
      }

      const avgCompressionRatio = compressedPages > 0 ? totalCompressionRatio / compressedPages : 0;
      const compressionRate = allPages.length > 0 ? (compressedPages / allPages.length) * 100 : 0;

      return {
        totalPages: allPages.length,
        compressedPages,
        totalStorage,
        storageSaved,
        avgCompressionRatio,
        compressionRate
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DBError(
        `Failed to get optimization metrics: ${errorMessage}`,
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Enhanced page data with proper content handling
   */
  private async enhancePageData(page: Page): Promise<PageV3> {
    const pageV3 = page as PageV3;

    // If content is compressed, decompress it
    if (pageV3.isContentCompressed && pageV3.compressedContent) {
      try {
        const decompressedContent = await contentCompressionService.decompressContent(pageV3.compressedContent);
        return {
          ...pageV3,
          content: decompressedContent
        };
      } catch (error) {
        console.error(`Failed to decompress content for page ${page.id}:`, error);
        // Fallback to empty content if decompression fails
        return {
          ...pageV3,
          content: '',
          isContentCompressed: false
        };
      }
    }

    // Return page with original content
    return pageV3;
  }

  /**
   * Close the storage service
   */
  public close(): void {
    this.dbService.close();
  }

  /**
   * Health check for the enhanced storage service
   */
  public async healthCheck(): Promise<{
    isHealthy: boolean;
    issues: string[];
    compressionHealth: {
      isSupported: boolean;
      metrics: any;
    };
  }> {
    const dbHealth = await this.dbService.healthCheck();
    const compressionSupported = ContentCompressionService.isSupported();
    const compressionMetrics = contentCompressionService.getMetrics();

    return {
      isHealthy: dbHealth.isHealthy && compressionSupported,
      issues: [
        ...dbHealth.issues,
        ...(compressionSupported ? [] : ['Content compression not supported in this environment'])
      ],
      compressionHealth: {
        isSupported: compressionSupported,
        metrics: compressionMetrics
      }
    };
  }

  /**
   * Delegate methods to underlying DB service
   */
  public async deletePage(id: string): Promise<boolean> {
    return this.dbService.deletePage(id);
  }

  public async deletePages(ids: string[]): Promise<number> {
    return this.dbService.deletePages(ids);
  }

  public async getPagesByDomain(domain: string): Promise<PageV3[]> {
    const pages = await this.dbService.getPagesByDomain(domain);
    return Promise.all(pages.map(page => this.enhancePageData(page)));
  }

  public async getPagesByTimeRange(startTime: number, endTime: number): Promise<PageV3[]> {
    const pages = await this.dbService.getPagesByTimeRange(startTime, endTime);
    return Promise.all(pages.map(page => this.enhancePageData(page)));
  }

  public async getStorageInfo(): Promise<{
    pagesCount: number;
    settingsCount: number;
    estimatedSize: number;
    compressionSavings?: number;
  }> {
    const storageInfo = await this.dbService.getStorageInfo();
    const optimizationMetrics = await this.getOptimizationMetrics();
    
    return {
      ...storageInfo,
      compressionSavings: optimizationMetrics.storageSaved
    };
  }

  // Settings methods (unchanged)
  public async getSetting(key: string): Promise<any> {
    return this.dbService.getSetting(key);
  }

  public async setSetting(key: string, value: any): Promise<void> {
    return this.dbService.setSetting(key, value);
  }

  public async deleteSetting(key: string): Promise<boolean> {
    return this.dbService.deleteSetting(key);
  }

  public async getAllSettings(): Promise<Record<string, any>> {
    return this.dbService.getAllSettings();
  }

  public async clearAllData(): Promise<void> {
    return this.dbService.clearAllData();
  }

  public async exportData(): Promise<{
    pages: PageV3[];
    settings: Record<string, any>;
    exportTime: number;
    version: string;
    compressionInfo?: StorageOptimizationMetrics;
  }> {
    const dbData = await this.dbService.exportData();
    const enhancedPages = await Promise.all(
      dbData.pages.map(page => this.enhancePageData(page))
    );
    const compressionInfo = await this.getOptimizationMetrics();

    return {
      ...dbData,
      pages: enhancedPages,
      compressionInfo
    };
  }

  public async importData(data: {
    pages: PageV3[];
    settings: Record<string, any>;
  }): Promise<void> {
    // Convert enhanced pages back to basic format for import
    const basicPages = data.pages.map(page => ({
      id: page.id,
      url: page.url,
      title: page.title,
      content: page.content,
      domain: page.domain,
      visitTime: page.visitTime,
      accessCount: page.accessCount,
      lastUpdated: page.lastUpdated,
      language: page.language,
      summary: page.summary,
      contentStatus: page.contentStatus,
      extractionError: page.extractionError,
      lastExtractionAttempt: page.lastExtractionAttempt
    }));

    return this.dbService.importData({
      pages: basicPages,
      settings: data.settings
    });
  }
}

// Export singleton instance
export const enhancedStorageService = EnhancedStorageService.getInstance();