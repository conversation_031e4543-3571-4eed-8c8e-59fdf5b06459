/**
 * Storage Layer Exports for Recall V3.0
 * 
 * Provides unified access to all storage-related functionality:
 * - IndexedDB service for traditional search
 * - Storage quota management with LRU cleanup
 * - Database migration utilities
 * - Type definitions and interfaces
 * (AI capabilities removed)
 */

// Core service
export { IndexedDBService, dbService } from './IndexedDBService';
import { IndexedDBService } from './IndexedDBService';
import { DB_CONFIG } from './types';
import type { StorageQuota } from './types';

// Migration utilities
export { 
  MigrationService, 
  createMigrationService, 
  MIGRATION_VERSIONS, 
  createMigrationContext 
} from './migrations';

// Storage management
export { 
  LRUCacheManager, 
  StorageMonitor, 
  createLRUCacheManager,
  StorageCleanupUtils 
} from './LRUCacheManager';

// Type definitions (AI types removed)
export type {
  // Enhanced data types
  PageV3,
  
  // Storage management types
  StorageQuota,
  LRUCacheEntry,
  
  // Operation types
  BatchOperation,
  MigrationContext,
  MigrationRecord,
  
  // Service interface
  IStorageService
} from './types';

// Constants and configuration
export { 
  DB_CONFIG as DB_CONFIG_V3, 
  DB_ERROR_CODES as DB_ERROR_CODES_V3 
} from './types';

/**
 * Storage layer version
 */
export const STORAGE_VERSION = '3.0.0';

/**
 * Storage feature flags (AI features removed)
 */
export const STORAGE_FEATURES = {
  LRU_CLEANUP: true,
  AUTO_MIGRATION: true,
  STORAGE_QUOTA: true
} as const;

/**
 * Create a fully configured storage service instance
 */
export async function createStorageService(): Promise<IndexedDBService> {
  const service = IndexedDBService.getInstance();
  await service.init();
  return service;
}

/**
 * Storage health utilities
 */
export class StorageHealth {
  /**
   * Perform comprehensive storage health check
   */
  static async checkHealth(service: IndexedDBService): Promise<{
    isHealthy: boolean;
    version: string;
    features: typeof STORAGE_FEATURES;
    quota: StorageQuota;
    issues: string[];
  }> {
    const healthCheck = await service.healthCheck();
    const quota = await service.getStorageQuota();
    
    return {
      isHealthy: healthCheck.isHealthy,
      version: STORAGE_VERSION,
      features: STORAGE_FEATURES,
      quota,
      issues: healthCheck.issues
    };
  }

  /**
   * Get storage recommendations
   */
  static getRecommendations(quota: StorageQuota): string[] {
    const recommendations: string[] = [];
    const usagePercentage = quota.currentUsage / quota.maxStorage;

    if (usagePercentage > 0.9) {
      recommendations.push('Critical: Storage almost full. Cleanup recommended.');
    } else if (usagePercentage > 0.8) {
      recommendations.push('Warning: Storage getting full. Consider cleanup.');
    }


    if (recommendations.length === 0) {
      recommendations.push('Storage is healthy.');
    }

    return recommendations;
  }
}

/**
 * Storage migration utilities
 */
export class StorageMigrationUtils {
  /**
   * Check if migration is available
   */
  static isMigrationSupported(fromVersion: number, toVersion: number): boolean {
    return fromVersion === 2 && toVersion === 3;
  }

  /**
   * Estimate migration time
   */
  static estimateMigrationTime(pageCount: number): number {
    // Rough estimate: 1ms per page for migration
    return Math.max(1000, pageCount * 1);
  }

  /**
   * Create migration progress tracker
   */
  static createProgressTracker(onProgress?: (progress: number, message: string) => void) {
    return (progress: number, message: string) => {
      console.log(`Migration: ${progress}% - ${message}`);
      onProgress?.(progress, message);
    };
  }
}


/**
 * Storage configuration presets
 */
export const STORAGE_PRESETS = {
  DEVELOPMENT: {
    autoCleanup: false,
    debugMode: true,
    maxStorageBytes: 100 * 1024 * 1024 // 100MB for development
  },
  
  PRODUCTION: {
    autoCleanup: true,
    debugMode: false,
    maxStorageBytes: DB_CONFIG.maxStorageBytes
  },
  
  TESTING: {
    autoCleanup: false,
    debugMode: true,
    maxStorageBytes: 10 * 1024 * 1024 // 10MB for testing
  }
} as const;