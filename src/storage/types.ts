/**
 * Recall Storage Types
 * 
 * Storage layer types for traditional search functionality
 */

import type { Page } from '../models';

/**
 * Enhanced Page interface with additional metadata
 */
export interface PageV3 extends Page {
  /** User-marked importance level (1-5) */
  importance?: number;
  
  /** Whether this page is marked as favorite */
  isFavorite?: boolean;
  
  /** Reading time in seconds */
  readingTime?: number;
  
  /** Content hash for change detection */
  contentHash?: string;
  
  /** Compressed content data (if content is compressed) */
  compressedContent?: Uint8Array;
  
  /** Whether content is compressed */
  isContentCompressed?: boolean;
  
  /** Original content size (before compression) */
  originalContentSize?: number;
  
  /** Compression ratio */
  compressionRatio?: number;
}

/**
 * Storage quota and usage tracking
 */
export interface StorageQuota {
  /** Maximum allowed storage in bytes (500MB) */
  maxStorage: number;
  
  /** Current storage usage in bytes */
  currentUsage: number;
  
  /** Storage breakdown by type */
  breakdown: {
    pages: number;
    settings: number;
    cache: number;
  };
  
  /** Last cleanup timestamp */
  lastCleanup?: number;
  
  /** Warning threshold (90% of max) */
  warningThreshold: number;
}

/**
 * LRU Cache entry for storage management
 */
export interface LRUCacheEntry {
  /** Page ID */
  pageId: string;
  
  /** Last access timestamp */
  lastAccessed: number;
  
  /** Access frequency */
  accessCount: number;
  
  /** Storage size in bytes */
  storageSize: number;
  
  /** Whether page is protected from cleanup */
  isProtected: boolean;
}

/**
 * Database migration record
 */
export interface MigrationRecord {
  /** Migration version */
  version: number;
  
  /** Migration timestamp */
  timestamp: number;
  
  /** Migration description */
  description: string;
  
  /** Success status */
  success: boolean;
  
  /** Error message if failed */
  error?: string;
}

/**
 * Database configuration
 */
export const DB_CONFIG = {
  /** Database name */
  name: 'RecallDB',
  
  /** Database version */
  version: 3,
  
  /** Storage quota in bytes (500MB) */
  maxStorageBytes: 500 * 1024 * 1024,
  
  /** Object stores */
  stores: {
    pages: 'pages',
    lruCache: 'lru_cache',
    settings: 'settings',
    blacklist: 'blacklist',
    migrations: 'migrations'
  },
  
  /** Indexes */
  indexes: {
    pages: {
      url: 'url',
      domain: 'domain',
      visitTime: 'visitTime',
      lastUpdated: 'lastUpdated',
      contentHash: 'contentHash',
      importance: 'importance'
    },
    lruCache: {
      lastAccessed: 'lastAccessed',
      accessCount: 'accessCount',
      storageSize: 'storageSize'
    }
  }
} as const;

/**
 * Error codes
 */
export const DB_ERROR_CODES = {
  DB_NOT_AVAILABLE: 'DB_NOT_AVAILABLE',
  STORE_NOT_FOUND: 'STORE_NOT_FOUND',
  TRANSACTION_FAILED: 'TRANSACTION_FAILED',
  DUPLICATE_KEY: 'DUPLICATE_KEY',
  ITEM_NOT_FOUND: 'ITEM_NOT_FOUND',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  NOT_FOUND: 'NOT_FOUND',
  MIGRATION_FAILED: 'MIGRATION_FAILED',
  STORAGE_QUOTA_WARNING: 'STORAGE_QUOTA_WARNING',
  LRU_CLEANUP_FAILED: 'LRU_CLEANUP_FAILED'
} as const;

/**
 * Batch operation interface
 */
export interface BatchOperation<T> {
  /** Operation type */
  type: 'insert' | 'update' | 'delete';
  
  /** Data for the operation */
  data: T;
  
  /** Operation metadata */
  metadata?: Record<string, any>;
}

/**
 * Storage migration context
 */
export interface MigrationContext {
  /** Source database version */
  fromVersion: number;
  
  /** Target database version */
  toVersion: number;
  
  /** Migration progress callback */
  onProgress?: (progress: number, message: string) => void;
  
  /** Whether to preserve old data during migration */
  preserveOldData?: boolean;
}

/**
 * Storage service interface
 */
export interface IStorageService {
  // Basic operations
  init(): Promise<void>;
  close(): void;
  
  // Page operations
  addPage(page: Partial<PageV3>): Promise<PageV3>;
  getPage(id: string): Promise<PageV3 | null>;
  updatePage(id: string, updates: Partial<PageV3>): Promise<PageV3>;
  deletePage(id: string): Promise<boolean>;
  
  // Storage management
  getStorageQuota(): Promise<StorageQuota>;
  cleanupStorage(): Promise<number>;
  
  // Migration
  migrateFrom(context: MigrationContext): Promise<void>;
  
  // Health check
  healthCheck(): Promise<{ isHealthy: boolean; issues: string[] }>;
}