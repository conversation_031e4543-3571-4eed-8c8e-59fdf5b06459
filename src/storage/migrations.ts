/**
 * Database Migration Service for Recall V3.0
 * 
 * Handles migration from V2 to V3 database schema
 * - Migrates existing pages to enhanced PageV3 format
 * - Creates new object stores for enhanced functionality
 * - Preserves user data and settings
 */

import { DBError, DB_ERROR_CODES } from '../models';
import { 
  DB_CONFIG
} from './types';
import type { 
  MigrationContext, 
  MigrationRecord,
  PageV3 
} from './types';

/**
 * Migration service class
 */
export class MigrationService {
  private db: IDBDatabase | null = null;

  constructor(db: IDBDatabase) {
    this.db = db;
  }

  /**
   * Execute migration from V2 to V3
   */
  async migrateToV3(context: MigrationContext): Promise<void> {
    const { fromVersion, toVersion, onProgress } = context;
    
    if (fromVersion >= toVersion) {
      throw new DBError(
        `Invalid migration: from ${fromVersion} to ${toVersion}`,
        DB_ERROR_CODES.MIGRATION_FAILED
      );
    }

    try {
      // Start migration transaction
      onProgress?.(0, 'Starting migration...');
      
      if (fromVersion === 2 && toVersion === 3) {
        await this.migrateV2ToV3(onProgress);
      } else {
        throw new DBError(
          `Unsupported migration path: ${fromVersion} -> ${toVersion}`,
          DB_ERROR_CODES.MIGRATION_FAILED
        );
      }

      // Record successful migration
      await this.recordMigration({
        version: toVersion,
        timestamp: Date.now(),
        description: `Migration from V${fromVersion} to V${toVersion}`,
        success: true
      });

      onProgress?.(100, 'Migration completed successfully');
      
    } catch (error) {
      // Record failed migration
      await this.recordMigration({
        version: toVersion,
        timestamp: Date.now(),
        description: `Failed migration from V${fromVersion} to V${toVersion}`,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      throw new DBError(
        `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        DB_ERROR_CODES.MIGRATION_FAILED,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Migrate from V2 to V3 schema
   */
  private async migrateV2ToV3(onProgress?: (progress: number, message: string) => void): Promise<void> {
    if (!this.db) {
      throw new DBError('Database not available', DB_ERROR_CODES.DB_NOT_AVAILABLE);
    }

    onProgress?.(10, 'Creating new object stores...');
    
    // Create new object stores for V3
    await this.createV3ObjectStores();
    
    onProgress?.(30, 'Migrating page data...');
    
    // Migrate existing pages to V3 format
    await this.migratePagesV2ToV3();
    
    onProgress?.(60, 'Migrating settings...');
    
    // Migrate settings
    await this.migrateSettingsV2ToV3();
    
    onProgress?.(80, 'Initializing new features...');
    
    // Initialize new V3 features
    await this.initializeV3Features();
    
    onProgress?.(90, 'Cleaning up...');
    
    // Optional: Clean up old stores if not preserving data
    // await this.cleanupOldStores();
  }

  /**
   * Create V3 object stores and indexes
   */
  private async createV3ObjectStores(): Promise<void> {
    if (!this.db) {
      throw new DBError('Database not available', DB_ERROR_CODES.DB_NOT_AVAILABLE);
    }

    // AI-related stores removed for traditional search mode

    // Create LRU cache store
    if (!this.db.objectStoreNames.contains(DB_CONFIG.stores.lruCache)) {
      const lruStore = this.db.createObjectStore(DB_CONFIG.stores.lruCache, {
        keyPath: 'pageId'
      });
      lruStore.createIndex('lastAccessed', 'lastAccessed', { unique: false });
      lruStore.createIndex('accessCount', 'accessCount', { unique: false });
      lruStore.createIndex('storageSize', 'storageSize', { unique: false });
    }

    // Create migrations store
    if (!this.db.objectStoreNames.contains(DB_CONFIG.stores.migrations)) {
      const migrationsStore = this.db.createObjectStore(DB_CONFIG.stores.migrations, {
        keyPath: 'version'
      });
      migrationsStore.createIndex('timestamp', 'timestamp', { unique: false });
    }

    // Create enhanced pages store
    if (!this.db.objectStoreNames.contains(DB_CONFIG.stores.pages)) {
      const pagesStore = this.db.createObjectStore(DB_CONFIG.stores.pages, {
        keyPath: 'id'
      });
      
      // Create all indexes for the new pages store
      pagesStore.createIndex('url', 'url', { unique: true });
      pagesStore.createIndex('domain', 'domain', { unique: false });
      pagesStore.createIndex('visitTime', 'visitTime', { unique: false });
      pagesStore.createIndex('lastUpdated', 'lastUpdated', { unique: false });
      pagesStore.createIndex('contentHash', 'contentHash', { unique: false });
      pagesStore.createIndex('knowledgeCenterId', 'knowledgeCenterId', { unique: false });
      pagesStore.createIndex('importance', 'importance', { unique: false });
    }

    // Create enhanced settings store
    if (!this.db.objectStoreNames.contains(DB_CONFIG.stores.settings)) {
      this.db.createObjectStore(DB_CONFIG.stores.settings, {
        keyPath: 'key'
      });
    }
  }

  /**
   * Migrate pages from V2 to V3 format
   */
  private async migratePagesV2ToV3(): Promise<void> {
    if (!this.db) {
      throw new DBError('Database not available', DB_ERROR_CODES.DB_NOT_AVAILABLE);
    }

    const transaction = this.db.transaction(['pages', DB_CONFIG.stores.pages], 'readwrite');
    const oldStore = transaction.objectStore('pages');
    const newStore = transaction.objectStore(DB_CONFIG.stores.pages);

    return new Promise((resolve, reject) => {
      const request = oldStore.openCursor();

      request.onsuccess = async (event) => {
        const cursor = (event.target as IDBRequest).result;

        if (!cursor) {
          resolve();
          return;
        }

        try {
          const oldPage = cursor.value;
          
          // Convert to V3 format (AI fields removed)
          const newPage: PageV3 = {
            ...oldPage,
            // Add new V3 fields with defaults
            importance: 3, // Default importance
            isFavorite: false,
            readingTime: undefined,
            contentHash: this.generateContentHash(oldPage.content)
          };

          // Store in new format
          await this.promiseRequest(newStore.put(newPage));
          
          cursor.continue();
        } catch (error) {
          reject(error);
        }
      };

      request.onerror = () => {
        reject(new DBError(
          'Failed to migrate pages',
          DB_ERROR_CODES.MIGRATION_FAILED,
          request.error || undefined
        ));
      };
    });
  }

  /**
   * Migrate settings from V2 to V3
   */
  private async migrateSettingsV2ToV3(): Promise<void> {
    if (!this.db) {
      throw new DBError('Database not available', DB_ERROR_CODES.DB_NOT_AVAILABLE);
    }

    const transaction = this.db.transaction(['settings', DB_CONFIG.stores.settings], 'readwrite');
    const oldStore = transaction.objectStore('settings');
    const newStore = transaction.objectStore(DB_CONFIG.stores.settings);

    return new Promise((resolve, reject) => {
      const request = oldStore.openCursor();

      request.onsuccess = async (event) => {
        const cursor = (event.target as IDBRequest).result;

        if (!cursor) {
          resolve();
          return;
        }

        try {
          // Copy settings as-is to new store
          await this.promiseRequest(newStore.put(cursor.value));
          cursor.continue();
        } catch (error) {
          reject(error);
        }
      };

      request.onerror = () => {
        reject(new DBError(
          'Failed to migrate settings',
          DB_ERROR_CODES.MIGRATION_FAILED,
          request.error || undefined
        ));
      };
    });
  }

  /**
   * Initialize V3-specific features
   */
  private async initializeV3Features(): Promise<void> {
    // Initialize default storage quota tracking
    const quotaTransaction = this.db!.transaction([DB_CONFIG.stores.settings], 'readwrite');
    const settingsStore = quotaTransaction.objectStore(DB_CONFIG.stores.settings);

    const defaultQuota = {
      key: 'storage_quota_v3',
      value: {
        maxStorage: DB_CONFIG.maxStorageBytes,
        currentUsage: 0,
        breakdown: {
          pages: 0,
          settings: 0,
          cache: 0
        },
        warningThreshold: DB_CONFIG.maxStorageBytes * 0.9
      },
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    await this.promiseRequest(settingsStore.put(defaultQuota));

    // Initialize default V3 settings (AI features disabled)
    const v3Settings = {
      key: 'v3_features',
      value: {
        traditionalSearchEnabled: true,
        autoIndexingEnabled: true,
        fuzzySearchEnabled: true,
        fullTextSearchEnabled: true
      },
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    await this.promiseRequest(settingsStore.put(v3Settings));
  }

  /**
   * Record migration in the migrations store
   */
  private async recordMigration(record: MigrationRecord): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction([DB_CONFIG.stores.migrations], 'readwrite');
      const store = transaction.objectStore(DB_CONFIG.stores.migrations);
      await this.promiseRequest(store.put(record));
    } catch (error) {
      console.warn('Failed to record migration:', error);
    }
  }

  /**
   * Get migration history
   */
  async getMigrationHistory(): Promise<MigrationRecord[]> {
    if (!this.db) {
      throw new DBError('Database not available', DB_ERROR_CODES.DB_NOT_AVAILABLE);
    }

    const transaction = this.db.transaction([DB_CONFIG.stores.migrations], 'readonly');
    const store = transaction.objectStore(DB_CONFIG.stores.migrations);
    
    return this.promiseRequest(store.getAll());
  }

  /**
   * Check if migration is needed
   */
  async needsMigration(currentVersion: number, targetVersion: number): Promise<boolean> {
    return currentVersion < targetVersion;
  }

  /**
   * Generate content hash for change detection
   */
  private generateContentHash(content: string): string {
    // Simple hash function for content change detection
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Promise wrapper for IndexedDB requests
   */
  private promiseRequest<T>(request: IDBRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

}

/**
 * Utility function to create migration service
 */
export function createMigrationService(db: IDBDatabase): MigrationService {
  return new MigrationService(db);
}

/**
 * Migration version checks
 */
export const MIGRATION_VERSIONS = {
  V2: 2,
  V3: 3
} as const;

/**
 * Default migration context
 */
export function createMigrationContext(
  fromVersion: number, 
  toVersion: number,
  onProgress?: (progress: number, message: string) => void
): MigrationContext {
  return {
    fromVersion,
    toVersion,
    onProgress,
    preserveOldData: true
  };
}