/**
 * Content Compression Service
 * 
 * Provides efficient text compression for page content storage optimization.
 * Uses gzip/deflate compression with smart thresholds and performance monitoring.
 * 
 * Key Features:
 * - Automatic compression threshold (1KB minimum)
 * - Transparent compression/decompression
 * - Performance metrics and monitoring
 * - Memory-efficient streaming for large content
 * - Fallback handling for compression failures
 */

export interface CompressionOptions {
  /** Minimum content size to trigger compression (bytes) */
  minSize: number;
  
  /** Compression level (1-9, higher = better compression but slower) */
  level: number;
  
  /** Enable streaming for large content (>10KB) */
  useStreaming: boolean;
  
  /** Timeout for compression operations (ms) */
  timeout: number;
}

export interface CompressionResult {
  /** Whether content was compressed */
  compressed: boolean;
  
  /** Compressed data (if compressed) or original data */
  data: Uint8Array | string;
  
  /** Original size in bytes */
  originalSize: number;
  
  /** Compressed size in bytes */
  compressedSize: number;
  
  /** Compression ratio (compressedSize / originalSize) */
  compressionRatio: number;
  
  /** Time taken for compression (ms) */
  compressionTime: number;
  
  /** Compression algorithm used */
  algorithm: 'none' | 'gzip' | 'deflate';
}

export interface CompressionMetrics {
  /** Total compressions performed */
  totalCompressions: number;
  
  /** Total decompressions performed */
  totalDecompressions: number;
  
  /** Total bytes compressed */
  totalBytesCompressed: number;
  
  /** Total bytes saved */
  totalBytesSaved: number;
  
  /** Average compression ratio */
  avgCompressionRatio: number;
  
  /** Average compression time (ms) */
  avgCompressionTime: number;
  
  /** Average decompression time (ms) */
  avgDecompressionTime: number;
  
  /** Compression error count */
  compressionErrors: number;
  
  /** Decompression error count */
  decompressionErrors: number;
}

const DEFAULT_OPTIONS: CompressionOptions = {
  minSize: 1024, // 1KB
  level: 6, // Balanced compression
  useStreaming: true,
  timeout: 5000 // 5 seconds
};

export class ContentCompressionService {
  private static instance: ContentCompressionService | null = null;
  private options: CompressionOptions;
  private metrics: CompressionMetrics;
  
  private constructor(options: Partial<CompressionOptions> = {}) {
    this.options = { ...DEFAULT_OPTIONS, ...options };
    this.metrics = {
      totalCompressions: 0,
      totalDecompressions: 0,
      totalBytesCompressed: 0,
      totalBytesSaved: 0,
      avgCompressionRatio: 0,
      avgCompressionTime: 0,
      avgDecompressionTime: 0,
      compressionErrors: 0,
      decompressionErrors: 0
    };
  }

  /**
   * Get singleton instance
   */
  public static getInstance(options?: Partial<CompressionOptions>): ContentCompressionService {
    if (!ContentCompressionService.instance) {
      ContentCompressionService.instance = new ContentCompressionService(options);
    }
    return ContentCompressionService.instance;
  }

  /**
   * Compress text content with automatic threshold checking
   */
  public async compressContent(content: string): Promise<CompressionResult> {
    const startTime = performance.now();
    const originalSize = new TextEncoder().encode(content).length;

    try {
      // Check if content meets minimum size threshold
      if (originalSize < this.options.minSize) {
        return {
          compressed: false,
          data: content,
          originalSize,
          compressedSize: originalSize,
          compressionRatio: 1.0,
          compressionTime: performance.now() - startTime,
          algorithm: 'none'
        };
      }

      // Choose compression method based on content size
      const useStreaming = this.options.useStreaming && originalSize > 10 * 1024; // 10KB
      
      let compressed: Uint8Array;
      let algorithm: 'gzip' | 'deflate';

      if (useStreaming) {
        compressed = await this.compressStreaming(content);
        algorithm = 'gzip';
      } else {
        compressed = await this.compressSync(content);
        algorithm = 'gzip';
      }

      const compressionTime = performance.now() - startTime;
      const compressionRatio = compressed.length / originalSize;

      // Update metrics
      this.updateCompressionMetrics(originalSize, compressed.length, compressionTime);

      return {
        compressed: true,
        data: compressed,
        originalSize,
        compressedSize: compressed.length,
        compressionRatio,
        compressionTime,
        algorithm
      };

    } catch (error) {
      this.metrics.compressionErrors++;
      console.warn('Compression failed, returning original content:', error);
      
      return {
        compressed: false,
        data: content,
        originalSize,
        compressedSize: originalSize,
        compressionRatio: 1.0,
        compressionTime: performance.now() - startTime,
        algorithm: 'none'
      };
    }
  }

  /**
   * Decompress compressed content
   */
  public async decompressContent(data: Uint8Array | string): Promise<string> {
    const startTime = performance.now();

    try {
      // If data is string, it's not compressed
      if (typeof data === 'string') {
        return data;
      }

      // Decompress the data
      const decompressed = await this.decompressData(data);
      const decompressionTime = performance.now() - startTime;

      // Update metrics
      this.updateDecompressionMetrics(decompressionTime);

      return decompressed;

    } catch (error) {
      this.metrics.decompressionErrors++;
      console.error('Decompression failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to decompress content: ${errorMessage}`);
    }
  }

  /**
   * Synchronous compression using CompressionStream API
   */
  private async compressSync(content: string): Promise<Uint8Array> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Compression timeout'));
      }, this.options.timeout);

      try {
        // Use browser's built-in compression
        const stream = new CompressionStream('gzip');
        const writer = stream.writable.getWriter();
        const reader = stream.readable.getReader();
        
        const chunks: Uint8Array[] = [];
        
        // Set up reader
        const readChunks = async () => {
          try {
            while (true) {
              const { done, value } = await reader.read();
              if (done) break;
              chunks.push(value);
            }
            
            // Combine chunks
            const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
            const result = new Uint8Array(totalLength);
            let offset = 0;
            
            for (const chunk of chunks) {
              result.set(chunk, offset);
              offset += chunk.length;
            }
            
            clearTimeout(timeout);
            resolve(result);
          } catch (error) {
            clearTimeout(timeout);
            reject(error);
          }
        };

        // Start reading
        readChunks();

        // Write data and close
        const encoder = new TextEncoder();
        const encoded = encoder.encode(content);
        writer.write(encoded);
        writer.close();

      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * Streaming compression for large content
   */
  private async compressStreaming(content: string): Promise<Uint8Array> {
    const stream = new CompressionStream('gzip');
    const writer = stream.writable.getWriter();
    const reader = stream.readable.getReader();
    
    const chunks: Uint8Array[] = [];
    
    // Read compressed chunks
    const readPromise = (async () => {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }
    })();

    // Write content in chunks for large content
    const encoder = new TextEncoder();
    const chunkSize = 64 * 1024; // 64KB chunks
    
    for (let i = 0; i < content.length; i += chunkSize) {
      const chunk = content.slice(i, i + chunkSize);
      const encoded = encoder.encode(chunk);
      await writer.write(encoded);
    }
    
    await writer.close();
    await readPromise;

    // Combine all chunks
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;
    
    for (const chunk of chunks) {
      result.set(chunk, offset);
      offset += chunk.length;
    }

    return result;
  }

  /**
   * Decompress data using DecompressionStream API
   */
  private async decompressData(data: Uint8Array): Promise<string> {
    const stream = new DecompressionStream('gzip');
    const writer = stream.writable.getWriter();
    const reader = stream.readable.getReader();
    
    const chunks: Uint8Array[] = [];
    
    // Read decompressed chunks
    const readPromise = (async () => {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }
    })();

    // Write compressed data
    await writer.write(data);
    await writer.close();
    await readPromise;

    // Combine chunks and decode
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
    const combined = new Uint8Array(totalLength);
    let offset = 0;
    
    for (const chunk of chunks) {
      combined.set(chunk, offset);
      offset += chunk.length;
    }

    const decoder = new TextDecoder();
    return decoder.decode(combined);
  }

  /**
   * Update compression metrics
   */
  private updateCompressionMetrics(originalSize: number, compressedSize: number, time: number): void {
    this.metrics.totalCompressions++;
    this.metrics.totalBytesCompressed += originalSize;
    this.metrics.totalBytesSaved += (originalSize - compressedSize);
    
    // Update averages
    const totalTime = this.metrics.avgCompressionTime * (this.metrics.totalCompressions - 1) + time;
    this.metrics.avgCompressionTime = totalTime / this.metrics.totalCompressions;
    
    const totalRatio = this.metrics.avgCompressionRatio * (this.metrics.totalCompressions - 1) + (compressedSize / originalSize);
    this.metrics.avgCompressionRatio = totalRatio / this.metrics.totalCompressions;
  }

  /**
   * Update decompression metrics
   */
  private updateDecompressionMetrics(time: number): void {
    this.metrics.totalDecompressions++;
    
    const totalTime = this.metrics.avgDecompressionTime * (this.metrics.totalDecompressions - 1) + time;
    this.metrics.avgDecompressionTime = totalTime / this.metrics.totalDecompressions;
  }

  /**
   * Get current compression metrics
   */
  public getMetrics(): CompressionMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset metrics (useful for testing)
   */
  public resetMetrics(): void {
    this.metrics = {
      totalCompressions: 0,
      totalDecompressions: 0,
      totalBytesCompressed: 0,
      totalBytesSaved: 0,
      avgCompressionRatio: 0,
      avgCompressionTime: 0,
      avgDecompressionTime: 0,
      compressionErrors: 0,
      decompressionErrors: 0
    };
  }

  /**
   * Get storage savings summary
   */
  public getStorageSavings(): {
    totalSavings: number;
    totalOriginal: number;
    savingsPercentage: number;
    averageCompressionRatio: number;
  } {
    const totalOriginal = this.metrics.totalBytesCompressed;
    const totalSavings = this.metrics.totalBytesSaved;
    const savingsPercentage = totalOriginal > 0 ? (totalSavings / totalOriginal) * 100 : 0;
    
    return {
      totalSavings,
      totalOriginal,
      savingsPercentage,
      averageCompressionRatio: this.metrics.avgCompressionRatio
    };
  }

  /**
   * Update compression options
   */
  public updateOptions(options: Partial<CompressionOptions>): void {
    this.options = { ...this.options, ...options };
  }

  /**
   * Check if browser supports compression APIs
   */
  public static isSupported(): boolean {
    return typeof CompressionStream !== 'undefined' && typeof DecompressionStream !== 'undefined';
  }
}

// Export singleton instance
export const contentCompressionService = ContentCompressionService.getInstance();