/**
 * IndexedDB Service for Recall
 * 
 * Provides storage capabilities for traditional search:
 * - Page storage and retrieval
 * - Storage quota management with LRU cleanup
 * - Database migration support
 * - No AI functionality
 */

import { DBError } from '../models';
import {
  DB_CONFIG,
  DB_ERROR_CODES
} from './types';
import type {
  PageV3,
  StorageQuota,
  LRUCacheEntry,
  MigrationContext,
  IStorageService
} from './types';
import { createMigrationService } from './migrations';

/**
 * IndexedDB Service for Traditional Search
 * Implements the IStorageService interface without AI capabilities
 */
export class IndexedDBService implements IStorageService {
  private static instance: IndexedDBService | null = null;
  private db: IDBDatabase | null = null;
  private initPromise: Promise<void> | null = null;

  private constructor() {}

  /**
   * Get singleton instance
   */
  public static getInstance(): IndexedDBService {
    if (!IndexedDBService.instance) {
      IndexedDBService.instance = new IndexedDBService();
    }
    return IndexedDBService.instance;
  }

  /**
   * Initialize the database with migration support
   */
  public async init(): Promise<void> {
    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.initializeDatabase();
    return this.initPromise;
  }

  /**
   * Database initialization with migration handling
   */
  private async initializeDatabase(): Promise<void> {
    return new Promise((resolve, reject) => {
      const globalScope = typeof window !== 'undefined' ? window : globalThis;
      if (!('indexedDB' in globalScope)) {
        reject(new DBError(
          'IndexedDB is not supported in this environment',
          DB_ERROR_CODES.DB_NOT_AVAILABLE
        ));
        return;
      }

      const request = globalScope.indexedDB.open(DB_CONFIG.name, DB_CONFIG.version);

      request.onerror = () => {
        reject(new DBError(
          'Failed to open database',
          DB_ERROR_CODES.DB_NOT_AVAILABLE,
          request.error || undefined
        ));
      };

      request.onsuccess = () => {
        this.db = request.result;
        
        this.db.onclose = () => {
          console.warn('Database connection closed unexpectedly');
          this.db = null;
          this.initPromise = null;
        };

        resolve();
      };

      request.onupgradeneeded = async (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        const oldVersion = event.oldVersion;
        const newVersion = event.newVersion || DB_CONFIG.version;
        
        try {
          // Handle migration if needed
          if (oldVersion > 0 && oldVersion < newVersion) {
            const migrationService = createMigrationService(db);
            const context: MigrationContext = {
              fromVersion: oldVersion,
              toVersion: newVersion,
              onProgress: (progress, message) => {
                console.log(`Migration progress: ${progress}% - ${message}`);
              }
            };
            
            await migrationService.migrateToV3(context);
          } else {
            // Fresh installation
            this.createV3ObjectStores(db);
          }
        } catch (error) {
          reject(new DBError(
            'Failed to create/migrate database schema',
            DB_ERROR_CODES.MIGRATION_FAILED,
            error as Error
          ));
        }
      };
    });
  }

  /**
   * Create V3 object stores for fresh installation
   */
  private createV3ObjectStores(db: IDBDatabase): void {
    // Enhanced pages store
    if (!db.objectStoreNames.contains(DB_CONFIG.stores.pages)) {
      const pagesStore = db.createObjectStore(DB_CONFIG.stores.pages, {
        keyPath: 'id'
      });
      
      Object.entries(DB_CONFIG.indexes.pages).forEach(([name, keyPath]) => {
        const unique = keyPath === 'url';
        pagesStore.createIndex(name, keyPath, { unique });
      });
    }

    // AI-related stores removed for traditional search mode

    // LRU cache store
    if (!db.objectStoreNames.contains(DB_CONFIG.stores.lruCache)) {
      const lruStore = db.createObjectStore(DB_CONFIG.stores.lruCache, {
        keyPath: 'pageId'
      });
      
      Object.entries(DB_CONFIG.indexes.lruCache).forEach(([name, keyPath]) => {
        lruStore.createIndex(name, keyPath, { unique: false });
      });
    }

    // Settings store
    if (!db.objectStoreNames.contains(DB_CONFIG.stores.settings)) {
      db.createObjectStore(DB_CONFIG.stores.settings, {
        keyPath: 'key'
      });
    }

    // Blacklist store (carried over from V2)
    if (!db.objectStoreNames.contains(DB_CONFIG.stores.blacklist)) {
      db.createObjectStore(DB_CONFIG.stores.blacklist, {
        keyPath: 'domain'
      });
    }

    // Migrations store
    if (!db.objectStoreNames.contains(DB_CONFIG.stores.migrations)) {
      const migrationsStore = db.createObjectStore(DB_CONFIG.stores.migrations, {
        keyPath: 'version'
      });
      migrationsStore.createIndex('timestamp', 'timestamp', { unique: false });
    }
  }

  /**
   * Ensure database is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.db) {
      await this.init();
    }
    
    if (!this.db) {
      throw new DBError(
        'Database is not available',
        DB_ERROR_CODES.DB_NOT_AVAILABLE
      );
    }
  }

  /**
   * Promise wrapper for IndexedDB requests
   */
  private promiseRequest<T>(request: IDBRequest<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // ==================== Page Operations ====================

  /**
   * Add page for traditional search
   */
  public async addPage(pageData: Partial<PageV3>): Promise<PageV3> {
    await this.ensureInitialized();

    if (!pageData.url || !pageData.title || !pageData.content) {
      throw new DBError(
        'Missing required page fields: url, title, content',
        DB_ERROR_CODES.VALIDATION_FAILED
      );
    }

    const page: PageV3 = {
      id: pageData.id || this.generateId(),
      url: pageData.url,
      title: pageData.title,
      content: pageData.content,
      domain: pageData.domain || new URL(pageData.url).hostname,
      visitTime: pageData.visitTime || Date.now(),
      accessCount: pageData.accessCount || 1,
      lastUpdated: Date.now(),
      language: pageData.language,
      summary: pageData.summary,
      importance: pageData.importance || 3,
      isFavorite: pageData.isFavorite || false,
      readingTime: pageData.readingTime,
      contentHash: this.generateContentHash(pageData.content)
    };

    const transaction = this.db!.transaction([DB_CONFIG.stores.pages, DB_CONFIG.stores.lruCache], 'readwrite');
    const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
    const lruStore = transaction.objectStore(DB_CONFIG.stores.lruCache);

    try {
      // Check if page already exists
      const urlIndex = pagesStore.index('url');
      const existingPage = await this.promiseRequest(urlIndex.get(page.url));

      if (existingPage) {
        // Update existing page
        const updatedPage = {
          ...existingPage,
          title: page.title,
          content: page.content,
          accessCount: existingPage.accessCount + 1,
          lastUpdated: page.lastUpdated,
          contentHash: page.contentHash
        };
        
        await this.promiseRequest(pagesStore.put(updatedPage));
        
        // Update LRU cache
        await this.updateLRUEntry(updatedPage.id, lruStore);
        
        return updatedPage;
      } else {
        // Add new page
        await this.promiseRequest(pagesStore.add(page));
        
        // Add to LRU cache
        await this.addLRUEntry(page, lruStore);
        
        return page;
      }
    } catch (error) {
      throw new DBError(
        'Failed to add page',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Get page by ID
   */
  public async getPage(id: string): Promise<PageV3 | null> {
    await this.ensureInitialized();

    const transaction = this.db!.transaction([DB_CONFIG.stores.pages], 'readonly');
    const store = transaction.objectStore(DB_CONFIG.stores.pages);

    try {
      return await this.promiseRequest(store.get(id));
    } catch (error) {
      throw new DBError(
        'Failed to get page',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Update page
   */
  public async updatePage(id: string, updates: Partial<PageV3>): Promise<PageV3> {
    await this.ensureInitialized();

    const transaction = this.db!.transaction([DB_CONFIG.stores.pages, DB_CONFIG.stores.lruCache], 'readwrite');
    const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
    const lruStore = transaction.objectStore(DB_CONFIG.stores.lruCache);

    try {
      const existingPage = await this.promiseRequest(pagesStore.get(id));
      if (!existingPage) {
        throw new DBError('Page not found', DB_ERROR_CODES.ITEM_NOT_FOUND);
      }

      const updatedPage: PageV3 = {
        ...existingPage,
        ...updates,
        lastUpdated: Date.now()
      };

      await this.promiseRequest(pagesStore.put(updatedPage));
      await this.updateLRUEntry(id, lruStore);

      return updatedPage;
    } catch (error) {
      throw new DBError(
        'Failed to update page',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Delete page and associated data
   */
  public async deletePage(id: string): Promise<boolean> {
    await this.ensureInitialized();

    const transaction = this.db!.transaction([
      DB_CONFIG.stores.pages,
      DB_CONFIG.stores.lruCache
    ], 'readwrite');

    try {
      const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
      const lruStore = transaction.objectStore(DB_CONFIG.stores.lruCache);

      // Check if page exists
      const page = await this.promiseRequest(pagesStore.get(id));
      if (!page) {
        return false;
      }

      // Delete page
      await this.promiseRequest(pagesStore.delete(id));

      // Delete LRU entry
      await this.promiseRequest(lruStore.delete(id));

      return true;
    } catch (error) {
      throw new DBError(
        'Failed to delete page',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  // ==================== AI Operations Removed ====================

  // ==================== Knowledge Center Operations Removed ====================

  // ==================== API Key Operations Removed ====================

  // ==================== Storage Management ====================

  /**
   * Get current storage quota status
   */
  public async getStorageQuota(): Promise<StorageQuota> {
    await this.ensureInitialized();

    try {
      const storageInfo = await this.calculateStorageUsage();
      
      return {
        maxStorage: DB_CONFIG.maxStorageBytes,
        currentUsage: storageInfo.totalSize,
        breakdown: storageInfo.breakdown,
        warningThreshold: DB_CONFIG.maxStorageBytes * 0.9
      };
    } catch (error) {
      throw new DBError(
        'Failed to get storage quota',
        DB_ERROR_CODES.TRANSACTION_FAILED,
        error as Error
      );
    }
  }

  /**
   * Clean up storage using LRU strategy
   */
  public async cleanupStorage(): Promise<number> {
    await this.ensureInitialized();

    const quota = await this.getStorageQuota();
    
    if (quota.currentUsage <= quota.warningThreshold) {
      return 0; // No cleanup needed
    }

    try {
      const targetSize = DB_CONFIG.maxStorageBytes * 0.8; // Clean up to 80%
      const toCleanup = quota.currentUsage - targetSize;
      
      return await this.performLRUCleanup(toCleanup);
    } catch (error) {
      throw new DBError(
        'Failed to cleanup storage',
        DB_ERROR_CODES.LRU_CLEANUP_FAILED,
        error as Error
      );
    }
  }

  /**
   * Migrate from previous version
   */
  public async migrateFrom(context: MigrationContext): Promise<void> {
    await this.ensureInitialized();
    
    const migrationService = createMigrationService(this.db!);
    await migrationService.migrateToV3(context);
  }

  /**
   * Health check
   */
  public async healthCheck(): Promise<{ isHealthy: boolean; issues: string[] }> {
    const issues: string[] = [];

    try {
      await this.ensureInitialized();

      if (!this.db) {
        issues.push('Database connection is not available');
        return { isHealthy: false, issues };
      }

      // Check object stores
      const requiredStores = Object.values(DB_CONFIG.stores);
      for (const store of requiredStores) {
        if (!this.db.objectStoreNames.contains(store)) {
          issues.push(`Missing object store: ${store}`);
        }
      }

      // Check storage quota
      try {
        const quota = await this.getStorageQuota();
        if (quota.currentUsage > quota.maxStorage) {
          issues.push('Storage quota exceeded');
        }
      } catch (error) {
        issues.push('Failed to check storage quota');
      }

    } catch (error) {
      issues.push(`Database health check failed: ${(error as Error).message}`);
    }

    return {
      isHealthy: issues.length === 0,
      issues
    };
  }

  /**
   * Close database connection
   */
  public close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.initPromise = null;
    }
  }

  // ==================== Private Helper Methods ====================

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate content hash
   */
  private generateContentHash(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return hash.toString(36);
  }

  // AI-related helper methods removed

  /**
   * Calculate current storage usage
   */
  private async calculateStorageUsage(): Promise<{
    totalSize: number;
    breakdown: StorageQuota['breakdown'];
  }> {
    // This is a simplified calculation
    // In production, you might want to use the Storage API for more accurate measurements
    
    const breakdown = {
      pages: 0,
      settings: 0,
      cache: 0
    };

    const transaction = this.db!.transaction(Object.values(DB_CONFIG.stores), 'readonly');
    
    // Estimate sizes (simplified)
    const pagesStore = transaction.objectStore(DB_CONFIG.stores.pages);
    const pagesCount = await this.promiseRequest(pagesStore.count());
    breakdown.pages = pagesCount * 5000; // ~5KB per page estimate

    // Settings and cache stores
    const settingsStore = transaction.objectStore(DB_CONFIG.stores.settings);
    const settingsCount = await this.promiseRequest(settingsStore.count());
    breakdown.settings = settingsCount * 100; // Small estimate for settings

    const lruStore = transaction.objectStore(DB_CONFIG.stores.lruCache);
    const cacheCount = await this.promiseRequest(lruStore.count());
    breakdown.cache = cacheCount * 200; // Small estimate for cache entries

    const totalSize = Object.values(breakdown).reduce((sum, size) => sum + size, 0);

    return { totalSize, breakdown };
  }

  /**
   * Add LRU cache entry
   */
  private async addLRUEntry(page: PageV3, lruStore: IDBObjectStore): Promise<void> {
    const entry: LRUCacheEntry = {
      pageId: page.id,
      lastAccessed: Date.now(),
      accessCount: 1,
      storageSize: this.estimatePageSize(page),
      isProtected: page.isFavorite || false
    };

    await this.promiseRequest(lruStore.put(entry));
  }

  /**
   * Update LRU cache entry
   */
  private async updateLRUEntry(pageId: string, lruStore: IDBObjectStore): Promise<void> {
    const existing = await this.promiseRequest(lruStore.get(pageId));
    if (existing) {
      existing.lastAccessed = Date.now();
      existing.accessCount += 1;
      await this.promiseRequest(lruStore.put(existing));
    }
  }

  /**
   * Estimate page storage size
   */
  private estimatePageSize(page: PageV3): number {
    const baseSize = JSON.stringify(page).length * 2; // UTF-16 encoding
    return baseSize;
  }

  /**
   * Perform LRU cleanup
   */
  private async performLRUCleanup(targetCleanupSize: number): Promise<number> {
    const transaction = this.db!.transaction([
      DB_CONFIG.stores.lruCache,
      DB_CONFIG.stores.pages
    ], 'readwrite');

    const lruStore = transaction.objectStore(DB_CONFIG.stores.lruCache);
    const index = lruStore.index('lastAccessed');
    
    let cleanedSize = 0;
    const toDelete: string[] = [];

    return new Promise((resolve, reject) => {
      const request = index.openCursor(null, 'next'); // Oldest first

      request.onsuccess = async (event) => {
        const cursor = (event.target as IDBRequest).result;

        if (!cursor || cleanedSize >= targetCleanupSize) {
          // Delete collected pages
          for (const pageId of toDelete) {
            await this.deletePage(pageId);
          }
          resolve(cleanedSize);
          return;
        }

        const entry = cursor.value as LRUCacheEntry;
        
        // Don't delete protected pages
        if (!entry.isProtected) {
          toDelete.push(entry.pageId);
          cleanedSize += entry.storageSize;
        }

        cursor.continue();
      };

      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Reset service instance (for testing)
   */
  public static reset(): void {
    if (IndexedDBService.instance) {
      IndexedDBService.instance.close();
      IndexedDBService.instance = null;
    }
  }
}

// Export singleton instance
export const dbService = IndexedDBService.getInstance();