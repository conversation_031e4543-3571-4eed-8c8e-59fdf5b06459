/**
 * @fileoverview Debounce utility for configuration input handling
 * Provides 500ms debounce functionality for form inputs and API calls
 * 
 * <AUTHOR> 3 - Configuration UX Team
 * @version 1.0.0
 */

/**
 * Type definition for debounced function
 */
export type DebouncedFunction<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): void;
  cancel: () => void;
};

/**
 * Creates a debounced function that delays invoking the provided function
 * until after the specified delay has elapsed since the last time it was invoked.
 * 
 * This is particularly useful for configuration inputs where we want to
 * validate or save settings only after the user has stopped typing.
 * 
 * @param func - The function to debounce
 * @param delay - The number of milliseconds to delay (default: 500ms)
 * @returns A debounced version of the provided function
 * 
 * @example
 * ```typescript
 * const debouncedSave = debounce((value: string) => {
 *   console.log('Saving:', value);
 * }, 500);
 * 
 * // These calls will be debounced
 * debouncedSave('a');     // Will be cancelled
 * debouncedSave('ab');    // Will be cancelled  
 * debouncedSave('abc');   // Will execute after 500ms
 * ```
 * 
 * @example
 * ```typescript
 * // With configuration form
 * const debouncedValidate = debounce((formData: FormData) => {
 *   validateConfiguration(formData);
 * }, 500);
 * 
 * // In component
 * const handleInputChange = (value: string) => {
 *   debouncedValidate(new FormData(formElement));
 * };
 * ```
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number = 500
): DebouncedFunction<T> {
  // Validate input function
  if (typeof func !== 'function') {
    throw new TypeError('Expected a function as the first argument');
  }

  // Normalize delay to prevent negative values
  const normalizedDelay = Math.max(0, delay);

  let timeoutId: NodeJS.Timeout | null = null;

  const debouncedFunction = (...args: Parameters<T>): void => {
    // Clear any existing timeout
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
    }

    // Set new timeout
    timeoutId = setTimeout(() => {
      timeoutId = null;
      func.apply(undefined, args);
    }, normalizedDelay);
  };

  // Add cancel method to manually cancel pending execution
  debouncedFunction.cancel = (): void => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  return debouncedFunction;
}

/**
 * Creates a debounced function specifically for configuration inputs
 * with pre-configured 500ms delay and input validation.
 * 
 * @param validationFn - Function to call for input validation
 * @returns Debounced validation function
 * 
 * @example
 * ```typescript
 * const validateApiKey = (key: string) => {
 *   if (key.length < 10) {
 *     showError('API key too short');
 *   } else {
 *     clearError();
 *   }
 * };
 * 
 * const debouncedValidation = createConfigInputDebounce(validateApiKey);
 * 
 * // Use in form handler
 * inputElement.addEventListener('input', (e) => {
 *   debouncedValidation(e.target.value);
 * });
 * ```
 */
export function createConfigInputDebounce<T extends (...args: any[]) => any>(
  validationFn: T
): DebouncedFunction<T> {
  return debounce(validationFn, 500);
}

/**
 * Default export for convenience
 */
export default debounce;