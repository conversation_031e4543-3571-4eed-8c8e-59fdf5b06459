/**
 * Centralized logging utility
 * 
 * Provides consistent logging across the application with proper prefixes,
 * log levels, and debug mode support.
 */

export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
} as const;

export type LogLevel = typeof LogLevel[keyof typeof LogLevel];

export interface LoggerConfig {
  level: LogLevel;
  prefix: string;
  enableTimestamp: boolean;
  enableStackTrace: boolean;
}

class Logger {
  private config: LoggerConfig;

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      prefix: '[Recall]',
      enableTimestamp: true,
      enableStackTrace: false,
      ...config
    };
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.config.level;
  }

  private formatMessage(level: string, message: unknown[], error?: Error): string {
    const timestamp = this.config.enableTimestamp ? new Date().toISOString() : '';
    const prefix = `${timestamp} ${this.config.prefix}[${level}]`;

    // Safely serialize message objects to avoid circular reference issues
    const messageText = message.map(m => {
      if (typeof m === 'object' && m !== null) {
        try {
          // For Error objects, use their message and name
          if (m instanceof Error) {
            return `${m.name}: ${m.message}`;
          }
          // For other objects, try JSON.stringify with error handling
          return JSON.stringify(m);
        } catch (error) {
          // Fallback for objects that can't be stringified (circular refs, etc.)
          return `[Object: ${Object.prototype.toString.call(m)}]`;
        }
      }
      return String(m);
    }).join(' ');

    let result = `${prefix} ${messageText}`;

    if (error && this.config.enableStackTrace && error.stack) {
      result += `\n${error.stack}`;
    }

    return result;
  }

  debug(...message: unknown[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.log(this.formatMessage('DEBUG', message));
    }
  }

  info(...message: unknown[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      console.log(this.formatMessage('INFO', message));
    }
  }

  warn(...message: unknown[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      console.warn(this.formatMessage('WARN', message));
    }
  }

  error(...message: unknown[]): void;
  error(error: Error, ...message: unknown[]): void;
  error(errorOrMessage: Error | unknown, ...message: unknown[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      if (errorOrMessage instanceof Error) {
        // Safely handle error object that might not have all properties
        const safeError = errorOrMessage.stack ? errorOrMessage : undefined;
        console.error(this.formatMessage('ERROR', message, safeError));
      } else {
        console.error(this.formatMessage('ERROR', [errorOrMessage, ...message]));
      }
    }
  }

  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  createChild(prefix: string): Logger {
    return new Logger({
      ...this.config,
      prefix: `${this.config.prefix}[${prefix}]`
    });
  }
}

// Default logger instance
export const logger = new Logger();

// Specialized loggers for different modules
export const dbLogger = logger.createChild('DB');
export const searchLogger = logger.createChild('Search');
export const contentLogger = logger.createChild('Content');
export const backgroundLogger = logger.createChild('Background');

// Development mode detection
const isDev = process.env.NODE_ENV === 'development';
if (isDev) {
  logger.setLevel(LogLevel.DEBUG);
}

export default logger;