/**
 * Centralized error handling utilities
 * 
 * Provides consistent error handling patterns, error reporting,
 * and recovery mechanisms across the application.
 */

import { logger } from './logger';

export const ErrorCode = {
  // Database errors
  DB_CONNECTION_FAILED: 'DB_CONNECTION_FAILED',
  DB_QUOTA_EXCEEDED: 'DB_QUOTA_EXCEEDED',
  DB_OPERATION_FAILED: 'DB_OPERATION_FAILED',
  
  // Search errors
  SEARCH_INDEX_CORRUPTED: 'SEARCH_INDEX_CORRUPTED',
  SEARCH_TIMEOUT: 'SEARCH_TIMEOUT',
  SEARCH_INVALID_QUERY: 'SEARCH_INVALID_QUERY',
  
  // AI errors
  AI_MODEL_LOAD_FAILED: 'AI_MODEL_LOAD_FAILED',
  AI_INFERENCE_FAILED: 'AI_INFERENCE_FAILED',
  AI_TIMEOUT: 'AI_TIMEOUT',
  
  // Security errors
  ENCRYPTION_FAILED: 'ENCRYPTION_FAILED',
  DECRYPTION_FAILED: 'DECRYPTION_FAILED',
  K<PERSON><PERSON>_DERIVATION_FAILED: 'KEY_DERIVATION_FAILED',
  
  // Network errors
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  NETWORK_UNAVAILABLE: 'NETWORK_UNAVAILABLE',
  API_KEY_INVALID: 'API_KEY_INVALID',
  
  // General errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED'
} as const;

export type ErrorCode = typeof ErrorCode[keyof typeof ErrorCode];

export interface ErrorInfo {
  code: ErrorCode;
  message: string;
  details?: unknown;
  timestamp: number;
  stack?: string;
  recoverable: boolean;
}

export class AppError extends Error {
  public readonly info: ErrorInfo;

  constructor(code: ErrorCode, message: string, details?: unknown, recoverable = false) {
    super(message);
    this.name = 'AppError';

    this.info = {
      code,
      message,
      details,
      timestamp: Date.now(),
      stack: this.stack || 'No stack trace available',
      recoverable
    };
  }

  static fromError(error: Error, code = ErrorCode.UNKNOWN_ERROR): AppError {
    if (error instanceof AppError) {
      return error;
    }
    
    return new AppError(code, error.message, { originalError: error });
  }

  toJSON(): ErrorInfo {
    return this.info;
  }
}

export interface ErrorHandler {
  (error: AppError): Promise<void> | void;
}

class ErrorManager {
  private handlers: Map<ErrorCode, ErrorHandler[]> = new Map();
  private globalHandlers: ErrorHandler[] = [];

  // Register error handler for specific error codes
  onError(codes: ErrorCode | ErrorCode[], handler: ErrorHandler): void {
    const codeArray = Array.isArray(codes) ? codes : [codes];
    
    for (const code of codeArray) {
      if (!this.handlers.has(code)) {
        this.handlers.set(code, []);
      }
      this.handlers.get(code)!.push(handler);
    }
  }

  // Register global error handler
  onAnyError(handler: ErrorHandler): void {
    this.globalHandlers.push(handler);
  }

  // Handle error with appropriate handlers
  async handle(error: Error | AppError): Promise<void> {
    const appError = error instanceof AppError ? error : AppError.fromError(error);

    // Log the error - safely handle potentially undefined properties
    const errorCode = appError.info?.code || 'UNKNOWN';
    const errorMessage = appError.message || 'Unknown error';
    logger.error(appError, `[${errorCode}] ${errorMessage}`);

    // Call specific handlers
    const specificHandlers = this.handlers.get(appError.info.code) || [];
    for (const handler of specificHandlers) {
      try {
        await handler(appError);
      } catch (handlerError) {
        logger.error(handlerError as Error, 'Error in error handler');
      }
    }

    // Call global handlers
    for (const handler of this.globalHandlers) {
      try {
        await handler(appError);
      } catch (handlerError) {
        logger.error(handlerError as Error, 'Error in global error handler');
      }
    }
  }
}

export const errorManager = new ErrorManager();

// Utility functions for common error handling patterns
export async function safeAsync<T>(
  fn: () => Promise<T>,
  fallback: T,
  errorCode = ErrorCode.UNKNOWN_ERROR
): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    await errorManager.handle(new AppError(errorCode, String(error)));
    return fallback;
  }
}

export function safeSync<T>(
  fn: () => T,
  fallback: T,
  errorCode = ErrorCode.UNKNOWN_ERROR
): T {
  try {
    return fn();
  } catch (error) {
    errorManager.handle(new AppError(errorCode, String(error))).catch(() => {
      // Ignore handler errors in sync context
    });
    return fallback;
  }
}

// Higher-order function for error handling (replaces decorator for compatibility)
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  errorCode: ErrorCode
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      await errorManager.handle(new AppError(errorCode, String(error)));
      throw error;
    }
  }) as T;
}

// Setup global error handlers
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    // Safely extract error message with proper null checking
    const errorMessage = event.error?.message || event.message || 'Unknown error';
    errorManager.handle(new AppError(ErrorCode.UNKNOWN_ERROR, errorMessage))
      .catch(() => {
        // Final fallback - safely handle event.error
        const fallbackError = event.error || new Error(errorMessage);
        console.error('Failed to handle error:', fallbackError);
      });
  });

  window.addEventListener('unhandledrejection', (event) => {
    // Safely convert reason to string
    const reasonString = event.reason ? String(event.reason) : 'Unknown rejection reason';
    errorManager.handle(new AppError(ErrorCode.UNKNOWN_ERROR, reasonString))
      .catch(() => {
        // Final fallback
        console.error('Failed to handle rejection:', event.reason);
      });
  });
}