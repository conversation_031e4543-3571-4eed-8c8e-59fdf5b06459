/**
 * @fileoverview Tests for formatting utility functions
 */

import { formatBytes, formatNumber, formatTime, formatPercentage } from './formatting';

describe('formatBytes', () => {
  it('should format bytes correctly', () => {
    expect(formatBytes(0)).toBe('0 B');
    expect(formatBytes(1024)).toBe('1.00 KB');
    expect(formatBytes(1536, 1)).toBe('1.5 KB');
    expect(formatBytes(1048576)).toBe('1.00 MB');
    expect(formatBytes(1073741824)).toBe('1.00 GB');
    expect(formatBytes(1099511627776)).toBe('1.00 TB');
  });

  it('should handle small values', () => {
    expect(formatBytes(512)).toBe('512 B');
    expect(formatBytes(100)).toBe('100 B');
    expect(formatBytes(1)).toBe('1 B');
  });

  it('should handle large values', () => {
    expect(formatBytes(5000000000)).toBe('4.66 GB');
    expect(formatBytes(500000000)).toBe('476.84 MB');
  });

  it('should respect decimal places', () => {
    expect(formatBytes(1536, 0)).toBe('2 KB');
    expect(formatBytes(1536, 3)).toBe('1.500 KB');
  });
});

describe('formatNumber', () => {
  it('should format numbers correctly', () => {
    expect(formatNumber(0)).toBe('0');
    expect(formatNumber(999)).toBe('999');
    expect(formatNumber(1234)).toBe('1.2K');
    expect(formatNumber(1234567)).toBe('1.2M');
    expect(formatNumber(1234567890)).toBe('1.2B');
  });
});

describe('formatTime', () => {
  it('should format milliseconds correctly', () => {
    expect(formatTime(500)).toBe('500ms');
    expect(formatTime(1500)).toBe('1s');
    expect(formatTime(65000)).toBe('1m 5s');
    expect(formatTime(3661000)).toBe('1h 1m 1s');
  });
});

describe('formatPercentage', () => {
  it('should format percentages correctly', () => {
    expect(formatPercentage(0.756)).toBe('75.6%');
    expect(formatPercentage(0.5, 0)).toBe('50%');
    expect(formatPercentage(1)).toBe('100.0%');
  });
});