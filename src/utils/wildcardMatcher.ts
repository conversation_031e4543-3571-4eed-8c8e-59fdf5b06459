export interface ConflictResult {
  isValid: boolean;
  conflicts: Array<{
    pattern1: string;
    pattern2: string;
    reason: string;
  }>;
}

export class WildcardMatcher {
  /**
   * Matches a domain against a wildcard pattern
   * @param pattern - The wildcard pattern (e.g., "*.example.com")
   * @param domain - The domain to match (e.g., "www.example.com")
   * @returns true if the domain matches the pattern
   */
  match(pattern: string | null | undefined, domain: string | null | undefined): boolean {
    // Handle null/undefined inputs
    if (!pattern || !domain) {
      return false;
    }

    // Handle empty strings
    if (pattern.trim() === '' || domain.trim() === '') {
      return false;
    }

    // Normalize inputs
    const normalizedPattern = this.normalizePattern(pattern);
    const normalizedDomain = this.normalizeDomain(domain);

    // Validate pattern
    if (!this.isValidPattern(normalizedPattern)) {
      throw new Error('Invalid wildcard pattern');
    }

    // Exact match
    if (normalizedPattern === normalizedDomain) {
      return true;
    }

    // Convert wildcard pattern to regex
    const regex = this.patternToRegex(normalizedPattern);
    return regex.test(normalizedDomain);
  }

  /**
   * Validates if a pattern is syntactically correct
   * @param pattern - The pattern to validate
   * @returns true if the pattern is valid
   */
  isValidPattern(pattern: string): boolean {
    if (!pattern || pattern.trim() === '') {
      return false;
    }

    const trimmed = pattern.trim().toLowerCase();

    // Check for invalid characters or patterns
    if (
      trimmed === '*' ||
      trimmed.includes('**') ||
      trimmed.includes('..') ||
      trimmed.startsWith('.') ||
      trimmed.endsWith('.')
    ) {
      return false;
    }

    // Pattern must contain at least one domain part
    const parts = trimmed.split('.');
    if (parts.length < 2) {
      return false;
    }

    // Each part should be valid (either * or a valid domain part)
    for (const part of parts) {
      if (part === '') {
        return false;
      }
      // Allow * or alphanumeric with hyphens and unicode characters for international domains
      if (part !== '*' && !/^[a-z0-9-\u00a1-\uffff]+$/.test(part)) {
        return false;
      }
    }

    // Check for invalid wildcard placement patterns like "example.*.*"
    // Consecutive wildcards are only invalid if they come after a specific part
    // Leading consecutive wildcards like "*.*.example.com" are valid
    
    // Check for specific invalid patterns like "example.*.*"
    // The rule: if you have a specific (non-wildcard) part followed by multiple wildcards, it's invalid
    // But patterns like "*.*.example.com" or "*.example.*" are valid
    for (let i = 0; i < parts.length - 2; i++) {
      if (parts[i] !== '*') {
        // Found a specific part, check if followed by multiple wildcards
        let consecutiveWildcards = 0;
        for (let j = i + 1; j < parts.length; j++) {
          if (parts[j] === '*') {
            consecutiveWildcards++;
          } else {
            break; // Hit a non-wildcard, stop counting
          }
        }
        
        // If we have a specific part followed by 2 or more consecutive wildcards, it's invalid
        // e.g., "example.*.*" but not "example.*.com"
        if (consecutiveWildcards >= 2) {
          return false;
        }
      }
    }
    
    return true;
  }

  /**
   * Checks if two patterns conflict with each other
   * @param pattern1 - First pattern
   * @param pattern2 - Second pattern
   * @returns true if patterns conflict
   */
  hasConflict(pattern1: string, pattern2: string): boolean {
    // Normalize patterns
    const p1 = this.normalizePattern(pattern1);
    const p2 = this.normalizePattern(pattern2);

    // Same patterns always conflict
    if (p1 === p2) {
      return true;
    }

    try {
      const regex1 = this.patternToRegex(p1);
      const regex2 = this.patternToRegex(p2);
      
      // Check for domain space conflicts (same base domain with different wildcard levels)
      // Extract base domain parts to see if they overlap
      const parts1 = p1.split('.');
      const parts2 = p2.split('.');
      
      // Check if one pattern could be a more specific version of the other
      // e.g., "example.com" vs "*.example.com" or "api.example.com" vs "*.example.com"
      if (this.hasBaseDomainConflict(parts1, parts2)) {
        return true;
      }
      
      // Direct pattern overlap check: if one pattern matches the other as a domain
      // Check if pattern1 (when treated as a domain) matches pattern2's regex
      if (!p1.includes('*') && regex2.test(p1)) {
        return true;
      }
      
      // Check if pattern2 (when treated as a domain) matches pattern1's regex
      if (!p2.includes('*') && regex1.test(p2)) {
        return true;
      }
      
      // For wildcard vs wildcard conflicts, check domain overlap
      if (p1.includes('*') && p2.includes('*')) {
        // Check if the patterns have overlapping domain spaces
        const testDomains1 = this.generateTestDomains(p1);
        const testDomains2 = this.generateTestDomains(p2);
        
        // Check if any test domain from pattern1 matches pattern2
        for (const domain of testDomains1) {
          if (regex2.test(domain)) {
            return true;
          }
        }
        
        // Check if any test domain from pattern2 matches pattern1
        for (const domain of testDomains2) {
          if (regex1.test(domain)) {
            return true;
          }
        }
      }
      
      return false;
    } catch {
      return false;
    }
  }

  /**
   * Validates a set of rules for conflicts
   * @param rules - Array of patterns to validate
   * @returns validation result with conflicts
   */
  validateRuleSet(rules: string[]): ConflictResult {
    const conflicts: ConflictResult['conflicts'] = [];

    for (let i = 0; i < rules.length; i++) {
      for (let j = i + 1; j < rules.length; j++) {
        if (this.hasConflict(rules[i], rules[j])) {
          conflicts.push({
            pattern1: rules[i],
            pattern2: rules[j],
            reason: 'Patterns overlap or conflict'
          });
        }
      }
    }

    return {
      isValid: conflicts.length === 0,
      conflicts
    };
  }

  /**
   * Checks if two patterns have conflicting base domains
   * @param parts1 - First pattern parts
   * @param parts2 - Second pattern parts
   * @returns true if they conflict
   */
  private hasBaseDomainConflict(parts1: string[], parts2: string[]): boolean {
    // Extract the base domain parts (non-wildcard parts)
    const baseParts1 = parts1.filter(part => part !== '*');
    const baseParts2 = parts2.filter(part => part !== '*');
    
    // If either pattern has no base parts, no conflict
    if (baseParts1.length === 0 || baseParts2.length === 0) {
      return false;
    }
    
    // Check if the base domain parts overlap
    // For example, "example.com" and "*.example.com" both have base "example.com"
    const base1 = baseParts1.join('.');
    const base2 = baseParts2.join('.');
    
    // If the base domains are exactly the same, they conflict
    if (base1 === base2) {
      return true;
    }
    
    // Check if one is a subdomain of the other
    // e.g., "api.example.com" vs "*.example.com" where "example.com" is the common base
    if (base1.endsWith('.' + base2) || base2.endsWith('.' + base1)) {
      return true;
    }
    
    return false;
  }

  /**
   * Normalizes a pattern for consistent processing
   * @param pattern - The pattern to normalize
   * @returns normalized pattern
   */
  private normalizePattern(pattern: string): string {
    return pattern.trim().toLowerCase();
  }

  /**
   * Normalizes a domain for consistent processing
   * @param domain - The domain to normalize
   * @returns normalized domain
   */
  private normalizeDomain(domain: string): string {
    return domain.trim().toLowerCase();
  }

  /**
   * Converts a wildcard pattern to a regular expression
   * @param pattern - The wildcard pattern
   * @returns RegExp object
   */
  private patternToRegex(pattern: string): RegExp {
    // Escape special regex characters except *
    let regexPattern = pattern.replace(/[.+?^${}()|[\]\\]/g, '\\$&');
    
    // Replace * with regex equivalent
    // * should match one or more characters that are not dots
    regexPattern = regexPattern.replace(/\*/g, '[^.]+');
    
    // Anchor the pattern
    return new RegExp(`^${regexPattern}$`);
  }


  /**
   * Generates test domains from a pattern for conflict testing
   * @param pattern - The pattern to generate test domains from
   * @returns array of test domains
   */
  private generateTestDomains(pattern: string): string[] {
    const parts = pattern.split('.');
    const testDomains: string[] = [];
    
    // Replace wildcards with common subdomains
    const commonSubdomains = ['www', 'api', 'mail', 'cdn'];
    
    function generateVariations(parts: string[], index: number, current: string[]): void {
      if (index >= parts.length) {
        testDomains.push(current.join('.'));
        return;
      }
      
      const part = parts[index];
      if (part === '*') {
        // Try each common subdomain
        for (const subdomain of commonSubdomains) {
          generateVariations(parts, index + 1, [...current, subdomain]);
        }
      } else {
        generateVariations(parts, index + 1, [...current, part]);
      }
    }
    
    generateVariations(parts, 0, []);
    return testDomains.slice(0, 10); // Limit to prevent explosion
  }
}