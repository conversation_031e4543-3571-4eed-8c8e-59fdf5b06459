/**
 * @fileoverview Formatting utility functions
 * Provides functions for formatting various data types including file sizes, numbers, etc.
 */

/**
 * Formats bytes to human-readable string with appropriate units (B, KB, MB, GB, TB)
 * @param bytes - Number of bytes to format
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted string with unit
 * 
 * @example
 * formatBytes(1024) // "1.00 KB"
 * formatBytes(1536, 1) // "1.5 KB"
 * formatBytes(1048576) // "1.00 MB"
 * formatBytes(0) // "0 B"
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  // For bytes (i=0), don't show decimals
  if (i === 0) {
    return Math.round(bytes) + ' ' + sizes[i];
  }
  
  return (bytes / Math.pow(k, i)).toFixed(dm) + ' ' + sizes[i];
}

/**
 * Formats a number with K/M/B suffixes for large numbers
 * @param num - Number to format
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted string with suffix
 * 
 * @example
 * formatNumber(1234) // "1.2K"
 * formatNumber(1234567) // "1.2M"
 * formatNumber(1234567890) // "1.2B"
 */
export function formatNumber(num: number, decimals: number = 1): string {
  if (num === 0) return '0';
  
  const k = 1000;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['', 'K', 'M', 'B', 'T'];
  
  if (num < k) return num.toString();
  
  const i = Math.floor(Math.log(num) / Math.log(k));
  
  return parseFloat((num / Math.pow(k, i)).toFixed(dm)) + sizes[i];
}

/**
 * Formats time in milliseconds to human-readable string
 * @param ms - Milliseconds to format
 * @returns Formatted time string
 * 
 * @example
 * formatTime(1500) // "1.5s"
 * formatTime(65000) // "1m 5s"
 * formatTime(3661000) // "1h 1m 1s"
 */
export function formatTime(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    const remainingMinutes = minutes % 60;
    const remainingSeconds = seconds % 60;
    return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}${remainingSeconds > 0 ? ` ${remainingSeconds}s` : ''}`;
  } else if (minutes > 0) {
    const remainingSeconds = seconds % 60;
    return `${minutes}m${remainingSeconds > 0 ? ` ${remainingSeconds}s` : ''}`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Formats a percentage with proper decimal places
 * @param value - Value to format as percentage (0-1 range)
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted percentage string
 * 
 * @example
 * formatPercentage(0.756) // "75.6%"
 * formatPercentage(0.5, 0) // "50%"
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return (value * 100).toFixed(decimals) + '%';
}