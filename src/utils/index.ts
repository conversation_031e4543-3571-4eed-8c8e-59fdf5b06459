/**
 * @fileoverview Utility functions export index
 * <AUTHOR> 3 - Configuration UX Team
 */

export { debounce, createConfigInputDebounce, type DebouncedFunction } from './debounce';
export { 
  logger, 
  dbLogger, 
  searchLogger, 
  contentLogger, 
  backgroundLogger,
  LogLevel,
  type LoggerConfig 
} from './logger';
export {
  AppError,
  ErrorCode,
  errorManager,
  safeAsync,
  safeSync,
  withErrorHandling,
  type ErrorInfo,
  type ErrorHandler
} from './errorHandler';
export {
  parallelLimit,
  batchProcess,
  withTimeout,
  withRetry,
  lazyMap,
  asyncDebounce,
  measurePerformance,
  MemoryTracker,
  LRUCache,
  DEFAULT_PERF_CONFIG,
  type PerformanceConfig
} from './performance';
export {
  formatBytes,
  formatNumber,
  formatTime,
  formatPercentage
} from './formatting';