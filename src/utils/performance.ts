/**
 * Performance optimization utilities
 * 
 * Provides utilities for optimizing async operations, memory usage,
 * and computational performance across the application.
 */

import { logger } from './logger';

export interface PerformanceConfig {
  timeout: number;
  retries: number;
  concurrency: number;
  batchSize: number;
}

export const DEFAULT_PERF_CONFIG: PerformanceConfig = {
  timeout: 5000,
  retries: 3,
  concurrency: 5,
  batchSize: 100
};

/**
 * Execute functions in parallel with concurrency limit
 */
export async function parallelLimit<T, R>(
  items: T[],
  fn: (item: T) => Promise<R>,
  limit = DEFAULT_PERF_CONFIG.concurrency
): Promise<R[]> {
  const results: R[] = [];
  const executing: Promise<void>[] = [];

  for (const item of items) {
    const promise = fn(item).then(result => {
      results.push(result);
    });

    executing.push(promise);

    if (executing.length >= limit) {
      await Promise.race(executing);
      executing.splice(executing.findIndex(p => p === promise), 1);
    }
  }

  await Promise.all(executing);
  return results;
}

/**
 * Process items in batches
 */
export async function batchProcess<T, R>(
  items: T[],
  fn: (batch: T[]) => Promise<R[]>,
  batchSize = DEFAULT_PERF_CONFIG.batchSize
): Promise<R[]> {
  const results: R[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await fn(batch);
    results.push(...batchResults);
  }
  
  return results;
}

/**
 * Add timeout to async operation
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs = DEFAULT_PERF_CONFIG.timeout
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) => 
      setTimeout(() => reject(new Error(`Operation timed out after ${timeoutMs}ms`)), timeoutMs)
    )
  ]);
}

/**
 * Retry operation with exponential backoff
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries = DEFAULT_PERF_CONFIG.retries,
  baseDelay = 100
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        throw lastError;
      }
      
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * Memory-efficient map for large datasets
 */
export async function lazyMap<T, R>(
  items: T[],
  fn: (item: T, index: number) => Promise<R>,
  options: Partial<PerformanceConfig> = {}
): Promise<R[]> {
  const config = { ...DEFAULT_PERF_CONFIG, ...options };
  const results: R[] = new Array(items.length);
  
  await parallelLimit(
    items.map((item, index) => ({ item, index })),
    async ({ item, index }) => {
      results[index] = await withTimeout(fn(item, index), config.timeout);
    },
    config.concurrency
  );
  
  return results;
}

/**
 * Debounce with promise support
 */
export function asyncDebounce<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  delay: number
): T {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastPromise: Promise<ReturnType<T>> | null = null;

  return ((...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (lastPromise) {
      return lastPromise;
    }

    lastPromise = new Promise((resolve, reject) => {
      timeoutId = setTimeout(async () => {
        try {
          const result = await fn(...args);
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          lastPromise = null;
          timeoutId = null;
        }
      }, delay);
    });

    return lastPromise;
  }) as T;
}

/**
 * Memory usage tracker
 */
export class MemoryTracker {
  private static instance: MemoryTracker | null = null;
  private measurements: Map<string, number> = new Map();

  static getInstance(): MemoryTracker {
    if (!MemoryTracker.instance) {
      MemoryTracker.instance = new MemoryTracker();
    }
    return MemoryTracker.instance;
  }

  track(label: string): void {
    const memory = (performance as any).memory;
    if (memory) {
      this.measurements.set(label, memory.usedJSHeapSize);
    }
  }

  measure(label: string): number {
    const memory = (performance as any).memory;
    if (!memory) return 0;
    
    const current = memory.usedJSHeapSize;
    const previous = this.measurements.get(label) || current;
    return current - previous;
  }

  report(): void {
    const memory = (performance as any).memory;
    if (!memory) {
      logger.warn('Memory API not available');
      return;
    }

    logger.info('Memory usage:', {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB',
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
    });
  }
}

/**
 * Performance measurement wrapper
 */
export function measurePerformance<T>(
  label: string,
  fn: () => Promise<T> | T
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const start = performance.now();
    const memTracker = MemoryTracker.getInstance();
    memTracker.track(`${label}_start`);
    
    try {
      const result = await fn();
      const duration = performance.now() - start;
      const memUsed = memTracker.measure(`${label}_start`);
      
      logger.debug(`Performance [${label}]:`, {
        duration: Math.round(duration * 100) / 100 + 'ms',
        memory: Math.round(memUsed / 1024) + 'KB'
      });
      
      resolve(result);
    } catch (error) {
      const duration = performance.now() - start;
      logger.error(`Performance [${label}] failed after ${Math.round(duration)}ms:`, error);
      reject(error);
    }
  });
}

/**
 * Cache with TTL and size limits
 */
export class LRUCache<K, V> {
  private cache = new Map<K, { value: V; timestamp: number; }>();
  private maxSize: number;
  private ttl: number;

  constructor(maxSize = 1000, ttl = 5 * 60 * 1000) { // 5 minutes default
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  get(key: K): V | undefined {
    const item = this.cache.get(key);
    if (!item) return undefined;
    
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return undefined;
    }
    
    // Move to end (most recently used)
    this.cache.delete(key);
    this.cache.set(key, item);
    return item.value;
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // Remove least recently used (first item)
      const firstKey = this.cache.keys().next().value;
      if (firstKey !== undefined) {
        this.cache.delete(firstKey);
      }
    }
    
    this.cache.set(key, { value, timestamp: Date.now() });
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}