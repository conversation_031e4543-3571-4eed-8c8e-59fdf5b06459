/**
 * Chrome Extension API Type Definitions
 * Comprehensive types for Chrome extension development
 */

declare namespace chrome {
  namespace runtime {
    interface MessageSender {
      tab?: chrome.tabs.Tab;
      frameId?: number;
      id?: string;
      url?: string;
      tlsChannelId?: string;
    }

    interface InstalledDetails {
      reason: string;
      previousVersion?: string;
    }

    interface Port {
      name: string;
      disconnect(): void;
      onDisconnect: {
        addListener(callback: (port: Port) => void): void;
      };
      onMessage: {
        addListener(callback: (message: any, port: Port) => void): void;
      };
      postMessage(message: any): void;
      sender?: MessageSender;
    }

    const id: string;
    const lastError: { message: string } | undefined;

    const onMessage: {
      addListener(callback: (message: any, sender: MessageSender, sendResponse: (response?: any) => void) => void): void;
      removeListener(callback: Function): void;
      hasListener(callback: Function): boolean;
    };

    const onInstalled: {
      addListener(callback: (details: InstalledDetails) => void): void;
    };

    const onStartup: {
      addListener(callback: () => void): void;
    };

    const onSuspend: {
      addListener(callback: () => void): void;
    };

    function sendMessage(message: any): Promise<any>;
    function sendMessage(message: any, callback: (response: any) => void): void;
    function sendMessage(extensionId: string, message: any): Promise<any>;
    function sendMessage(extensionId: string, message: any, callback: (response: any) => void): void;

    function getManifest(): any;
    function getURL(path: string): string;
    function reload(): void;
  }

  namespace tabs {
    interface Tab {
      id?: number;
      index: number;
      windowId: number;
      highlighted: boolean;
      active: boolean;
      pinned: boolean;
      url?: string;
      title?: string;
      favIconUrl?: string;
      status?: string;
      incognito: boolean;
      width?: number;
      height?: number;
      sessionId?: string;
    }

    interface QueryInfo {
      active?: boolean;
      pinned?: boolean;
      highlighted?: boolean;
      currentWindow?: boolean;
      lastFocusedWindow?: boolean;
      status?: string;
      title?: string;
      url?: string | string[];
      windowId?: number;
      windowType?: string;
      index?: number;
    }

    interface CreateProperties {
      windowId?: number;
      index?: number;
      url?: string;
      active?: boolean;
      pinned?: boolean;
      openerTabId?: number;
    }

    interface UpdateProperties {
      url?: string;
      active?: boolean;
      highlighted?: boolean;
      pinned?: boolean;
      muted?: boolean;
      openerTabId?: number;
    }

    interface ChangeInfo {
      status?: string;
      url?: string;
      pinned?: boolean;
      audible?: boolean;
      discarded?: boolean;
      autoDiscardable?: boolean;
      mutedInfo?: any;
      favIconUrl?: string;
      title?: string;
    }

    const onUpdated: {
      addListener(callback: (tabId: number, changeInfo: ChangeInfo, tab: Tab) => void): void;
      removeListener(callback: Function): void;
      hasListener(callback: Function): boolean;
    };

    const onActivated: {
      addListener(callback: (activeInfo: { tabId: number; windowId: number }) => void): void;
    };

    const onRemoved: {
      addListener(callback: (tabId: number, removeInfo: { windowId: number; isWindowClosing: boolean }) => void): void;
    };

    function query(queryInfo: QueryInfo): Promise<Tab[]>;
    function query(queryInfo: QueryInfo, callback: (result: Tab[]) => void): void;

    function get(tabId: number): Promise<Tab>;
    function get(tabId: number, callback: (tab: Tab) => void): void;

    function create(createProperties: CreateProperties): Promise<Tab>;
    function create(createProperties: CreateProperties, callback: (tab: Tab) => void): void;

    function update(tabId: number, updateProperties: UpdateProperties): Promise<Tab>;
    function update(tabId: number, updateProperties: UpdateProperties, callback: (tab?: Tab) => void): void;
    function update(updateProperties: UpdateProperties): Promise<Tab>;
    function update(updateProperties: UpdateProperties, callback: (tab?: Tab) => void): void;

    function remove(tabId: number): Promise<void>;
    function remove(tabId: number, callback: () => void): void;
    function remove(tabIds: number[]): Promise<void>;
    function remove(tabIds: number[], callback: () => void): void;

    function sendMessage(tabId: number, message: any): Promise<any>;
    function sendMessage(tabId: number, message: any, callback: (response: any) => void): void;

    function reload(tabId?: number): Promise<void>;
    function reload(tabId: number, callback: () => void): void;
    function reload(callback: () => void): void;
  }

  namespace storage {
    interface StorageArea {
      get(callback: (items: { [key: string]: any }) => void): void;
      get(keys: string | string[] | { [key: string]: any } | null, callback: (items: { [key: string]: any }) => void): void;
      getBytesInUse(callback: (bytesInUse: number) => void): void;
      getBytesInUse(keys: string | string[] | null, callback: (bytesInUse: number) => void): void;
      set(items: { [key: string]: any }, callback?: () => void): void;
      remove(keys: string | string[], callback?: () => void): void;
      clear(callback?: () => void): void;
    }

    interface StorageChange {
      oldValue?: any;
      newValue?: any;
    }

    interface StorageChanges {
      [key: string]: StorageChange;
    }

    const local: StorageArea;
    const sync: StorageArea;
    const session: StorageArea;
    const managed: StorageArea;

    const onChanged: {
      addListener(callback: (changes: StorageChanges, areaName: string) => void): void;
    };
  }

  namespace action {
    interface TabDetails {
      tabId?: number;
    }

    function setTitle(details: { title: string; tabId?: number }): Promise<void>;
    function getTitle(details: TabDetails): Promise<string>;
    function setIcon(details: { imageData?: ImageData; path?: string; tabId?: number }): Promise<void>;
    function setPopup(details: { popup: string; tabId?: number }): Promise<void>;
    function getPopup(details: TabDetails): Promise<string>;
    function setBadgeText(details: { text: string; tabId?: number }): Promise<void>;
    function getBadgeText(details: TabDetails): Promise<string>;
    function setBadgeBackgroundColor(details: { color: string | [number, number, number, number]; tabId?: number }): Promise<void>;
    function getBadgeBackgroundColor(details: TabDetails): Promise<[number, number, number, number]>;
    function enable(tabId?: number): Promise<void>;
    function disable(tabId?: number): Promise<void>;

    const onClicked: {
      addListener(callback: (tab: chrome.tabs.Tab) => void): void;
    };
  }

  namespace webNavigation {
    interface WebNavigationFramedCallbackDetails {
      tabId: number;
      url: string;
      frameId: number;
      parentFrameId?: number;
      timeStamp: number;
    }

    interface WebNavigationCallbackDetails extends WebNavigationFramedCallbackDetails {
      processId?: number;
    }

    const onBeforeNavigate: {
      addListener(callback: (details: WebNavigationCallbackDetails) => void): void;
    };

    const onCommitted: {
      addListener(callback: (details: WebNavigationCallbackDetails) => void): void;
    };

    const onCompleted: {
      addListener(callback: (details: WebNavigationCallbackDetails) => void): void;
    };

    const onDOMContentLoaded: {
      addListener(callback: (details: WebNavigationCallbackDetails) => void): void;
    };
  }

  namespace scripting {
    interface InjectionTarget {
      tabId: number;
      frameIds?: number[];
      documentIds?: string[];
      allFrames?: boolean;
    }

    interface ScriptInjection {
      target: InjectionTarget;
      files?: string[];
      func?: Function;
      args?: any[];
      world?: string;
      injectImmediately?: boolean;
    }

    interface CSSInjection {
      target: InjectionTarget;
      files?: string[];
      css?: string;
      origin?: string;
    }

    function executeScript(injection: ScriptInjection): Promise<any[]>;
    function insertCSS(injection: CSSInjection): Promise<void>;
    function removeCSS(injection: CSSInjection): Promise<void>;
  }

  namespace permissions {
    interface Permissions {
      permissions?: string[];
      origins?: string[];
    }

    function contains(permissions: Permissions): Promise<boolean>;
    function request(permissions: Permissions): Promise<boolean>;
    function remove(permissions: Permissions): Promise<boolean>;
    function getAll(): Promise<Permissions>;

    const onAdded: {
      addListener(callback: (permissions: Permissions) => void): void;
    };

    const onRemoved: {
      addListener(callback: (permissions: Permissions) => void): void;
    };
  }

  namespace contextMenus {
    interface CreateProperties {
      type?: string;
      id?: string;
      title?: string;
      checked?: boolean;
      contexts?: string[];
      onclick?: (info: OnClickData, tab: chrome.tabs.Tab) => void;
      parentId?: number | string;
      documentUrlPatterns?: string[];
      targetUrlPatterns?: string[];
      enabled?: boolean;
      visible?: boolean;
    }

    interface OnClickData {
      menuItemId: number | string;
      parentMenuItemId?: number | string;
      mediaType?: string;
      linkUrl?: string;
      srcUrl?: string;
      pageUrl?: string;
      frameUrl?: string;
      selectionText?: string;
      editable: boolean;
      wasChecked?: boolean;
      checked?: boolean;
    }

    function create(createProperties: CreateProperties, callback?: () => void): number | string;
    function update(id: number | string, updateProperties: CreateProperties, callback?: () => void): void;
    function remove(menuItemId: number | string, callback?: () => void): void;
    function removeAll(callback?: () => void): void;

    const onClicked: {
      addListener(callback: (info: OnClickData, tab?: chrome.tabs.Tab) => void): void;
    };
  }
}

// Global chrome object
declare const chrome: typeof chrome;

export {};