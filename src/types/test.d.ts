/**
 * Type declarations for test environment
 */

declare global {
  const global: any;
  const process: {
    env: {
      NODE_ENV?: string;
    };
  };
  
  // Jest globals
  const jest: any;
  const describe: any;
  const it: any;
  const test: any;
  const expect: any;
  const beforeEach: any;
  const afterEach: any;
  const beforeAll: any;
  const afterAll: any;
  
  // Node.js globals
  const require: any;
  const module: any;
  const exports: any;
}

export {};
