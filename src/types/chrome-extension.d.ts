/**
 * Chrome Extension API type declarations for testing
 * Provides runtime chrome API types that work in both production and test environments
 */

declare global {
  // Chrome Extension APIs
  var chrome: {
    runtime: {
      onMessage: {
        addListener: (callback: (message: any, sender: any, sendResponse: (response?: any) => void) => void) => void;
        removeListener: (callback: Function) => void;
        hasListener: (callback: Function) => boolean;
      };
      sendMessage: (message: any) => Promise<any>;
      getManifest: () => any;
      id: string;
    };
    tabs: {
      query: (queryInfo: { active?: boolean; currentWindow?: boolean }) => Promise<Array<{ id: number; url: string; active: boolean }>>;
      sendMessage: (tabId: number, message: any) => Promise<any>;
      onUpdated: {
        addListener: (callback: Function) => void;
        removeListener: (callback: Function) => void;
      };
    };
    storage: {
      local: {
        get: (keys?: string | string[] | { [key: string]: any }) => Promise<{ [key: string]: any }>;
        set: (items: { [key: string]: any }) => Promise<void>;
        remove: (keys: string | string[]) => Promise<void>;
        clear: () => Promise<void>;
      };
      sync: {
        get: (keys?: string | string[] | { [key: string]: any }) => Promise<{ [key: string]: any }>;
        set: (items: { [key: string]: any }) => Promise<void>;
        remove: (keys: string | string[]) => Promise<void>;
        clear: () => Promise<void>;
      };
    };
    webNavigation: {
      onCompleted: {
        addListener: (callback: Function) => void;
        removeListener: (callback: Function) => void;
      };
    };
  };

  // Service Worker global scope
  var ServiceWorkerGlobalScope: any;
  
  // Service Worker specific APIs
  var importScripts: (...urls: string[]) => void;
  
  // Web Worker APIs
  var WorkerGlobalScope: any;
  var DedicatedWorkerGlobalScope: any;
  var SharedWorkerGlobalScope: any;
}

export {};