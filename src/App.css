/* Recall 应用样式 - 重新设计的UI */

/* 设计系统变量 */
:root {
  /* 间距系统 - 基于4px网格 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  
  /* 尺寸变量 */
  --popup-width-compact: 400px;
  --popup-width-default: 550px;
  --popup-width-expanded: 750px;
  --popup-height-compact: 600px;
  --popup-height-default: 680px;
  --popup-height-expanded: 850px;
  
  /* 圆角系统 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 10px;
  --radius-xl: 12px;
  
  /* 阴影系统 */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 10px 40px rgba(0, 0, 0, 0.2);
  
  /* 颜色系统 */
  --color-primary: #667eea;
  --color-primary-dark: #5a6fd8;
  --color-secondary: #764ba2;
  --color-border: #e2e8f0;
  --color-border-hover: #cbd5e1;
  --color-bg-subtle: #f8fafc;
  --color-bg-muted: #f1f5f9;
  
  /* 文字颜色 */
  --color-text-primary: #1e293b;
  --color-text-secondary: #475569;
  --color-text-muted: #64748b;
  --color-text-subtle: #94a3b8;
  
  /* 滚动条颜色 - 更subtle */
  
  /* 动画变量 */
  --transition-fast: 0.15s ease;
  --transition-medium: 0.25s ease;
  --transition-slow: 0.4s ease;
  
  /* AI初始化相关颜色 */
  --color-ai-primary: #8b5cf6;
  --color-ai-secondary: #a78bfa;
  --color-ai-bg: rgba(139, 92, 246, 0.05);
  --scrollbar-thumb: rgba(148, 163, 184, 0.5);
  --scrollbar-thumb-hover: rgba(148, 163, 184, 0.8);
  --scrollbar-track: rgba(241, 245, 249, 0.3);
}

/* 密度模式 */
[data-density="compact"] {
  /* 紧凑模式下减少间距 */
  --spacing-xs: 3px;
  --spacing-sm: 6px;
  --spacing-md: 9px;
  --spacing-lg: 12px;
  --spacing-xl: 15px;
  --spacing-2xl: 18px;
  --spacing-3xl: 24px;
}

[data-density="comfortable"] {
  /* 舒适模式使用默认间距 */
}

/* 密度模式特定调整 */
[data-density="compact"] .rc-header {
  padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-xs);
}

[data-density="compact"] .rc-search-section {
  padding: var(--spacing-sm) var(--spacing-lg);
}

[data-density="compact"] .rc-results-container {
  padding: var(--spacing-xs) var(--spacing-sm);
}

[data-density="compact"] .rc-footer {
  /* 紧凑模式下状态栏保持最小高度 */
  min-height: 40px;
}

[data-density="compact"] .result-item {
  padding: var(--spacing-md);
  min-height: 80px;
  margin-bottom: var(--spacing-sm);
}

/* ========================================
   虚拟滚动样式
   ======================================== */

/* 虚拟滚动容器 */
.virtual-result-list {
  width: 100%;
  height: 100%;
}

.virtual-results-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 虚拟滚动主容器 */
.virtual-scroll-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: white;
  position: relative;
}

/* 优化滚动条样式 */
.virtual-scroll-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 4px;
  border: 1px solid transparent;
  background-clip: content-box;
  transition: background var(--transition-fast);
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
  background-clip: content-box;
}

/* 虚拟滚动占位容器 */
.virtual-scroll-spacer {
  width: 100%;
  position: relative;
}

/* 可见项目容器 */
.virtual-scroll-items {
  width: 100%;
}

/* 虚拟滚动结果项 */
.virtual-result-item {
  width: 100%;
  padding: var(--spacing-lg);
  background: white;
  border-bottom: 1px solid var(--color-border);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-sizing: border-box;
}

.virtual-result-item:hover {
  background: var(--color-bg-subtle);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.virtual-result-item:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
  z-index: 1;
}

.virtual-result-item:active {
  transform: translateY(0);
  box-shadow: var(--shadow-xs);
}

/* 滚动时的简化样式 */
.virtual-result-item.scrolling {
  pointer-events: none;
}

.virtual-result-item.scrolling .result-highlights,
.virtual-result-item.scrolling .result-content {
  opacity: 0.7;
}

.scrolling-placeholder {
  background: var(--color-bg-muted);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  color: var(--color-text-muted);
  font-style: italic;
}

/* 非虚拟滚动模式 */
.results-list.non-virtual {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: white;
}

.results-list.non-virtual .virtual-result-item {
  border-bottom: 1px solid var(--color-border);
  margin-bottom: 0;
}

.results-list.non-virtual .virtual-result-item:last-child {
  border-bottom: none;
}

/* 结果统计增强 */
.results-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--color-bg-subtle);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
}

.results-info {
  flex: 1;
}

.virtual-scroll-hint {
  color: var(--color-primary);
  font-size: 0.85em;
  font-weight: 500;
  margin-left: var(--spacing-sm);
}

/* 性能统计 */
.performance-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.8em;
  color: var(--color-text-muted);
  background: rgba(139, 92, 246, 0.05);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid rgba(139, 92, 246, 0.1);
}

.stats-icon {
  font-size: 1em;
}

.stats-text {
  font-weight: 500;
}

/* 键盘提示 */
.keyboard-hints {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--color-bg-muted);
  border-radius: var(--radius-md);
  margin-top: var(--spacing-md);
}

.keyboard-hints .hint-text {
  font-size: 0.8em;
  color: var(--color-text-muted);
  margin: 0;
}

/* 虚拟滚动模式指示器 */
.virtual-result-list.virtual-enabled {
  position: relative;
}

.virtual-result-list.virtual-enabled::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 20px;
  background: linear-gradient(45deg, var(--color-primary), var(--color-ai-primary));
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
  z-index: 10;
  opacity: 0.6;
}

/* 流式加载与虚拟滚动结合的进度条 */
.virtual-results-container .streaming-progress {
  margin-top: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.virtual-results-container .streaming-indicator {
  font-size: 0.85em;
  color: var(--color-primary);
  font-weight: 500;
}

.virtual-results-container .streaming-progress-bar {
  flex: 1;
  height: 3px;
  background: var(--color-bg-muted);
  border-radius: 2px;
  overflow: hidden;
}

.virtual-results-container .streaming-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-ai-primary));
  border-radius: 2px;
  transition: width var(--transition-fast);
}

/* 响应式调整 */
@media (max-width: 480px) {
  .virtual-result-item {
    padding: var(--spacing-md);
  }
  
  .results-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .performance-stats {
    align-self: flex-end;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .virtual-scroll-container {
    background: #1e293b;
    border-color: #334155;
  }
  
  .virtual-result-item {
    background: #1e293b;
    border-color: #334155;
    color: #f1f5f9;
  }
  
  .virtual-result-item:hover {
    background: #334155;
    border-color: #475569;
  }
  
  .scrolling-placeholder {
    background: #334155;
    color: #94a3b8;
  }
  
  .performance-stats {
    background: rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
    color: #e2e8f0;
  }
  
  .keyboard-hints {
    background: #334155;
  }
  
  .keyboard-hints .hint-text {
    color: #94a3b8;
  }
}

/* 辅助功能增强 */
@media (prefers-reduced-motion: reduce) {
  .virtual-result-item {
    transition: none;
  }
  
  .virtual-result-item:hover {
    transform: none;
  }
  
  .streaming-progress-fill {
    transition: none;
  }
}

/* ========================================
   性能信息提示样式
   ======================================== */

.performance-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  margin-top: var(--spacing-xs);
  background: rgba(139, 92, 246, 0.05);
  border: 1px solid rgba(139, 92, 246, 0.1);
  border-radius: var(--radius-sm);
  font-size: 0.75em;
  color: var(--color-text-muted);
  transition: all var(--transition-fast);
}

.performance-info:hover {
  background: rgba(139, 92, 246, 0.08);
  border-color: rgba(139, 92, 246, 0.2);
}

.perf-icon {
  font-size: 1.1em;
  display: flex;
  align-items: center;
}

.perf-text {
  font-weight: 500;
  color: var(--color-text-secondary);
}

.perf-savings {
  font-weight: 600;
  color: var(--color-primary);
  background: rgba(102, 126, 234, 0.1);
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-size: 0.9em;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  .performance-info {
    background: rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
    color: #e2e8f0;
  }
  
  .performance-info:hover {
    background: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
  }
  
  .perf-text {
    color: #f1f5f9;
  }
  
  .perf-savings {
    background: rgba(102, 126, 234, 0.2);
    color: #a78bfa;
  }
}

[data-density="compact"] .rc-title {
  font-size: 16px;
}

[data-density="compact"] .rc-subtitle {
  font-size: 12px;
}

[data-density="compact"] .search-input {
  min-height: 36px;
  font-size: 14px;
}

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans",
    "Droid Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 应用主容器 */
.rc-app {
  width: var(--popup-width-default);
  min-height: var(--popup-height-default);
  max-height: var(--popup-height-default);
  background: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  transition: width 0.3s ease, height 0.3s ease;
}

/* 紧凑模式 */
.rc-app[data-size="compact"] {
  width: var(--popup-width-compact);
  min-height: var(--popup-height-compact);
  max-height: var(--popup-height-compact);
}

/* 扩展模式 */
.rc-app[data-size="expanded"] {
  width: var(--popup-width-expanded);
  min-height: var(--popup-height-expanded);
  max-height: var(--popup-height-expanded);
}

/* 加载状态样式 */
.rc-app-loading {
  justify-content: center;
  align-items: center;
}

.rc-loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.rc-loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: rc-spin 1s linear infinite;
}

@keyframes rc-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.rc-loading-state p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* 头部区域 */
.rc-header {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl) var(--spacing-sm);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rc-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rc-header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.rc-language-selector {
  /* Ensure the language selector blends with header */
}

/* 尺寸和密度切换按钮 */
.rc-size-toggle,
.rc-density-toggle {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.rc-size-toggle:hover,
.rc-density-toggle:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.rc-size-toggle:active,
.rc-density-toggle:active {
  transform: translateY(0);
}

.rc-size-toggle svg,
.rc-density-toggle svg {
  width: 16px;
  height: 16px;
}

.rc-logo {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.rc-logo svg {
  color: white;
}

.rc-brand-text {
  flex: 1;
}

.rc-title {
  font-size: 18px;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
}

.rc-subtitle {
  font-size: 13px;
  opacity: 0.9;
  font-weight: 400;
}

/* 搜索区域 */
.rc-search-section {
  background: var(--color-bg-subtle);
  padding: var(--spacing-md) var(--spacing-xl);
  border-bottom: 1px solid var(--color-border);
  flex-shrink: 0;
}

.rc-search-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 0;
}

.rc-search-container .search-bar {
  flex: 1;
}

.rc-filters-container {
  /* Remove margin since filters are now inline */
}

/* 内容区域 */
.rc-content {
  flex: 1;
  overflow-y: hidden;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 允许flex子项缩小 */
}

/* 结果容器 - 优化滚动体验 */
.rc-results-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--spacing-sm) var(--spacing-md);
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* iOS Safari平滑滚动 */
  scrollbar-gutter: stable; /* 为滚动条预留稳定空间 */
  position: relative;
  min-height: 0; /* 允许容器正确缩小，配合flex使用 */
}

/* 滚动渐变遮罩效果 */
.rc-results-container::before,
.rc-results-container::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 16px;
  pointer-events: none;
  z-index: 1;
  transition: opacity 0.3s ease;
}

.rc-results-container::before {
  top: 0;
  background: linear-gradient(to bottom, 
    rgba(255, 255, 255, 1) 0%, 
    rgba(255, 255, 255, 0.8) 50%, 
    transparent 100%);
  opacity: 0;
}

.rc-results-container::after {
  bottom: 0;
  background: linear-gradient(to top, 
    rgba(255, 255, 255, 1) 0%, 
    rgba(255, 255, 255, 0.8) 50%, 
    transparent 100%);
  opacity: 0;
}

.rc-results-container.scrolled-top::before {
  opacity: 0;
}

.rc-results-container.scrolled-middle::before,
.rc-results-container.scrolled-middle::after {
  opacity: 1;
}

.rc-results-container.scrolled-bottom::after {
  opacity: 0;
}

/* 滚动内容区域动画 */
.rc-results-content {
  animation: scrollContentFadeIn 0.4s ease-out;
}

@keyframes scrollContentFadeIn {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 空状态 */
.rc-empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 48px 32px;
  text-align: center;
  overflow-y: auto;
  min-height: 500px;
}

/* 功能特性展示 */
.rc-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 32px;
}

.rc-feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f1f5f9;
  border-radius: 8px;
  font-size: 13px;
  color: #475569;
  font-weight: 500;
}

.rc-feature-icon {
  width: 24px;
  height: 24px;
  background: #e2e8f0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.rc-feature-icon svg {
  color: #64748b;
}

/* 搜索技巧 */
.rc-search-tips {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  text-align: left;
}

.rc-tips-title {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.rc-tips-list {
  list-style: none;
  font-size: 12px;
  color: #6b7280;
  line-height: 1.5;
}

.rc-tips-list li {
  position: relative;
  padding-left: 16px;
  margin-bottom: 4px;
}

.rc-tips-list li:before {
  content: "•";
  position: absolute;
  left: 0;
  color: #9ca3af;
}

/* 键盘快捷键区域 */
.rc-keyboard-shortcuts {
  background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.rc-shortcuts-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.rc-shortcuts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.rc-shortcut-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.rc-shortcut-item:hover {
  background: #f8faff;
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.rc-kbd {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  font-size: 10px;
  font-weight: 600;
  color: #475569;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 20px;
  text-align: center;
}

.rc-kbd-plus {
  color: #94a3b8;
  font-size: 10px;
  font-weight: 500;
  margin: 0 2px;
}

.rc-shortcut-desc {
  color: #64748b;
  font-size: 11px;
  font-weight: 500;
  flex: 1;
}

/* 搜索示例区域 */
.rc-search-examples {
  background: linear-gradient(135deg, #f8faff 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.rc-examples-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  text-align: center;
}

.rc-examples-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.rc-example-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 16px 12px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-height: 80px;
  position: relative;
  overflow: hidden;
}

.rc-example-btn:hover {
  border-color: #667eea;
  background: #f8faff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.rc-example-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.rc-example-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.rc-example-text {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
  background: #f1f5f9;
  padding: 2px 6px;
  border-radius: 4px;
  margin-bottom: 2px;
}

.rc-example-desc {
  font-size: 10px;
  color: #6b7280;
  font-weight: 500;
}

/* 错误状态 - 重新设计的友好界面 */
.rc-error-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  text-align: center;
  overflow-y: auto;
  animation: errorFadeIn 0.4s ease-out;
}

.rc-error-container {
  max-width: 400px;
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  padding: 32px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #f1f5f9;
}

.rc-error-icon {
  margin-bottom: 20px;
  position: relative;
}

.rc-error-icon svg {
  width: 48px;
  height: 48px;
  animation: errorPulse 2s ease-in-out infinite;
}

/* 错误类型特定图标颜色 */
.rc-error-icon.network svg {
  color: #f59e0b; /* 网络错误 - 黄色 */
}

.rc-error-icon.service-init svg {
  color: #8b5cf6; /* 服务初始化错误 - 紫色 */
}

.rc-error-icon.search svg {
  color: #3b82f6; /* 搜索错误 - 蓝色 */
}

.rc-error-icon.database svg {
  color: #ef4444; /* 数据库错误 - 红色 */
}

.rc-error-icon.unknown svg {
  color: #6b7280; /* 未知错误 - 灰色 */
}

.rc-error-title {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
  line-height: 1.3;
}

.rc-error-message {
  font-size: 15px;
  color: #64748b;
  margin-bottom: 24px;
  line-height: 1.5;
}

.rc-error-solutions {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 28px;
  text-align: left;
}

.rc-solutions-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rc-solutions-title svg {
  color: #10b981;
  width: 16px;
  height: 16px;
}

.rc-solutions-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rc-solution-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  font-size: 13px;
  color: #475569;
  line-height: 1.4;
}

.rc-solution-number {
  background: #667eea;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 1px;
}

.rc-solution-text {
  flex: 1;
}

.rc-error-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}

.rc-action-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 14px 24px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.rc-action-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.rc-action-primary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.rc-action-secondary {
  background: #f8fafc;
  color: #475569;
  border: 2px solid #e2e8f0;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.rc-action-secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #334155;
  transform: translateY(-1px);
}

.rc-action-secondary:active {
  transform: translateY(0);
}

.rc-actions-row {
  display: flex;
  gap: 12px;
  width: 100%;
}

.rc-actions-row .rc-action-secondary {
  flex: 1;
}

/* 错误状态动画 */
@keyframes errorFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes errorPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* 响应式调整 */
@media (max-width: 520px) {
  .rc-error-container {
    padding: 24px 20px;
    margin: 0 12px;
  }

  .rc-error-title {
    font-size: 18px;
  }

  .rc-error-message {
    font-size: 14px;
  }

  .rc-actions-row {
    flex-direction: column;
  }

  .rc-action-primary, 
  .rc-action-secondary {
    padding: 12px 20px;
    font-size: 14px;
  }
}

/* 底部状态栏 - 重新设计：固定在底部 */
.rc-footer {
  position: sticky;
  bottom: 0;
  z-index: 10;
  border-top: 1px solid #e2e8f0;
  background: rgba(248, 250, 252, 0.95);
  backdrop-filter: blur(10px);
  flex-shrink: 0; /* 防止footer被压缩 */
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05); /* 轻微阴影提供视觉分层 */
}

/* 状态栏样式 - 全新设计 */
.rc-status-bar {
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
  backdrop-filter: blur(10px);
}

.rc-status-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  min-height: 56px;
}

.rc-status-content {
  flex: 1;
  margin-right: 16px;
}

/* 加载状态 */
.rc-status-loading {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rc-loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.rc-loading-text {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 指标组 */
.rc-status-metrics {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.rc-metric-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.rc-metric-group:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.rc-metric-icon {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.rc-metric-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 1px;
}

.rc-metric-value {
  font-size: 13px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.rc-metric-label {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1;
}

/* 特定指标样式 */
.rc-results-metric {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.rc-performance-metric {
  border-color: #10b981;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}

.rc-database-metric {
  border-color: #8b5cf6;
  background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
}

.rc-health-metric {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

/* 操作按钮组 */
.rc-status-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rc-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.rc-action-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.rc-action-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.rc-action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.rc-action-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

/* 图标动画 */
.rc-expand-icon {
  transition: transform 0.3s ease;
}

.rc-expand-icon.expanded {
  transform: rotate(180deg);
}

.rc-refresh-icon {
  transition: transform 0.3s ease;
}

.rc-refresh-icon.spinning {
  animation: spin 1s linear infinite;
}

.rc-settings-icon {
  transition: transform 0.2s ease;
}

.rc-action-btn:hover .rc-settings-icon {
  transform: rotate(90deg);
}

/* 详情面板 */
.rc-status-details {
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  padding: 20px;
  animation: slideDown 0.3s ease-out;
}

.rc-details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.rc-details-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.rc-last-update {
  font-size: 11px;
  color: #64748b;
  background: #e2e8f0;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 详情网格 */
.rc-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.rc-detail-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.rc-detail-card:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.rc-detail-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.rc-detail-icon {
  font-size: 16px;
}

.rc-detail-title {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.rc-detail-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rc-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rc-detail-label {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.rc-detail-value {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
}

.rc-performance-badge {
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(16, 185, 129, 0.1);
  font-size: 10px !important;
}

/* 快速操作 */
.rc-quick-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.rc-quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  max-width: 80px;
}

.rc-quick-action-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rc-action-icon {
  font-size: 18px;
}

.rc-action-text {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

/* 动画 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 680px) {
  .rc-app {
    width: 100vw;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
  }

  .rc-header {
    border-radius: 0;
  }

  .rc-empty-state {
    padding: 32px 20px;
    justify-content: flex-start;
  }

  .rc-features {
    gap: 8px;
  }

  .rc-feature-item {
    padding: 10px 12px;
    font-size: 12px;
  }

  .rc-keyboard-shortcuts {
    padding: 16px;
    margin-top: 16px;
  }

  .rc-shortcuts-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .rc-shortcut-item {
    padding: 6px 10px;
    font-size: 11px;
  }

  .rc-kbd {
    font-size: 9px;
    padding: 1px 4px;
  }

  .rc-search-examples {
    padding: 16px;
    margin-top: 16px;
  }

  .rc-examples-grid {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .rc-example-btn {
    min-height: 70px;
    padding: 12px 10px;
  }

  .rc-example-text {
    font-size: 11px;
  }

  .rc-status-metrics {
    gap: 8px;
  }

  .rc-metric-group {
    padding: 4px 8px;
  }

  .rc-details-grid {
    grid-template-columns: 1fr;
  }

  .rc-quick-actions {
    flex-wrap: wrap;
  }

  .rc-quick-action-btn {
    max-width: none;
    min-width: 70px;
  }
}

/* 增强滚动条样式 - 更subtle的设计 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: var(--radius-sm);
  margin: var(--spacing-xs) 0;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: var(--radius-sm);
  border: none;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

::-webkit-scrollbar-thumb:active {
  background: var(--scrollbar-thumb-hover);
}

/* 专用于结果容器的滚动条 */
.rc-results-container::-webkit-scrollbar {
  width: 5px;
}

.rc-results-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: var(--radius-sm);
  margin: var(--spacing-sm) 0;
}

.rc-results-container::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: var(--radius-sm);
  border: none;
  transition: background 0.2s ease;
}

.rc-results-container::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

.rc-results-container::-webkit-scrollbar-thumb:active {
  background: var(--color-text-muted);
}

/* 动画效果 */
.rc-app {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rc-feature-item {
  transition: all 0.2s ease;
}

.rc-feature-item:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
}

/* 焦点状态 */
.rc-retry-button:focus,
.rc-action-btn:focus,
.rc-quick-action-btn:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .rc-header {
    background: #1a202c;
  }

  .rc-feature-item {
    border: 1px solid #cbd5e1;
  }

  .rc-search-tips {
    border-width: 2px;
  }

  .rc-metric-group {
    border-width: 2px;
  }

  .rc-detail-card {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .rc-app {
    animation: none;
  }

  .rc-feature-item {
    transition: none;
  }

  .rc-retry-button {
    transition: none;
  }

  .rc-action-btn {
    transition: none;
  }

  .rc-quick-action-btn {
    transition: none;
  }

  .rc-status-details {
    animation: none;
  }

  .rc-refresh-icon.spinning {
    animation: none;
  }

  /* 禁用滚动相关动画 */
  .rc-results-container {
    scroll-behavior: auto;
  }

  .rc-results-container::before,
  .rc-results-container::after {
    transition: none;
  }

  .rc-results-content {
    animation: none;
  }

  .result-item {
    animation: none;
    transition: none;
    will-change: auto;
  }

  .rc-shortcut-item {
    transition: none;
  }

  @keyframes resultItemSlideIn {
    from, to {
      opacity: 1;
      transform: none;
    }
  }

  @keyframes scrollContentFadeIn {
    from, to {
      opacity: 1;
      transform: none;
    }
  }
}

.recall-popup {
  width: 650px;
  min-height: 750px;
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", sans-serif;
}

.popup-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 14px 20px;
  text-align: center;
}

.popup-header h1 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

.popup-header p {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

.popup-main {
  padding: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--color-border);
  border-radius: var(--radius-lg);
  font-size: 15px;
  outline: none;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
  min-height: 42px;
}

.search-input:focus {
  border-color: var(--color-primary);
}

.results-section {
  min-height: 300px;
}

.welcome-message {
  text-align: center;
  color: #666;
  padding: 40px 20px;
}

.search-results {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #333;
}

/* 搜索栏样式 */
.search-bar {
  position: relative;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 14px;
  z-index: 1;
  color: #6c757d;
  font-size: 18px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-icon.loading {
  color: #667eea;
}

.search-icon-symbol {
  transition: transform 0.2s ease;
}

.search-icon:hover .search-icon-symbol {
  transform: scale(1.1);
}

.search-input {
  padding-left: 44px;
  padding-right: 88px; /* Increased to accommodate help button */
}

.search-help-container {
  position: absolute;
  right: 48px; /* Position before clear button */
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.clear-button {
  position: absolute;
  right: 14px;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 18px;
  padding: 6px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.clear-button:hover {
  background-color: #f8f9fa;
  color: #495057;
}

/* 搜索建议 */
.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f8f9fa;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-icon {
  font-size: 14px;
  opacity: 0.7;
}

.suggestion-text {
  font-size: 14px;
  color: #495057;
}

/* 过滤器样式 */
.filters {
  margin-bottom: 16px;
}

.filters-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.filters-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: 1px solid #e9ecef;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
  transition: all 0.2s ease;
}

.filters-toggle-btn:hover {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.filters-toggle-btn.expanded {
  background-color: #e7f1ff;
  border-color: #667eea;
  color: #667eea;
}

.filters-icon {
  font-size: 16px;
}

.active-indicator {
  color: #667eea;
  font-size: 8px;
}

.expand-icon {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.expand-icon.rotated {
  transform: rotate(180deg);
}

.clear-filters-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.clear-filters-btn:hover {
  background-color: #f8d7da;
}

/* 过滤器面板 */
.filters-panel {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-top: 8px;
}

.filter-group {
  margin-bottom: 16px;
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.filter-option {
  background: white;
  border: 1px solid #e9ecef;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #495057;
}

.filter-option:hover {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

.filter-option.active {
  background-color: #667eea;
  border-color: #667eea;
  color: white;
}

.filter-row {
  display: flex;
  gap: 8px;
}

.filter-select {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  color: #495057;
}

/* 自定义域名输入 */
.custom-domain-input {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.domain-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 12px;
}

.add-domain-btn {
  background: #667eea;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-domain-btn:hover:not(:disabled) {
  background: #5a6fd8;
}

.add-domain-btn:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

/* 已选择的域名 */
.selected-domains {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.selected-label {
  font-size: 11px;
  color: #6c757d;
  font-weight: 500;
}

.selected-domain {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #e7f1ff;
  color: #667eea;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  border: 1px solid #b8d4ff;
}

.remove-domain-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 10px;
  padding: 0;
  margin-left: 2px;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.remove-domain-btn:hover {
  background-color: #667eea;
  color: white;
}

/* 结果列表样式 */
.result-list {
  flex: 1;
  overflow-y: auto;
}

.results-container {
  animation: fadeIn 0.3s ease-out;
}

.results-summary {
  margin-bottom: 16px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #667eea;
}

.results-summary p {
  margin: 0;
  font-size: 13px;
  color: #495057;
}

.results-truncated {
  color: #6c757d;
  font-weight: normal;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) 0;
}

/* 结果项样式 - 增强滚动动画 */
.result-item {
  background: white;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  transform: translateZ(0); /* 启用硬件加速 */
  will-change: transform, box-shadow; /* 优化动画性能 */
  animation: resultItemSlideIn 0.4s ease-out backwards;
  margin-bottom: var(--spacing-md);
  min-height: 100px; /* 确保结果项有足够高度 */
}

/* 结果项入场动画 */
@keyframes resultItemSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* 为不同的结果项添加延迟，创造级联效果 */
.result-item:nth-child(1) { animation-delay: 0ms; }
.result-item:nth-child(2) { animation-delay: 50ms; }
.result-item:nth-child(3) { animation-delay: 100ms; }
.result-item:nth-child(4) { animation-delay: 150ms; }
.result-item:nth-child(5) { animation-delay: 200ms; }
.result-item:nth-child(6) { animation-delay: 250ms; }
.result-item:nth-child(7) { animation-delay: 300ms; }
.result-item:nth-child(8) { animation-delay: 350ms; }
.result-item:nth-child(9) { animation-delay: 400ms; }
.result-item:nth-child(10) { animation-delay: 450ms; }

.result-item:hover {
  border-color: #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px) translateZ(0);
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
}

.result-item:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2), 0 4px 12px rgba(102, 126, 234, 0.15);
  background: linear-gradient(135deg, #f8faff 0%, #f1f5f9 100%);
  transform: translateY(-1px) translateZ(0);
}

/* 键盘导航专用样式 */
.result-item:focus:not(:hover) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3), 0 2px 8px rgba(102, 126, 234, 0.2);
  background: linear-gradient(135deg, #f0f4ff 0%, #e6f1ff 100%);
}

/* 结果头部 */
.result-header {
  margin-bottom: 16px;
}

.result-title-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.domain-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.result-title {
  flex: 1;
  font-size: 17px;
  font-weight: 600;
  color: #212529;
  margin: 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: calc(100% - 80px); /* 为评分预留空间 */
}

.result-score {
  background: #e7f1ff;
  color: #667eea;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
}

.result-url {
  font-size: 12px;
  color: #28a745;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 4px;
}

/* 结果内容 */
.result-body {
  margin-bottom: 16px;
}

.result-content {
  font-size: 15px;
  color: #6c757d;
  line-height: 1.5;
  margin: 0;
  max-height: none; /* 允许显示更多内容 */
}

.result-highlights {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.result-highlight {
  font-size: 15px;
  color: #495057;
  line-height: 1.5;
  margin: 0;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.result-highlight mark {
  background-color: #fff3cd;
  color: #856404;
  padding: 2px 4px;
  border-radius: 3px;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(255, 193, 7, 0.2);
}

/* 结果元信息 */
.result-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #6c757d;
}

.result-meta-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.result-meta-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.visit-time,
.access-count,
.domain,
.content-length {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 加载状态 */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.loading-state p {
  margin-top: 16px;
  font-size: 14px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  color: #495057;
  margin-bottom: 8px;
}

.empty-state > p {
  margin-bottom: 24px;
  line-height: 1.5;
}

.empty-tips {
  text-align: left;
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #ffc107;
  max-width: 300px;
  margin: 0 auto;
}

.empty-tips p {
  font-weight: 600;
  margin-bottom: 8px;
  color: #495057;
}

.empty-tips ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.empty-tips li {
  padding: 4px 0;
  font-size: 13px;
  position: relative;
  padding-left: 16px;
}

.empty-tips li::before {
  content: "•";
  color: #ffc107;
  position: absolute;
  left: 0;
}

/* 更多结果提示 */
.more-results-hint {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  border-top: 1px solid #e9ecef;
  margin-top: 16px;
}

.more-results-hint p {
  margin: 0;
  font-size: 13px;
}

.hint-text {
  font-size: 12px;
  opacity: 0.8;
  margin-top: 4px !important;
}

/* 状态栏样式 */
.status-bar {
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.status-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #6c757d;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: #6c757d;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  font-size: 12px;
}

.status-separator {
  color: #dee2e6;
  margin: 0 4px;
}

.details-toggle {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.details-toggle:hover {
  background-color: #e9ecef;
}

.toggle-icon {
  transition: transform 0.2s ease;
}

.toggle-icon.expanded {
  transform: scale(1.1);
}

/* 状态详情面板 */
.status-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.detail-label {
  color: #6c757d;
  font-weight: 500;
}

.detail-value {
  color: #495057;
  font-weight: 600;
}

.detail-value.healthy {
  color: #28a745;
}

.detail-value.unhealthy {
  color: #dc3545;
}

/* 状态操作按钮 */
.status-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  background: white;
  border: 1px solid #e9ecef;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #495057;
}

.action-btn:hover {
  background-color: #f8f9fa;
  border-color: #dee2e6;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(102, 126, 234, 0.1);
  border-top: 2px solid #667eea;
  border-right: 2px solid #764ba2;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  filter: drop-shadow(0 1px 2px rgba(102, 126, 234, 0.2));
}

.loading-spinner-small {
  width: 12px;
  height: 12px;
  border: 1px solid #f3f3f3;
  border-top: 1px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner-large {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

/* 响应式设计 */
@media (max-width: 680px) {
  .recall-popup {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    max-height: none;
  }

  .result-title-section {
    flex-wrap: wrap;
  }

  .result-meta-left {
    flex-wrap: wrap;
    gap: 8px;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .filter-options {
    justify-content: center;
  }
}

/* 滚动条样式 */
.results-section::-webkit-scrollbar,
.suggestions-dropdown::-webkit-scrollbar {
  width: 6px;
}

.results-section::-webkit-scrollbar-track,
.suggestions-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.results-section::-webkit-scrollbar-thumb,
.suggestions-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.results-section::-webkit-scrollbar-thumb:hover,
.suggestions-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 焦点样式 */
.search-input:focus,
.filter-select:focus,
.domain-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 搜索输入框状态样式 */
.search-input.loading {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: linear-gradient(90deg, #ffffff 0%, #f8faff 50%, #ffffff 100%);
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

.search-input.has-value {
  border-color: #28a745;
  background-color: #f8fff9;
}

.search-input.has-value:focus {
  border-color: #667eea;
  background-color: #ffffff;
}

/* 禁用状态 */
.search-input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 搜索输入框动画 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .result-item {
    border-width: 2px;
  }

  .result-item:hover {
    border-width: 2px;
  }

  .filter-option.active {
    border-width: 2px;
  }
}

/* 语法帮助样式 */
.syntax-help-container {
  position: relative;
  display: inline-block;
}

.syntax-help-trigger {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
  min-width: 52px;
}

.syntax-help-trigger:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.syntax-help-trigger:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.4);
}

.syntax-help-trigger .help-icon {
  font-size: 12px;
}

.syntax-help-trigger .help-text {
  font-size: 11px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* Modal backdrop styles - Optimized for popup context */
.syntax-help-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px; /* Reduced from 20px for better space utilization in popup */
  animation: backdropFadeIn 0.2s ease-out;
  box-sizing: border-box;
}

/* Modal content styles - Optimized for popup constraints */
.syntax-help-modal {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 450px;
  min-width: 320px; /* Reduced from 380px for better flexibility */
  max-height: 75vh; /* Reduced from 85vh for better popup compatibility */
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
  outline: none;
  box-sizing: border-box;
  /* Ensure proper scrolling within popup context */
  overflow-x: hidden;
}

/* Modal content wrapper - Ensure proper containment */
.syntax-help-wrapper {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow proper flex shrinking */
}

/* Legacy syntax-help class for backward compatibility */
.syntax-help {
  position: absolute;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  z-index: 1001;
  width: 320px;
  max-height: 400px;
  overflow-y: auto;
  animation: syntaxHelpFadeIn 0.2s ease-out;
}

.syntax-help.bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
}

.syntax-help.top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
}

.syntax-help.left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-right: 8px;
}

.syntax-help.right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 8px;
}

/* Animation keyframes */
@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes syntaxHelpFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.syntax-help-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f8f9fa;
  width: 100%;
  box-sizing: border-box;
}

.syntax-help-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.syntax-help-close {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.syntax-help-close:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.syntax-help-content {
  padding: 16px 20px 20px;
  width: 100%;
  box-sizing: border-box;
  flex: 1;
  min-height: 0; /* Allow content to shrink and scroll if needed */
  overflow-y: auto; /* Enable scrolling for content if modal height is constrained */
}

.syntax-help-intro {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
}

.syntax-examples {
  margin-bottom: 20px;
  width: 100%;
}

.syntax-example {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #667eea;
  width: 100%;
  box-sizing: border-box;
}

.syntax-example:last-child {
  margin-bottom: 0;
}

.example-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.example-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.example-syntax {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 12px;
  color: #495057;
  font-weight: 500;
}

.example-description {
  margin: 0;
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
}

.syntax-tips {
  margin-bottom: 20px;
  padding: 12px;
  background: #fff3cd;
  border-radius: 8px;
  border-left: 3px solid #ffc107;
}

.tips-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #856404;
}

.tips-list {
  margin: 0;
  padding-left: 16px;
  font-size: 13px;
  color: #856404;
  line-height: 1.5;
}

.tips-list li {
  margin-bottom: 4px;
}

.tips-list li:last-child {
  margin-bottom: 0;
}

.syntax-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button.primary {
  background: #667eea;
  color: white;
}

.action-button.primary:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.action-button.primary:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Responsive adjustments for syntax help modal in popup context */
@media (max-height: 700px) {
  .syntax-help-modal-backdrop {
    padding: 8px; /* Further reduce padding for smaller popup heights */
  }

  .syntax-help-modal {
    max-height: 70vh; /* More conservative height for smaller popups */
  }
}

@media (max-width: 600px) {
  .syntax-help-modal-backdrop {
    padding: 8px;
  }

  .syntax-help-modal {
    min-width: 280px; /* More flexible minimum width */
    max-width: calc(100vw - 16px); /* Ensure modal fits within viewport */
  }
}

/* Ensure modal content is properly contained within popup boundaries */
@media (max-height: 600px) {
  .syntax-help-modal {
    max-height: 65vh;
  }

  .syntax-help-content {
    padding: 12px 16px 16px; /* Reduce content padding for smaller heights */
  }

  .syntax-help-header {
    padding: 12px 16px 8px; /* Reduce header padding */
  }
}

/* Specific optimizations for popup context */
.syntax-help-modal-backdrop.popup-context {
  /* Ensure the modal is properly contained within popup boundaries */
  position: fixed;
  /* Add a subtle backdrop blur for better focus */
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

.syntax-help-modal-backdrop.popup-context .syntax-help-modal {
  /* Ensure modal doesn't exceed popup boundaries */
  max-width: min(450px, calc(100vw - 24px));
  max-height: min(75vh, calc(100vh - 24px));
  /* Improve shadow for popup context */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.rc-content,
.rc-results-container,
.rc-empty-state,
.rc-error-state {
  scroll-behavior: smooth;
}

/* 改进的滚动条样式 */
.rc-results-container::-webkit-scrollbar {
  width: 6px;
}

.rc-results-container::-webkit-scrollbar-track {
  background: transparent;
}

.rc-results-container::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.rc-results-container::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* 当鼠标离开容器时隐藏滚动条 */
.rc-results-container:not(:hover)::-webkit-scrollbar-thumb {
  background: transparent;
}

/* 内联紧凑过滤器样式 */
.filters-inline {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-dropdown {
  position: relative;
}

.filter-select-compact {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 13px;
  color: #343a40;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 90px;
  min-height: 44px;
  font-weight: 500;
}

.filter-select-compact:hover {
  border-color: #667eea;
  background-color: #f1f5f9;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.1);
}

.filter-select-compact:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
  background-color: #ffffff;
}

.filter-btn-compact {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 44px;
  min-height: 44px;
  justify-content: center;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-btn-compact:hover {
  border-color: #667eea;
  background-color: #f1f5f9;
  box-shadow: 0 3px 8px rgba(102, 126, 234, 0.15);
  transform: translateY(-1px);
}

.filter-btn-compact.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

.filter-count {
  background: rgba(255, 255, 255, 0.95);
  color: #667eea;
  border-radius: 12px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 700;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

.filter-btn-compact.active .filter-count {
  background: rgba(255, 255, 255, 0.9);
  color: #667eea;
}

.clear-filters-compact {
  background: #dc3545;
  border: 2px solid #dc3545;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 44px;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.clear-filters-compact:hover {
  background: #c82333;
  border-color: #c82333;
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
  transform: translateY(-1px);
}

/* 域名过滤容器 */
.filter-domain-container {
  position: relative;
}

.domain-input-popup {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  padding: 12px;
  min-width: 200px;
  margin-top: 4px;
}

.domain-input-header {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
}

.domain-input-compact {
  flex: 1;
  padding: 4px 6px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 12px;
}

.add-btn-compact {
  background: #28a745;
  border: 1px solid #28a745;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.add-btn-compact:hover:not(:disabled) {
  background: #218838;
}

.add-btn-compact:disabled {
  background: #e9ecef;
  border-color: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

.quick-domains {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.quick-domain-btn {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 4px 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.quick-domain-btn:hover {
  background: #e9ecef;
}

.quick-domain-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.selected-domains-compact {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 80px;
  overflow-y: auto;
}

.selected-domain-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #e7f1ff;
  color: #667eea;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  border: 1px solid #b8d4ff;
}

.remove-domain-compact {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 10px;
  padding: 0;
  margin-left: 4px;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.remove-domain-compact:hover {
  background-color: #667eea;
  color: white;
}

/* 响应式调整 */
@media (max-width: 680px) {
  .rc-search-container {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .filters-inline {
    justify-content: center;
    gap: 6px;
  }

  .filter-select-compact {
    min-width: 70px;
    font-size: 11px;
  }

  .domain-input-popup {
    right: auto;
    left: 0;
    min-width: 180px;
  }
}

/* =============================================================================
   AI 初始化覆盖层样式
   ============================================================================= */

.rc-ai-initialization-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn var(--transition-medium);
}

.rc-ai-init-content {
  text-align: center;
  padding: var(--spacing-3xl);
  max-width: 300px;
}

.rc-ai-init-content h3 {
  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
  color: var(--color-ai-primary);
  font-size: 18px;
  font-weight: 600;
}

.rc-ai-init-content p {
  margin: 0 0 var(--spacing-xl) 0;
  color: var(--color-text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.rc-ai-init-progress {
  margin-top: var(--spacing-xl);
}

.rc-progress-bar {
  width: 100%;
  height: 6px;
  background: var(--color-border);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.rc-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-ai-primary), var(--color-ai-secondary));
  border-radius: 3px;
  transition: width var(--transition-slow);
  animation: shimmer 2s infinite;
}

.rc-progress-text {
  font-size: 12px;
  color: var(--color-text-muted);
}

/* 进度条发光动画 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .rc-ai-initialization-overlay {
    background: rgba(23, 25, 35, 0.95);
  }
  
  .rc-ai-init-content h3 {
    color: var(--color-ai-secondary);
  }
}

/* =============================================================================
   骨架屏加载器样式
   ============================================================================= */

/* 骨架屏容器 */
.skeleton-loader {
  padding: var(--spacing-sm) var(--spacing-lg);
  animation: fadeIn var(--transition-medium);
}

/* 单个骨架项 */
.skeleton-item {
  background: #ffffff;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-xs);
  transition: all var(--transition-fast);
}

/* 骨架内容容器 */
.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 标题区域 */
.skeleton-title {
  margin-bottom: var(--spacing-xs);
}

/* URL区域 */
.skeleton-url {
  margin-bottom: var(--spacing-sm);
}

/* 文本内容区域 */
.skeleton-text {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

/* 骨架线条基础样式 */
.skeleton-line {
  height: 12px;
  border-radius: 6px;
  background: var(--color-bg-muted);
  position: relative;
  overflow: hidden;
}

/* 标题线条 */
.skeleton-line-title {
  height: 16px;
  border-radius: 8px;
  background: var(--color-bg-subtle);
}

/* URL线条 */
.skeleton-line-url {
  height: 10px;
  border-radius: 5px;
  background: var(--color-bg-muted);
}

/* 文本线条 */
.skeleton-line-text {
  height: 12px;
  border-radius: 6px;
  background: var(--color-bg-muted);
}

/* Shimmer动画效果 */
.skeleton-animate .skeleton-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  animation: skeleton-shimmer 1.8s infinite ease-in-out;
}

/* Shimmer动画关键帧 */
@keyframes skeleton-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 屏幕阅读器隐藏 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 尺寸模式适配 */
[data-size="compact"] .skeleton-item {
  padding: var(--spacing-md);
  min-height: 65px;
  margin-bottom: var(--spacing-sm);
}

[data-size="expanded"] .skeleton-item {
  padding: var(--spacing-xl);
  min-height: 95px;
  margin-bottom: var(--spacing-lg);
}

/* 密度模式适配 */
[data-density="compact"] .skeleton-loader {
  padding: var(--spacing-xs) var(--spacing-md);
}

[data-density="compact"] .skeleton-item {
  padding: var(--spacing-md);
  min-height: 70px;
  margin-bottom: var(--spacing-sm);
}

[data-density="compact"] .skeleton-content {
  gap: var(--spacing-xs);
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .skeleton-item {
    background: #1e293b;
    border-color: #334155;
  }
  
  .skeleton-line {
    background: #334155;
  }
  
  .skeleton-line-title {
    background: #475569;
  }
  
  .skeleton-line-url {
    background: #334155;
  }
  
  .skeleton-animate .skeleton-line::before {
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(148, 163, 184, 0.3) 50%,
      transparent 100%
    );
  }
}

/* 骨架屏进入动画优化 */
.skeleton-item:nth-child(1) { animation-delay: 0ms; }
.skeleton-item:nth-child(2) { animation-delay: 100ms; }
.skeleton-item:nth-child(3) { animation-delay: 200ms; }
.skeleton-item:nth-child(4) { animation-delay: 300ms; }
.skeleton-item:nth-child(5) { animation-delay: 400ms; }

/* =============================================================================
   流式结果加载样式
   ============================================================================= */

/* 流式加载进度指示器 */
.streaming-progress {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-left: var(--spacing-md);
  font-size: 12px;
  color: var(--color-text-muted);
}

.streaming-indicator {
  color: var(--color-primary);
  font-weight: 500;
}

/* 流式加载进度条 */
.streaming-progress-bar {
  display: inline-block;
  width: 60px;
  height: 3px;
  background: var(--color-border);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.streaming-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-dark));
  border-radius: 2px;
  transition: width var(--transition-fast);
  animation: streaming-pulse 1.5s infinite;
}

/* 流式加载进度条脉动动画 */
@keyframes streaming-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 结果项渐入动画 */
.results-list .result-item {
  animation: result-fade-in 0.3s ease-out forwards;
  opacity: 0;
  transform: translateY(10px);
}

@keyframes result-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 为流式加载的结果项添加延迟动画 */
.results-list .result-item:nth-child(1) { animation-delay: 0ms; }
.results-list .result-item:nth-child(2) { animation-delay: 50ms; }
.results-list .result-item:nth-child(3) { animation-delay: 100ms; }
.results-list .result-item:nth-child(4) { animation-delay: 150ms; }
.results-list .result-item:nth-child(5) { animation-delay: 200ms; }
.results-list .result-item:nth-child(6) { animation-delay: 250ms; }
.results-list .result-item:nth-child(7) { animation-delay: 300ms; }
.results-list .result-item:nth-child(8) { animation-delay: 350ms; }
.results-list .result-item:nth-child(9) { animation-delay: 400ms; }
.results-list .result-item:nth-child(10) { animation-delay: 450ms; }

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .streaming-progress-bar {
    background: #334155;
  }
  
  .streaming-indicator {
    color: var(--color-ai-secondary);
  }
}
