<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Recall - 配置中心</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        min-height: 100vh;
        background-color: #f8f9fa;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      }
      
      /* Loading styles */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        flex-direction: column;
        gap: 16px;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #6c757d;
        font-size: 16px;
      }
    </style>
  </head>
  <body>
    <div id="options-root">
      <!-- Loading state while React app loads -->
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载配置中心...</div>
      </div>
    </div>
    <script type="module" src="/src/options/main.tsx"></script>
  </body>
</html>
